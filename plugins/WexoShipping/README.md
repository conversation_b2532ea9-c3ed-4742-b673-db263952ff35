# WEXO shipping core for Shopware 6

This plugin contains the core functionality that extends Shopware 6 to allow for 
advanced features like parcel shop shipping methods, day-by-day shipping deadlines, shipping comments and more.

### Before use

Note that the deadline feature uses server time to determine the correct time and date. 
Ensure that PHP is configured to use the desired timezone, otherwise this feature does not work as expected.

This module also calculates the price of each shipping method in checkout which is not standard in Shopware 6 at the moment. 
To do so, it uses the `Wexo\Shipping\Service\ShippingPriceCalculator` service which leans on Shopwares own `Shopware\Core\Checkout\Cart\Delivery\DeliveryCalculator`.
We do not use this class directly as this would require cloning the entire Cart for each price calculation, while the current solution only requires cloning Delivery objects.

### Adding a new parcel shop provider

Adding a new parcel shop provider is as simple as a plugin with a service that implements the `Wexo\Shipping\Service\ShippingMethodTypeInterface`.
This interface defines a service capable of searching for parcel shops in the providers API using address fields, and looking up a parcel shop based on the providers ID.

This service must then be tagged with the `wexo.shipping.type` tag along with a `key` to identify which parcelshops belong to this provider.
It should ideally also contain an image of the providers logo.
From here, the core module should take care of the rest.

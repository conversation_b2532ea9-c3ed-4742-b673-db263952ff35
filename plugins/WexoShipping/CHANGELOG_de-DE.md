# 3.1.0
* Der Routenname des Paketshops wurde von wexo.frontend.parcel-shops in frontend.wexo.parcel-shops geändert

# 3.0.3
* Verwende richtige URL für Paketshopsuche

# 3.0.2
* Unterstützung für Store API hinzugefügt

# 3.0.1
* Leichte Codeänderungen, um mehr Flexibilität beim Erweitern des Plugins zu ermöglichen

# 3.0.0
* Kompatibilität mit Shopware 6.5

# 2.1.5
* <PERSON>s wurde ein Problem behoben, bei dem Warteschlangenarbeiter Nachrichten manchmal nicht deserialisieren konnten, wenn PHP 8.2 ausgeführt wurde

# 2.1.4
* Unterstützung für Feature-Flag 6_5_0_0 hinzugefügt

# 2.1.3
* Problem behoben, bei dem es nicht möglich war, die aktuelle Adresse des Paketshops zu ändern

# 2.1.2
* Problem behoben, bei dem ContextSwitch die Versandmethode nicht änderte, wenn kein Paketshop ausgewählt wurde
* Problem behoben, bei dem der ausgewählte Paketshop gelegentlich nicht mit dem aktuellen Paketshop-Typ übereinstimmte

# 2.1.1
* Entfernen Sie die Verwendung von @RouteScope, das veraltet ist

# 2.1.0
* Einstellung zum Speichern der Adresse des Paketshops als Lieferadresse des Kunden hinzugefügt. Standardmäßig aktiviert, da dies das aktuelle Verhalten ist.

# 2.0.23
* Behoben, dass norwegische Übersetzungen aufgrund falscher Groß- und Kleinschreibung im Dateinamen nicht funktionierten

# 2.0.22
* Es wurde ein Fehler behoben, bei dem Lieferkosten falsch berechnet werden konnten

# 2.0.21

* Norwegische (nb_NO) Übersetzungen hinzugefügt (Danke an Kurt Inge Smådal / Flow Retail für die Bereitstellung der Übersetzungen)
* Überprüfung hinzugefügt, um sicherzustellen, dass die Versandsteuer bei der Berechnung der Versandkosten definiert ist
* Die englischen und dänischen Übersetzungen für noParcelShop wurden korrigiert
* Problem behoben, bei dem Paketshop-Optionen gelegentlich nicht geladen wurden

# 2.0.20

* Es wurde ein Fehler behoben, bei dem nicht verfügbare Versandmethoden Probleme beim Bezahlvorgang verursachen konnten

# 2.0.19

* Mehr Anpassbarkeit in Übersetzungs-Snippets.
* Ein Fehler mit angezeigten Versandaktionspreisen wurde behoben.

# 2.0.18

* Paketshop-Etiketten für Eingabefelder weniger fehleranfällig gemacht
* Paketshops ohne Breiten-/Längengrad zulassen

# 2.0.16

* Shopware ******* Kompatibilität

# 2.0.15

* Styling-Problem mit Versandkommentar beheben

# 2.0.14

* Label "empfohlen" entfernt

# 2.0.12

* Block für die Schaltfläche zum Senden hinzugefügt, um eine einfache Erweiterung zu ermöglichen

# 2.0.11

* Aktivieren Sie die einfachere Twig-Erweiterung

# 2.0.10

* Wählen Sie im Checkout automatisch den nächsten Paketshop aus, wenn der Paketshop die Standard-Versandmethode ist

# 2.0.8

* Behandeln Sie potenzielle Fehler bei der Verwendung von zusammengeschlossenen Paketshops

# 2.0.7

* Zusätzliche Validierung, wenn Versandanbieter-Plugins deaktiviert sind

# 2.0.6

* Volle Reaktionsfähigkeit für Modal
* Immer 200 OK im Netzwerk-Tab anzeigen 200

# 2.0.5

* Besserer Umgang mit fehlender Paketshop-ID
* Zählerstrafen sind erforderlich
* Versandkommentar-Etikett behoben

# 2.0.4

* Kompilierte JS-Dateien hinzugefügt

# 2.0.3

* Behoben, dass ein Versandkommentar erforderlich ist
* Shopware 6.4-Kompatibilität
* MySQL 5.7-Kompatibilität

# 1.0.19

* Explizite Backticks zu allen SQL-Migrationsanweisungen zur Unterstützung von MariaDB

# 1.0.18

* Korrektur, dass die Schaltfläche "Speichern" auf kleinen Geräten teilweise ausgeblendet ist
* Modal beim Speichern schließen
* Behebung, dass Modal unter Safari / MacOS nicht richtig funktioniert

# 1.0.14

* Deaktivieren Sie den Paketladen im Offcanvas-Einkaufswagen
* Schnipsel für Lieferkommentar
* Problemumgehung für Shopware onSave JS Super Bug
* Ermöglichen Sie das Senden von Straße / Postleitzahl an Paket-Shop-APIs

# 1.0.8

* Größe des Plugin-Symbols korrigieren

# 1.0.7

* Kombinieren Sie Versandanbieter in einer einzigen Karte
* Entfernungsstrafe für Versandanbieter
* Frist für die nächste Lieferzeit
* Übermittlungskommentarfeld. Kann nach Bedarf eingestellt werden

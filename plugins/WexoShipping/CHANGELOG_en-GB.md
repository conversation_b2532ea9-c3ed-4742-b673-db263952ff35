# 3.1.0
* Changed parcel shop route name from wexo.frontend.parcel-shops to frontend.wexo.parcel-shops

# 3.0.3
* Use correct url parcel shop search

# 3.0.2
* Added support for Store Api

# 3.0.1
* Slight code changes to allow more flexibility when extending the plugin

# 3.0.0
* Shopware 6.5 compatibility

# 2.1.5
* Fixed an issue where queue workers would sometimes fail to deserialise messages when running PHP 8.2

# 2.1.4
* Added support for feature flag 6_5_0_0

# 2.1.3
* Fixed issue where it wasn't possible to change current parcelshop address

# 2.1.2
* Fixed issue where ContextSwitch wouldn't change shipping method if a parcelshop wasn't selected
* Fixed issue where the selected parcelshop occasionally didn't belong to the selected provider  

# 2.1.1
* Remove the use of @RouteScope which is deprecated

# 2.1.0
* Added setting for saving the parcelshops address as the customers delivery address. Enabled by default as it is the current behavior.

# 2.0.23
* Fixed norwegian translations not working due to wrong casing in file name

# 2.0.22
* Fixed a bug where delivery costs could be calculated incorrectly

# 2.0.21

* Added Norwegian (nb_NO) translations (Thanks to <PERSON> / Flow Retail for providing the translations)
* Added check to ensure shipping tax is defined when calculating shipping cost
* Fixed the English & Danish translations for noParcelShop
* Fixed issue where parcelshop options occasionally weren't loaded

# 2.0.20

* Fixed a bug where unavailable shipping methods could cause issues in checkout

# 2.0.19

* More customizability in translation snippets.
* Fixed a bug with shown shipping promotion prices.

# 2.0.18

* Made Parcelshop labels for input fields less error-prone
* Allow Parcelshops without latitude/longitude

# 2.0.16

* Shopware ******* compatability

# 2.0.15

* Fix styling issue with shipping comment 

# 2.0.14

* Removed "Recommended" label

# 2.0.12

* Added block for the submit button for easy extend

# 2.0.11

* Enable easier Twig extension

# 2.0.10

* Automatically choose nearest parcel shop in checkout if parcel shop is the default shipping method

# 2.0.8

* Handle potential error when using merged parcel shops 

# 2.0.7

* Extra validation if shipping provider plugins are disabled

# 2.0.6

* Full responsiveness for modal
* Always show 200 OK in the network tab

# 2.0.5

* Better handling of missing parcel shop ID
* Meter penalties are required
* Shipping comment label fix

# 2.0.4

* Added compiled JS files

# 2.0.3

* Fix shipping comment being required
* Shopware 6.4 compatibility
* MySQL 5.7 compatibility

# 1.0.19

* Explicit backticks to all migration SQL statements to support MariaDB

# 1.0.18

* Fix save button being partially hidden on small devices
* Close modal on save
* Fix modal not working correctly on Safari/macOS

# 1.0.14

* Disable parcel shop in offcanvas cart
* Snippets for delivery comment
* Workaround for Shopware onSave JS super bug
* Allow submitting street / zip code to parcel shop APIs

# 1.0.8

* Fix size of plugin icon

# 1.0.7

* Combine shipping providers in single map
* Distance penalty for shipping providers
* Deadline for next shipping time
* Delivery comment field. Can be set as required

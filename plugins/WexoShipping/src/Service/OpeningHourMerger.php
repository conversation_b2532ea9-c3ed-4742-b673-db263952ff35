<?php declare(strict_types=1);

namespace Wexo\Shipping\Service;

class OpeningHourMerger
{
    /**
     * Takes a list of opening hours in the format:
     * [
     *      [
     *          'hours' => '08:00 - 20:00',
     *          'day' => 'Mon'
     *      ],
     *      [
     *          'hours' => '08:00 - 20:00',
     *          'day' => 'Tue'
     *      ],
     *      ...
     * ]
     * and merges these into a user-friendly array with combined days where possible:
     * [
     *      [
     *          'hours' => '08:00 - 20:00',
     *          'day' => 'Mon-Fri'
     *      ],
     *      [
     *          'hours' => '08:00 - 13:00',
     *          'day' => 'Sat'
     *      ],
     *      [
     *          'hours' => '10:00 - 12:00',
     *          'day' => 'Sun'
     *      ],
     * ]
     */
    public function mergeOpeningHours(array $openingHours): array
    {
        $combinedOpeningHours = [];
        $startDay = null;
        $prevDayIndex = null;
        $prevHours = null;
        $lastDayIndex = array_key_last($openingHours);

        foreach ($openingHours as $day => $openingHourData) {
            // Check if the previous day is yesterday and if it matches today's opening hours.
            if (($day - 1) === $prevDayIndex && $openingHourData['hours'] === $prevHours) {
                // If this is the start of a group, note the start day
                if (!$startDay) {
                    $startDay = $openingHours[$prevDayIndex]['day'];
                }
            } else {
                if ($startDay) {
                    // If there is a startDay but previous day wasn't yesterday or the hours didn't match,
                    // the previous combination ends.
                    $prevDay = $openingHours[$prevDayIndex]['day'];
                    $combinedOpeningHours[] = [
                        'day' => $startDay . '-' . $prevDay,
                        'hours' => $prevHours
                    ];
                    $startDay = null;
                } elseif ($prevDayIndex !== null && $prevHours) {
                    // If there is no startDay and previous day doesn't match this day, add the previous day alone.
                    $prevDay = $openingHours[$prevDayIndex]['day'];
                    $combinedOpeningHours[] = [
                        'day' => $prevDay,
                        'hours' => $prevHours
                    ];
                }
            }

            // If this is the final day of the week, we add this final combination/day to the combined array.
            if ($lastDayIndex === $day) {
                //Check if there's a combination being built
                if ($startDay) {
                    $combinedOpeningHours[] = [
                        'day' => $startDay . '-' . $openingHourData['day'],
                        'hours' => $openingHourData['hours']
                    ];
                } else {
                    $combinedOpeningHours[] = [
                        'day' => $openingHourData['day'],
                        'hours' => $openingHourData['hours']
                    ];
                }
            } else {
                // Expose necessary data to next iteration
                $prevDayIndex = $day;
                $prevHours = $openingHourData['hours'];
            }
        }

        return $combinedOpeningHours;
    }
}

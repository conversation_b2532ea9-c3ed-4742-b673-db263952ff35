<?php declare(strict_types=1);

namespace Wexo\Shipping\Service;

class TypeOptionService
{
    private readonly array $typeOptions;

    public function __construct(\Traversable $types)
    {
        $this->typeOptions = iterator_to_array($types);
    }

    public function getType(string $key): ?ShippingMethodTypeInterface
    {
        return $this->typeOptions[$key] ?? null;
    }

    public function getTypes(): array
    {
        return $this->typeOptions;
    }
}

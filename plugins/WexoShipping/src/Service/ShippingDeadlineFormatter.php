<?php declare(strict_types=1);

namespace Wexo\Shipping\Service;

use DateTime;
use Exception;
use Symfony\Contracts\Translation\TranslatorInterface;
use Wexo\Shipping\Core\Content\ShippingMethodDeadline\ShippingMethodDeadlineEntity;
use Wexo\Shipping\Struct\FormattedDeadline;
use function array_filter;
use function date;
use function strtotime;
use function time;

class ShippingDeadlineFormatter
{
    public function __construct(private readonly TranslatorInterface $translator)
    {
    }

    public function getFormattedDeadline(ShippingMethodDeadlineEntity $deadline): ?FormattedDeadline
    {
        //Ensure that deadlines are actually configured so we don't infinite loop.
        if (empty(array_filter($deadline->getDeadlines()))) {
            return null;
        }
        $now = time();
        try {
            $candidateTimestamp = $this->findNextShippingDay($deadline, $now);
        } catch (Exception) {
            return null;
        }
        $dayOfWeek = date('l', $candidateTimestamp);
        if ($candidateTimestamp - $now >= 604800) {
            // The timestamp is more than a week ahead (86400 seconds * 7), so we need date + weekday + time.
            $formattedDeadlineString = $this->translator->trans("shippingMethod.deadline.orderBeforeDate", [
                '%date%' => date('Y-m-d', $candidateTimestamp),
                '%weekday%' => $this->translator->trans("shippingMethod.dates." . $dayOfWeek),
                '%timestamp%' => $deadline->getDeadlines()[$dayOfWeek]
            ]);
        } elseif ($candidateTimestamp > $now) {
            // The timestamp is less than a week ahead so weekday + time is good enough to discern the deadline.
            $formattedDeadlineString = $this->translator->trans("shippingMethod.deadline.orderBeforeWeekday", [
                '%weekday%' => $this->translator->trans("shippingMethod.dates." . $dayOfWeek),
                '%timestamp%' => $deadline->getDeadlines()[$dayOfWeek]
            ]);
        } else {
            //The timestamp is today, so the time is enough.
            $formattedDeadlineString = $this->translator->trans("shippingMethod.deadline.orderBeforeTime", [
                '%timestamp%' => $deadline->getDeadlines()[$dayOfWeek]
            ]);
        }
        return new FormattedDeadline($formattedDeadlineString);
    }

    /**
     * Finds the next available day for shipping.
     * @throws Exception
     */
    protected function findNextShippingDay(
        ShippingMethodDeadlineEntity $deadline,
        int $candidateTimestamp
    ): int {
        $dayOfWeek = date('l', $candidateTimestamp);
        $date = date('Y-m-d', $candidateTimestamp);
        $dateTime = new DateTime($date);
        // Check if we're past this weekday's deadline (if there even is one)
        $isPastShippingDeadline = !isset($deadline->getDeadlines()[$dayOfWeek]) ||
            strtotime($date . ' ' . $deadline->getDeadlines()[$dayOfWeek]) <= $candidateTimestamp;
        $isExcludedDate = !empty(
            array_filter(
                $deadline->getExcludedDates(),
                fn(array $excludedDate) => $excludedDate['date'] === $date
            )
        );
        if ($isPastShippingDeadline || $isExcludedDate) {
            //Try next day and ensure that we look from the start of the day to not miss next day's deadline.
            $dateTime->modify('+1 day');
            $dateTime->setTime(0, 0);
            return $this->findNextShippingDay($deadline, $dateTime->getTimestamp());
        }
        return $candidateTimestamp;
    }
}

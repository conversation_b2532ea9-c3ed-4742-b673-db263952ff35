<?php declare(strict_types=1);

namespace Wexo\Shipping\Service;

use Shopware\Core\System\SalesChannel\SalesChannelContext;
use Symfony\Component\HttpFoundation\ParameterBag;
use Wexo\Shipping\Core\Content\ParcelShop\ParcelShopEntity;

/**
 * Any advanced shipping method must implement this interface
 * and tag the service as "wexo.shipping.type" along with a key.
 * Interface TypeOptionInterface
 * @package Wexo\Shipping\Service
 */
interface ShippingMethodTypeInterface
{
    /**
     * Administration label for the option
     * @return string
     */
    public function getLabel(): string;

    /**
     * Logo of the provider
     * @return string
     */
    public function getLogoUrl(): string;

    /**
     * Lookup a list of parcel shops from the provider based on country code/zipcode/street
     * @return array<ParcelShopEntity>
     */
    public function getParcelShops(ParameterBag $parameterBag, SalesChannelContext $context): array;

    /**
     * Get a parcel shop based on an ID. ParameterBag contains an ID and a country code.
     * @param ParameterBag $parameterBag
     * @param SalesChannelContext $context
     * @return ParcelShopEntity|null
     */
    public function getParcelShop(ParameterBag $parameterBag, SalesChannelContext $context): ?ParcelShopEntity;
}

<?php declare(strict_types=1);

namespace Wexo\Shipping\Controller;

use Shopware\Core\Framework\Routing\Annotation\RouteScope;
use Symfony\Component\Routing\Annotation\Route;
use Shopware\Core\Framework\Context;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Wexo\Shipping\Service\TypeOptionService;

#[Route(defaults: ['_routeScope' => ['api']])]
class ShippingTypeController
{
    public function __construct(
        private readonly TypeOptionService $typeOptionService,
    ) {
    }

    #[Route(path: '/api/wexo/shipping-types', name: 'api.action.wexo.shipping-types', methods: ['GET'])]
    public function getShippingTypes(Request $request, Context $context): JsonResponse
    {
        $options = [
            [
                'label' => "-",
                'value' => null
            ]
        ];
        foreach ($this->typeOptionService->getTypes() as $key => $typeOption) {
            $options[] = [
                'label' => $typeOption->getLabel(),
                'value' => $key
            ];
        }
        return new JsonResponse($options);
    }
}

!function(e){var n={};function i(t){if(n[t])return n[t].exports;var o=n[t]={i:t,l:!1,exports:{}};return e[t].call(o.exports,o,o.exports,i),o.l=!0,o.exports}i.m=e,i.c=n,i.d=function(e,n,t){i.o(e,n)||Object.defineProperty(e,n,{enumerable:!0,get:t})},i.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},i.t=function(e,n){if(1&n&&(e=i(e)),8&n)return e;if(4&n&&"object"==typeof e&&e&&e.__esModule)return e;var t=Object.create(null);if(i.r(t),Object.defineProperty(t,"default",{enumerable:!0,value:e}),2&n&&"string"!=typeof e)for(var o in e)i.d(t,o,function(n){return e[n]}.bind(null,o));return t},i.n=function(e){var n=e&&e.__esModule?function(){return e.default}:function(){return e};return i.d(n,"a",n),n},i.o=function(e,n){return Object.prototype.hasOwnProperty.call(e,n)},i.p=(window.__sw__.assetPath + '/bundles/wexoshipping/'),i(i.s="I4Q4")}({"B/2W":function(e){e.exports=JSON.parse('{"wexoShipping":{"generalConfig":"Ekstra indstillinger","shippingCommentRequired":"Fragt kommentar er påkrævet","shippingCommentRequiredHelp":"Slå denne indstilling til, hvis fragtudbyderen kræver kommentar omkring levering","noParcelShop":"Ingen (Ikke-pakkeshop fragtmetode)","parcelShop":"Pakkeshop","provider":{"choose":"Vælg pakkeshop udbyder"},"penalty":{"from":"Fra","to":"Til","penalty-amount":"Antal","addTimeSlot":"Tilføj meterstraf tidsperiode"},"shippingComment":"Fragt kommentar","deadline":{"title":"Deadline for daglige leveringer","time":"Klokkeslæt","excludedWeekdays":"Ugedage uden levering","days":{"monday":"Mandag","tuesday":"Tirsdag","wednesday":"Onsdag","thursday":"Torsdag","friday":"Fredag","saturday":"Lørdag","sunday":"Søndag"},"exception":"Dato uden levering","addDeadlineException":"Tilføj dato uden levering"}}}')},ClHn:function(e){e.exports=JSON.parse('{"wexoShipping":{"generalConfig":"Additional settings","shippingCommentRequired":"Shipping comment is required","shippingCommentRequiredHelp":"Enable this setting if the provider requires the customer to fill out a comment for delivery.","noParcelShop":"None (non-parcel shop shipping method)","parcelShop":"Parcel Shop","provider":{"choose":"Choose parcel shop provider"},"penalty":{"from":"From","to":"To","penaltyAmount":"Amount","addTimeSlot":"Add meter-penalty timeslot"},"shippingComment":"Shipping comment","deadline":{"title":"Deadline for shipping","time":"Time","excludedWeekdays":"Weekdays without shipping","days":{"monday":"Monday","tuesday":"Tuesday","wednesday":"Wednesday","thursday":"Thursday","friday":"Friday","saturday":"Saturday","sunday":"Sunday"},"exception":"Date without delivery","addDeadlineException":"Add date without delivery"}}}')},EoUx:function(e){e.exports=JSON.parse('{"wexoShipping":{"generalConfig":"Zusätzliche Einstellungen","shippingCommentRequired":"Versandkommentar ist erforderlich","shippingCommentRequiredHelp":"Aktivieren Sie diese Einstellung, wenn der Anbieter vom Kunden verlangt, einen Kommentar zur Lieferung auszufüllen","noParcelShop":"Keine (Nicht-Paket-Shop Versandmethode)","parcelShop":"Paketladen","provider":{"choose":"Wählen Sie den Paketanbieter"},"penalty":{"from":"Von","to":"Zu","penaltyAmount":"Menge","addTimeSlot":"Zeitfenster für Meter-Strafe hinzufügen"},"shippingComment":"Versandkommentar","deadline":{"title":"Versandfrist","time":"Zeit","excludedWeekdays":"Wochentags ohne Versand","days":{"monday":"Montag","tuesday":"Dienstag","wednesday":"Mittwoch","thursday":"Donnerstag","friday":"Freitag","saturday":"Samstag","sunday":"Sonntag"},"exception":"Datum ohne Lieferung","addDeadlineException":"Datum ohne Lieferung hinzufügen"}}}')},I4Q4:function(e,n,i){"use strict";i.r(n);var t=Shopware,o=t.Component,p=t.Context,r=t.Data.Criteria;o.override("sw-settings-shipping-detail",{template:'{% block sw_settings_shipping_detail_top_ruleshippingPriceStore %}\n    {% parent() %}\n\n    <sw-card v-if="shippingMethodConfig"\n         class="sw-settings-shipping-detail__wexo_shipping_method_config"\n         :title="$tc(\'wexoShipping.generalConfig\')">\n        <sw-checkbox-field :label="$tc(\'wexoShipping.shippingCommentRequired\')"\n                           :helpText="$tc(\'wexoShipping.shippingCommentRequiredHelp\')"\n                           v-model="shippingMethodConfig.shippingCommentRequired">\n        </sw-checkbox-field>\n    </sw-card>\n\n    <sw-card v-if="shippingMethodDeadline && feature.isActive(\'FEATURE_WEXOSHIPPING_1\')"\n             class="sw-settings-shipping-detail__wexo_shipping_method_deadline"\n             :title="$tc(\'wexoShipping.deadline.title\')">\n        {% block sw_settings_shipping_deadline %}\n            <sw-container columns="1fr 1fr 1fr 1fr" gap="16px">\n                <sw-datepicker v-model="shippingMethodDeadline.deadlines.Monday"\n                               :config="{enableTime: true, time_24hr: true, dateFormat: \'H:i\', noCalendar: true}"\n                               :label="$tc(\'wexoShipping.deadline.days.monday\')"\n                               dateType="time">\n                </sw-datepicker>\n                <sw-datepicker v-model="shippingMethodDeadline.deadlines.Tuesday"\n                               :config="{enableTime: true, time_24hr: true, dateFormat: \'H:i\', noCalendar: true}"\n                               :label="$tc(\'wexoShipping.deadline.days.tuesday\')"\n                               dateType="time">\n                </sw-datepicker>\n                <sw-datepicker v-model="shippingMethodDeadline.deadlines.Wednesday"\n                               :config="{enableTime: true, time_24hr: true, dateFormat: \'H:i\', noCalendar: true}"\n                               :label="$tc(\'wexoShipping.deadline.days.wednesday\')"\n                               dateType="time">\n                </sw-datepicker>\n                <sw-datepicker v-model="shippingMethodDeadline.deadlines.Thursday"\n                               :config="{enableTime: true, time_24hr: true, dateFormat: \'H:i\', noCalendar: true}"\n                               :label="$tc(\'wexoShipping.deadline.days.thursday\')"\n                               dateType="time">\n                </sw-datepicker>\n                <sw-datepicker v-model="shippingMethodDeadline.deadlines.Friday"\n                               :config="{enableTime: true, time_24hr: true, dateFormat: \'H:i\', noCalendar: true}"\n                               :label="$tc(\'wexoShipping.deadline.days.friday\')"\n                               dateType="time">\n                </sw-datepicker>\n                <sw-datepicker v-model="shippingMethodDeadline.deadlines.Saturday"\n                               :config="{enableTime: true, time_24hr: true, dateFormat: \'H:i\', noCalendar: true}"\n                               :label="$tc(\'wexoShipping.deadline.days.saturday\')"\n                               dateType="time">\n                </sw-datepicker>\n                <sw-datepicker v-model="shippingMethodDeadline.deadlines.Sunday"\n                               :config="{enableTime: true, time_24hr: true, dateFormat: \'H:i\', noCalendar: true}"\n                               :label="$tc(\'wexoShipping.deadline.days.sunday\')"\n                               dateType="time">\n                </sw-datepicker>\n            </sw-container>\n            <sw-container v-for="(excludedDate, index) in shippingMethodDeadline.excludedDates" columns="1fr 24px" gap="16px" :key="index">\n                <sw-datepicker :config="{dateFormat: \'Y-m-d\'}"\n                               v-model="excludedDate.date"\n                               :label="$tc(\'wexoShipping.deadline.exception\')"\n                               dateType="date">\n                </sw-datepicker>\n                <sw-icon style="margin-top: 36px; cursor: pointer;" name="default-basic-x-line" @click="() => deleteDeadlineException(index)"></sw-icon>\n            </sw-container>\n            <sw-button variant="primary"\n                       :block="true"\n                       @click="addDeadlineException">\n                {{ $tc(\'wexoShipping.deadline.addDeadlineException\') }}\n            </sw-button>\n        {% endblock %}\n    </sw-card>\n\n    <sw-card v-if="shippingMethodType"\n             class="sw-settings-shipping-detail__wexo_shipping_method_type"\n             :title="$tc(\'wexoShipping.parcelShop\')">\n        {% block sw_settings_shipping_parcel_shop_provider %}\n            <sw-single-select v-model="shippingMethodType.typeKey"\n                              :isLoading="loadingShippingTypes"\n                              :disabled="loadingShippingTypes || !acl.can(\'shipping.editor\')"\n                              :options="shippingMethodTypes"\n                              :label="$tc(\'wexoShipping.provider.choose\')">\n            </sw-single-select>\n        {% endblock %}\n        {% block sw_settings_shipping_ordering_penalties %}\n            <template v-if="shippingMethodType.typeKey && feature.isActive(\'FEATURE_WEXOSHIPPING_1\')">\n                <sw-container v-for="(penalty, index) in shippingMethodType.penalties" columns="1fr 1fr 1fr 24px" gap="16px" :key="index">\n                    <sw-datepicker :config="{enableTime: true, time_24hr: true, dateFormat: \'H:i\', noCalendar: true}"\n                                   v-model="penalty.from"\n                                   :label="$tc(\'wexoShipping.penalty.from\')"\n                                   :required="true"\n                                   dateType="time">\n                    </sw-datepicker>\n                    <sw-datepicker :config="{enableTime: true, time_24hr: true, dateFormat: \'H:i\', noCalendar: true}"\n                                   v-model="penalty.to"\n                                   :label="$tc(\'wexoShipping.penalty.to\')"\n                                   :required="true"\n                                   dateType="time">\n                    </sw-datepicker>\n                    <sw-number-field :label="$tc(\'wexoShipping.penalty.penaltyAmount\')"\n                                     v-model="penalty.penalty"\n                                     :min="0"\n                                     :allowEmpty="false"\n                                     :required="true"\n                                     placeholder="m">\n                    </sw-number-field>\n                    <sw-icon style="margin-top: 36px; cursor: pointer;" name="default-basic-x-line" @click="() => deletePenalty(index)"></sw-icon>\n                </sw-container>\n                <sw-button variant="primary"\n                           :isLoading="loadingShippingTypes"\n                           :block="true"\n                           @click="addParcelShopPenalty">\n                    {{ $tc(\'wexoShipping.penalty.addTimeSlot\') }}\n                </sw-button>\n            </template>\n        {% endblock %}\n    </sw-card>\n{% endblock %}\n\n{% block sw_settings_shipping_detail_actions_save %}\n    <sw-button-process\n        class="sw-settings-shipping-method-detail__save-action"\n        :isLoading="isProcessLoading"\n        v-model="isSaveSuccessful"\n        :disabled="isProcessLoading || !acl.can(\'shipping.editor\')"\n        variant="primary"\n        v-tooltip.bottom="tooltipSave"\n        @click.prevent="onSaveOverride"> \n        {{ $tc(\'sw-settings-shipping.detail.buttonSave\') }}\n    </sw-button-process>\n{% endblock %}\n',inject:["ShippingMethodTypeService","feature"],data:function(){return{shippingMethodConfigRepository:null,shippingMethodConfig:null,shippingMethodDeadlineRepository:null,shippingMethodDeadline:null,shippingMethodTypeRepository:null,shippingMethodType:null,shippingMethodTypes:[],loadingShippingTypes:!0}},methods:{createdComponent:function(){var e=this;this.$super("createdComponent"),this.ShippingMethodTypeService.getTypes().then((function(n){e.shippingMethodTypes=n,e.loadingShippingTypes=!1})).catch((function(n){e.createNotificationError({title:"Error loading available shipping method types",message:n})})),this.shippingMethodConfigRepository=this.repositoryFactory.create("wexo_shipping_method_config"),this.shippingMethodTypeRepository=this.repositoryFactory.create("wexo_shipping_method_type"),this.shippingMethodDeadlineRepository=this.repositoryFactory.create("wexo_shipping_method_deadline");var n=new r;n.addFilter(r.equals("shippingMethodId",this.shippingMethodId)),this.shippingMethodConfigRepository.search(n,p.api).then((function(n){n.total?e.shippingMethodConfig=n.first():(e.shippingMethodConfig=e.shippingMethodConfigRepository.create(p.api),e.shippingMethodConfig.shippingMethodId=e.shippingMethod.id||e.shippingMethodId,e.shippingMethodConfig.shippingCommentRequired=!1)})),this.shippingMethodTypeRepository.search(n,p.api).then((function(n){n.total?e.shippingMethodType=n.first():(e.shippingMethodType=e.shippingMethodTypeRepository.create(p.api),e.shippingMethodType.shippingMethodId=e.shippingMethod.id||e.shippingMethodId,e.shippingMethodType.typeKey=null)})).catch((function(n){e.createNotificationError({title:"Error loading shipping method type",message:n})})),this.shippingMethodDeadlineRepository.search(n,p.api).then((function(n){n.total?e.shippingMethodDeadline=n.first():(e.shippingMethodDeadline=e.shippingMethodDeadlineRepository.create(p.api),e.shippingMethodDeadline.shippingMethodId=e.shippingMethod.id||e.shippingMethodId,e.shippingMethodDeadline.deadlines={Monday:null,Tuesday:null,Wednesday:null,Thursday:null,Friday:null,Saturday:null,Sunday:null},e.shippingMethodDeadline.excludedDates=[])})).catch((function(n){e.createNotificationError({title:"Error loading shipping method deadline",message:n})}))},onSaveOverride:function(){var e=this,n=[];this.shippingMethodType.typeKey||this.shippingMethodType._isNew||n.push(this.deleteParcelShop());var i=Object.values(this.shippingMethodDeadline.deadlines).filter((function(e){return null!==e})).length;return i||this.shippingMethodDeadline._isNew||n.push(this.deleteDeadline()),Promise.all(n).then((function(){return e.onSave().then((function(){e.shippingMethodType.typeKey&&e.saveParcelShop(),i&&e.saveDeadline(),e.saveAdditionalConfig()}))}))},saveAdditionalConfig:function(){var e=this;this.shippingMethodConfigRepository.save(this.shippingMethodConfig,p.api).then((function(){e.shippingMethodConfig._isNew=!1})).catch((function(n){e.createNotificationError({title:"Error saving additional WEXO shipping method configuration",message:n})}))},deleteParcelShop:function(){var e=this;return this.shippingMethodTypeRepository.delete(this.shippingMethodType.id,p.api).then((function(){e.shippingMethodType=e.shippingMethodTypeRepository.create(p.api),e.shippingMethodType.shippingMethodId=e.shippingMethodId,e.shippingMethodType.typeKey=null,e.shippingMethodType.penalties=[]})).catch((function(n){e.createNotificationError({title:"Error deleting shipping method type",message:n})}))},saveParcelShop:function(){var e=this;this.shippingMethodType.penalties&&(this.shippingMethodType.penalties=this.shippingMethodType.penalties.filter((function(e){return e.from&&e.to&&e.penalty}))),this.shippingMethodTypeRepository.save(this.shippingMethodType,p.api).then((function(){e.shippingMethodType._isNew=!1})).catch((function(n){e.createNotificationError({title:"Error saving shipping method type",message:n})}))},deleteDeadline:function(){var e=this;return this.shippingMethodDeadlineRepository.delete(this.shippingMethodDeadline.id,p.api).then((function(){e.shippingMethodDeadline=e.shippingMethodDeadlineRepository.create(p.api),e.shippingMethodDeadline.shippingMethodId=e.shippingMethodId,e.shippingMethodDeadline.deadlines={Monday:null,Tuesday:null,Wednesday:null,Thursday:null,Friday:null,Saturday:null,Sunday:null},e.shippingMethodDeadline.excludedDates=[]})).catch((function(n){e.createNotificationError({title:"Error deleting shipping method deadline",message:n})}))},saveDeadline:function(){var e=this;this.shippingMethodDeadlineRepository.save(this.shippingMethodDeadline,p.api).then((function(){e.shippingMethodDeadline._isNew=!1})).catch((function(n){e.createNotificationError({title:"Error saving shipping method deadline",message:n})}))},addDeadlineException:function(){this.shippingMethodDeadline.excludedDates.push({date:null})},deleteDeadlineException:function(e){this.shippingMethodDeadline.excludedDates.splice(e,1)},addParcelShopPenalty:function(){Array.isArray(this.shippingMethodType.penalties)||(this.shippingMethodType.penalties=[]),this.shippingMethodType.penalties.push({to:null,from:null,penalty:null})},deletePenalty:function(e){this.shippingMethodType.penalties.splice(e,1)}}});function a(e){return(a="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function d(e,n){if(!(e instanceof n))throw new TypeError("Cannot call a class as a function")}function s(e,n){for(var i=0;i<n.length;i++){var t=n[i];t.enumerable=t.enumerable||!1,t.configurable=!0,"value"in t&&(t.writable=!0),Object.defineProperty(e,(o=t.key,p=void 0,p=function(e,n){if("object"!==a(e)||null===e)return e;var i=e[Symbol.toPrimitive];if(void 0!==i){var t=i.call(e,n||"default");if("object"!==a(t))return t;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===n?String:Number)(e)}(o,"string"),"symbol"===a(p)?p:String(p)),t)}var o,p}function l(e,n){return(l=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,n){return e.__proto__=n,e})(e,n)}function h(e){var n=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var i,t=u(e);if(n){var o=u(this).constructor;i=Reflect.construct(t,arguments,o)}else i=t.apply(this,arguments);return c(this,i)}}function c(e,n){if(n&&("object"===a(n)||"function"==typeof n))return n;if(void 0!==n)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}function u(e){return(u=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}Shopware.Component.override("sw-order-detail-details",{template:'{% block sw_order_detail_details_shipping %}\n    {% parent() %}\n    <sw-card v-if="order.extensions.shippingComment"\n             :title="$tc(\'wexoShipping.shippingComment\')"\n             position-identifier="sw-order-detail-details-shipping-comment">\n            <sw-textarea-field\n                    v-model="order.extensions.shippingComment.comment"\n                    disabled\n            />\n    </sw-card>\n    <sw-card v-if="order.extensions.parcelShop"\n            :title="$tc(\'wexoShipping.parcelShop\')"\n            position-identifier="sw-order-detail-details-parcel-shop"\n    >\n        <sw-container\n                gap="0px 30px"\n                columns="1fr 1fr"\n        >\n            <sw-text-field\n                    v-model="order.extensions.parcelShop.name"\n                    :label="$tc(\'sw-customer.detailAddresses.columnCompany\')"\n                    disabled\n            />\n            <sw-text-field\n                    v-model="order.extensions.parcelShop.street"\n                    :label="$tc(\'sw-customer.detailAddresses.columnStreet\')"\n                    disabled\n            />\n            <sw-text-field\n                    v-model="order.extensions.parcelShop.zipCode"\n                    :label="$tc(\'sw-customer.detailAddresses.columnZipCode\')"\n                    disabled\n            />\n            <sw-text-field\n                    v-model="order.extensions.parcelShop.city"\n                    :label="$tc(\'sw-customer.detailAddresses.columnCity\')"\n                    disabled\n            />\n        </sw-container>\n    </sw-card>\n{% endblock %}'});var g=Shopware.Classes.ApiService,y=function(e){!function(e,n){if("function"!=typeof n&&null!==n)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(n&&n.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),n&&l(e,n)}(p,e);var n,i,t,o=h(p);function p(e,n){var i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"wexo/shipping-types";return d(this,p),o.call(this,e,n,i)}return n=p,(i=[{key:"getTypes",value:function(){return this.httpClient.get(this.getApiBasePath(),{headers:this.getBasicHeaders()}).then((function(e){return g.handleResponse(e)}))}}])&&s(n.prototype,i),t&&s(n,t),Object.defineProperty(n,"prototype",{writable:!1}),p}(g),f=i("EoUx"),m=i("ClHn"),w=i("B/2W");Shopware.Locale.extend("de-DE",f),Shopware.Locale.extend("en-GB",m),Shopware.Locale.extend("da-DK",w);var v=Shopware.Application;v.addServiceProvider("ShippingMethodTypeService",(function(e){var n=v.getContainer("init");return new y(n.httpClient,e.loginService)}))}});
//# sourceMappingURL=wexo-shipping.js.map
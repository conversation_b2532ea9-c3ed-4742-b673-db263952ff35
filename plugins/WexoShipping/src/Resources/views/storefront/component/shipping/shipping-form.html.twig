{% sw_extends '@Storefront/storefront/component/shipping/shipping-form.html.twig' %}

{# Add a comment field for shipping comments #}
{% block page_checkout_change_shipping_form_element %}
    {{ parent() }}

    {% block page_checkout_shipping_form_comment_field %}
        {% set shippingCommentRequired = context.shippingMethod.extension('shippingMethodConfig').shippingCommentRequired %}

        <div class="form-group shipping-comment-container" data-shipping-comment-plugin>
            <label class="form-label" for="shippingComment">
                {{ 'shippingMethod.shippingComment'|trans }}{% if shippingCommentRequired %}*{% endif %}
            </label>
            <textarea class="form-control" form="confirmOrderForm"
                {% if shippingCommentRequired %}
                    required="required"
                {% endif %}
                name="shippingComment">{{ page.cart.extensions['wexo-shipping-cart-extension'].shippingComment }}</textarea>
        </div>
    {% endblock %}
{% endblock %}

{% sw_extends '@Storefront/storefront/component/shipping/shipping-fields.html.twig' %}

{# Exclude normal parcelshop methods when unified parcel shop is enabled #}
{% block component_shipping_method %}
    {% if page.extensions.unified_parcel_shop_method %}
        {% if visibleShippingMethodsLimit is not null %}
            {# Figure out how many of the visible shipping methods are parcel shops #}
            {# Bump the limit by that many since these methods aren't displayed by themselves #}
            {% for shipping in page.shippingMethods[:visibleShippingMethodsLimit] %}
                {% if shipping.extension('shippingMethodType') is not null %}
                    {% set visibleShippingMethodsLimit = visibleShippingMethodsLimit + 1 %}
                {% endif %}
            {% endfor %}
            {# Subtract one to account for the Unified parcelshop view which is inserted instead #}
            {% set visibleShippingMethodsLimit = visibleShippingMethodsLimit - 1 %}
        {% endif %}

        {# Render the unified view #}
        {% set shipping = page.extensions.unified_parcel_shop_method %}
        {% sw_include '@Storefront/storefront/component/shipping/shipping-method.html.twig' %}

        {# Render any non-parcelshop shipping methods  #}
        {% for shipping in page.shippingMethods[:visibleShippingMethodsLimit] %}
            {% if shipping.extension('shippingMethodType') is null %}
                {% sw_include '@Storefront/storefront/component/shipping/shipping-method.html.twig' %}
            {% endif %}
        {% endfor %}
        {{ block('component_shipping_method_collapse') }}
    {% else %}
        {{ parent() }}
    {% endif %}
{% endblock %}

{# Exclude normal parcelshop methods from collapse when unified parcel shop is enabled #}
{% block component_shipping_method_collapse %}
    {% if page.extensions.unified_parcel_shop_method %}
        {% if page.shippingMethods | length > visibleShippingMethodsLimit and visibleShippingMethodsLimit is not same as(null) %}
            <div class="collapse">
                {% for shipping in page.shippingMethods[visibleShippingMethodsLimit:] %}
                    {% if shipping.extension('shippingMethodType') is null %}
                        {% sw_include '@Storefront/storefront/component/shipping/shipping-method.html.twig' %}
                    {% endif %}
                {% endfor %}
            </div>
            {{ block('component_shipping_method_collapse_trigger') }}
        {% endif %}
    {% else %}
        {{ parent() }}
    {% endif %}
{% endblock %}

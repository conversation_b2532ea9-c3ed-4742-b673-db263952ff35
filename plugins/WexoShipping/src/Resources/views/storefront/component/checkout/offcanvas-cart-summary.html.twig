{% sw_extends '@Storefront/storefront/component/checkout/offcanvas-cart-summary.html.twig' %}

{% block component_offcanvas_summary_content_shipping_select %}
    <select class="form-select mt-2 col-12" name="shippingMethodId">
        <option disabled>{{ "checkout.confirmChangeShipping"|trans|sw_sanitize }}</option>

        {% for shippingMethod in page.shippingMethods %}
            <option
                value="{{ shippingMethod.id }}"
                {% if shippingMethod.extension('shippingMethodType') %}disabled{% endif %}
                {% if shippingMethod.id is same as(activeShipping.shippingMethod.id) %}selected="selected"{% endif %} >
                {{ shippingMethod.translated.name }} - {{ shippingMethod.extension('calculatedPrice').totalPrice|currency }}
            </option>
        {% endfor %}
    </select>
{% endblock %}

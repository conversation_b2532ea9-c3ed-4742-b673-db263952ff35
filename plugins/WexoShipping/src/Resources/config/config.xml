<?xml version="1.0" encoding="UTF-8"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="https://raw.githubusercontent.com/shopware/platform/master/src/Core/System/SystemConfig/Schema/config.xsd">
    <card>
        <title>WexoShipping Settings</title>
        <title lang="de-DE">WexoShipping Einstellungen</title>
        <title lang="da-DK">WexoShipping Indstillinger</title>

        <input-field type="int">
            <name>numberOfShopsToLoad</name>
            <label>Number of shops to load initially</label>
            <label lang="da-DK">Antal butikker der begyndelsesvis indlæses i checkout</label>
            <label lang="de-DE">Anzahl der Geschäfte, die anfänglich geladen werden sollen</label>
            <required>true</required>
            <defaultValue>20</defaultValue>
        </input-field>
        <input-field type="bool">
            <name>unifiedView</name>
            <label>Combine parcel shops from different shipping methods in one view</label>
            <label lang="da-DK">Vis pakkeshops fra forskellige fragtmetoder i én visning</label>
            <label lang="de-DE">Kombinieren Sie Paketgeschäfte aus verschiedenen Versandarten in einer Ansicht</label>
            <helpText>If this setting is enabled, any active parcel shop shipping methods on the storefront will be combined into a single shipping method containing parcel shops from all providers.</helpText>
            <helpText lang="da-DK">Hvis denne indstilling er aktiveret vil alle pakkeshop fragtmetoder blive vist som en samlet fragtmetode indeholdende pakkeshops fra alle udbydere.</helpText>
            <helpText lang="de-DE">Wenn diese Einstellung aktiviert ist, werden alle aktiven Versandmethoden für Paketgeschäfte in der Storefront zu einer einzigen Versandmethode kombiniert, die Paketgeschäfte aller Anbieter enthält.</helpText>
            <defaultValue>false</defaultValue>
        </input-field>
        <input-field type="text">
            <name>unifiedViewName</name>
            <label>Display name for combined shipping methods</label>
            <label lang="da-DK">Vist navn til samlet visning af pakkeshops</label>
            <label lang="de-DE">Anzeigename für kombinierte Versandarten</label>
            <helpText>If the above setting is enabled, this is the shown name for the shipping method.</helpText>
            <helpText lang="da-DK">Hvis ovenstående indstilling er aktiveret, vil dette være navnet på den samlede fragtmetode.</helpText>
            <helpText lang="de-DE">Wenn die obige Einstellung aktiviert ist, ist dies der angezeigte Name für die Versandart.</helpText>
            <defaultValue>Parcel Shop</defaultValue>
        </input-field>
        <input-field type="bool">
            <name>automaticParcelShopSelection</name>
            <label>Automatically pick the nearest parcelshop if none is selected</label>
            <label lang="da-DK">Vælg automatisk nærmeste pakkeshop hvis ingen er valgt</label>
            <label lang="de-DE">Automatisch den nächstgelegenen Paketshop auswählen, wenn keiner ausgewählt ist</label>
            <helpText>If this setting is enabled, the nearest parcelshop from the selected provider will automatically be selected for the customer if missing.</helpText>
            <helpText lang="da-DK">Hvis denne indstilling er slået til, vil den nærmeste pakkeshop fra valgte udbyder automatisk blive valgt for kunden, hvis mangler.</helpText>
            <helpText lang="de-DE">Ist diese Einstellung aktiviert, wird beim Fehlen automatisch der nächstgelegene Paketshop des ausgewählten Anbieters für den Kunden ausgewählt.</helpText>
            <defaultValue>true</defaultValue>
        </input-field>
        <input-field type="bool">
            <name>setParcelShopAsShippingAddress</name>
            <label>Set parcelshop as shipping address on orders</label>
            <label lang="da-DK">Set pakkeshop som leveringsadresse på ordrer</label>
            <label lang="de-DE">Paketshop als Lieferadresse bei Bestellungen angeben</label>
            <helpText>If enabled, the customers shipping address will be set to the address of the parcelshop</helpText>
            <helpText lang="da-DK">Hvis denne indstilling er slået til, vil kundens leveringsadresse blive sat til pakkeshoppens adresse</helpText>
            <helpText lang="de-DE">Wenn aktiviert, wird die Lieferadresse des Kunden auf die Adresse des Paketshops gesetzt</helpText>
            <defaultValue>true</defaultValue>
        </input-field>
    </card>
</config>

<?xml version="1.0" ?>

<container xmlns="http://symfony.com/schema/dic/services"
           xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
           xsi:schemaLocation="http://symfony.com/schema/dic/services http://symfony.com/schema/dic/services/services-1.0.xsd">
    <services>
        <service id="Wexo\Shipping\Core\Content\ShippingMethod\ShippingMethodExtension">
            <tag name="shopware.entity.extension" />
        </service>
        <service id="Wexo\Shipping\Core\Content\Order\OrderExtension">
            <tag name="shopware.entity.extension" />
        </service>
        <service id="Wexo\Shipping\Core\Content\ShippingMethodType\ShippingMethodTypeDefinition">
            <tag name="shopware.entity.definition" entity="wexo_shipping_method_type" />
        </service>
        <service id="Wexo\Shipping\Core\Content\ParcelShop\ParcelShopDefinition">
            <tag name="shopware.entity.definition" entity="wexo_shipping_parcel_shop" />
        </service>
        <service id="Wexo\Shipping\Core\Content\ShippingMethodDeadline\ShippingMethodDeadlineDefinition">
            <tag name="shopware.entity.definition" entity="wexo_shipping_method_deadline" />
        </service>
        <service id="Wexo\Shipping\Core\Content\ShippingMethodConfig\ShippingMethodConfigDefinition">
            <tag name="shopware.entity.definition" entity="wexo_shipping_method_config" />
        </service>
        <service id="Wexo\Shipping\Core\Content\ShippingComment\ShippingCommentDefinition">
            <tag name="shopware.entity.definition" entity="wexo_shipping_comment" />
        </service>
        <service id="Wexo\Shipping\Service\TypeOptionService" public="true">
            <argument type="tagged_iterator" tag="wexo.shipping.type" index-by="key" />
            <argument type="service" id="custom_field.repository" />
        </service>
        <service id="Wexo\Shipping\Service\ShippingPriceCalculator" public="true">
            <argument type="service" id="Shopware\Core\Checkout\Cart\Delivery\DeliveryProcessor" />
            <argument type="service" id="Shopware\Core\Checkout\Promotion\Cart\PromotionDeliveryProcessor" />
        </service>
        <service id="Wexo\Shipping\Service\OpeningHourMerger" public="true" />
        <service id="Wexo\Shipping\Service\ShippingDeadlineFormatter" public="true" >
            <argument type="service" id="Shopware\Core\Framework\Adapter\Translation\Translator" />
        </service>
        <service id="Wexo\Shipping\Controller\ShippingTypeController" public="true">
            <argument type="service" id="Wexo\Shipping\Service\TypeOptionService" />
        </service>
        <service id="Wexo\Shipping\Controller\Api\ParcelShopController" public="true">
            <argument type="service" id="Wexo\Shipping\Service\TypeOptionService" />
            <argument type="service" id="shipping_method.repository" />
            <argument type="service" id="logger"/>
        </service>

        <service id="Wexo\Shipping\Controller\ParcelShopStorefrontController" public="true">
            <argument type="service" id="Wexo\Shipping\Controller\Api\ParcelShopController"/>
            <argument type="service" id="service_container"/>
        </service>

        <service id="Wexo\Shipping\Subscriber\Checkout">
            <tag name="kernel.event_subscriber" />
            <argument type="service" id="Wexo\Shipping\Service\TypeOptionService" />
            <argument type="service" id="Shopware\Core\System\SystemConfig\SystemConfigService" />
            <argument type="service" id="translator" />
            <argument type="service" id="Wexo\Shipping\Service\ShippingPriceCalculator" />
            <argument type="service" id="Wexo\Shipping\Service\ShippingDeadlineFormatter" />
            <argument type="service" id="shopware.asset.asset" />
        </service>
        <service id="Wexo\Shipping\Subscriber\Offcanvas">
            <tag name="kernel.event_subscriber" />
            <argument type="service" id="Wexo\Shipping\Service\ShippingPriceCalculator" />
            <argument type="service" id="Shopware\Core\Checkout\Shipping\SalesChannel\ShippingMethodRoute" />
        </service>
        <service id="Wexo\Shipping\Subscriber\OrderConverter">
            <tag name="kernel.event_subscriber" />
            <argument type="service" id="Shopware\Core\System\SystemConfig\SystemConfigService" />
        </service>
        <service id="Wexo\Shipping\Core\System\SalesChannel\SalesChannel\ContextSwitchRouteDecorator"
                 decorates="Shopware\Core\System\SalesChannel\SalesChannel\ContextSwitchRoute" >
            <argument type="service" id="Wexo\Shipping\Core\System\SalesChannel\SalesChannel\ContextSwitchRouteDecorator.inner" />
            <argument type="service" id="Wexo\Shipping\Service\TypeOptionService" />
            <argument type="service" id="shipping_method.repository" />
            <argument type="service" id="Shopware\Core\Checkout\Cart\SalesChannel\CartService" />
            <argument type="service" id="Shopware\Core\Checkout\Cart\CartPersister" />
            <argument type="service" id="translator" />
            <argument type="service" id="request_stack" />
        </service>
        <service id="Wexo\Shipping\Core\Checkout\Cart\SalesChannel\CartOrderRouteDecorator"
                 decorates="Shopware\Core\Checkout\Cart\SalesChannel\CartOrderRoute">
            <argument type="service" id="Wexo\Shipping\Core\Checkout\Cart\SalesChannel\CartOrderRouteDecorator.inner" />
            <argument type="service" id="Shopware\Core\Checkout\Cart\CartPersister"/>
        </service>
        <service id="Wexo\Shipping\Core\Checkout\Cart\Custom\ParcelShopValidator">
            <tag name="shopware.cart.validator" />
            <argument type="service" id="Wexo\Shipping\Service\TypeOptionService" />
            <argument type="service" id="Shopware\Core\System\SystemConfig\SystemConfigService" />
            <argument type="service" id="Shopware\Core\Checkout\Cart\CartPersister" />
        </service>
    </services>
</container>

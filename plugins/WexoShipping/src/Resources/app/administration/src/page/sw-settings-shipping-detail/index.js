import template from './sw-settings-shipping-detail.html.twig';

const { Component, Context, Data } = Shopware;
const { Criteria } = Data;

Component.override('sw-settings-shipping-detail', {
    template,

    inject: [
        'ShippingMethodTypeService',
        'feature'
    ],

    data() {
        return {
            shippingMethodConfigRepository: null,
            shippingMethodConfig: null,
            shippingMethodDeadlineRepository: null,
            shippingMethodDeadline: null,
            shippingMethodTypeRepository: null,
            shippingMethodType: null,
            shippingMethodTypes: [],
            loadingShippingTypes: true
        };
    },
    
    methods: {
        createdComponent() {
            this.$super('createdComponent');

            this.ShippingMethodTypeService.getTypes()
                .then(types => {
                    this.shippingMethodTypes = types;
                    this.loadingShippingTypes = false;
                })
                .catch(error => {
                    this.createNotificationError({
                        title: 'Error loading available shipping method types',
                        message: error
                    })
                });

            this.shippingMethodConfigRepository = this.repositoryFactory.create('wexo_shipping_method_config')
            this.shippingMethodTypeRepository = this.repositoryFactory.create('wexo_shipping_method_type')
            this.shippingMethodDeadlineRepository = this.repositoryFactory.create('wexo_shipping_method_deadline')

            const criteria = new Criteria();
            criteria.addFilter(Criteria.equals('shippingMethodId', this.shippingMethodId));

            this.shippingMethodConfigRepository.search(criteria, Context.api)
                .then(response => {
                    if (response.total) {
                        this.shippingMethodConfig = response.first();
                    } else {
                        this.shippingMethodConfig = this.shippingMethodConfigRepository.create(Context.api);
                        this.shippingMethodConfig.shippingMethodId = this.shippingMethod.id || this.shippingMethodId;
                        this.shippingMethodConfig.shippingCommentRequired = false;
                    }
                })
            this.shippingMethodTypeRepository.search(criteria, Context.api)
                .then(response => {
                    if (response.total) {
                        this.shippingMethodType = response.first();
                    } else {
                        this.shippingMethodType = this.shippingMethodTypeRepository.create(Context.api);
                        this.shippingMethodType.shippingMethodId = this.shippingMethod.id || this.shippingMethodId;
                        this.shippingMethodType.typeKey = null;
                    }
                })
                .catch(error => {
                    this.createNotificationError({
                        title: 'Error loading shipping method type',
                        message: error
                    })
                })
            this.shippingMethodDeadlineRepository.search(criteria, Context.api)
                .then(response => {
                    if (response.total) {
                        this.shippingMethodDeadline = response.first();
                    } else {
                        this.shippingMethodDeadline = this.shippingMethodDeadlineRepository.create(Context.api);
                        this.shippingMethodDeadline.shippingMethodId = this.shippingMethod.id || this.shippingMethodId;
                        this.shippingMethodDeadline.deadlines = {
                            Monday: null,
                            Tuesday: null,
                            Wednesday: null,
                            Thursday: null,
                            Friday: null,
                            Saturday: null,
                            Sunday: null
                        };
                        this.shippingMethodDeadline.excludedDates = [];
                    }
                })
                .catch(error => {
                    this.createNotificationError({
                        title: 'Error loading shipping method deadline',
                        message: error
                    })
                })
        },
        onSaveOverride() {
            const promises = [];
            if (!this.shippingMethodType.typeKey && !this.shippingMethodType._isNew) {
                promises.push(this.deleteParcelShop());
            }
            const deadlinesSet = Object.values(this.shippingMethodDeadline.deadlines)
                .filter(deadline => deadline !== null)
                .length
            if (!deadlinesSet && !this.shippingMethodDeadline._isNew)  {
                promises.push(this.deleteDeadline());
            }
            return Promise.all(promises).then(() => {
                return this.onSave().then(() => {
                    if (this.shippingMethodType.typeKey) {
                        this.saveParcelShop();
                    }
                    if (deadlinesSet) {
                        this.saveDeadline();
                    }
                    this.saveAdditionalConfig();
                });
            });
        },
        saveAdditionalConfig() {
            this.shippingMethodConfigRepository
                .save(this.shippingMethodConfig, Context.api)
                .then(() => {
                    this.shippingMethodConfig._isNew = false;
                })
                .catch(error => {
                    this.createNotificationError({
                        title: 'Error saving additional WEXO shipping method configuration',
                        message: error
                    })
                })
        },
        deleteParcelShop() {
            return this.shippingMethodTypeRepository.delete(this.shippingMethodType.id, Context.api)
                .then(() => {
                    this.shippingMethodType = this.shippingMethodTypeRepository.create(Context.api);
                    this.shippingMethodType.shippingMethodId = this.shippingMethodId;
                    this.shippingMethodType.typeKey = null;
                    this.shippingMethodType.penalties = [];
                })
                .catch(error => {
                    this.createNotificationError({
                        title: 'Error deleting shipping method type',
                        message: error
                    })
                });
        },
        saveParcelShop() {
            if (this.shippingMethodType.penalties) {
                this.shippingMethodType.penalties = this.shippingMethodType.penalties.filter(penalty => {
                    return penalty.from && penalty.to && penalty.penalty;
                })
            }
            this.shippingMethodTypeRepository
                .save(this.shippingMethodType, Context.api)
                .then(() => {
                    this.shippingMethodType._isNew = false;
                })
                .catch(error => {
                    this.createNotificationError({
                        title: 'Error saving shipping method type',
                        message: error
                    })
                })
        },
        deleteDeadline() {
            return this.shippingMethodDeadlineRepository.delete(this.shippingMethodDeadline.id, Context.api)
                .then(() => {
                    this.shippingMethodDeadline = this.shippingMethodDeadlineRepository.create(Context.api);
                    this.shippingMethodDeadline.shippingMethodId = this.shippingMethodId;
                    this.shippingMethodDeadline.deadlines = {
                        Monday: null,
                        Tuesday: null,
                        Wednesday: null,
                        Thursday: null,
                        Friday: null,
                        Saturday: null,
                        Sunday: null
                    };
                    this.shippingMethodDeadline.excludedDates = [];
                })
                .catch(error => {
                    this.createNotificationError({
                        title: 'Error deleting shipping method deadline',
                        message: error
                    })
                });
        },
        saveDeadline() {
            this.shippingMethodDeadlineRepository
                .save(this.shippingMethodDeadline, Context.api)
                .then(() => {
                    this.shippingMethodDeadline._isNew = false;
                })
                .catch(error => {
                    this.createNotificationError({
                        title: 'Error saving shipping method deadline',
                        message: error
                    })
                })
        },
        addDeadlineException() {
            this.shippingMethodDeadline.excludedDates.push({
                date: null
            });
        },
        deleteDeadlineException(index) {
            this.shippingMethodDeadline.excludedDates.splice(index, 1);
        },
        addParcelShopPenalty() {
            if (!Array.isArray(this.shippingMethodType.penalties)) {
                this.shippingMethodType.penalties = [];
            }
            this.shippingMethodType.penalties.push({to: null, from: null, penalty: null});
        },
        deletePenalty(index) {
            this.shippingMethodType.penalties.splice(index, 1);
        }
    }
})

{"wexoShipping": {"generalConfig": "Additional settings", "shippingCommentRequired": "Shipping comment is required", "shippingCommentRequiredHelp": "Enable this setting if the provider requires the customer to fill out a comment for delivery.", "noParcelShop": "None (non-parcel shop shipping method)", "parcelShop": "Parcel Shop", "provider": {"choose": "Choose parcel shop provider"}, "penalty": {"from": "From", "to": "To", "penaltyAmount": "Amount", "addTimeSlot": "Add meter-penalty timeslot"}, "shippingComment": "Shipping comment", "deadline": {"title": "Deadline for shipping", "time": "Time", "excludedWeekdays": "Weekdays without shipping", "days": {"monday": "Monday", "tuesday": "Tuesday", "wednesday": "Wednesday", "thursday": "Thursday", "friday": "Friday", "saturday": "Saturday", "sunday": "Sunday"}, "exception": "Date without delivery", "addDeadlineException": "Add date without delivery"}}}
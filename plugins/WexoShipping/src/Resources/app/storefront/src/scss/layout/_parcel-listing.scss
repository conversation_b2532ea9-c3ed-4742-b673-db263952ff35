$parcel-list-item-border-color: #e1e1e1;

.parcel-shop-plugin-form {
    display: contents;
}

.parcel-shop-plugin {
    height: 100%;
    display: flex;
    flex-direction: column;
    .parcel-shop-content {
        flex: 1 1 0%;
        overflow-y: hidden;

        @include media-breakpoint-up(lg) {
            display: flex;
        }

        .parcel-shop-left, .parcel-shop-right {
            padding-top: 20px;
        }

        .parcel-shop-left {
            width: 100%;
            display: flex;
            flex-direction: column;
            height: 100%;
            padding-top: 0;
            .parcel-shop-headline {
                align-items: end;
                .parcel-shop-input {
                    input {
                        border-color: black;
                        border-radius: 0;
                    }
                    margin-right: 8px;
                }

                .search-parcel-shop {
                    min-width: 75px;
                    text-transform: uppercase;
                    height: 42px;
                    color: black;
                    background: $gray-200;
                    border: none;
                    border-radius: 0;
                    &:hover {
                        background: $gray-500;
                    }
                }
            }
            .parcel-shop-list-container {
                overflow-y: auto;
                height: 100%;

                .parcel-shop-list {
                    list-style: none;
                    padding: 0;
                    border-bottom: none;

                    .parcel-shop-error {
                        text-align: center;
                        padding: 20px;
                    }

                    .parcel-shop-list-item {
                        display: flex;
                        justify-content: space-between;
                        align-items: center;
                        border: 2px solid $parcel-list-item-border-color;
                        padding: 20px;

                        @include media-breakpoint-down(sm) {
                            -webkit-flex-direction: column;
                            flex-direction: column;
                        }

                        &:not(:last-child) {
                            border-bottom: 0;
                        }

                        &.selected {
                            border-color: black;
                            &+ .parcel-shop-list-item {
                                border-top-color: black;
                            }
                        }

                        .parcel-shop-info-wrapper {
                            @include media-breakpoint-down(sm) {
                                width: 100%;
                            }
                            @include media-breakpoint-up(sm) {
                                width: 35%;
                            }
                            .provider-image {
                                height: 24px;
                                margin-bottom: 8px;
                            }

                            .address-wrapper {
                                margin-bottom: 8px;

                                &:first-child {
                                    font-weight: bold;
                                }
                            }
                        }

                        .opening-hours-box-container {
                            max-width: 200px;
                            position: relative;
                            align-self: stretch;
                            margin: 0;
                            .label {
                                position: absolute;
                                text-align: center;
                                width: 100%;
                                color: white;
                                font-weight: bold;

                                &.free {
                                    background: #E57D22;
                                }

                                &.recommended {
                                    background: #0D8302;
                                }
                            }

                            .opening-hours-container {
                                display: flex;
                                min-height: 100%;
                                align-items: center;
                                .opening-hours {
                                    width: 100%;
                                }
                            }
                        }

                        .show-on-map {
                            cursor: pointer;
                            text-align: center;
                            color: #14baee;
                            text-decoration: underline;
                            flex: 1 1;

                            @include media-breakpoint-down(lg) {
                                display: none;
                            }
                        }

                        .select-parcel-shop {
                            display: flex;
                            justify-content: flex-end;
                            @include media-breakpoint-down(md) {
                                flex: 1;
                            }
                        }
                    }
                }


                .load-more-shops-button-container {
                    margin-bottom: 20px;
                    width: 100%;
                    text-align: center;
                    .load-more-shops {
                        transition: background 0.3s ease-out;
                        border: 1px solid black;
                        font-weight: bold;
                        &:hover {
                            background: black;
                            color: white;
                        }
                    }
                }

                @include media-breakpoint-up(xl) {
                    min-width: 700px;
                }
            }
        }

        .parcel-shop-right {
            .parcel-shop-map {
                height: 100%;
                transition: width 0.2s ease-out;
                width: 0;
                &.show {
                    margin-left: 15px;
                    width: 600px;
                }

                .leaflet-marker-icon {
                    filter: grayscale(1);
                    &.selected {
                        filter: none;
                        z-index: 999!important;
                    }
                }
            }
        }
    }

    // Used both in leaflet map and listing
    .parcel-shop-map .opening-hours, .opening-hours-box-container {
        padding: 0;
        list-style: none;
        flex: 1 0 auto;
        margin: auto;
        text-align: left;

        tr {
            &:nth-child(even) {
                background: inherit;
            }

            td:last-child {
                text-align: right;
            }
        }
    }

    // Used both in leaflet map and listing
    .select-parcel-shop {
        @include media-breakpoint-down(sm) {
            width: 100%;
            margin-top: 20px;
        }
        .btn {
            &:not(:disabled) {
                background: black;
                color: white;
                @include media-breakpoint-down(sm) {
                    width: 100%;
                }
            }
            text-transform: uppercase;
        }
    }
}

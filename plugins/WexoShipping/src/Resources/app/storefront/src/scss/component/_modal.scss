.parcel-shop-modal {
    .modal-dialog {
        &, .modal-content {
            max-width: 75vw;
            max-height: 75vh;
            height: 75vh;
            width: auto;
            margin: auto;
        }

        .modal-content {
            .modal-header {
                display: flex;
                flex: 0 0 50px;
                background: #fff;
                border: none;

                .btn-close {
                    opacity: 1;
                    width: fit-content;
                    background-position: bottom right;
                    padding-right: 20px;
                    //prevent margin collapse
                    margin-right: 1px;

                    .close-text {
                        color: black;
                        font-size: 16px;
                        text-transform: uppercase;
                        vertical-align: bottom;
                        font-weight: 700;
                    }
                }
            }

            .modal-body {
                flex: 1 1 0%;
                padding-top: 0;
                overflow: hidden;
            }
        }

        @include media-breakpoint-down(md) {
            &, .modal-content {
                max-width: 100%;
                max-height: 100%;
                height: 100vh;
                width: 100%;
                margin: 0;
            }

            .modal-content {
                border-radius: 0;
                display: flex;
                flex-direction: column;

                .modal-body {
                    overflow-y: scroll;
                    .element-loader-backdrop {
                        position: fixed;
                    }
                }
            }
        }
    }
}

.shipping-methods {
    .modal-body {
        padding: 16px;
    }
}

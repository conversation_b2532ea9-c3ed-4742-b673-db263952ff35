import ParcelShopPlugin from "./plugin/parcel-shop/parcel-shop.plugin";
import ShippingCommentPlugin from "./plugin/shipping-comment/shipping-comment.plugin";

const PluginManager = window.PluginManager;
PluginManager.register('ParcelShopPlugin', ParcelShopPlugin, '[data-parcel-shop-plugin]');
PluginManager.register('ShippingCommentPlugin', ShippingCommentPlugin, '[data-shipping-comment-plugin]');

if (module.hot) {
    module.hot.accept();
}


import DomAccess from 'src/helper/dom-access.helper';

export default class ParcelShopValidator {
    validate(parcelShopPlugin) {
        return parcelShopPlugin.currentParcelShop;
    }

    onValid(parcelShopPlugin) {
        const ele = DomAccess.querySelector(parcelShopPlugin.shippingMethodContainer, `.${this._getElementSelector()}`, false);

        if (ele) {
            ele.remove();
        }
    }

    onInvalid(parcelShopPlugin) {
        this.onValid(parcelShopPlugin);

        const errorText = document.createElement('p');
        errorText.classList.add(this._getElementSelector(), 'text-danger', 'm-0', 'p-3');
        errorText.textContent = parcelShopPlugin.options.translations.validation.parcelShopMissing;

        parcelShopPlugin.shippingMethodContainer.appendChild(errorText);

        errorText.scrollIntoView({behavior: "smooth"});
    }

    _getElementSelector() {
        return 'parcel-shop-missing-error';
    }
}

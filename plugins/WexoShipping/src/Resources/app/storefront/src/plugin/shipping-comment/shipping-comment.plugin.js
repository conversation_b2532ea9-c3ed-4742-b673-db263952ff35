import Plugin from 'src/plugin-system/plugin.class';
import DomAccess from 'src/helper/dom-access.helper';

export default class ShippingCommentPlugin extends Plugin {
    static options = {
        shippingFormSelector: '#changeShippingForm',
        shippingCommentSelector: 'textarea[name="shippingComment"]',
        shippingMethodSelector: 'input.shipping-method-input'
    };

    init() {
        this.shippingForm = DomAccess.querySelector(document, this.options.shippingFormSelector);
        this.shippingCommentField = DomAccess.querySelector(document, this.options.shippingCommentSelector);
        const shippingMethod = DomAccess.querySelectorAll(this.shippingForm, this.options.shippingMethodSelector)
            .forEach(element => {
                element.addEventListener('change', (event => {
                    this.shippingCommentField.required = event.target.dataset.shippingCommentRequired;
                }).bind(this));
            });
    };
}

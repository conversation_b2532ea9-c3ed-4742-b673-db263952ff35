export default class ParcelShopList {
    constructor(storeData, element) {
        this.storeData = storeData;
        this.el = element;
        this.el.innerHTML = '';
        this.storeData.parcelShops.forEach((parcelShop, index) => {
            const listEntry = this.createParcelShopListEntry(parcelShop, index);
            this.el.appendChild(listEntry);
        });

        // Set selected parcelShop on init
        if (this.storeData.selectedParcelShop) {
            this.setParcelShop(this.storeData.selectedParcelShop, false);
        }
    };

    createParcelShopListEntry(parcelShop, index) {
        const listElement = document.createElement('li');
        listElement.classList.add('parcel-shop-list-item');
        listElement.dataset.id = this.getParcelShopIdentifier(parcelShop);
        listElement.appendChild(this.createParcelShopInfoWrapper(parcelShop));
        listElement.appendChild(this.createOpeningHours(parcelShop, index));
        listElement.appendChild(this.createShowOnMap(parcelShop));
        listElement.appendChild(this.createButton(parcelShop));
        return listElement;
    };

    getParcelShopIdentifier(parcelShop) {
        return `${parcelShop.typeKey}-${parcelShop.providerId}`;
    };

    createParcelShopInfoWrapper(parcelShop) {
        const parcelShopInfoWrapper = document.createElement('div')
        parcelShopInfoWrapper.classList.add('parcel-shop-info-wrapper')

        const providerImage = document.createElement('img')
        providerImage.classList.add('provider-image');
        providerImage.src = this.storeData.options.images[parcelShop.typeKey];
        parcelShopInfoWrapper.appendChild(providerImage);

        const addressWrapper = document.createElement('div');
        addressWrapper.classList.add('address-wrapper');
        parcelShopInfoWrapper.appendChild(addressWrapper);

        const name = document.createElement('div');
        name.innerText = parcelShop.name;
        addressWrapper.appendChild(name);

        const street = document.createElement('div');
        street.innerText = parcelShop.street;
        addressWrapper.appendChild(street);

        const city = document.createElement('div');
        city.innerText = `${parcelShop.zipCode} ${parcelShop.city}`;
        addressWrapper.appendChild(city);

        const distance = document.createElement('div');
        distance.innerText = `${(parcelShop.distance / 1000).toFixed(1)} km`;
        parcelShopInfoWrapper.appendChild(distance)

        const priceElement = document.createElement('div');
        const shippingPrice = this.storeData.options.shippingCosts[parcelShop.typeKey];
        priceElement.innerText = `${shippingPrice.total.toFixed(2)} ${shippingPrice.currency}`;
        parcelShopInfoWrapper.appendChild(priceElement);

        return parcelShopInfoWrapper;
    };

    createOpeningHours(parcelShop, index) {
        let labelContent = {};
        if (this.storeData.options.shippingCosts[parcelShop.typeKey].total < 1) {
            labelContent.type = 'free';
            labelContent.content = this.storeData.options.translations.freeLabel;
        }

        const openingHoursBoxContainer = document.createElement('div');
        openingHoursBoxContainer.classList.add('opening-hours-box-container');

        if (Object.keys(labelContent).length) {
            const label = document.createElement('div');
            label.classList.add('label', labelContent.type);
            label.innerHTML = `<span>${labelContent.content}</span>`;
            openingHoursBoxContainer.appendChild(label);
        }

        const openingHoursContainer = document.createElement('div');
        openingHoursContainer.classList.add('opening-hours-container');

        const openingHours = document.createElement('table');
        openingHours.classList.add('opening-hours');

        const openingHoursData = parcelShop.openingHours;

        openingHoursData.forEach(day => {
            const dayElementContainer = document.createElement('tr'),
                dayElement = document.createElement('td'),
                timeElement = document.createElement('td');

            dayElement.innerText = day['day'];
            timeElement.innerText = day['hours'];

            dayElementContainer.appendChild(dayElement);
            dayElementContainer.appendChild(timeElement);
            openingHours.appendChild(dayElementContainer);
        });

        openingHoursContainer.appendChild(openingHours);
        openingHoursBoxContainer.appendChild(openingHoursContainer);

        return openingHoursBoxContainer;
    };

    createShowOnMap(parcelShop) {
        const link = document.createElement('a');
        link.classList.add('show-on-map');
        link.innerText = this.storeData.options.translations.showOnMap;
        link.addEventListener('click', () => {
            this.storeData.showParcelShopOnMap(parcelShop);
        })
        return link;
    }

    createButton(parcelShop) {
        const selected = this.storeData.selectedParcelShop === parcelShop;
        const wrapper = document.createElement('div');
        wrapper.classList.add('select-parcel-shop');
        const button = document.createElement('button')
        button.classList.add('btn');
        button.innerText = selected ?
            this.storeData.options.translations.selected :
            this.storeData.options.translations.select;
        button.disabled = selected;
        button.type = 'button';
        button.addEventListener('click', () => {
            this.storeData.setParcelShop(parcelShop);
        });
        wrapper.appendChild(button);
        return wrapper;
    };

    setParcelShop(parcelShop, scroll = true) {
        // When a new parcelShop is selected, always unselect the currently selected one
        document.querySelectorAll('.parcel-shop-list-item.selected').forEach(ele => {
            ele.classList.remove('selected');
            const button = ele.querySelector('button');

            button.innerText = this.storeData.options.translations.select;
            button.disabled = false;
        });

        const listElement = this.el.querySelector(`.parcel-shop-list-item[data-id="${this.getParcelShopIdentifier(parcelShop)}"]`);
        const button = listElement.querySelector('button');
        button.innerText = this.storeData.options.translations.selected;
        button.disabled = true;
        listElement.classList.add('selected');

        if (scroll) {
            listElement.scrollIntoView({behavior: 'smooth'});
        }
    };

    show() {
        this.el.style.display = 'block';
    }

    hide() {
        this.el.style.display = 'none';
    }
};

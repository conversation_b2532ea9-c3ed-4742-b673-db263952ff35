"use strict";(self.webpackChunk=self.webpackChunk||[]).push([["wexo-shipping"],{5690:(e,t,i)=>{var s=i(6285),o=i(3206);class n{constructor(e,t){if(this.storeData=e,this.el=t,this.map=L.map(this.el),this.markers={},<PERSON><PERSON>tileLayer("https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png",{attribution:'&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'}).addTo(this.map),this.storeData.parcelShops.length){const e=this.storeData.parcelShops.find((e=>e===this.storeData.selectedParcelShop))||this.storeData.parcelShops[0];this.map.setView([e.latitude,e.longitude],12),this.storeData.parcelShops.forEach((e=>{this.createMarker(e)})),this.map.invalidateSize()}this.el.closest(".modal").addEventListener("shown.bs.modal",this.invalidateSize.bind(this)),this.el.addEventListener("transitionend",this.invalidateSize.bind(this))}invalidateSize(){this.map&&this.map.invalidateSize()}createMarker(e){const t=L.icon({iconUrl:this.storeData.options.images[e.typeKey],iconSize:[50,30]}),i=L.marker([e.latitude,e.longitude],{icon:t}),s=document.createElement("div");s.classList.add("parcel-shop-marker"),s.appendChild(this.createName(e)),s.appendChild(this.createAddress(e)),s.appendChild(this.createOpeningHours(e)),s.appendChild(this.createButton(e)),i.bindPopup(s),i.addTo(this.map);this.storeData.selectedParcelShop===e&&i._icon.classList.add("selected"),this.markers[e.providerId]=i}createName(e){const t=document.createElement("div");return t.classList.add("name"),t.classList.add("parcel-shop-name"),t.innerText=e.name,t}createAddress(e){const t=document.createElement("div");return t.classList.add("parcel-shop-address"),t.innerText=e.street,t}createOpeningHours(e){const t=document.createElement("table");t.classList.add("opening-hours");return e.openingHours.forEach((e=>{const i=document.createElement("tr"),s=document.createElement("td"),o=document.createElement("td");s.innerText=e.day,o.innerText=e.hours,i.appendChild(s),i.appendChild(o),t.appendChild(i)})),t}createButton(e){const t=this.storeData.selectedParcelShop===e,i=document.createElement("div");i.classList.add("select-parcel-shop");const s=document.createElement("button");return s.classList.add("btn"),s.innerText=t?this.storeData.options.translations.selected:this.storeData.options.translations.select,s.disabled=t,s.type="button",s.addEventListener("click",(()=>{this.storeData.setParcelShop(e)})),i.appendChild(s),i}setParcelShop(e){if(this.storeData.selectedParcelShop){const e=this.markers[this.storeData.selectedParcelShop.providerId];e._icon.classList.remove("selected");const t=e.getPopup()._content.querySelector("button");t.innerText=this.storeData.options.translations.select,t.disabled=!1}const t=this.markers[e.providerId];this.map.setView([e.latitude,e.longitude],14),t._icon.classList.add("selected"),t.closePopup();const i=t.getPopup()._content.querySelector("button");i.innerText=this.storeData.options.translations.selected,i.disabled=!0}scrollToParcelShop(e){this.map.setView([e.latitude,e.longitude])}show(){this.el.classList.add("show"),this.invalidateSize()}hide(){this.el.classList.remove("show")}destroy(){this.map.remove()}}class r{constructor(e,t){this.storeData=e,this.el=t,this.el.innerHTML="",this.storeData.parcelShops.forEach(((e,t)=>{const i=this.createParcelShopListEntry(e,t);this.el.appendChild(i)})),this.storeData.selectedParcelShop&&this.setParcelShop(this.storeData.selectedParcelShop,!1)}createParcelShopListEntry(e,t){const i=document.createElement("li");return i.classList.add("parcel-shop-list-item"),i.dataset.id=this.getParcelShopIdentifier(e),i.appendChild(this.createParcelShopInfoWrapper(e)),i.appendChild(this.createOpeningHours(e,t)),i.appendChild(this.createShowOnMap(e)),i.appendChild(this.createButton(e)),i}getParcelShopIdentifier(e){return`${e.typeKey}-${e.providerId}`}createParcelShopInfoWrapper(e){const t=document.createElement("div");t.classList.add("parcel-shop-info-wrapper");const i=document.createElement("img");i.classList.add("provider-image"),i.src=this.storeData.options.images[e.typeKey],t.appendChild(i);const s=document.createElement("div");s.classList.add("address-wrapper"),t.appendChild(s);const o=document.createElement("div");o.innerText=e.name,s.appendChild(o);const n=document.createElement("div");n.innerText=e.street,s.appendChild(n);const r=document.createElement("div");r.innerText=`${e.zipCode} ${e.city}`,s.appendChild(r);const a=document.createElement("div");a.innerText=`${(e.distance/1e3).toFixed(1)} km`,t.appendChild(a);const l=document.createElement("div"),p=this.storeData.options.shippingCosts[e.typeKey];return l.innerText=`${p.total.toFixed(2)} ${p.currency}`,t.appendChild(l),t}createOpeningHours(e,t){let i={};this.storeData.options.shippingCosts[e.typeKey].total<1&&(i.type="free",i.content=this.storeData.options.translations.freeLabel);const s=document.createElement("div");if(s.classList.add("opening-hours-box-container"),Object.keys(i).length){const e=document.createElement("div");e.classList.add("label",i.type),e.innerHTML=`<span>${i.content}</span>`,s.appendChild(e)}const o=document.createElement("div");o.classList.add("opening-hours-container");const n=document.createElement("table");n.classList.add("opening-hours");return e.openingHours.forEach((e=>{const t=document.createElement("tr"),i=document.createElement("td"),s=document.createElement("td");i.innerText=e.day,s.innerText=e.hours,t.appendChild(i),t.appendChild(s),n.appendChild(t)})),o.appendChild(n),s.appendChild(o),s}createShowOnMap(e){const t=document.createElement("a");return t.classList.add("show-on-map"),t.innerText=this.storeData.options.translations.showOnMap,t.addEventListener("click",(()=>{this.storeData.showParcelShopOnMap(e)})),t}createButton(e){const t=this.storeData.selectedParcelShop===e,i=document.createElement("div");i.classList.add("select-parcel-shop");const s=document.createElement("button");return s.classList.add("btn"),s.innerText=t?this.storeData.options.translations.selected:this.storeData.options.translations.select,s.disabled=t,s.type="button",s.addEventListener("click",(()=>{this.storeData.setParcelShop(e)})),i.appendChild(s),i}setParcelShop(e,t=!0){document.querySelectorAll(".parcel-shop-list-item.selected").forEach((e=>{e.classList.remove("selected");const t=e.querySelector("button");t.innerText=this.storeData.options.translations.select,t.disabled=!1}));const i=this.el.querySelector(`.parcel-shop-list-item[data-id="${this.getParcelShopIdentifier(e)}"]`),s=i.querySelector("button");s.innerText=this.storeData.options.translations.selected,s.disabled=!0,i.classList.add("selected"),t&&i.scrollIntoView({behavior:"smooth"})}show(){this.el.style.display="block"}hide(){this.el.style.display="none"}}var a=i(4690),l=i(8254);class p{validate(e){return e.currentParcelShop}onValid(e){const t=o.Z.querySelector(e.shippingMethodContainer,`.${this._getElementSelector()}`,!1);t&&t.remove()}onInvalid(e){this.onValid(e);const t=document.createElement("p");t.classList.add(this._getElementSelector(),"text-danger","m-0","p-3"),t.textContent=e.options.translations.validation.parcelShopMissing,e.shippingMethodContainer.appendChild(t),t.scrollIntoView({behavior:"smooth"})}_getElementSelector(){return"parcel-shop-missing-error"}}class h extends s.Z{init(){this.currentParcelShop=this.options.currentParcelShop,this._initElements(),this._initData(),this._registerListeners()}_initElements(){this.shippingMethodContainer=this.el.closest(this.options.methodContainerSelector),this.searchButton=o.Z.querySelector(this.el,this.options.searchButtonSelector),this.shippingMethodForm=o.Z.querySelector(document,this.options.methodForm),this.shippingMethodInputElement=o.Z.querySelector(this.shippingMethodContainer,".shipping-method-input"),this.parcelShopIdElement=o.Z.querySelector(this.el,this.options.parcelShopIdInputSelector),this.parcelShopStreetElement=o.Z.querySelector(this.el,this.options.parcelShopStreetInputSelector),this.parcelShopZipCodeElement=o.Z.querySelector(this.el,this.options.parcelShopZipCodeInputSelector),this.selectionElements=o.Z.querySelectorAll(this.shippingMethodContainer,".parcel-shop-selection"),this.mapElement=o.Z.querySelector(this.el,`[data-provider-map=${this.options.typeKey}]`),this.listElement=o.Z.querySelector(this.el,`[data-provider-list=${this.options.typeKey}]`),this.loadMoreButton=o.Z.querySelector(this.el,this.options.loadMoreButtonSelector),this._httpClient=new l.Z}_initData(){this.parcelShops=[],this.search()}_registerListeners(){this.shippingMethodForm.addEventListener("submit",this.validate.bind(this)),this.searchButton.addEventListener("click",this.search.bind(this)),this.loadMoreButton.addEventListener("click",this.loadMore.bind(this)),this.shippingMethodInputElement.addEventListener("click",(e=>{e.preventDefault()})),this.el.querySelectorAll("input").forEach((e=>{e.addEventListener("keypress",(e=>{10!==e.which&&13!==e.which||(e.preventDefault(),this.searchButton.click())}))})),o.Z.querySelector(this.shippingMethodContainer,".parcel-shop-save").addEventListener("click",this.onSave.bind(this))}search(){const e=this.el.querySelector("input[name=parcel-shop-zipcode]"),t=this.el.querySelector("input[name=parcel-shop-street]");if(t.value||e.value||(t.value=this.options.street),!e.value){if(!this.options.zipcode)return Promise.resolve();e.value=this.options.zipcode,t.value=this.options.street}const i=this.parcelShopIdElement.value||null;return this.startLoading(),new Promise(((s,o)=>{this._httpClient.get(`${this.options.apiUrl}?typeKey=${this.options.typeKey}&zipCode=${e.value}&countryCode=${this.options.countryCode}&street=${t.value}&amount=${this.options.amount}`,(e=>{try{e=JSON.parse(e)}catch(t){e={error:"Unkown error"}}if(e.error)return this.showMap(!1),this.listElement.innerHTML='<p class="parcel-shop-error">'+e.error+"</p>",o();this.parcelShops=e,this.selectedParcelShop=this.parcelShops.find((e=>e.providerId===i)),this.initViews(),s()}))})).then((()=>{this.stopLoading()})).catch((()=>{this.stopLoading(),this.loadMoreButton.disabled=!0}))}async loadMore(){this.parcelShops.length&&(this.options.amount+=20,await this.search(),this.parcelShops.length<this.options.amount&&(this.loadMoreButton.disabled=!0))}stopLoading(){a.Z.remove(this.el),o.Z.querySelectorAll(this.el,".load-button").forEach((e=>e.disabled=!1))}startLoading(){a.Z.create(this.el),o.Z.querySelectorAll(this.el,".load-button").forEach((e=>e.disabled=!0))}showMap(e=!0){this.mapPlugin&&(e?this.mapPlugin.show():this.mapPlugin.hide())}initViews(){this.mapPlugin&&this.mapPlugin.destroy(),this.mapPlugin=new n(this,this.mapElement),this.listPlugin=new r(this,this.listElement)}setParcelShop(e){this.mapPlugin.setParcelShop(e),this.listPlugin.setParcelShop(e),this.selectedParcelShop=e,this.parcelShopIdElement.value=e.providerId,this.parcelShopStreetElement.value=e.street,this.parcelShopZipCodeElement.value=e.zipCode,this._handleUpdate(e)}_handleUpdate(e){o.Z.querySelectorAll(document,this.options.parcelShopIdInputSelector).forEach((e=>{e.disabled=!e.isSameNode(this.parcelShopIdElement)})),o.Z.querySelectorAll(document,this.options.parcelShopStreetInputSelector).forEach((e=>{e.disabled=!e.isSameNode(this.parcelShopStreetElement)})),o.Z.querySelectorAll(document,this.options.parcelShopZipCodeInputSelector).forEach((e=>{e.disabled=!e.isSameNode(this.parcelShopZipCodeElement)})),this.options.shippingMethodIds&&(this.shippingMethodInputElement.value=this.options.shippingMethodIds[e.typeKey]),this.selectionElements.forEach((t=>{t.innerHTML=`${e.name} - ${e.street}, ${e.zipCode} ${e.city}`}))}showParcelShopOnMap(e){this.mapPlugin.scrollToParcelShop(e),this.showMap()}onSave(){if(this.currentParcelShop=this.selectedParcelShop,this.currentParcelShop){this.shippingMethodInputElement.checked=!0;const e=new CustomEvent("change",{bubbles:!0});this.shippingMethodInputElement.dispatchEvent(e)}}validate(e){if(this.shippingMethodInputElement.checked){let t=!0;const i=new p;return i.validate(this)?i.onValid(this):(i.onInvalid(this),e.preventDefault(),t=!1),t}return!0}}var c,d,u;c=h,u={apiUrl:"/wexo/parcel-shops",methodForm:"#changeShippingForm",methodContainerSelector:".shipping-method",searchButtonSelector:".search-parcel-shop",loadMoreButtonSelector:".load-more-shops",parcelShopIdInputSelector:"input[name=parcelShopId]",parcelShopStreetInputSelector:"input[name=parcelShopStreet]",parcelShopZipCodeInputSelector:"input[name=parcelShopZipCode]",currentParcelShop:null,shippingCommentRequired:!1,shippingCommentSelector:'[name="shippingComment"]'},(d=function(e){var t=function(e,t){if("object"!=typeof e||null===e)return e;var i=e[Symbol.toPrimitive];if(void 0!==i){var s=i.call(e,t||"default");if("object"!=typeof s)return s;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:String(t)}(d="options"))in c?Object.defineProperty(c,d,{value:u,enumerable:!0,configurable:!0,writable:!0}):c[d]=u;class m extends s.Z{init(){this.shippingForm=o.Z.querySelector(document,this.options.shippingFormSelector),this.shippingCommentField=o.Z.querySelector(document,this.options.shippingCommentSelector);o.Z.querySelectorAll(this.shippingForm,this.options.shippingMethodSelector).forEach((e=>{e.addEventListener("change",(e=>{this.shippingCommentField.required=e.target.dataset.shippingCommentRequired}).bind(this))}))}}!function(e,t,i){(t=function(e){var t=function(e,t){if("object"!=typeof e||null===e)return e;var i=e[Symbol.toPrimitive];if(void 0!==i){var s=i.call(e,t||"default");if("object"!=typeof s)return s;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:String(t)}(t))in e?Object.defineProperty(e,t,{value:i,enumerable:!0,configurable:!0,writable:!0}):e[t]=i}(m,"options",{shippingFormSelector:"#changeShippingForm",shippingCommentSelector:'textarea[name="shippingComment"]',shippingMethodSelector:"input.shipping-method-input"});const S=window.PluginManager;S.register("ParcelShopPlugin",h,"[data-parcel-shop-plugin]"),S.register("ShippingCommentPlugin",m,"[data-shipping-comment-plugin]")}},e=>{e.O(0,["vendor-node","vendor-shared"],(()=>{return t=5690,e(e.s=t);var t}));e.O()}]);
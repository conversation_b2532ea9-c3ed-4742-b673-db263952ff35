{"shippingMethod": {"deadline": {"orderBeforeDate": "Order before %weekday% %date% at %timestamp% and we will ship your order on %weekday% %date%", "orderBeforeWeekday": "Order before %weekday% at %timestamp% and we will ship your order on %weekday%", "orderBeforeTime": "Order before %timestamp% and we will ship your order today"}, "dates": {"Monday": "Monday", "Tuesday": "Tuesday", "Wednesday": "Wednesday", "Thursday": "Thursday", "Friday": "Friday", "Saturday": "Saturday", "Sunday": "Sunday"}, "shippingComment": "Comment for shipping", "noParcelShop": "None (non-parcel shop shipping method)", "selectParcelShop": "Select parcel shop:", "parcelShop": "Parcel shop:", "switchDisplayMode": "Change view", "zipcode": "Zipcode", "street": "Street", "chooseZipcode": "Search by zipcode", "chooseStreet": "Search near address", "search": "Search", "loadMore": "Load more", "select": "Select", "selected": "Selected", "showOnMap": "Show on map", "validation": {"parcelShopMissing": "Remember to choose a parcel shop", "shippingCommentMissing": "This field is required"}, "modal": {"close": "Close", "save": "Save"}}, "openingHours": {"monday": "Mon", "tuesday": "<PERSON><PERSON>", "wednesday": "Wed", "thursday": "<PERSON>hu", "friday": "<PERSON><PERSON>", "saturday": "Sat", "sunday": "Sun"}, "labels": {"free": "Free"}, "checkout": {"shippingCommentTitle": "Shipping Comment:", "missing-parcel-shop-error": "The selected shipping method requires a parcel shop to be selected."}, "error": {"missing-parcel-shop-error": "The selected shipping method requires a parcel shop to be selected."}}
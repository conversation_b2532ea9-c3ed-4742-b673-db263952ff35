<?php declare(strict_types=1);

namespace Wexo\Shipping\Migration;

use Doctrine\DBAL\Connection;
use Shopware\Core\Framework\Migration\MigrationStep;

/**
 * Class Migration1611749679AddOrderVersioningToShippingCommentAndParcelShop
 * @package Wexo\Shipping\Migration
 */
class Migration1611749679AddOrderVersioningToShippingCommentAndParcelShop extends MigrationStep
{
    /**
     * @return int
     */
    public function getCreationTimestamp(): int
    {
        return 1_611_749_679;
    }

    /**
     * @param Connection $connection
     * @throws \Doctrine\DBAL\DBALException
     */
    public function update(Connection $connection): void
    {
        $connection->executeUpdate('
            ALTER TABLE `wexo_shipping_comment`
            ADD `order_version_id` BINARY(16) NOT NULL AFTER `order_id`;

            ALTER TABLE `wexo_parcel_shop`
            ADD `order_version_id` BINARY(16) NOT NULL AFTER `order_id`;
        ');
    }

    /**
     * @param Connection $connection
     */
    public function updateDestructive(Connection $connection): void
    {
        try {
            $connection->executeUpdate('
                ALTER TABLE `wexo_shipping_comment` DROP FOREIGN KEY `fk.wexo_shipping_comment.shipping_method_id`;
    
                ALTER TABLE `wexo_shipping_comment`
                    ADD CONSTRAINT `fk.wexo_shipping_comment.order_id__order_version_id`
                        FOREIGN KEY (`order_id`, `order_version_id`) REFERENCES `order` (`id`, `version_id`)
                            ON UPDATE CASCADE ON DELETE CASCADE;
    
                ALTER TABLE `wexo_parcel_shop` DROP FOREIGN KEY `fk.wexo_shipping.order_id`;
    
                ALTER TABLE `wexo_parcel_shop`
                    ADD CONSTRAINT `fk.wexo_parcel_shop.order_id__order_version_id`
                        FOREIGN KEY (`order_id`, `order_version_id`) REFERENCES `order` (`id`, `version_id`)
                            ON UPDATE CASCADE ON DELETE CASCADE;
            ');
        } catch (\Exception $e) {
            // This change has been duplicated in the following non-destructive migration,
            // as some shops do not run destructive migrations and thus may never have this updated foreign key.
            // This should not have been a destructive migration, but for the sake of not breaking existing installs,
            // the behavior has been kept here.
        }
    }
}

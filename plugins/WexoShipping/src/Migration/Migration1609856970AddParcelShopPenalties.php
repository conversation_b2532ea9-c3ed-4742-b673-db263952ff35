<?php declare(strict_types=1);

namespace Wexo\Shipping\Migration;

use Doctrine\DBAL\Connection;
use Shopware\Core\Framework\Migration\MigrationStep;

/**
 * Class Migration1609856970AddParcelShopPenalties
 * @package Wexo\Shipping\Migration
 */
class Migration1609856970AddParcelShopPenalties extends MigrationStep
{
    /**
     * @return int
     */
    public function getCreationTimestamp(): int
    {
        return 1_609_856_970;
    }

    /**
     * @param Connection $connection
     * @throws \Doctrine\DBAL\DBALException
     */
    public function update(Connection $connection): void
    {
        $connection->executeUpdate('
            ALTER TABLE `wexo_shipping_method_type`
	        ADD `penalties` JSON NULL AFTER `type_key`;
        ');
    }

    /**
     * @param Connection $connection
     */
    public function updateDestructive(Connection $connection): void
    {
        // implement update destructive
    }
}

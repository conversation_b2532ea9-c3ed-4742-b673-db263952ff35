<?php declare(strict_types=1);

namespace Wexo\Shipping\Migration;

use Doctrine\DBAL\Connection;
use Shopware\Core\Framework\Migration\MigrationStep;

class Migration1679400332EnsureOrderForeignKey extends MigrationStep
{
    public function getCreationTimestamp(): int
    {
        return 1679400332;
    }

    public function update(Connection $connection): void
    {
        try {
            $connection->executeUpdate('
                ALTER TABLE `wexo_shipping_comment` DROP FOREIGN KEY `fk.wexo_shipping_comment.shipping_method_id`;
    
                ALTER TABLE `wexo_shipping_comment`
                    ADD CONSTRAINT `fk.wexo_shipping_comment.order_id__order_version_id`
                        FOREIGN KEY (`order_id`, `order_version_id`) REFERENCES `order` (`id`, `version_id`)
                            ON UPDATE CASCADE ON DELETE CASCADE;
    
                ALTER TABLE `wexo_parcel_shop` DROP FOREIGN KEY `fk.wexo_shipping.order_id`;
    
                ALTER TABLE `wexo_parcel_shop`
                    ADD CONSTRAINT `fk.wexo_parcel_shop.order_id__order_version_id`
                        FOREIGN KEY (`order_id`, `order_version_id`) REFERENCES `order` (`id`, `version_id`)
                            ON UPDATE CASCADE ON DELETE CASCADE;
            ');
        } catch (\Exception $e) {
            // Fails if destructive migration on previous migration has been run.
            // See previous migration.
        }
    }

    public function updateDestructive(Connection $connection): void
    {
        // implement update destructive
    }
}

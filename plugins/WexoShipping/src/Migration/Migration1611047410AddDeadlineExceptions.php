<?php declare(strict_types=1);

namespace Wexo\Shipping\Migration;

use Doctrine\DBAL\Connection;
use Shopware\Core\Framework\Migration\MigrationStep;

/**
 * Class Migration1611047410AddDeadlineExceptions
 * @package Wexo\Shipping\Migration
 */
class Migration1611047410AddDeadlineExceptions extends MigrationStep
{
    /**
     * @return int
     */
    public function getCreationTimestamp(): int
    {
        return 1_611_047_410;
    }

    /**
     * @param Connection $connection
     * @throws \Doctrine\DBAL\DBALException
     */
    public function update(Connection $connection): void
    {
        $connection->executeUpdate("
            ALTER TABLE `wexo_shipping_method_deadline`
	        ADD `excluded_weekdays` JSON NOT NULL AFTER `deadline`,
	        ADD `excluded_dates` JSON NOT NULL AFTER `excluded_weekdays`;
        ");
    }

    /**
     * @param Connection $connection
     */
    public function updateDestructive(Connection $connection): void
    {
        // implement update destructive
    }
}

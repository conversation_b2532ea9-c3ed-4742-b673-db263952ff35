<?php declare(strict_types=1);

namespace Wexo\Shipping\Core\Checkout\Order\Exception;

use Shopware\Core\Framework\ShopwareHttpException;
use Symfony\Component\HttpFoundation\Response;

class MissingShippingCommentException extends ShopwareHttpException
{
    public function __construct()
    {
        parent::__construct('Order contains no shipping comment');
    }

    public function getErrorCode(): string
    {
        return 'CHECKOUT__ORDER_MISSING_SHIPPING_COMMENT';
    }

    public function getStatusCode(): int
    {
        return Response::HTTP_BAD_REQUEST;
    }
}

<?php declare(strict_types=1);

namespace Wexo\Shipping\Core\Checkout\Cart\Custom;

use Shopware\Core\Checkout\Cart\AbstractCartPersister;
use Shopware\Core\Checkout\Cart\Cart;
use Shopware\Core\Checkout\Cart\CartValidatorInterface;
use Shopware\Core\Checkout\Cart\Error\ErrorCollection;
use Shopware\Core\System\SalesChannel\SalesChannelContext;
use Shopware\Core\System\SystemConfig\SystemConfigService;
use Symfony\Component\HttpFoundation\ParameterBag;
use Wexo\Shipping\Core\Content\Cart\CartExtension;
use Wexo\Shipping\Core\Content\Cart\MissingParcelShopError;
use Wexo\Shipping\Core\Content\ShippingMethodType\ShippingMethodTypeEntity;
use Wexo\Shipping\Service\TypeOptionService;

class ParcelShopValidator implements CartValidatorInterface
{
    public function __construct(
        private readonly TypeOptionService $typeOptionService,
        private readonly SystemConfigService $systemConfigService,
        private readonly AbstractCartPersister $cartPersister
    ) {
    }

    public function validate(Cart $cart, ErrorCollection $errors, SalesChannelContext $context): void
    {
        $delivery = $cart->getDeliveries()->first();
        if (!$delivery) {
            return;
        }
        
        /** @var $shippingMethodTypeEntity ShippingMethodTypeEntity */
        $shippingMethodTypeEntity = $delivery->getShippingMethod()->getExtension('shippingMethodType');
        if (!$shippingMethodTypeEntity) {
            //Either we couldn't find the type provider (perhaps a previously used shipping method)
            //Or the customer already has a parcelshop selected.
            return;
        }

        /** @var ?CartExtension $cartExtension */
        $cartExtension = $cart->getExtension(CartExtension::KEY);
        // If a parcelshop provider becomes blocked, for example by a rule.
        // The selected parcelshop has to change to one from the new provider.
        if ($cartExtension &&
            $cartExtension->parcelShop !== null &&
            $cartExtension->parcelShop->getTypeKey() === $shippingMethodTypeEntity->getTypeKey()
        ) {
            // The chosen parcelshop belongs to the parcelshop provider, and thus we don't need to do anything.
            return;
        }

        $shippingMethodTypeService = $this->typeOptionService->getType($shippingMethodTypeEntity->getTypeKey());
        $automaticParcelShopSelectionEnabled = $this->systemConfigService->getBool(
            'WexoShipping.config.automaticParcelShopSelection',
            $context->getSalesChannelId()
        );

        if ($automaticParcelShopSelectionEnabled && $shippingMethodTypeService) {
            if (!$delivery->getLocation()->getAddress()) {
                // Customer hasn't entered an address yet - so we can't locate nearby shops yet.
                return;
            }

            $params = new ParameterBag([
                'countryCode' => $delivery->getLocation()->getAddress()->getCountry()->getIso(),
                'zipCode' => $delivery->getLocation()->getAddress()->getZipcode(),
                'street' => $delivery->getLocation()->getAddress()->getStreet(),
                'amount' => 2 // Set as 2 instead of X
                // 2: Keep result as a "collection" and API shouldn't change format
            ]);
            $parcelShops = $shippingMethodTypeService->getParcelShops($params, $context);
            if (isset($parcelShops[0]) && (!$cartExtension || $cartExtension->parcelShop !== $parcelShops[0])) {
                if (!$cartExtension || !$cart->hasExtension(CartExtension::KEY)) {
                    $cartExtension = new CartExtension();
                    $cart->addExtension(CartExtension::KEY, $cartExtension);
                }

                $cartExtension->parcelShop = $parcelShops[0];
                $this->cartPersister->save($cart, $context);
                return;
            }
        }
        $errors->add(new MissingParcelShopError());
    }
}

(this["webpackJsonpPluginswag-commercial"]=this["webpackJsonpPluginswag-commercial"]||[]).push([[0],{P8hj:function(e,n,t){"use strict";function r(e,n){for(var t=[],r={},i=0;i<n.length;i++){var s=n[i],a=s[0],o={id:e+":"+i,css:s[1],media:s[2],sourceMap:s[3]};r[a]?r[a].parts.push(o):t.push(r[a]={id:a,parts:[o]})}return t}t.r(n),t.d(n,"default",(function(){return h}));var i="undefined"!=typeof document;if("undefined"!=typeof DEBUG&&DEBUG&&!i)throw new Error("vue-style-loader cannot be used in a non-browser environment. Use { target: 'node' } in your Webpack config to indicate a server-rendering environment.");var s={},a=i&&(document.head||document.getElementsByTagName("head")[0]),o=null,l=0,u=!1,d=function(){},c=null,f="data-vue-ssr-id",p="undefined"!=typeof navigator&&/msie [6-9]\b/.test(navigator.userAgent.toLowerCase());function h(e,n,t,i){u=t,c=i||{};var a=r(e,n);return m(a),function(n){for(var t=[],i=0;i<a.length;i++){var o=a[i];(l=s[o.id]).refs--,t.push(l)}n?m(a=r(e,n)):a=[];for(i=0;i<t.length;i++){var l;if(0===(l=t[i]).refs){for(var u=0;u<l.parts.length;u++)l.parts[u]();delete s[l.id]}}}}function m(e){for(var n=0;n<e.length;n++){var t=e[n],r=s[t.id];if(r){r.refs++;for(var i=0;i<r.parts.length;i++)r.parts[i](t.parts[i]);for(;i<t.parts.length;i++)r.parts.push(g(t.parts[i]));r.parts.length>t.parts.length&&(r.parts.length=t.parts.length)}else{var a=[];for(i=0;i<t.parts.length;i++)a.push(g(t.parts[i]));s[t.id]={id:t.id,refs:1,parts:a}}}}function v(){var e=document.createElement("style");return e.type="text/css",a.appendChild(e),e}function g(e){var n,t,r=document.querySelector("style["+f+'~="'+e.id+'"]');if(r){if(u)return d;r.parentNode.removeChild(r)}if(p){var i=l++;r=o||(o=v()),n=b.bind(null,r,i,!1),t=b.bind(null,r,i,!0)}else r=v(),n=x.bind(null,r),t=function(){r.parentNode.removeChild(r)};return n(e),function(r){if(r){if(r.css===e.css&&r.media===e.media&&r.sourceMap===e.sourceMap)return;n(e=r)}else t()}}var y,w=(y=[],function(e,n){return y[e]=n,y.filter(Boolean).join("\n")});function b(e,n,t,r){var i=t?"":r.css;if(e.styleSheet)e.styleSheet.cssText=w(n,i);else{var s=document.createTextNode(i),a=e.childNodes;a[n]&&e.removeChild(a[n]),a.length?e.insertBefore(s,a[n]):e.appendChild(s)}}function x(e,n){var t=n.css,r=n.media,i=n.sourceMap;if(r&&e.setAttribute("media",r),c.ssrId&&e.setAttribute(f,n.id),i&&(t+="\n/*# sourceURL="+i.sources[0]+" */",t+="\n/*# sourceMappingURL=data:application/json;base64,"+btoa(unescape(encodeURIComponent(JSON.stringify(i))))+" */"),e.styleSheet)e.styleSheet.cssText=t;else{for(;e.firstChild;)e.removeChild(e.firstChild);e.appendChild(document.createTextNode(t))}}},"e/Rw":function(e,n,t){},oe8t:function(e,n,t){"use strict";t.r(n);t("pqNr");var r=Shopware.Component;Shopware.Classes.ShopwareError,n.default=r.wrapComponentConfig({template:'<sw-text-field\n    {% if VUE3 %}\n    v-model:value="currentValue"\n    {% else %}\n    v-model="currentValue"\n    {% endif %}\n    {% if VUE3 != true %}\n    v-bind="$attrs"\n    v-on="$listeners"\n    {% endif %}\n    class="sw-text-field-ai"\n    :value="currentValue"\n    :error="errorField"\n    :readonly="isLoading"\n    {% if VUE3 %}\n    @update:value="onInput"\n    @keyup.enter="onEnter"\n    @submit-retry="onRetry"\n    @submit-cancel="onCancel"\n    {% else %}\n    @input="onInput"\n    {% endif %}\n>\n    <template #prefix>\n        <sw-loader v-if="isLoading" class="sw-text-field-ai__loader" size="16px"/>\n    </template>\n\n    <template #suffix>\n        <div class="sw-text-field-ai__btn-area">\n            <sw-icon\n                v-if="isRetryAble"\n                v-tooltip="{\n                    message: $tc(\'sw-text-field-ai.retry\'),\n                    width: 75,\n                    position: \'top\',\n                    showDelay: 300,\n                    hideDelay: 0\n                }"\n                name="regular-undo-xs"\n                class="sw-text-field-ai__btn-retry"\n                @click="onRetry"\n            />\n            <sw-icon\n                v-if="isLoading"\n                v-tooltip="{\n                    message: $tc(\'sw-text-field-ai.cancel\'),\n                    width: 80,\n                    position: \'top\',\n                    showDelay: 300,\n                    hideDelay: 0\n                }"\n                name="regular-times-xs"\n                class="sw-text-field-ai__btn-cancel"\n                @click="onCancel"\n            />\n            <sw-icon\n                v-tooltip="{\n                    message: $tc(\'sw-text-field-ai.ai\'),\n                    width: 50,\n                    position: \'top\',\n                    showDelay: 300,\n                    hideDelay: 0\n                }"\n                name="solid-sparkles"\n                size="14"\n                class="sw-text-field-ai__btn-submit"\n            />\n        </div>\n    </template>\n</sw-text-field>\n',props:{value:{type:String,required:!1},selectedText:{type:String,default:""},error:{type:Object,default:null},isLoading:{type:Boolean,default:!1},isRetryAble:{type:Boolean,default:!1},isAutoFocus:{type:Boolean,default:!0}},data:function(){return{currentValue:this.value,errorField:this.error}},watch:{value:function(e){this.currentValue=e},error:function(e){this.errorField=e}},methods:{onCancel:function(e){e.stopPropagation(),this.$emit("submit-cancel")},onRetry:function(){this.$emit("submit-retry")},onInput:function(e){this.errorField&&e&&(this.errorField=null),this.$emit("change",e)},onEnter:function(){this.$emit("submit")}}})},pqNr:function(e,n,t){var r=t("e/Rw");r.__esModule&&(r=r.default),"string"==typeof r&&(r=[[e.i,r,""]]),r.locals&&(e.exports=r.locals);(0,t("P8hj").default)("72b25225",r,!0,{})}}]);
//# sourceMappingURL=0.js.map
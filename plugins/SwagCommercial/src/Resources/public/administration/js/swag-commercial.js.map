{"version": 3, "sources": ["webpack:///webpack/bootstrap", "webpack:////tmp/extension2400992207/SwagCommercial/src/Resources/app/administration/src/main.js"], "names": ["webpackJsonpCallback", "data", "moduleId", "chunkId", "chunkIds", "moreModules", "i", "resolves", "length", "Object", "prototype", "hasOwnProperty", "call", "installedChunks", "push", "modules", "parentJsonpFunction", "shift", "installedModules", "installedCssChunks", "__webpack_require__", "exports", "module", "l", "e", "promises", "Promise", "resolve", "reject", "href", "fullhref", "p", "existingLinkTags", "document", "getElementsByTagName", "dataHref", "tag", "getAttribute", "rel", "existingStyleTags", "linkTag", "createElement", "type", "onerror", "onload", "event", "errorType", "realHref", "target", "err", "Error", "code", "request", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "head", "append<PERSON><PERSON><PERSON>", "then", "installedChunkData", "promise", "onScriptComplete", "script", "charset", "timeout", "nc", "setAttribute", "src", "jsonpScriptSrc", "error", "clearTimeout", "chunk", "realSrc", "message", "name", "undefined", "setTimeout", "all", "m", "c", "d", "getter", "o", "defineProperty", "enumerable", "get", "r", "Symbol", "toStringTag", "value", "t", "mode", "__esModule", "ns", "create", "key", "bind", "n", "object", "property", "oe", "console", "jsonpArray", "this", "oldJsonpFunction", "slice", "s", "Shopware", "Component", "register", "updateLicense", "Application", "getContainer", "httpClient", "headers", "Accept", "Authorization", "concat", "Service", "getToken", "catch", "License", "flag", "State", "app", "config", "licenseToggles", "set"], "mappings": "aACE,SAASA,EAAqBC,GAQ7B,IAPA,IAMIC,EAAUC,EANVC,EAAWH,EAAK,GAChBI,EAAcJ,EAAK,GAKAK,EAAI,EAAGC,EAAW,GACpCD,EAAIF,EAASI,OAAQF,IACzBH,EAAUC,EAASE,GAChBG,OAAOC,UAAUC,eAAeC,KAAKC,EAAiBV,IAAYU,EAAgBV,IACpFI,EAASO,KAAKD,EAAgBV,GAAS,IAExCU,EAAgBV,GAAW,EAE5B,IAAID,KAAYG,EACZI,OAAOC,UAAUC,eAAeC,KAAKP,EAAaH,KACpDa,EAAQb,GAAYG,EAAYH,IAKlC,IAFGc,GAAqBA,EAAoBf,GAEtCM,EAASC,QACdD,EAASU,OAATV,GAOF,IAAIW,EAAmB,GAGnBC,EAAqB,CACxB,kBAAmB,GAMhBN,EAAkB,CACrB,kBAAmB,GAWpB,SAASO,EAAoBlB,GAG5B,GAAGgB,EAAiBhB,GACnB,OAAOgB,EAAiBhB,GAAUmB,QAGnC,IAAIC,EAASJ,EAAiBhB,GAAY,CACzCI,EAAGJ,EACHqB,GAAG,EACHF,QAAS,IAUV,OANAN,EAAQb,GAAUU,KAAKU,EAAOD,QAASC,EAAQA,EAAOD,QAASD,GAG/DE,EAAOC,GAAI,EAGJD,EAAOD,QAKfD,EAAoBI,EAAI,SAAuBrB,GAC9C,IAAIsB,EAAW,GAKZN,EAAmBhB,GAAUsB,EAASX,KAAKK,EAAmBhB,IACzB,IAAhCgB,EAAmBhB,IAFX,CAAC,EAAI,GAEkCA,IACtDsB,EAASX,KAAKK,EAAmBhB,GAAW,IAAIuB,SAAQ,SAASC,EAASC,GAIzE,IAHA,IAAIC,EAAO,eAAiB,GAAG1B,IAAUA,GAAW,OAChD2B,EAAWV,EAAoBW,EAAIF,EACnCG,EAAmBC,SAASC,qBAAqB,QAC7C5B,EAAI,EAAGA,EAAI0B,EAAiBxB,OAAQF,IAAK,CAChD,IACI6B,GADAC,EAAMJ,EAAiB1B,IACR+B,aAAa,cAAgBD,EAAIC,aAAa,QACjE,GAAe,eAAZD,EAAIE,MAAyBH,IAAaN,GAAQM,IAAaL,GAAW,OAAOH,IAErF,IAAIY,EAAoBN,SAASC,qBAAqB,SACtD,IAAQ5B,EAAI,EAAGA,EAAIiC,EAAkB/B,OAAQF,IAAK,CACjD,IAAI8B,EAEJ,IADID,GADAC,EAAMG,EAAkBjC,IACT+B,aAAa,gBAChBR,GAAQM,IAAaL,EAAU,OAAOH,IAEvD,IAAIa,EAAUP,SAASQ,cAAc,QAErCD,EAAQF,IAAM,aACdE,EAAQE,KAAO,WAkBfF,EAAQG,QAAUH,EAAQI,OAjBL,SAAUC,GAG9B,GADAL,EAAQG,QAAUH,EAAQI,OAAS,KAChB,SAAfC,EAAMH,KACTf,QACM,CACN,IAAImB,EAAYD,IAAyB,SAAfA,EAAMH,KAAkB,UAAYG,EAAMH,MAChEK,EAAWF,GAASA,EAAMG,QAAUH,EAAMG,OAAOnB,MAAQC,EACzDmB,EAAM,IAAIC,MAAM,qBAAuB/C,EAAU,cAAgB4C,EAAW,KAChFE,EAAIE,KAAO,wBACXF,EAAIP,KAAOI,EACXG,EAAIG,QAAUL,SACP5B,EAAmBhB,GAC1BqC,EAAQa,WAAWC,YAAYd,GAC/BZ,EAAOqB,KAITT,EAAQX,KAAOC,EAEfG,SAASsB,KAAKC,YAAYhB,MACxBiB,MAAK,WACPtC,EAAmBhB,GAAW,MAMhC,IAAIuD,EAAqB7C,EAAgBV,GACzC,GAA0B,IAAvBuD,EAGF,GAAGA,EACFjC,EAASX,KAAK4C,EAAmB,QAC3B,CAEN,IAAIC,EAAU,IAAIjC,SAAQ,SAASC,EAASC,GAC3C8B,EAAqB7C,EAAgBV,GAAW,CAACwB,EAASC,MAE3DH,EAASX,KAAK4C,EAAmB,GAAKC,GAGtC,IACIC,EADAC,EAAS5B,SAASQ,cAAc,UAGpCoB,EAAOC,QAAU,QACjBD,EAAOE,QAAU,IACb3C,EAAoB4C,IACvBH,EAAOI,aAAa,QAAS7C,EAAoB4C,IAElDH,EAAOK,IA3GV,SAAwB/D,GACvB,OAAOiB,EAAoBW,EAAI,cAAgB,GAAG5B,IAAUA,GAAW,MA0GxDgE,CAAehE,GAG5B,IAAIiE,EAAQ,IAAIlB,MAChBU,EAAmB,SAAUf,GAE5BgB,EAAOlB,QAAUkB,EAAOjB,OAAS,KACjCyB,aAAaN,GACb,IAAIO,EAAQzD,EAAgBV,GAC5B,GAAa,IAAVmE,EAAa,CACf,GAAGA,EAAO,CACT,IAAIxB,EAAYD,IAAyB,SAAfA,EAAMH,KAAkB,UAAYG,EAAMH,MAChE6B,EAAU1B,GAASA,EAAMG,QAAUH,EAAMG,OAAOkB,IACpDE,EAAMI,QAAU,iBAAmBrE,EAAU,cAAgB2C,EAAY,KAAOyB,EAAU,IAC1FH,EAAMK,KAAO,iBACbL,EAAM1B,KAAOI,EACbsB,EAAMhB,QAAUmB,EAChBD,EAAM,GAAGF,GAEVvD,EAAgBV,QAAWuE,IAG7B,IAAIX,EAAUY,YAAW,WACxBf,EAAiB,CAAElB,KAAM,UAAWM,OAAQa,MAC1C,MACHA,EAAOlB,QAAUkB,EAAOjB,OAASgB,EACjC3B,SAASsB,KAAKC,YAAYK,GAG5B,OAAOnC,QAAQkD,IAAInD,IAIpBL,EAAoByD,EAAI9D,EAGxBK,EAAoB0D,EAAI5D,EAGxBE,EAAoB2D,EAAI,SAAS1D,EAASoD,EAAMO,GAC3C5D,EAAoB6D,EAAE5D,EAASoD,IAClChE,OAAOyE,eAAe7D,EAASoD,EAAM,CAAEU,YAAY,EAAMC,IAAKJ,KAKhE5D,EAAoBiE,EAAI,SAAShE,GACX,oBAAXiE,QAA0BA,OAAOC,aAC1C9E,OAAOyE,eAAe7D,EAASiE,OAAOC,YAAa,CAAEC,MAAO,WAE7D/E,OAAOyE,eAAe7D,EAAS,aAAc,CAAEmE,OAAO,KAQvDpE,EAAoBqE,EAAI,SAASD,EAAOE,GAEvC,GADU,EAAPA,IAAUF,EAAQpE,EAAoBoE,IAC/B,EAAPE,EAAU,OAAOF,EACpB,GAAW,EAAPE,GAA8B,iBAAVF,GAAsBA,GAASA,EAAMG,WAAY,OAAOH,EAChF,IAAII,EAAKnF,OAAOoF,OAAO,MAGvB,GAFAzE,EAAoBiE,EAAEO,GACtBnF,OAAOyE,eAAeU,EAAI,UAAW,CAAET,YAAY,EAAMK,MAAOA,IACtD,EAAPE,GAA4B,iBAATF,EAAmB,IAAI,IAAIM,KAAON,EAAOpE,EAAoB2D,EAAEa,EAAIE,EAAK,SAASA,GAAO,OAAON,EAAMM,IAAQC,KAAK,KAAMD,IAC9I,OAAOF,GAIRxE,EAAoB4E,EAAI,SAAS1E,GAChC,IAAI0D,EAAS1D,GAAUA,EAAOqE,WAC7B,WAAwB,OAAOrE,EAAgB,SAC/C,WAA8B,OAAOA,GAEtC,OADAF,EAAoB2D,EAAEC,EAAQ,IAAKA,GAC5BA,GAIR5D,EAAoB6D,EAAI,SAASgB,EAAQC,GAAY,OAAOzF,OAAOC,UAAUC,eAAeC,KAAKqF,EAAQC,IAGzG9E,EAAoBW,EAAI,2BAGxBX,EAAoB+E,GAAK,SAASlD,GAA2B,MAApBmD,QAAQhC,MAAMnB,GAAYA,GAEnE,IAAIoD,EAAaC,KAAK,qCAAuCA,KAAK,sCAAwC,GACtGC,EAAmBF,EAAWvF,KAAKiF,KAAKM,GAC5CA,EAAWvF,KAAOd,EAClBqG,EAAaA,EAAWG,QACxB,IAAI,IAAIlG,EAAI,EAAGA,EAAI+F,EAAW7F,OAAQF,IAAKN,EAAqBqG,EAAW/F,IAC3E,IAAIU,EAAsBuF,EAInBnF,EAAoBA,EAAoBqF,EAAI,Q,uBC3PrDC,SAASC,UAAUC,SAAS,oBAAoB,kBAAM,oCAEtD,IAAMC,EAAgB,WAClBH,SAASI,YAAYC,aAAa,QAAQC,WAAW5B,IACjD,mBACA,CACI6B,QAAS,CACLC,OAAQ,mBACRC,cAAc,UAADC,OAAYV,SAASW,QAAQ,gBAAgBC,UAC1D,eAAgB,mBAChB,oBAAqB,0BAG/BC,OAAM,qBAIa7C,IAArBgC,SAASc,SACT/G,OAAOyE,eAAewB,SAAU,UAAW,CACvCtB,IAAG,WACC,OAAO3E,OAAOyE,eAAe,GAAI,MAAO,CACpCE,IAAG,WACC,OAAO,SAACqC,GACJ,OAAOf,SAASgB,MAAMtC,IAAI,WAAWuC,IAAIC,OAAOC,eAAeJ,KAIvEK,IAAG,WACCjB,QAKZiB,IAAG,WACCjB", "file": "static/js/swag-commercial.js", "sourcesContent": [" \t// install a JSONP callback for chunk loading\n \tfunction webpackJsonpCallback(data) {\n \t\tvar chunkIds = data[0];\n \t\tvar moreModules = data[1];\n\n\n \t\t// add \"moreModules\" to the modules object,\n \t\t// then flag all \"chunkIds\" as loaded and fire callback\n \t\tvar moduleId, chunkId, i = 0, resolves = [];\n \t\tfor(;i < chunkIds.length; i++) {\n \t\t\tchunkId = chunkIds[i];\n \t\t\tif(Object.prototype.hasOwnProperty.call(installedChunks, chunkId) && installedChunks[chunkId]) {\n \t\t\t\tresolves.push(installedChunks[chunkId][0]);\n \t\t\t}\n \t\t\tinstalledChunks[chunkId] = 0;\n \t\t}\n \t\tfor(moduleId in moreModules) {\n \t\t\tif(Object.prototype.hasOwnProperty.call(moreModules, moduleId)) {\n \t\t\t\tmodules[moduleId] = moreModules[moduleId];\n \t\t\t}\n \t\t}\n \t\tif(parentJsonpFunction) parentJsonpFunction(data);\n\n \t\twhile(resolves.length) {\n \t\t\tresolves.shift()();\n \t\t}\n\n \t};\n\n\n \t// The module cache\n \tvar installedModules = {};\n\n \t// object to store loaded CSS chunks\n \tvar installedCssChunks = {\n \t\t\"swag-commercial\": 0\n \t};\n\n \t// object to store loaded and loading chunks\n \t// undefined = chunk not loaded, null = chunk preloaded/prefetched\n \t// Promise = chunk loading, 0 = chunk loaded\n \tvar installedChunks = {\n \t\t\"swag-commercial\": 0\n \t};\n\n\n\n \t// script path function\n \tfunction jsonpScriptSrc(chunkId) {\n \t\treturn __webpack_require__.p + \"static/js/\" + ({}[chunkId]||chunkId) + \".js\"\n \t}\n\n \t// The require function\n \tfunction __webpack_require__(moduleId) {\n\n \t\t// Check if module is in cache\n \t\tif(installedModules[moduleId]) {\n \t\t\treturn installedModules[moduleId].exports;\n \t\t}\n \t\t// Create a new module (and put it into the cache)\n \t\tvar module = installedModules[moduleId] = {\n \t\t\ti: moduleId,\n \t\t\tl: false,\n \t\t\texports: {}\n \t\t};\n\n \t\t// Execute the module function\n \t\tmodules[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n\n \t\t// Flag the module as loaded\n \t\tmodule.l = true;\n\n \t\t// Return the exports of the module\n \t\treturn module.exports;\n \t}\n\n \t// This file contains only the entry chunk.\n \t// The chunk loading function for additional chunks\n \t__webpack_require__.e = function requireEnsure(chunkId) {\n \t\tvar promises = [];\n\n\n \t\t// mini-css-extract-plugin CSS loading\n \t\tvar cssChunks = {\"0\":1};\n \t\tif(installedCssChunks[chunkId]) promises.push(installedCssChunks[chunkId]);\n \t\telse if(installedCssChunks[chunkId] !== 0 && cssChunks[chunkId]) {\n \t\t\tpromises.push(installedCssChunks[chunkId] = new Promise(function(resolve, reject) {\n \t\t\t\tvar href = \"static/css/\" + ({}[chunkId]||chunkId) + \".css\";\n \t\t\t\tvar fullhref = __webpack_require__.p + href;\n \t\t\t\tvar existingLinkTags = document.getElementsByTagName(\"link\");\n \t\t\t\tfor(var i = 0; i < existingLinkTags.length; i++) {\n \t\t\t\t\tvar tag = existingLinkTags[i];\n \t\t\t\t\tvar dataHref = tag.getAttribute(\"data-href\") || tag.getAttribute(\"href\");\n \t\t\t\t\tif(tag.rel === \"stylesheet\" && (dataHref === href || dataHref === fullhref)) return resolve();\n \t\t\t\t}\n \t\t\t\tvar existingStyleTags = document.getElementsByTagName(\"style\");\n \t\t\t\tfor(var i = 0; i < existingStyleTags.length; i++) {\n \t\t\t\t\tvar tag = existingStyleTags[i];\n \t\t\t\t\tvar dataHref = tag.getAttribute(\"data-href\");\n \t\t\t\t\tif(dataHref === href || dataHref === fullhref) return resolve();\n \t\t\t\t}\n \t\t\t\tvar linkTag = document.createElement(\"link\");\n\n \t\t\t\tlinkTag.rel = \"stylesheet\";\n \t\t\t\tlinkTag.type = \"text/css\";\n \t\t\t\tvar onLinkComplete = function (event) {\n \t\t\t\t\t// avoid mem leaks.\n \t\t\t\t\tlinkTag.onerror = linkTag.onload = null;\n \t\t\t\t\tif (event.type === 'load') {\n \t\t\t\t\t\tresolve();\n \t\t\t\t\t} else {\n \t\t\t\t\t\tvar errorType = event && (event.type === 'load' ? 'missing' : event.type);\n \t\t\t\t\t\tvar realHref = event && event.target && event.target.href || fullhref;\n \t\t\t\t\t\tvar err = new Error(\"Loading CSS chunk \" + chunkId + \" failed.\\n(\" + realHref + \")\");\n \t\t\t\t\t\terr.code = \"CSS_CHUNK_LOAD_FAILED\";\n \t\t\t\t\t\terr.type = errorType;\n \t\t\t\t\t\terr.request = realHref;\n \t\t\t\t\t\tdelete installedCssChunks[chunkId]\n \t\t\t\t\t\tlinkTag.parentNode.removeChild(linkTag)\n \t\t\t\t\t\treject(err);\n \t\t\t\t\t}\n \t\t\t\t};\n \t\t\t\tlinkTag.onerror = linkTag.onload = onLinkComplete;\n \t\t\t\tlinkTag.href = fullhref;\n\n \t\t\t\tdocument.head.appendChild(linkTag);\n \t\t\t}).then(function() {\n \t\t\t\tinstalledCssChunks[chunkId] = 0;\n \t\t\t}));\n \t\t}\n\n \t\t// JSONP chunk loading for javascript\n\n \t\tvar installedChunkData = installedChunks[chunkId];\n \t\tif(installedChunkData !== 0) { // 0 means \"already installed\".\n\n \t\t\t// a Promise means \"currently loading\".\n \t\t\tif(installedChunkData) {\n \t\t\t\tpromises.push(installedChunkData[2]);\n \t\t\t} else {\n \t\t\t\t// setup Promise in chunk cache\n \t\t\t\tvar promise = new Promise(function(resolve, reject) {\n \t\t\t\t\tinstalledChunkData = installedChunks[chunkId] = [resolve, reject];\n \t\t\t\t});\n \t\t\t\tpromises.push(installedChunkData[2] = promise);\n\n \t\t\t\t// start chunk loading\n \t\t\t\tvar script = document.createElement('script');\n \t\t\t\tvar onScriptComplete;\n\n \t\t\t\tscript.charset = 'utf-8';\n \t\t\t\tscript.timeout = 120;\n \t\t\t\tif (__webpack_require__.nc) {\n \t\t\t\t\tscript.setAttribute(\"nonce\", __webpack_require__.nc);\n \t\t\t\t}\n \t\t\t\tscript.src = jsonpScriptSrc(chunkId);\n\n \t\t\t\t// create error before stack unwound to get useful stacktrace later\n \t\t\t\tvar error = new Error();\n \t\t\t\tonScriptComplete = function (event) {\n \t\t\t\t\t// avoid mem leaks in IE.\n \t\t\t\t\tscript.onerror = script.onload = null;\n \t\t\t\t\tclearTimeout(timeout);\n \t\t\t\t\tvar chunk = installedChunks[chunkId];\n \t\t\t\t\tif(chunk !== 0) {\n \t\t\t\t\t\tif(chunk) {\n \t\t\t\t\t\t\tvar errorType = event && (event.type === 'load' ? 'missing' : event.type);\n \t\t\t\t\t\t\tvar realSrc = event && event.target && event.target.src;\n \t\t\t\t\t\t\terror.message = 'Loading chunk ' + chunkId + ' failed.\\n(' + errorType + ': ' + realSrc + ')';\n \t\t\t\t\t\t\terror.name = 'ChunkLoadError';\n \t\t\t\t\t\t\terror.type = errorType;\n \t\t\t\t\t\t\terror.request = realSrc;\n \t\t\t\t\t\t\tchunk[1](error);\n \t\t\t\t\t\t}\n \t\t\t\t\t\tinstalledChunks[chunkId] = undefined;\n \t\t\t\t\t}\n \t\t\t\t};\n \t\t\t\tvar timeout = setTimeout(function(){\n \t\t\t\t\tonScriptComplete({ type: 'timeout', target: script });\n \t\t\t\t}, 120000);\n \t\t\t\tscript.onerror = script.onload = onScriptComplete;\n \t\t\t\tdocument.head.appendChild(script);\n \t\t\t}\n \t\t}\n \t\treturn Promise.all(promises);\n \t};\n\n \t// expose the modules object (__webpack_modules__)\n \t__webpack_require__.m = modules;\n\n \t// expose the module cache\n \t__webpack_require__.c = installedModules;\n\n \t// define getter function for harmony exports\n \t__webpack_require__.d = function(exports, name, getter) {\n \t\tif(!__webpack_require__.o(exports, name)) {\n \t\t\tObject.defineProperty(exports, name, { enumerable: true, get: getter });\n \t\t}\n \t};\n\n \t// define __esModule on exports\n \t__webpack_require__.r = function(exports) {\n \t\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n \t\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n \t\t}\n \t\tObject.defineProperty(exports, '__esModule', { value: true });\n \t};\n\n \t// create a fake namespace object\n \t// mode & 1: value is a module id, require it\n \t// mode & 2: merge all properties of value into the ns\n \t// mode & 4: return value when already ns object\n \t// mode & 8|1: behave like require\n \t__webpack_require__.t = function(value, mode) {\n \t\tif(mode & 1) value = __webpack_require__(value);\n \t\tif(mode & 8) return value;\n \t\tif((mode & 4) && typeof value === 'object' && value && value.__esModule) return value;\n \t\tvar ns = Object.create(null);\n \t\t__webpack_require__.r(ns);\n \t\tObject.defineProperty(ns, 'default', { enumerable: true, value: value });\n \t\tif(mode & 2 && typeof value != 'string') for(var key in value) __webpack_require__.d(ns, key, function(key) { return value[key]; }.bind(null, key));\n \t\treturn ns;\n \t};\n\n \t// getDefaultExport function for compatibility with non-harmony modules\n \t__webpack_require__.n = function(module) {\n \t\tvar getter = module && module.__esModule ?\n \t\t\tfunction getDefault() { return module['default']; } :\n \t\t\tfunction getModuleExports() { return module; };\n \t\t__webpack_require__.d(getter, 'a', getter);\n \t\treturn getter;\n \t};\n\n \t// Object.prototype.hasOwnProperty.call\n \t__webpack_require__.o = function(object, property) { return Object.prototype.hasOwnProperty.call(object, property); };\n\n \t// __webpack_public_path__\n \t__webpack_require__.p = \"/bundles/swagcommercial/\";\n\n \t// on error function for async loading\n \t__webpack_require__.oe = function(err) { console.error(err); throw err; };\n\n \tvar jsonpArray = this[\"webpackJsonpPluginswag-commercial\"] = this[\"webpackJsonpPluginswag-commercial\"] || [];\n \tvar oldJsonpFunction = jsonpArray.push.bind(jsonpArray);\n \tjsonpArray.push = webpackJsonpCallback;\n \tjsonpArray = jsonpArray.slice();\n \tfor(var i = 0; i < jsonpArray.length; i++) webpackJsonpCallback(jsonpArray[i]);\n \tvar parentJsonpFunction = oldJsonpFunction;\n\n\n \t// Load entry module and return exports\n \treturn __webpack_require__(__webpack_require__.s = \"Xasc\");\n", "Shopware.Component.register('sw-text-field-ai', () => import('./components/sw-text-field-ai'));\n\nconst updateLicense = () => {\n    Shopware.Application.getContainer('init').httpClient.get(\n        '_admin/known-ips',\n        {\n            headers: {\n                Accept: 'application/json',\n                Authorization: `Bearer ${Shopware.Service('loginService').getToken}`,\n                'Content-Type': 'application/json',\n                'sw-license-toggle': 'FLOW_BUILDER-2909938',\n            },\n        },\n    ).catch(() => {});\n};\n\n/* istanbul ignore next */\nif (Shopware.License === undefined) {\n    Object.defineProperty(Shopware, 'License', {\n        get() {\n            return Object.defineProperty({}, 'get', {\n                get() {\n                    return (flag) => {\n                        return Shopware.State.get('context').app.config.licenseToggles[flag];\n                    };\n                },\n\n                set() {\n                    updateLicense();\n                },\n            });\n        },\n\n        set() {\n            updateLicense();\n        },\n    });\n}\n"], "sourceRoot": ""}
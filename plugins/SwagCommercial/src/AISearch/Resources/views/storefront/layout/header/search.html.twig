{# @experimental stableVersion:v6.7.0 feature:NaturalLanguageSearch #}
{% sw_extends '@Storefront/storefront/layout/header/search.html.twig' %}

{% block layout_header_search_form %}
    {% if getLicense('NATURAL_LANGUAGE_SEARCH-4165825') and config('AISearch.naturalLanguageSearch.enabled') %}
        {% block layout_header_search_form_nls %}
            <a class="nls-search-page-link" href="{{ path('frontend.natlang.page') }}">
                <button
                    id="naturalLanguageSearch"
                    class="btn header-actions-btn"
                    type="button"
                    aria-label="{{ "naturalLanguageSearch.searchButton"|trans|striptags }}"
                    title="{{ "naturalLanguageSearch.searchButton"|trans|striptags }}"
                >
                    {% sw_icon 'sparkles' style {'pack': 'default'} %}
                </button>
            </a>
        {% endblock %}
    {% endif %}

    {{ parent() }}
{% endblock %}

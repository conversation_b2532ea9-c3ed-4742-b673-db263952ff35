{# @experimental stableVersion:v6.7.0 feature:NaturalLanguageSearch #}
{% sw_extends '@Storefront/storefront/component/product/card/action.html.twig' %}

{% block component_product_box_action_inner %}
    {% set id = product.id %}
    <div class="product-action">
        {% set isAvailable = not product.isCloseout or (product.availableStock >= product.minPurchase) %}
        {% set displayFrom = product.calculatedPrices.count > 1 %}
        {% set displayBuyButton = isAvailable and not displayFrom and product.childCount <= 0 %}

        {% block component_product_box_action_detail %}
            <div class="d-grid">
                <a href="{{ seoUrl('frontend.detail.page', {'productId': id}) }}"
                   class="btn btn-primary"
                   title="{{ 'naturalLanguageSearch.productDetailButton'|trans|striptags }}">
                    {{ 'naturalLanguageSearch.productDetailButton'|trans|sw_sanitize }}
                </a>
            </div>
        {% endblock %}
    </div>

    {% block component_product_box_action_meta %}
        <input type="hidden"
               name="product-name"
               value="{{ product.translated.name }}">

        <input type="hidden"
               name="product-id"
               value="{{ id }}">
    {% endblock %}
{% endblock %}

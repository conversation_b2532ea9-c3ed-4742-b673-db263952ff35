{# @experimental stableVersion:v6.7.0 feature:NaturalLanguageSearch #}
{% sw_extends '@Storefront/storefront/component/product/card/box-standard.html.twig' %}

{% block component_product_box_info %}
    <div class="product-info">
        {% block component_product_box_name %}
            <a href="{{ seoUrl('frontend.detail.page', {'productId': id}) }}"
               class="product-name"
               title="{{ name }}">
                {{ name }}
            </a>
        {% endblock %}

        {% block component_product_box_variant_characteristics %}
            {{  parent() }}
        {%  endblock %}

        {% block component_product_box_action %}
            {% sw_include '@AISearch/storefront/component/natural-language-search/product/card/action.html.twig' %}
        {% endblock %}
    </div>
{%  endblock %}

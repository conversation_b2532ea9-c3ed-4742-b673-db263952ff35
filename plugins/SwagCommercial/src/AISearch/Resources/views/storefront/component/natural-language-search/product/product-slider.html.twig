{# @experimental stableVersion:v6.7.0 feature:NaturalLanguageSearch #}
{% sw_extends "@Storefront/storefront/element/cms-element-product-slider.html.twig"  %}

{% set sliderConfig = {
    'border': {
        'value': false
    },
    'rotate': {
        'value': false
    },
    'elMinWidth': {
        'value': '300px'
    },
    'navigation': {
        'value': true
    },
} %}

{% set element = {
    data: {
        products: {
            elements : products
        }
    },
    type: 'product-slider'
} %}

{% block element_product_slider_title %}
    {% if searchReason %}
        <h3>{{ searchReason }}</h3>
    {% endif %}
    {% if searchTerm %}
        <h6>{{ 'naturalLanguageSearch.searchTerm'|trans|striptags }} "{{ searchTerm }}"</h6>
    {% endif %}
{% endblock %}

{% block element_product_slider_inner_item %}
    <div class="product-slider-item">
        <div class="product-slider-item">
            {% sw_include '@AISearch/storefront/component/natural-language-search/product/card/search-card.html.twig' %}
        </div>
    </div>
{% endblock %}

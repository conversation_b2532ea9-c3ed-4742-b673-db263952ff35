{# @experimental stableVersion:v6.7.0 feature:NaturalLanguageSearch #}
{% sw_extends '@Storefront/storefront/base.html.twig' %}

{% block base_content %}

    {% if getLicense('NATURAL_LANGUAGE_SEARCH-4165825') %}

        {% set naturalLanguageSearchOptions = {
            searchTermsUrl: path('frontend.natlang.search.terms'),
            searchProductsUrl: path('frontend.natlang.search.products'),
            snippets: {
                title: 'naturalLanguageSearch.title'|trans,
                loadingTitle: 'naturalLanguageSearch.loadingTitle'|trans,
                resultTitle: 'naturalLanguageSearch.resultTitle'|trans,
                noResultsTitle: 'naturalLanguageSearch.noResultsTitle'|trans,
                noResultsSubTitle: 'naturalLanguageSearch.noResultsSubTitle'|trans,
            }
        } %}

        {% set searchExamples = [
            config('AISearch.naturalLanguageSearch.searchSuggestionsOne'),
            config('AISearch.naturalLanguageSearch.searchSuggestionsTwo'),
            config('AISearch.naturalLanguageSearch.searchSuggestionsThree'),
        ] | map(item => item[context.getLanguageId()]) | filter(item => item != '') %}

        <div
            class="nls-search-page d-flex flex-column align-self-stretch align-items-center"
            data-natural-language-search="true"
            data-natural-language-search-options="{{ naturalLanguageSearchOptions|json_encode }}"
        >

            <div class="container">
                <div class="row">
                    <div class="col-md-8 mx-auto">
                        <div class="nls-search-container d-flex flex-column">
                            {% block page_search_title %}
                                <div class="nls-title d-flex flex-column align-self-stretch gap-4 text-center">
                                    <h1 class="nls-title-primary align-self-stretch text-primary m-0">{{ 'naturalLanguageSearch.title'|trans }}</h1>
                                </div>
                            {% endblock %}

                            {% block page_search_natural_language_search_bar %}
                                <div class="nls-search d-flex flex-column align-items-center align-items-stretch gap-4">

                                    {% block page_search_natural_language_search_bar_inner %}
                                        <div class="nls-search-inner d-flex flex-column gap-1">

                                            {% block page_search_natural_language_search_input_group %}
                                                <div class="nls-search-bar input-group align-items-center">

                                                    <span class="nls-search-bar-icon">
                                                        {% sw_icon 'sparkles' style {'color': 'ai-primary', 'pack': 'solid'} %}
                                                    </span>

                                                    {% block layout_header_search_input %}
                                                        <input type="search"
                                                               name="search"
                                                               class="form-control nls-search-input border-0 shadow-none"
                                                               autocomplete="off"
                                                               autocapitalize="off"
                                                               maxlength="{{ constant('Shopware\\Commercial\\AISearch\\NaturalLanguageSearch\\SalesChannel\\Product\\SearchTerm\\SearchTermRoute::QUERY_MAX_LENGTH') }}"
                                                               placeholder="{{ 'naturalLanguageSearch.searchPlaceholder'|trans|striptags }}"
                                                               aria-label="{{ 'naturalLanguageSearch.searchPlaceholder'|trans|striptags }}"
                                                               value="{{ page.searchQuery }}"
                                                        >
                                                    {% endblock %}

                                                    {% block layout_header_search_button %}
                                                        <button type="button"
                                                                class="btn nls-search-bar-button input-group-append gap-4 border-0"
                                                                aria-label="{{ 'header.searchButton'|trans|striptags }}">
                                                            <span class="nls-search-bar-icon">
                                                                {% sw_icon 'arrow-right' style {
                                                                    'color': 'ai-primary'
                                                                } %}
                                                            </span>
                                                        </button>
                                                    {% endblock %}
                                                </div>
                                            {% endblock %}

                                            {% block page_search_natural_language_search_bar_counter %}
                                                <div class="small align-self-end">
                                                    <span class="nls-input-counter">0</span> / {{ constant('Shopware\\Commercial\\AISearch\\NaturalLanguageSearch\\SalesChannel\\Product\\SearchTerm\\SearchTermRoute::QUERY_MAX_LENGTH') }}
                                                </div>
                                            {% endblock %}
                                        </div>
                                    {% endblock %}

                                    {% block layout_header_search_error %}
                                        <div class="nls-search-error d-none text-danger d-flex gap-1">
                                            <div>
                                                {% sw_icon 'exclamation-circle' style {
                                                    'namespace': 'AISearch',
                                                    'color': 'danger',
                                                    'size' : 'xs',
                                                    'pack' : 'solid',
                                                } %}
                                            </div>
                                            <div>{{ 'naturalLanguageSearch.error'|trans }}</div>
                                        </div>
                                    {% endblock %}

                                    {% block page_search_natural_language_search_examples %}
                                        <div class="nls-example d-flex flex-column align-items-start gap-2">
                                            {% if searchExamples|length > 0  %}
                                                <p class="nls-example-suggestion">{{ 'naturalLanguageSearch.suggestion'|trans }}</p>
                                                <p class="nls-example-items d-flex flex-wrap gap-2">
                                                    {% for example in searchExamples %}
                                                        <button type="button" class="nls-example-item btn btn-sm btn-outline-primary lh-sm">{{ example }}</button>
                                                    {% endfor %}
                                                </p>
                                             {% endif %}
                                             <p class="nls-example-description text-center">{{ 'naturalLanguageSearch.description'|trans }}</p>
                                        </div>
                                    {% endblock %}
                                </div>
                            {% endblock %}

                            {% block page_search_natural_language_search_loading_spinner %}
                                <div class="nls-loading-spinner d-none text-center">
                                    <svg width="128" height="128" viewBox="0 0 453 378" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M243.186 159.604C244.153 156.992 247.847 156.992 248.814 159.604L260.916 192.311C261.22 193.132 261.868 193.78 262.689 194.084L295.396 206.186C298.008 207.153 298.008 210.847 295.396 211.814L262.689 223.916C261.868 224.22 261.22 224.868 260.916 225.689L248.814 258.396C247.847 261.008 244.153 261.008 243.186 258.396L231.084 225.689C230.78 224.868 230.132 224.22 229.311 223.916L196.604 211.814C193.992 210.847 193.992 207.153 196.604 206.186L229.311 194.084C230.132 193.78 230.78 193.132 231.084 192.311L243.186 159.604Z" fill="#0B539B"/>
                                        <path d="M180.686 138.604C181.653 135.992 185.347 135.992 186.314 138.604L192.339 154.888C192.643 155.709 193.291 156.357 194.112 156.661L210.396 162.686C213.008 163.653 213.008 167.347 210.396 168.314L194.112 174.339C193.291 174.643 192.643 175.291 192.339 176.112L186.314 192.396C185.347 195.008 181.653 195.008 180.686 192.396L174.661 176.112C174.357 175.291 173.709 174.643 172.888 174.339L156.604 168.314C153.992 167.347 153.992 163.653 156.604 162.686L172.888 156.661C173.709 156.357 174.357 155.709 174.661 154.888L180.686 138.604Z" fill="#0B539B"/>
                                        <path d="M226.124 116.069C226.769 114.328 229.231 114.328 229.876 116.069L233.893 126.925C234.096 127.473 234.527 127.904 235.075 128.107L245.931 132.124C247.672 132.769 247.672 135.231 245.931 135.876L235.075 139.893C234.527 140.096 234.096 140.527 233.893 141.075L229.876 151.931C229.231 153.672 226.769 153.672 226.124 151.931L222.107 141.075C221.904 140.527 221.473 140.096 220.925 139.893L210.069 135.876C208.328 135.231 208.328 132.769 210.069 132.124L220.925 128.107C221.473 127.904 221.904 127.473 222.107 126.925L226.124 116.069Z" fill="#0B539B"/>
                                    </svg>
                                </div>
                            {% endblock %}
                        </div>
                    </div>
                </div>
            </div>

            {% block page_search_natural_language_search_results %}
                <div class="nls-search-results d-flex flex-column align-self-stretch gap-5"></div>
            {% endblock %}

        </div>

    {% endif %}

{% endblock %}


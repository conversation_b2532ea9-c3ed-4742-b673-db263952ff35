{"openapi": "3.0.0", "info": {"title": "SwagCommercial NaturalLanguageSearch API route definition", "version": "1.0.0"}, "components": {"schemas": {}}, "paths": {"/store-api/product/natural-language/search-term": {"post": {"tags": ["Natural language search", "Experimental"], "summary": "Fetch search terms by natural language query", "description": "<b>Experimental until v6.7.0</b>\n\nFetch search terms by natural language query", "operationId": "naturalLanguageSearchTerm", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"query": {"description": "Natural language query", "type": "string", "example": "I'm looking for a present for my wife"}}}}}}, "responses": {"200": {"description": "Returns search terms", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/NaturalLanguageSearchTermResponse"}}}}}}}}}
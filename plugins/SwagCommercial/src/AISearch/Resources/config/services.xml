<?xml version="1.0" ?>
<container xmlns="http://symfony.com/schema/dic/services"
           xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
           xsi:schemaLocation="http://symfony.com/schema/dic/services http://symfony.com/schema/dic/services/services-1.0.xsd">
    <services>
        <service id="shopware.ai.client" class="GuzzleHttp\Client"/>

        <service id="Shopware\Commercial\AISearch\NaturalLanguageSearch\Domain\Product\SearchTerm\SearchTermBuilder">
            <argument type="service" id="Shopware\Commercial\AISearch\NaturalLanguageSearch\Domain\Product\SearchTerm\SearchTermRequest"/>
            <argument type="service" id="Doctrine\DBAL\Connection"/>
        </service>

        <service id="Shopware\Commercial\AISearch\NaturalLanguageSearch\Domain\Product\SearchTerm\SearchTermRequest">
            <argument type="service" id="shopware.ai.client"/>
            <argument type="service" id="Shopware\Core\System\SystemConfig\SystemConfigService"/>
        </service>


        <service id="Shopware\Commercial\AISearch\NaturalLanguageSearch\SalesChannel\Product\SearchTerm\SearchTermRoute" public="true">
            <argument type="service" id="Shopware\Commercial\AISearch\NaturalLanguageSearch\Domain\Product\SearchTerm\SearchTermBuilder"/>
        </service>

        <service id="Shopware\Commercial\AISearch\NaturalLanguageSearch\SalesChannel\Product\SearchTerm\CachedSearchTermRoute"
            decorates="Shopware\Commercial\AISearch\NaturalLanguageSearch\SalesChannel\Product\SearchTerm\SearchTermRoute"
            decoration-priority="-1000" public="true">
            <argument type="service" id="Shopware\Commercial\AISearch\NaturalLanguageSearch\SalesChannel\Product\SearchTerm\CachedSearchTermRoute.inner"/>
            <argument type="service" id="cache.object"/>
            <argument type="service" id="Shopware\Core\Framework\Adapter\Cache\CacheTracer"/>
            <argument type="service" id="event_dispatcher"/>
            <argument>%ai_search.cache.invalidation.search_term_route%</argument>
        </service>

        <service id="Shopware\Commercial\AISearch\NaturalLanguageSearch\Storefront\Search\Page\SearchPageLoader">
            <argument type="service" id="Shopware\Storefront\Page\GenericPageLoader"/>
            <argument type="service" id="event_dispatcher"/>
            <argument type="service" id="Shopware\Commercial\AISearch\NaturalLanguageSearch\SalesChannel\Product\SearchTerm\SearchTermRoute"/>
        </service>

        <service id="Shopware\Commercial\AISearch\NaturalLanguageSearch\Storefront\Controller\SearchController" public="true">
            <argument type="service" id="Shopware\Commercial\AISearch\NaturalLanguageSearch\Storefront\Search\Page\SearchPageLoader"/>
            <argument type="service" id="Shopware\Core\System\SystemConfig\SystemConfigService"/>
            <argument type="service" id="event_dispatcher"/>
            <argument type="service" id="Shopware\Commercial\AISearch\NaturalLanguageSearch\SalesChannel\Product\SearchTerm\SearchTermRoute"/>
            <argument type="service" id="Shopware\Core\Content\Product\SalesChannel\Search\ProductSearchRoute"/>

            <call method="setContainer">
                <argument type="service" id="service_container"/>
            </call>
            <call method="setTwig">
                <argument type="service" id="twig"/>
            </call>
        </service>

        <service id="Shopware\Commercial\AISearch\NaturalLanguageSearch\Subscriber\CacheInvalidationSubscriber">
            <argument type="service" id="Shopware\Core\Framework\Adapter\Cache\CacheInvalidator"/>
            <tag name="kernel.event_subscriber"/>
        </service>
    </services>
</container>

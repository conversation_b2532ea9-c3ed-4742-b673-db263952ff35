<?xml version="1.0" encoding="UTF-8"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="https://raw.githubusercontent.com/shopware/platform/trunk/src/Core/System/SystemConfig/Schema/config.xsd">
    <card>
        <title>Search by context</title>
        <input-field type="bool">
            <name>enabled</name>
            <label>Activate search by context</label>
            <label lang="de-DE">Kontextbasierte Suche aktivieren</label>
            <helpText>When this feature is active, customers can search for products using natural language.</helpText>
            <helpText lang="de-DE">Wenn diese Funktion aktiviert ist, können <PERSON> in natürlicher Sprache nach Produkten suchen.</helpText>
        </input-field>
        <input-field type="textarea">
            <name>salesChannelDescription</name>
            <label>Description</label>
            <label lang="de-DE">Beschreibung</label>
            <copyable>true</copyable>
            <required>true</required>
            <placeholder>Enter description...</placeholder>
            <placeholder lang="de-DE">Beschreibung eingeben ...</placeholder>
            <options>
                <option>
                    <id>sectionSubtitle</id>
                    <name>To get the best results, describe your business in a few keywords.</name>
                    <name lang="de-DE">Um die besten Ergebnisse zu erzielen, beschreibe Dein Unternehmen in wenigen Stichworten.</name>
                </option>
                <option>
                    <id>sectionTitle</id>
                    <name>Sales Channel description</name>
                    <name lang="de-DE">Verkaufskanalbeschreibung</name>
                </option>
            </options>
        </input-field>
        <input-field>
            <name>searchSuggestionsOne</name>
            <label>Example 1</label>
            <label lang="de-DE">Beispiel 1</label>
            <placeholder>Enter prompt...</placeholder>
            <placeholder lang="de-DE">Anweisung eingeben ...</placeholder>
            <options>
                <option>
                    <id>sectionSubtitle</id>
                    <name>Add example search prompts that will be displayed in the storefront.</name>
                    <name lang="de-DE">Füge Beispiel-Suchanfragen hinzu, die in der Storefront angezeigt werden sollen.</name>
                </option>
                <option>
                    <id>sectionTitle</id>
                    <name>Example prompts</name>
                    <name lang="de-DE">Beispiel-Anweisungen</name>
                </option>
            </options>
        </input-field>
        <input-field>
            <name>searchSuggestionsTwo</name>
            <label>Example 2</label>
            <label lang="de-DE">Beispiel 2</label>
            <placeholder>Enter prompt...</placeholder>
            <placeholder lang="de-DE">Anweisung eingeben ...</placeholder>
        </input-field>
        <input-field>
            <name>searchSuggestionsThree</name>
            <label>Example 3</label>
            <label lang="de-DE">Beispiel 3</label>
            <placeholder>Enter prompt...</placeholder>
            <placeholder lang="de-DE">Anweisung eingeben ...</placeholder>
        </input-field>
    </card>
</config>

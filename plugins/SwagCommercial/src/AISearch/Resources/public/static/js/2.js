(this["webpackJsonpPlugina-i-search"]=this["webpackJsonpPlugina-i-search"]||[]).push([[2],{"9RuO":function(e,n,a){"use strict";a.r(n);var s=Shopware.Component,t=Shopware.Data.Criteria;n.default=s.wrapComponentConfig({template:'<sw-single-select\n    class="sw-settings-search-live-search__sales-channel-select"\n    value-property="id"\n    label-property="translated.name"\n    :placeholder="$tc(\'sw-settings-search.liveSearchTab.textPlaceholderSalesChannel\')"\n    :label="$tc(\'sw-settings-search.liveSearchTab.labelSalesChannelSelect\')"\n    :value="salesChannelId"\n    :options="salesChannels"\n    {% if VUE3 %}\n        @update:value="onChange"\n    {% else %}\n        @change="onChange"\n    {% endif %}\n/>\n\n',inject:["repositoryFactory"],props:{salesChannelId:{type:[String,null],default:null}},data:function(){return{salesChannels:[]}},computed:{salesChannelRepository:function(){return this.repositoryFactory.create("sales_channel")}},created:function(){this.fetchSalesChannels()},methods:{onChange:function(e){e&&this.$emit("change-sales-channel-id",e)},fetchSalesChannels:function(){var e=this;return this.salesChannelRepository.search(new t(1,25)).then((function(n){var a,s=null===(a=n[0])||void 0===a?void 0:a.id;n&&void 0!==s&&(e.salesChannels=n,e.$emit("change-sales-channel-id",s))}))}}})}}]);
//# sourceMappingURL=2.js.map
{"version": 3, "sources": ["webpack:///./node_modules/vue-style-loader/lib/listToStyles.js", "webpack:///./node_modules/vue-style-loader/lib/addStylesClient.js", "webpack:////tmp/extension2400992207/SwagCommercial/src/AISearch/Resources/app/administration/src/module/sw-settings-aisearch/page/sw-settings-search-view-aisearch/sw-settings-search-view-aisearch.html.twig", "webpack:////tmp/extension2400992207/SwagCommercial/src/AISearch/Resources/app/administration/src/module/sw-settings-aisearch/page/sw-settings-search-view-aisearch/index.ts", "webpack:////tmp/extension2400992207/SwagCommercial/src/AISearch/Resources/app/administration/src/module/sw-settings-aisearch/page/sw-settings-search-view-aisearch/sw-settings-search-view-aisearch.scss"], "names": ["listToStyles", "parentId", "list", "styles", "newStyles", "i", "length", "item", "id", "part", "css", "media", "sourceMap", "parts", "push", "hasDocument", "document", "DEBUG", "Error", "stylesInDom", "head", "getElementsByTagName", "singletonElement", "singletonCounter", "isProduction", "noop", "options", "ssrIdKey", "isOldIE", "navigator", "test", "userAgent", "toLowerCase", "addStylesClient", "_isProduction", "_options", "addStylesToDom", "newList", "<PERSON><PERSON><PERSON><PERSON>", "domStyle", "refs", "j", "addStyle", "createStyleElement", "styleElement", "createElement", "type", "append<PERSON><PERSON><PERSON>", "obj", "update", "remove", "querySelector", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "styleIndex", "applyToSingletonTag", "bind", "applyToTag", "newObj", "textStore", "replaceText", "index", "replacement", "filter", "Boolean", "join", "styleSheet", "cssText", "cssNode", "createTextNode", "childNodes", "insertBefore", "setAttribute", "ssrId", "sources", "btoa", "unescape", "encodeURIComponent", "JSON", "stringify", "<PERSON><PERSON><PERSON><PERSON>", "_regeneratorRuntime", "exports", "Op", "Object", "prototype", "hasOwn", "hasOwnProperty", "defineProperty", "key", "desc", "value", "$Symbol", "Symbol", "iteratorSymbol", "iterator", "asyncIteratorSymbol", "asyncIterator", "toStringTagSymbol", "toStringTag", "define", "enumerable", "configurable", "writable", "err", "wrap", "innerFn", "outerFn", "self", "tryLocsList", "protoGenerator", "Generator", "generator", "create", "context", "Context", "makeInvokeMethod", "tryCatch", "fn", "arg", "call", "ContinueSentinel", "GeneratorFunction", "GeneratorFunctionPrototype", "IteratorPrototype", "getProto", "getPrototypeOf", "NativeIteratorPrototype", "values", "Gp", "defineIteratorMethods", "for<PERSON>ach", "method", "_invoke", "AsyncIterator", "PromiseImpl", "invoke", "resolve", "reject", "record", "result", "_typeof", "__await", "then", "unwrapped", "error", "previousPromise", "callInvokeWithMethodAndArg", "state", "doneResult", "delegate", "delegate<PERSON><PERSON><PERSON>", "maybeInvokeDelegate", "sent", "_sent", "dispatchException", "abrupt", "done", "methodName", "undefined", "return", "TypeError", "info", "resultName", "next", "nextLoc", "pushTryEntry", "locs", "entry", "tryLoc", "catchLoc", "finallyLoc", "afterLoc", "tryEntries", "resetTryEntry", "completion", "reset", "iterable", "iteratorMethod", "isNaN", "displayName", "isGeneratorFunction", "gen<PERSON>un", "ctor", "constructor", "name", "mark", "setPrototypeOf", "__proto__", "awrap", "async", "Promise", "iter", "keys", "val", "object", "reverse", "pop", "skip<PERSON>emp<PERSON><PERSON><PERSON>", "prev", "char<PERSON>t", "slice", "stop", "rootRecord", "rval", "exception", "handle", "loc", "caught", "hasCatch", "hasFinally", "finallyEntry", "complete", "finish", "catch", "thrown", "<PERSON><PERSON><PERSON>", "asyncGeneratorStep", "gen", "_next", "_throw", "_asyncToGenerator", "args", "arguments", "apply", "_Shopware", "Shopware", "Component", "License", "Mixin", "wrapComponentConfig", "template", "inject", "mixins", "getByName", "data", "currentSalesChannelId", "configSchema", "isLoading", "domains", "currentLanguageId", "api", "languageId", "saveSuccess", "saveLoading", "computed", "showAISearchView", "get", "TOGGLE_KEY_9467395", "created", "this", "createdComponent", "methods", "_this", "_callee", "_context", "loadConfigSchema", "_this2", "_ref", "_callee2", "domain", "responseData", "config", "_context2", "systemConfigApiService", "getConfig", "createNotificationError", "message", "$tc", "_objectSpread", "elements", "reduceConfigFields", "t0", "_x", "onSalesChannelChanged", "salesChannelId", "onLanguageChange", "_configSchema$element", "map", "element", "newElement", "reduce", "configElement", "_defineProperty", "getConfigSchema", "onSave", "$root", "$emit", "SETTINGS_AI<PERSON>AR<PERSON>_SAVE_EVENT", "onSaveSuccess", "saveFinish", "content", "__esModule", "default", "module", "locals", "add"], "mappings": ";sJAIe,SAASA,EAAcC,EAAUC,GAG9C,IAFA,IAAIC,EAAS,GACTC,EAAY,GACPC,EAAI,EAAGA,EAAIH,EAAKI,OAAQD,IAAK,CACpC,IAAIE,EAAOL,EAAKG,GACZG,EAAKD,EAAK,GAIVE,EAAO,CACTD,GAAIP,EAAW,IAAMI,EACrBK,IALQH,EAAK,GAMbI,MALUJ,EAAK,GAMfK,UALcL,EAAK,IAOhBH,EAAUI,GAGbJ,EAAUI,GAAIK,MAAMC,KAAKL,GAFzBN,EAAOW,KAAKV,EAAUI,GAAM,CAAEA,GAAIA,EAAIK,MAAO,CAACJ,KAKlD,OAAON,E,+CCjBT,IAAIY,EAAkC,oBAAbC,SAEzB,GAAqB,oBAAVC,OAAyBA,QAC7BF,EACH,MAAM,IAAIG,MACV,2JAkBJ,IAAIC,EAAc,GAQdC,EAAOL,IAAgBC,SAASI,MAAQJ,SAASK,qBAAqB,QAAQ,IAC9EC,EAAmB,KACnBC,EAAmB,EACnBC,GAAe,EACfC,EAAO,aACPC,EAAU,KACVC,EAAW,kBAIXC,EAA+B,oBAAdC,WAA6B,eAAeC,KAAKD,UAAUE,UAAUC,eAE3E,SAASC,EAAiBhC,EAAUC,EAAMgC,EAAeC,GACtEX,EAAeU,EAEfR,EAAUS,GAAY,GAEtB,IAAIhC,EAASH,EAAaC,EAAUC,GAGpC,OAFAkC,EAAejC,GAER,SAAiBkC,GAEtB,IADA,IAAIC,EAAY,GACPjC,EAAI,EAAGA,EAAIF,EAAOG,OAAQD,IAAK,CACtC,IAAIE,EAAOJ,EAAOE,IACdkC,EAAWpB,EAAYZ,EAAKC,KACvBgC,OACTF,EAAUxB,KAAKyB,GAEbF,EAEFD,EADAjC,EAASH,EAAaC,EAAUoC,IAGhClC,EAAS,GAEX,IAASE,EAAI,EAAGA,EAAIiC,EAAUhC,OAAQD,IAAK,CACzC,IAAIkC,EACJ,GAAsB,KADlBA,EAAWD,EAAUjC,IACZmC,KAAY,CACvB,IAAK,IAAIC,EAAI,EAAGA,EAAIF,EAAS1B,MAAMP,OAAQmC,IACzCF,EAAS1B,MAAM4B,YAEVtB,EAAYoB,EAAS/B,OAMpC,SAAS4B,EAAgBjC,GACvB,IAAK,IAAIE,EAAI,EAAGA,EAAIF,EAAOG,OAAQD,IAAK,CACtC,IAAIE,EAAOJ,EAAOE,GACdkC,EAAWpB,EAAYZ,EAAKC,IAChC,GAAI+B,EAAU,CACZA,EAASC,OACT,IAAK,IAAIC,EAAI,EAAGA,EAAIF,EAAS1B,MAAMP,OAAQmC,IACzCF,EAAS1B,MAAM4B,GAAGlC,EAAKM,MAAM4B,IAE/B,KAAOA,EAAIlC,EAAKM,MAAMP,OAAQmC,IAC5BF,EAAS1B,MAAMC,KAAK4B,EAASnC,EAAKM,MAAM4B,KAEtCF,EAAS1B,MAAMP,OAASC,EAAKM,MAAMP,SACrCiC,EAAS1B,MAAMP,OAASC,EAAKM,MAAMP,YAEhC,CACL,IAAIO,EAAQ,GACZ,IAAS4B,EAAI,EAAGA,EAAIlC,EAAKM,MAAMP,OAAQmC,IACrC5B,EAAMC,KAAK4B,EAASnC,EAAKM,MAAM4B,KAEjCtB,EAAYZ,EAAKC,IAAM,CAAEA,GAAID,EAAKC,GAAIgC,KAAM,EAAG3B,MAAOA,KAK5D,SAAS8B,IACP,IAAIC,EAAe5B,SAAS6B,cAAc,SAG1C,OAFAD,EAAaE,KAAO,WACpB1B,EAAK2B,YAAYH,GACVA,EAGT,SAASF,EAAUM,GACjB,IAAIC,EAAQC,EACRN,EAAe5B,SAASmC,cAAc,SAAWxB,EAAW,MAAQqB,EAAIxC,GAAK,MAEjF,GAAIoC,EAAc,CAChB,GAAIpB,EAGF,OAAOC,EAOPmB,EAAaQ,WAAWC,YAAYT,GAIxC,GAAIhB,EAAS,CAEX,IAAI0B,EAAa/B,IACjBqB,EAAetB,IAAqBA,EAAmBqB,KACvDM,EAASM,EAAoBC,KAAK,KAAMZ,EAAcU,GAAY,GAClEJ,EAASK,EAAoBC,KAAK,KAAMZ,EAAcU,GAAY,QAGlEV,EAAeD,IACfM,EAASQ,EAAWD,KAAK,KAAMZ,GAC/BM,EAAS,WACPN,EAAaQ,WAAWC,YAAYT,IAMxC,OAFAK,EAAOD,GAEA,SAAsBU,GAC3B,GAAIA,EAAQ,CACV,GAAIA,EAAOhD,MAAQsC,EAAItC,KACnBgD,EAAO/C,QAAUqC,EAAIrC,OACrB+C,EAAO9C,YAAcoC,EAAIpC,UAC3B,OAEFqC,EAAOD,EAAMU,QAEbR,KAKN,IACMS,EADFC,GACED,EAAY,GAET,SAAUE,EAAOC,GAEtB,OADAH,EAAUE,GAASC,EACZH,EAAUI,OAAOC,SAASC,KAAK,QAI1C,SAASV,EAAqBX,EAAciB,EAAOX,EAAQF,GACzD,IAAItC,EAAMwC,EAAS,GAAKF,EAAItC,IAE5B,GAAIkC,EAAasB,WACftB,EAAasB,WAAWC,QAAUP,EAAYC,EAAOnD,OAChD,CACL,IAAI0D,EAAUpD,SAASqD,eAAe3D,GAClC4D,EAAa1B,EAAa0B,WAC1BA,EAAWT,IAAQjB,EAAaS,YAAYiB,EAAWT,IACvDS,EAAWhE,OACbsC,EAAa2B,aAAaH,EAASE,EAAWT,IAE9CjB,EAAaG,YAAYqB,IAK/B,SAASX,EAAYb,EAAcI,GACjC,IAAItC,EAAMsC,EAAItC,IACVC,EAAQqC,EAAIrC,MACZC,EAAYoC,EAAIpC,UAiBpB,GAfID,GACFiC,EAAa4B,aAAa,QAAS7D,GAEjCe,EAAQ+C,OACV7B,EAAa4B,aAAa7C,EAAUqB,EAAIxC,IAGtCI,IAGFF,GAAO,mBAAqBE,EAAU8D,QAAQ,GAAK,MAEnDhE,GAAO,uDAAyDiE,KAAKC,SAASC,mBAAmBC,KAAKC,UAAUnE,MAAgB,OAG9HgC,EAAasB,WACftB,EAAasB,WAAWC,QAAUzD,MAC7B,CACL,KAAOkC,EAAaoC,YAClBpC,EAAaS,YAAYT,EAAaoC,YAExCpC,EAAaG,YAAY/B,SAASqD,eAAe3D,O,mDC3NtC,I,4vCCCfuE,EAAA,kBAAAC,GAAA,IAAAA,EAAA,GAAAC,EAAAC,OAAAC,UAAAC,EAAAH,EAAAI,eAAAC,EAAAJ,OAAAI,gBAAA,SAAAxC,EAAAyC,EAAAC,GAAA1C,EAAAyC,GAAAC,EAAAC,OAAAC,EAAA,mBAAAC,cAAA,GAAAC,EAAAF,EAAAG,UAAA,aAAAC,EAAAJ,EAAAK,eAAA,kBAAAC,EAAAN,EAAAO,aAAA,yBAAAC,EAAApD,EAAAyC,EAAAE,GAAA,OAAAP,OAAAI,eAAAxC,EAAAyC,EAAA,CAAAE,QAAAU,YAAA,EAAAC,cAAA,EAAAC,UAAA,IAAAvD,EAAAyC,GAAA,IAAAW,EAAA,aAAAI,GAAAJ,EAAA,SAAApD,EAAAyC,EAAAE,GAAA,OAAA3C,EAAAyC,GAAAE,GAAA,SAAAc,EAAAC,EAAAC,EAAAC,EAAAC,GAAA,IAAAC,EAAAH,KAAAtB,qBAAA0B,EAAAJ,EAAAI,EAAAC,EAAA5B,OAAA6B,OAAAH,EAAAzB,WAAA6B,EAAA,IAAAC,EAAAN,GAAA,WAAArB,EAAAwB,EAAA,WAAArB,MAAAyB,EAAAV,EAAAE,EAAAM,KAAAF,EAAA,SAAAK,EAAAC,EAAAtE,EAAAuE,GAAA,WAAAzE,KAAA,SAAAyE,IAAAD,EAAAE,KAAAxE,EAAAuE,IAAA,MAAAf,GAAA,OAAA1D,KAAA,QAAAyE,IAAAf,IAAAtB,EAAAuB,OAAA,IAAAgB,EAAA,YAAAV,KAAA,SAAAW,KAAA,SAAAC,KAAA,IAAAC,EAAA,GAAAxB,EAAAwB,EAAA9B,GAAA,8BAAA+B,EAAAzC,OAAA0C,eAAAC,EAAAF,OAAAG,EAAA,MAAAD,OAAA5C,GAAAG,EAAAkC,KAAAO,EAAAjC,KAAA8B,EAAAG,GAAA,IAAAE,EAAAN,EAAAtC,UAAA0B,EAAA1B,UAAAD,OAAA6B,OAAAW,GAAA,SAAAM,EAAA7C,GAAA,0BAAA8C,SAAA,SAAAC,GAAAhC,EAAAf,EAAA+C,GAAA,SAAAb,GAAA,YAAAc,QAAAD,EAAAb,SAAA,SAAAe,EAAAtB,EAAAuB,GAAA,SAAAC,EAAAJ,EAAAb,EAAAkB,EAAAC,GAAA,IAAAC,EAAAtB,EAAAL,EAAAoB,GAAApB,EAAAO,GAAA,aAAAoB,EAAA7F,KAAA,KAAA8F,EAAAD,EAAApB,IAAA5B,EAAAiD,EAAAjD,MAAA,OAAAA,GAAA,UAAAkD,EAAAlD,IAAAL,EAAAkC,KAAA7B,EAAA,WAAA4C,EAAAE,QAAA9C,EAAAmD,SAAAC,MAAA,SAAApD,GAAA6C,EAAA,OAAA7C,EAAA8C,EAAAC,MAAA,SAAAlC,GAAAgC,EAAA,QAAAhC,EAAAiC,EAAAC,MAAAH,EAAAE,QAAA9C,GAAAoD,MAAA,SAAAC,GAAAJ,EAAAjD,MAAAqD,EAAAP,EAAAG,MAAA,SAAAK,GAAA,OAAAT,EAAA,QAAAS,EAAAR,EAAAC,QAAAC,EAAApB,KAAA,IAAA2B,EAAA1D,EAAA,gBAAAG,MAAA,SAAAyC,EAAAb,GAAA,SAAA4B,IAAA,WAAAZ,GAAA,SAAAE,EAAAC,GAAAF,EAAAJ,EAAAb,EAAAkB,EAAAC,MAAA,OAAAQ,MAAAH,KAAAI,YAAA,SAAA/B,EAAAV,EAAAE,EAAAM,GAAA,IAAAkC,EAAA,iCAAAhB,EAAAb,GAAA,iBAAA6B,EAAA,UAAAlI,MAAA,iDAAAkI,EAAA,cAAAhB,EAAA,MAAAb,EAAA,OAAA8B,IAAA,IAAAnC,EAAAkB,SAAAlB,EAAAK,QAAA,KAAA+B,EAAApC,EAAAoC,SAAA,GAAAA,EAAA,KAAAC,EAAAC,EAAAF,EAAApC,GAAA,GAAAqC,EAAA,IAAAA,IAAA9B,EAAA,gBAAA8B,GAAA,YAAArC,EAAAkB,OAAAlB,EAAAuC,KAAAvC,EAAAwC,MAAAxC,EAAAK,SAAA,aAAAL,EAAAkB,OAAA,uBAAAgB,EAAA,MAAAA,EAAA,YAAAlC,EAAAK,IAAAL,EAAAyC,kBAAAzC,EAAAK,SAAA,WAAAL,EAAAkB,QAAAlB,EAAA0C,OAAA,SAAA1C,EAAAK,KAAA6B,EAAA,gBAAAT,EAAAtB,EAAAX,EAAAE,EAAAM,GAAA,cAAAyB,EAAA7F,KAAA,IAAAsG,EAAAlC,EAAA2C,KAAA,6BAAAlB,EAAApB,MAAAE,EAAA,gBAAA9B,MAAAgD,EAAApB,IAAAsC,KAAA3C,EAAA2C,MAAA,UAAAlB,EAAA7F,OAAAsG,EAAA,YAAAlC,EAAAkB,OAAA,QAAAlB,EAAAK,IAAAoB,EAAApB,OAAA,SAAAiC,EAAAF,EAAApC,GAAA,IAAA4C,EAAA5C,EAAAkB,SAAAkB,EAAAvD,SAAA+D,GAAA,QAAAC,IAAA3B,EAAA,OAAAlB,EAAAoC,SAAA,eAAAQ,GAAAR,EAAAvD,SAAAiE,SAAA9C,EAAAkB,OAAA,SAAAlB,EAAAK,SAAAwC,EAAAP,EAAAF,EAAApC,GAAA,UAAAA,EAAAkB,SAAA,WAAA0B,IAAA5C,EAAAkB,OAAA,QAAAlB,EAAAK,IAAA,IAAA0C,UAAA,oCAAAH,EAAA,aAAArC,EAAA,IAAAkB,EAAAtB,EAAAe,EAAAkB,EAAAvD,SAAAmB,EAAAK,KAAA,aAAAoB,EAAA7F,KAAA,OAAAoE,EAAAkB,OAAA,QAAAlB,EAAAK,IAAAoB,EAAApB,IAAAL,EAAAoC,SAAA,KAAA7B,EAAA,IAAAyC,EAAAvB,EAAApB,IAAA,OAAA2C,IAAAL,MAAA3C,EAAAoC,EAAAa,YAAAD,EAAAvE,MAAAuB,EAAAkD,KAAAd,EAAAe,QAAA,WAAAnD,EAAAkB,SAAAlB,EAAAkB,OAAA,OAAAlB,EAAAK,SAAAwC,GAAA7C,EAAAoC,SAAA,KAAA7B,GAAAyC,GAAAhD,EAAAkB,OAAA,QAAAlB,EAAAK,IAAA,IAAA0C,UAAA,oCAAA/C,EAAAoC,SAAA,KAAA7B,GAAA,SAAA6C,EAAAC,GAAA,IAAAC,EAAA,CAAAC,OAAAF,EAAA,SAAAA,IAAAC,EAAAE,SAAAH,EAAA,SAAAA,IAAAC,EAAAG,WAAAJ,EAAA,GAAAC,EAAAI,SAAAL,EAAA,SAAAM,WAAA/J,KAAA0J,GAAA,SAAAM,EAAAN,GAAA,IAAA7B,EAAA6B,EAAAO,YAAA,GAAApC,EAAA7F,KAAA,gBAAA6F,EAAApB,IAAAiD,EAAAO,WAAApC,EAAA,SAAAxB,EAAAN,GAAA,KAAAgE,WAAA,EAAAJ,OAAA,SAAA5D,EAAAsB,QAAAmC,EAAA,WAAAU,OAAA,YAAAhD,EAAAiD,GAAA,GAAAA,EAAA,KAAAC,EAAAD,EAAAnF,GAAA,GAAAoF,EAAA,OAAAA,EAAA1D,KAAAyD,GAAA,sBAAAA,EAAAb,KAAA,OAAAa,EAAA,IAAAE,MAAAF,EAAA3K,QAAA,KAAAD,GAAA,EAAA+J,EAAA,SAAAA,IAAA,OAAA/J,EAAA4K,EAAA3K,QAAA,GAAAgF,EAAAkC,KAAAyD,EAAA5K,GAAA,OAAA+J,EAAAzE,MAAAsF,EAAA5K,GAAA+J,EAAAP,MAAA,EAAAO,EAAA,OAAAA,EAAAzE,WAAAoE,EAAAK,EAAAP,MAAA,EAAAO,GAAA,OAAAA,UAAA,OAAAA,KAAAf,GAAA,SAAAA,IAAA,OAAA1D,WAAAoE,EAAAF,MAAA,UAAAnC,EAAArC,UAAAsC,EAAAnC,EAAAyC,EAAA,eAAAtC,MAAAgC,EAAArB,cAAA,IAAAd,EAAAmC,EAAA,eAAAhC,MAAA+B,EAAApB,cAAA,IAAAoB,EAAA0D,YAAAhF,EAAAuB,EAAAzB,EAAA,qBAAAhB,EAAAmG,oBAAA,SAAAC,GAAA,IAAAC,EAAA,mBAAAD,KAAAE,YAAA,QAAAD,QAAA7D,GAAA,uBAAA6D,EAAAH,aAAAG,EAAAE,QAAAvG,EAAAwG,KAAA,SAAAJ,GAAA,OAAAlG,OAAAuG,eAAAvG,OAAAuG,eAAAL,EAAA3D,IAAA2D,EAAAM,UAAAjE,EAAAvB,EAAAkF,EAAApF,EAAA,sBAAAoF,EAAAjG,UAAAD,OAAA6B,OAAAgB,GAAAqD,GAAApG,EAAA2G,MAAA,SAAAtE,GAAA,OAAAuB,QAAAvB,IAAAW,EAAAI,EAAAjD,WAAAe,EAAAkC,EAAAjD,UAAAW,GAAA,0BAAAd,EAAAoD,gBAAApD,EAAA4G,MAAA,SAAApF,EAAAC,EAAAC,EAAAC,EAAA0B,QAAA,IAAAA,MAAAwD,SAAA,IAAAC,EAAA,IAAA1D,EAAA7B,EAAAC,EAAAC,EAAAC,EAAAC,GAAA0B,GAAA,OAAArD,EAAAmG,oBAAA1E,GAAAqF,IAAA5B,OAAArB,MAAA,SAAAH,GAAA,OAAAA,EAAAiB,KAAAjB,EAAAjD,MAAAqG,EAAA5B,WAAAlC,EAAAD,GAAA7B,EAAA6B,EAAA/B,EAAA,aAAAE,EAAA6B,EAAAnC,GAAA,0BAAAM,EAAA6B,EAAA,qDAAA/C,EAAA+G,KAAA,SAAAC,GAAA,IAAAC,EAAA/G,OAAA8G,GAAAD,EAAA,WAAAxG,KAAA0G,EAAAF,EAAAnL,KAAA2E,GAAA,OAAAwG,EAAAG,UAAA,SAAAhC,IAAA,KAAA6B,EAAA3L,QAAA,KAAAmF,EAAAwG,EAAAI,MAAA,GAAA5G,KAAA0G,EAAA,OAAA/B,EAAAzE,MAAAF,EAAA2E,EAAAP,MAAA,EAAAO,EAAA,OAAAA,EAAAP,MAAA,EAAAO,IAAAlF,EAAA8C,SAAAb,EAAA9B,UAAA,CAAAmG,YAAArE,EAAA6D,MAAA,SAAAsB,GAAA,QAAAC,KAAA,OAAAnC,KAAA,OAAAX,KAAA,KAAAC,WAAAK,EAAA,KAAAF,MAAA,OAAAP,SAAA,UAAAlB,OAAA,YAAAb,SAAAwC,EAAA,KAAAc,WAAA1C,QAAA2C,IAAAwB,EAAA,QAAAb,KAAA,WAAAA,EAAAe,OAAA,IAAAlH,EAAAkC,KAAA,KAAAiE,KAAAN,OAAAM,EAAAgB,MAAA,WAAAhB,QAAA1B,IAAA2C,KAAA,gBAAA7C,MAAA,MAAA8C,EAAA,KAAA9B,WAAA,GAAAE,WAAA,aAAA4B,EAAA7J,KAAA,MAAA6J,EAAApF,IAAA,YAAAqF,MAAAjD,kBAAA,SAAAkD,GAAA,QAAAhD,KAAA,MAAAgD,EAAA,IAAA3F,EAAA,cAAA4F,EAAAC,EAAAC,GAAA,OAAArE,EAAA7F,KAAA,QAAA6F,EAAApB,IAAAsF,EAAA3F,EAAAkD,KAAA2C,EAAAC,IAAA9F,EAAAkB,OAAA,OAAAlB,EAAAK,SAAAwC,KAAAiD,EAAA,QAAA3M,EAAA,KAAAwK,WAAAvK,OAAA,EAAAD,GAAA,IAAAA,EAAA,KAAAmK,EAAA,KAAAK,WAAAxK,GAAAsI,EAAA6B,EAAAO,WAAA,YAAAP,EAAAC,OAAA,OAAAqC,EAAA,UAAAtC,EAAAC,QAAA,KAAA8B,KAAA,KAAAU,EAAA3H,EAAAkC,KAAAgD,EAAA,YAAA0C,EAAA5H,EAAAkC,KAAAgD,EAAA,iBAAAyC,GAAAC,EAAA,SAAAX,KAAA/B,EAAAE,SAAA,OAAAoC,EAAAtC,EAAAE,UAAA,WAAA6B,KAAA/B,EAAAG,WAAA,OAAAmC,EAAAtC,EAAAG,iBAAA,GAAAsC,GAAA,QAAAV,KAAA/B,EAAAE,SAAA,OAAAoC,EAAAtC,EAAAE,UAAA,YAAAwC,EAAA,UAAAhM,MAAA,kDAAAqL,KAAA/B,EAAAG,WAAA,OAAAmC,EAAAtC,EAAAG,gBAAAf,OAAA,SAAA9G,EAAAyE,GAAA,QAAAlH,EAAA,KAAAwK,WAAAvK,OAAA,EAAAD,GAAA,IAAAA,EAAA,KAAAmK,EAAA,KAAAK,WAAAxK,GAAA,GAAAmK,EAAAC,QAAA,KAAA8B,MAAAjH,EAAAkC,KAAAgD,EAAA,oBAAA+B,KAAA/B,EAAAG,WAAA,KAAAwC,EAAA3C,EAAA,OAAA2C,IAAA,UAAArK,GAAA,aAAAA,IAAAqK,EAAA1C,QAAAlD,MAAA4F,EAAAxC,aAAAwC,EAAA,UAAAxE,EAAAwE,IAAApC,WAAA,UAAApC,EAAA7F,OAAA6F,EAAApB,MAAA4F,GAAA,KAAA/E,OAAA,YAAAgC,KAAA+C,EAAAxC,WAAAlD,GAAA,KAAA2F,SAAAzE,IAAAyE,SAAA,SAAAzE,EAAAiC,GAAA,aAAAjC,EAAA7F,KAAA,MAAA6F,EAAApB,IAAA,gBAAAoB,EAAA7F,MAAA,aAAA6F,EAAA7F,KAAA,KAAAsH,KAAAzB,EAAApB,IAAA,WAAAoB,EAAA7F,MAAA,KAAA8J,KAAA,KAAArF,IAAAoB,EAAApB,IAAA,KAAAa,OAAA,cAAAgC,KAAA,kBAAAzB,EAAA7F,MAAA8H,IAAA,KAAAR,KAAAQ,GAAAnD,GAAA4F,OAAA,SAAA1C,GAAA,QAAAtK,EAAA,KAAAwK,WAAAvK,OAAA,EAAAD,GAAA,IAAAA,EAAA,KAAAmK,EAAA,KAAAK,WAAAxK,GAAA,GAAAmK,EAAAG,eAAA,YAAAyC,SAAA5C,EAAAO,WAAAP,EAAAI,UAAAE,EAAAN,GAAA/C,IAAA6F,MAAA,SAAA7C,GAAA,QAAApK,EAAA,KAAAwK,WAAAvK,OAAA,EAAAD,GAAA,IAAAA,EAAA,KAAAmK,EAAA,KAAAK,WAAAxK,GAAA,GAAAmK,EAAAC,WAAA,KAAA9B,EAAA6B,EAAAO,WAAA,aAAApC,EAAA7F,KAAA,KAAAyK,EAAA5E,EAAApB,IAAAuD,EAAAN,GAAA,OAAA+C,GAAA,UAAArM,MAAA,0BAAAsM,cAAA,SAAAvC,EAAAd,EAAAE,GAAA,YAAAf,SAAA,CAAAvD,SAAAiC,EAAAiD,GAAAd,aAAAE,WAAA,cAAAjC,SAAA,KAAAb,SAAAwC,GAAAtC,IAAAvC,EAAA,SAAAuI,EAAAC,EAAAjF,EAAAC,EAAAiF,EAAAC,EAAAnI,EAAA8B,GAAA,QAAA2C,EAAAwD,EAAAjI,GAAA8B,GAAA5B,EAAAuE,EAAAvE,MAAA,MAAAsD,GAAA,YAAAP,EAAAO,GAAAiB,EAAAL,KAAApB,EAAA9C,GAAAoG,QAAAtD,QAAA9C,GAAAoD,KAAA4E,EAAAC,GAAA,SAAAC,EAAAvG,GAAA,sBAAAV,EAAA,KAAAkH,EAAAC,UAAA,WAAAhC,SAAA,SAAAtD,EAAAC,GAAA,IAAAgF,EAAApG,EAAA0G,MAAApH,EAAAkH,GAAA,SAAAH,EAAAhI,GAAA8H,EAAAC,EAAAjF,EAAAC,EAAAiF,EAAAC,EAAA,OAAAjI,GAAA,SAAAiI,EAAApH,GAAAiH,EAAAC,EAAAjF,EAAAC,EAAAiF,EAAAC,EAAA,QAAApH,GAAAmH,OAAA5D,OAiBA,IAAAkE,EAA+CC,SAAvCC,EAASF,EAATE,UAAWC,EAAOH,EAAPG,QAASC,EAAKJ,EAALI,MAAOlH,EAAO8G,EAAP9G,QAEpBgH,YAAUG,oBAAoB,CACzCC,SDrBW,45FCuBXC,OAAQ,CAAC,0BAETC,OAAQ,CACJJ,EAAMK,UAAU,iBAGpBC,KAAI,WASA,MAAO,CACHC,sBAAuB,KACvBC,aAAc,KACdC,WAAW,EACXC,QAAS,CAAC,kCACVC,kBAAmB7H,EAAQ8H,IAAIC,WAC/BC,aAAa,EACbC,aAAa,IAIrBC,SAAU,CACNC,iBAAgB,WACZ,OAAOlB,EAAQmB,IAAIC,OAI3BC,QAAO,WACHC,KAAKC,oBAGTC,QAAS,CACCD,iBAAgB,WAAmB,IAADE,EAAA,YAAAhC,EAAA5I,IAAAyG,MAAA,SAAAoE,IAAA,OAAA7K,IAAAwB,MAAA,SAAAsJ,GAAA,cAAAA,EAAAxD,KAAAwD,EAAA3F,MAAA,cAAA2F,EAAA3F,KAAA,EAC9ByF,EAAKG,mBAAmB,KAAD,mBAAAD,EAAArD,UAAAoD,MADOjC,IAIxCmC,iBAAgB,WAAU,IAADC,EAAA,KACrBP,KAAKZ,WAAY,EAEjBY,KAAKX,QAAQ5G,QAAO,eAAA+H,EAAArC,EAAA5I,IAAAyG,MAAC,SAAAyE,EAAOC,GAAc,IAAAC,EAAAC,EAAA,OAAArL,IAAAwB,MAAA,SAAA8J,GAAA,cAAAA,EAAAhE,KAAAgE,EAAAnG,MAAA,cAAAmG,EAAAhE,KAAA,EAAAgE,EAAAnG,KAAA,EAEP6F,EAAKO,uBAAuBC,UAAUL,GAAQ,KAAD,EAC1C,GADxBC,EAAYE,EAAA9G,KAGb6G,OAFCA,EAASD,EAAa,KAEvBC,EAAQ/K,eAAe,aAAgB+K,WAAQ/K,eAAe,SAAQ,CAAAgL,EAAAnG,KAAA,QAIpE,OAFH6F,EAAKS,wBAAwB,CACzBC,QAASV,EAAKW,IAAI,0DACnBL,EAAA3G,OAAA,iBAKPqG,EAAKpB,aAAe,GAEpBoB,EAAKpB,aAAauB,GAAOS,IAAA,GAClBP,GAAM,IACTQ,SAAUb,EAAKc,mBAAmBT,KACpCC,EAAAnG,KAAA,iBAAAmG,EAAAhE,KAAA,GAAAgE,EAAAS,GAAAT,EAAA,SAEFN,EAAKS,wBAAwB,CACzBC,QAASV,EAAKW,IAAI,0DACnB,QAEoB,OAFpBL,EAAAhE,KAAA,GAEH0D,EAAKnB,WAAY,EAAMyB,EAAAlD,OAAA,6BAAAkD,EAAA7D,UAAAyD,EAAA,yBAE9B,gBAAAc,GAAA,OAAAf,EAAAlC,MAAA,KAAAD,YA3BmB,KA8BxBmD,sBAAqB,SAACC,GAClBzB,KAAKd,sBAAwBuC,GAGjCC,iBAAgB,SAAClC,GACbQ,KAAKV,kBAAoBE,GAG7B6B,mBAAkB,SAAClC,GAA0D,IAADwC,EACxE,OAAOxC,SAAsB,QAAVwC,EAAZxC,EAAciC,gBAAQ,IAAAO,OAAV,EAAZA,EAAwBC,KAAI,SAACC,GAChC,IAQMC,EAAkCD,EACxC,OAAKC,EAAWlB,OAAO/K,eAAe,YAItCiM,EAAWlB,OAAO5O,QAAU6P,EAAQjB,OAAO5O,QAAQ+P,QAb7B,SAClBzO,EACA0O,GAEA,OAAAb,IAAA,GAAY7N,GAAG,GAAA2O,EAAA,GAAGD,EAAclR,GAAKkR,MASgC,IAElEH,GALIC,MASnBI,gBAAe,SAACxB,GACZ,OAAOV,KAAKb,aAAauB,IAG7ByB,OAAM,WACFnC,KAAKP,aAAc,EACnBO,KAAKN,aAAc,EAEnBM,KAAKoC,MAAMC,MAAMC,MAGrBC,cAAa,WACTvC,KAAKN,aAAc,EACnBM,KAAKP,aAAc,GAGvB+C,WAAU,WACNxC,KAAKP,aAAc,EACnBO,KAAKN,aAAc,O,uBC7I/B,IAAI+C,EAAU,EAAQ,QACnBA,EAAQC,aAAYD,EAAUA,EAAQE,SACnB,iBAAZF,IAAsBA,EAAU,CAAC,CAACG,EAAOjS,EAAI8R,EAAS,MAC7DA,EAAQI,SAAQD,EAAOpN,QAAUiN,EAAQI,SAG/BC,EADH,EAAQ,QAAqLH,SACtL,WAAYF,GAAS,EAAM", "file": "static/js/0.js", "sourcesContent": ["/**\n * Translates the list format produced by css-loader into something\n * easier to manipulate.\n */\nexport default function listToStyles (parentId, list) {\n  var styles = []\n  var newStyles = {}\n  for (var i = 0; i < list.length; i++) {\n    var item = list[i]\n    var id = item[0]\n    var css = item[1]\n    var media = item[2]\n    var sourceMap = item[3]\n    var part = {\n      id: parentId + ':' + i,\n      css: css,\n      media: media,\n      sourceMap: sourceMap\n    }\n    if (!newStyles[id]) {\n      styles.push(newStyles[id] = { id: id, parts: [part] })\n    } else {\n      newStyles[id].parts.push(part)\n    }\n  }\n  return styles\n}\n", "/*\n  MIT License http://www.opensource.org/licenses/mit-license.php\n  Author <PERSON> @sokra\n  Modified by <PERSON> @yyx990803\n*/\n\nimport listToStyles from './listToStyles'\n\nvar hasDocument = typeof document !== 'undefined'\n\nif (typeof DEBUG !== 'undefined' && DEBUG) {\n  if (!hasDocument) {\n    throw new Error(\n    'vue-style-loader cannot be used in a non-browser environment. ' +\n    \"Use { target: 'node' } in your Webpack config to indicate a server-rendering environment.\"\n  ) }\n}\n\n/*\ntype StyleObject = {\n  id: number;\n  parts: Array<StyleObjectPart>\n}\n\ntype StyleObjectPart = {\n  css: string;\n  media: string;\n  sourceMap: ?string\n}\n*/\n\nvar stylesInDom = {/*\n  [id: number]: {\n    id: number,\n    refs: number,\n    parts: Array<(obj?: StyleObjectPart) => void>\n  }\n*/}\n\nvar head = hasDocument && (document.head || document.getElementsByTagName('head')[0])\nvar singletonElement = null\nvar singletonCounter = 0\nvar isProduction = false\nvar noop = function () {}\nvar options = null\nvar ssrIdKey = 'data-vue-ssr-id'\n\n// Force single-tag solution on IE6-9, which has a hard limit on the # of <style>\n// tags it will allow on a page\nvar isOldIE = typeof navigator !== 'undefined' && /msie [6-9]\\b/.test(navigator.userAgent.toLowerCase())\n\nexport default function addStylesClient (parentId, list, _isProduction, _options) {\n  isProduction = _isProduction\n\n  options = _options || {}\n\n  var styles = listToStyles(parentId, list)\n  addStylesToDom(styles)\n\n  return function update (newList) {\n    var mayRemove = []\n    for (var i = 0; i < styles.length; i++) {\n      var item = styles[i]\n      var domStyle = stylesInDom[item.id]\n      domStyle.refs--\n      mayRemove.push(domStyle)\n    }\n    if (newList) {\n      styles = listToStyles(parentId, newList)\n      addStylesToDom(styles)\n    } else {\n      styles = []\n    }\n    for (var i = 0; i < mayRemove.length; i++) {\n      var domStyle = mayRemove[i]\n      if (domStyle.refs === 0) {\n        for (var j = 0; j < domStyle.parts.length; j++) {\n          domStyle.parts[j]()\n        }\n        delete stylesInDom[domStyle.id]\n      }\n    }\n  }\n}\n\nfunction addStylesToDom (styles /* Array<StyleObject> */) {\n  for (var i = 0; i < styles.length; i++) {\n    var item = styles[i]\n    var domStyle = stylesInDom[item.id]\n    if (domStyle) {\n      domStyle.refs++\n      for (var j = 0; j < domStyle.parts.length; j++) {\n        domStyle.parts[j](item.parts[j])\n      }\n      for (; j < item.parts.length; j++) {\n        domStyle.parts.push(addStyle(item.parts[j]))\n      }\n      if (domStyle.parts.length > item.parts.length) {\n        domStyle.parts.length = item.parts.length\n      }\n    } else {\n      var parts = []\n      for (var j = 0; j < item.parts.length; j++) {\n        parts.push(addStyle(item.parts[j]))\n      }\n      stylesInDom[item.id] = { id: item.id, refs: 1, parts: parts }\n    }\n  }\n}\n\nfunction createStyleElement () {\n  var styleElement = document.createElement('style')\n  styleElement.type = 'text/css'\n  head.appendChild(styleElement)\n  return styleElement\n}\n\nfunction addStyle (obj /* StyleObjectPart */) {\n  var update, remove\n  var styleElement = document.querySelector('style[' + ssrIdKey + '~=\"' + obj.id + '\"]')\n\n  if (styleElement) {\n    if (isProduction) {\n      // has SSR styles and in production mode.\n      // simply do nothing.\n      return noop\n    } else {\n      // has SSR styles but in dev mode.\n      // for some reason Chrome can't handle source map in server-rendered\n      // style tags - source maps in <style> only works if the style tag is\n      // created and inserted dynamically. So we remove the server rendered\n      // styles and inject new ones.\n      styleElement.parentNode.removeChild(styleElement)\n    }\n  }\n\n  if (isOldIE) {\n    // use singleton mode for IE9.\n    var styleIndex = singletonCounter++\n    styleElement = singletonElement || (singletonElement = createStyleElement())\n    update = applyToSingletonTag.bind(null, styleElement, styleIndex, false)\n    remove = applyToSingletonTag.bind(null, styleElement, styleIndex, true)\n  } else {\n    // use multi-style-tag mode in all other cases\n    styleElement = createStyleElement()\n    update = applyToTag.bind(null, styleElement)\n    remove = function () {\n      styleElement.parentNode.removeChild(styleElement)\n    }\n  }\n\n  update(obj)\n\n  return function updateStyle (newObj /* StyleObjectPart */) {\n    if (newObj) {\n      if (newObj.css === obj.css &&\n          newObj.media === obj.media &&\n          newObj.sourceMap === obj.sourceMap) {\n        return\n      }\n      update(obj = newObj)\n    } else {\n      remove()\n    }\n  }\n}\n\nvar replaceText = (function () {\n  var textStore = []\n\n  return function (index, replacement) {\n    textStore[index] = replacement\n    return textStore.filter(Boolean).join('\\n')\n  }\n})()\n\nfunction applyToSingletonTag (styleElement, index, remove, obj) {\n  var css = remove ? '' : obj.css\n\n  if (styleElement.styleSheet) {\n    styleElement.styleSheet.cssText = replaceText(index, css)\n  } else {\n    var cssNode = document.createTextNode(css)\n    var childNodes = styleElement.childNodes\n    if (childNodes[index]) styleElement.removeChild(childNodes[index])\n    if (childNodes.length) {\n      styleElement.insertBefore(cssNode, childNodes[index])\n    } else {\n      styleElement.appendChild(cssNode)\n    }\n  }\n}\n\nfunction applyToTag (styleElement, obj) {\n  var css = obj.css\n  var media = obj.media\n  var sourceMap = obj.sourceMap\n\n  if (media) {\n    styleElement.setAttribute('media', media)\n  }\n  if (options.ssrId) {\n    styleElement.setAttribute(ssrIdKey, obj.id)\n  }\n\n  if (sourceMap) {\n    // https://developer.chrome.com/devtools/docs/javascript-debugging\n    // this makes source maps inside style tags work properly in Chrome\n    css += '\\n/*# sourceURL=' + sourceMap.sources[0] + ' */'\n    // http://stackoverflow.com/a/26603875\n    css += '\\n/*# sourceMappingURL=data:application/json;base64,' + btoa(unescape(encodeURIComponent(JSON.stringify(sourceMap)))) + ' */'\n  }\n\n  if (styleElement.styleSheet) {\n    styleElement.styleSheet.cssText = css\n  } else {\n    while (styleElement.firstChild) {\n      styleElement.removeChild(styleElement.firstChild)\n    }\n    styleElement.appendChild(document.createTextNode(css))\n  }\n}\n", "export default \"<sw-page\\n    class=\\\"sw-settings-search-view-aisearch\\\"\\n    v-if=\\\"showAISearchView\\\"\\n>\\n    \\n    {% block sw_settings_aisearch_header %}\\n        <template #smart-bar-header>\\n            <h2>{{ $tc('sw-settings.index.title') }} <sw-icon\\n                name=\\\"regular-chevron-right-xs\\\"\\n                small\\n            /> {{ $tc('sw-settings-aisearch.general.title') }} </h2>\\n        </template>\\n    {% endblock %}\\n\\n    \\n    {% block sw_settings_aisearch_language_switch %}\\n        <template #language-switch>\\n            <sw-language-switch @on-change=\\\"onLanguageChange\\\" />\\n        </template>\\n    {% endblock %}\\n\\n    \\n    {% block sw_settings_aisearch_smart_bar_actions %}\\n        <template #smart-bar-actions>\\n            \\n            {% block sw_settings_reviews_actions_save %}\\n                <sw-button-process\\n                        class=\\\"sw-settings-aisearch__save-action\\\"\\n                        :is-loading=\\\"isLoading || saveLoading\\\"\\n                        :disabled=\\\"isLoading || saveLoading\\\"\\n                        variant=\\\"primary\\\"\\n                        :process-success=\\\"saveSuccess\\\"\\n                        {% if VUE3 %}\\n                            @update:processSuccess=\\\"saveFinish\\\"\\n                        {% else %}\\n                            @process-finish=\\\"saveFinish\\\"\\n                        {% endif %}\\n                        @click=\\\"onSave\\\"\\n                >\\n                    {{ $tc('sw-settings-translator.detail.buttonSave') }}\\n                </sw-button-process>\\n            {% endblock %}\\n        </template>\\n    {% endblock %}\\n\\n    {% block sw_settings_aisearch_content %}\\n        <template #content>\\n            <sw-card-view>\\n                <template v-if=\\\"isLoading || !currentSalesChannelId\\\">\\n                    <sw-skeleton variant=\\\"detail-bold\\\" />\\n                    <sw-skeleton />\\n                </template>\\n\\n                <sw-sales-channel-switch-aisearch\\n                    v-show=\\\"!isLoading && currentSalesChannelId\\\"\\n                    class=\\\"sw-settings-search-view-aisearch__sales-channel-switch\\\"\\n                    :label=\\\"$tc('sw-settings.system-config.labelSalesChannelSelect')\\\"\\n                    :sales-channel-id=\\\"currentSalesChannelId\\\"\\n                    @change-sales-channel-id=\\\"onSalesChannelChanged\\\"\\n                />\\n\\n                <sw-settings-search-aisearch\\n                    v-if=\\\"!isLoading && configSchema && currentSalesChannelId\\\"\\n                    domain=\\\"AISearch.naturalLanguageSearch\\\"\\n                    :configFields=\\\"getConfigSchema('AISearch.naturalLanguageSearch')\\\"\\n                    :currentSalesChannelId=\\\"currentSalesChannelId\\\"\\n                    :currentLanguageId=\\\"currentLanguageId\\\"\\n                    @save-success=\\\"onSaveSuccess\\\"\\n                    @save-finish=\\\"saveFinish\\\"\\n                />\\n            </sw-card-view>\\n        </template>\\n    {% endblock %}\\n\\n</sw-page>\\n\";", "/**\n * @package system-settings\n * @experimental stableVersion:v6.7.0 feature:NaturalLanguageSearch\n */\n\nimport template from './sw-settings-search-view-aisearch.html.twig';\nimport './sw-settings-search-view-aisearch.scss';\n\nimport { SETTINGS_AISEARCH_SAVE_EVENT, TOGGLE_KEY_9467395 } from '../../../../config';\nimport {\n    SchemaResponseItem,\n    SchemaResponseItemElement,\n    ConfigSchema,\n    ConfigElementOption,\n    ConfigSchemaDomain,\n    ConfigSchemaElement,\n} from '../../types.d';\n\nconst { Component, License, Mixin, Context } = Shopware;\n\nexport default Component.wrapComponentConfig({\n    template,\n\n    inject: ['systemConfigApiService'],\n\n    mixins: [\n        Mixin.getByName('notification'),\n    ],\n\n    data(): {\n        currentSalesChannelId: string|null,\n        configSchema: ConfigSchemaDomain|null,\n        isLoading: boolean,\n        domains: string[],\n        currentLanguageId: string,\n        saveSuccess: boolean,\n        saveLoading: boolean,\n        } {\n        return {\n            currentSalesChannelId: null,\n            configSchema: null,\n            isLoading: false,\n            domains: ['AISearch.naturalLanguageSearch'],\n            currentLanguageId: Context.api.languageId,\n            saveSuccess: false,\n            saveLoading: false,\n        };\n    },\n\n    computed: {\n        showAISearchView(): boolean {\n            return License.get(TOGGLE_KEY_9467395);\n        },\n    },\n\n    created() {\n        this.createdComponent();\n    },\n\n    methods: {\n        async createdComponent(): Promise<void> {\n            await this.loadConfigSchema();\n        },\n\n        loadConfigSchema(): void {\n            this.isLoading = true;\n\n            this.domains.forEach(async (domain: string) => {\n                try {\n                    const responseData = await this.systemConfigApiService.getConfig(domain);\n                    const config = responseData[0] as SchemaResponseItem;\n\n                    if (!config?.hasOwnProperty('elements') || !config?.hasOwnProperty('title')\n                    ) {\n                        this.createNotificationError({\n                            message: this.$tc('sw-settings-aisearch.notification.schemaErrorMessage'),\n                        });\n\n                        return;\n                    }\n\n                    this.configSchema = {};\n\n                    this.configSchema[domain] = {\n                        ...config,\n                        elements: this.reduceConfigFields(config),\n                    };\n                } catch {\n                    this.createNotificationError({\n                        message: this.$tc('sw-settings-aisearch.notification.schemaErrorMessage'),\n                    });\n                } finally {\n                    this.isLoading = false;\n                }\n            });\n        },\n\n        onSalesChannelChanged(salesChannelId: string|null): void {\n            this.currentSalesChannelId = salesChannelId;\n        },\n\n        onLanguageChange(languageId: string): void {\n            this.currentLanguageId = languageId;\n        },\n\n        reduceConfigFields(configSchema: SchemaResponseItem): ConfigSchemaElement[] {\n            return configSchema?.elements?.map((element: SchemaResponseItemElement) => {\n                const reduceOptions = (\n                    obj: {[key:string]: ConfigElementOption},\n                    configElement: ConfigElementOption,\n                ) => {\n                    return { ...obj, [configElement.id]: configElement };\n                };\n\n                // Add variable to account for type switch between parameter and return\n                const newElement: ConfigSchemaElement = element;\n                if (!newElement.config.hasOwnProperty('options')) {\n                    return newElement;\n                }\n\n                newElement.config.options = element.config.options.reduce(reduceOptions, {});\n\n                return element;\n            });\n        },\n\n        getConfigSchema(domain: string): ConfigSchema {\n            return this.configSchema[domain];\n        },\n\n        onSave(): void {\n            this.saveSuccess = false;\n            this.saveLoading = true;\n\n            this.$root.$emit(SETTINGS_AISEARCH_SAVE_EVENT);\n        },\n\n        onSaveSuccess(): void {\n            this.saveLoading = false;\n            this.saveSuccess = true;\n        },\n\n        saveFinish(): void {\n            this.saveSuccess = false;\n            this.saveLoading = false;\n        },\n    },\n});\n", "// style-loader: Adds some css to the DOM by adding a <style> tag\n\n// load the styles\nvar content = require(\"!!../../../../../../../../../../../../../builds/shopware/6/product/commercial/src/Administration/Resources/app/administration/node_modules/mini-css-extract-plugin/dist/loader.js??ref--15-1!../../../../../../../../../../../../../builds/shopware/6/product/commercial/src/Administration/Resources/app/administration/node_modules/css-loader/dist/cjs.js??ref--15-2!../../../../../../../../../../../../../builds/shopware/6/product/commercial/src/Administration/Resources/app/administration/node_modules/sass-loader/dist/cjs.js??ref--15-3!./sw-settings-search-view-aisearch.scss\");\nif(content.__esModule) content = content.default;\nif(typeof content === 'string') content = [[module.id, content, '']];\nif(content.locals) module.exports = content.locals;\n// add the styles to the DOM\nvar add = require(\"!../../../../../../../../../../../../../builds/shopware/6/product/commercial/src/Administration/Resources/app/administration/node_modules/vue-style-loader/lib/addStylesClient.js\").default\nvar update = add(\"9942f252\", content, true, {});"], "sourceRoot": ""}
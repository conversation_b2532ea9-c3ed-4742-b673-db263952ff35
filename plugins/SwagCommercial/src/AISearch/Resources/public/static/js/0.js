/*! For license information please see 0.js.LICENSE.txt */
(this["webpackJsonpPlugina-i-search"]=this["webpackJsonpPlugina-i-search"]||[]).push([[0],{"9+zH":function(e,t,n){},P8hj:function(e,t,n){"use strict";function r(e,t){for(var n=[],r={},o=0;o<t.length;o++){var i=t[o],a=i[0],s={id:e+":"+o,css:i[1],media:i[2],sourceMap:i[3]};r[a]?r[a].parts.push(s):n.push(r[a]={id:a,parts:[s]})}return n}n.r(t),n.d(t,"default",(function(){return p}));var o="undefined"!=typeof document;if("undefined"!=typeof DEBUG&&DEBUG&&!o)throw new Error("vue-style-loader cannot be used in a non-browser environment. Use { target: 'node' } in your Webpack config to indicate a server-rendering environment.");var i={},a=o&&(document.head||document.getElementsByTagName("head")[0]),s=null,c=0,u=!1,l=function(){},h=null,f="data-vue-ssr-id",d="undefined"!=typeof navigator&&/msie [6-9]\b/.test(navigator.userAgent.toLowerCase());function p(e,t,n,o){u=n,h=o||{};var a=r(e,t);return g(a),function(t){for(var n=[],o=0;o<a.length;o++){var s=a[o];(c=i[s.id]).refs--,n.push(c)}t?g(a=r(e,t)):a=[];for(o=0;o<n.length;o++){var c;if(0===(c=n[o]).refs){for(var u=0;u<c.parts.length;u++)c.parts[u]();delete i[c.id]}}}}function g(e){for(var t=0;t<e.length;t++){var n=e[t],r=i[n.id];if(r){r.refs++;for(var o=0;o<r.parts.length;o++)r.parts[o](n.parts[o]);for(;o<n.parts.length;o++)r.parts.push(m(n.parts[o]));r.parts.length>n.parts.length&&(r.parts.length=n.parts.length)}else{var a=[];for(o=0;o<n.parts.length;o++)a.push(m(n.parts[o]));i[n.id]={id:n.id,refs:1,parts:a}}}}function v(){var e=document.createElement("style");return e.type="text/css",a.appendChild(e),e}function m(e){var t,n,r=document.querySelector("style["+f+'~="'+e.id+'"]');if(r){if(u)return l;r.parentNode.removeChild(r)}if(d){var o=c++;r=s||(s=v()),t=b.bind(null,r,o,!1),n=b.bind(null,r,o,!0)}else r=v(),t=S.bind(null,r),n=function(){r.parentNode.removeChild(r)};return t(e),function(r){if(r){if(r.css===e.css&&r.media===e.media&&r.sourceMap===e.sourceMap)return;t(e=r)}else n()}}var y,w=(y=[],function(e,t){return y[e]=t,y.filter(Boolean).join("\n")});function b(e,t,n,r){var o=n?"":r.css;if(e.styleSheet)e.styleSheet.cssText=w(t,o);else{var i=document.createTextNode(o),a=e.childNodes;a[t]&&e.removeChild(a[t]),a.length?e.insertBefore(i,a[t]):e.appendChild(i)}}function S(e,t){var n=t.css,r=t.media,o=t.sourceMap;if(r&&e.setAttribute("media",r),h.ssrId&&e.setAttribute(f,t.id),o&&(n+="\n/*# sourceURL="+o.sources[0]+" */",n+="\n/*# sourceMappingURL=data:application/json;base64,"+btoa(unescape(encodeURIComponent(JSON.stringify(o))))+" */"),e.styleSheet)e.styleSheet.cssText=n;else{for(;e.firstChild;)e.removeChild(e.firstChild);e.appendChild(document.createTextNode(n))}}},mAb1:function(e,t,n){"use strict";n.r(t);n("ub+m");var r=n("uzim");function o(e){return(o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function i(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function a(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?i(Object(n),!0).forEach((function(t){s(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):i(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function s(e,t,n){return(t=function(e){var t=function(e,t){if("object"!==o(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!==o(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===o(t)?t:String(t)}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function c(){c=function(){return e};var e={},t=Object.prototype,n=t.hasOwnProperty,r=Object.defineProperty||function(e,t,n){e[t]=n.value},i="function"==typeof Symbol?Symbol:{},a=i.iterator||"@@iterator",s=i.asyncIterator||"@@asyncIterator",u=i.toStringTag||"@@toStringTag";function l(e,t,n){return Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{l({},"")}catch(e){l=function(e,t,n){return e[t]=n}}function h(e,t,n,o){var i=t&&t.prototype instanceof p?t:p,a=Object.create(i.prototype),s=new O(o||[]);return r(a,"_invoke",{value:x(e,n,s)}),a}function f(e,t,n){try{return{type:"normal",arg:e.call(t,n)}}catch(e){return{type:"throw",arg:e}}}e.wrap=h;var d={};function p(){}function g(){}function v(){}var m={};l(m,a,(function(){return this}));var y=Object.getPrototypeOf,w=y&&y(y(j([])));w&&w!==t&&n.call(w,a)&&(m=w);var b=v.prototype=p.prototype=Object.create(m);function S(e){["next","throw","return"].forEach((function(t){l(e,t,(function(e){return this._invoke(t,e)}))}))}function L(e,t){function i(r,a,s,c){var u=f(e[r],e,a);if("throw"!==u.type){var l=u.arg,h=l.value;return h&&"object"==o(h)&&n.call(h,"__await")?t.resolve(h.__await).then((function(e){i("next",e,s,c)}),(function(e){i("throw",e,s,c)})):t.resolve(h).then((function(e){l.value=e,s(l)}),(function(e){return i("throw",e,s,c)}))}c(u.arg)}var a;r(this,"_invoke",{value:function(e,n){function r(){return new t((function(t,r){i(e,n,t,r)}))}return a=a?a.then(r,r):r()}})}function x(e,t,n){var r="suspendedStart";return function(o,i){if("executing"===r)throw new Error("Generator is already running");if("completed"===r){if("throw"===o)throw i;return k()}for(n.method=o,n.arg=i;;){var a=n.delegate;if(a){var s=C(a,n);if(s){if(s===d)continue;return s}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if("suspendedStart"===r)throw r="completed",n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);r="executing";var c=f(e,t,n);if("normal"===c.type){if(r=n.done?"completed":"suspendedYield",c.arg===d)continue;return{value:c.arg,done:n.done}}"throw"===c.type&&(r="completed",n.method="throw",n.arg=c.arg)}}}function C(e,t){var n=t.method,r=e.iterator[n];if(void 0===r)return t.delegate=null,"throw"===n&&e.iterator.return&&(t.method="return",t.arg=void 0,C(e,t),"throw"===t.method)||"return"!==n&&(t.method="throw",t.arg=new TypeError("The iterator does not provide a '"+n+"' method")),d;var o=f(r,e.iterator,t.arg);if("throw"===o.type)return t.method="throw",t.arg=o.arg,t.delegate=null,d;var i=o.arg;return i?i.done?(t[e.resultName]=i.value,t.next=e.nextLoc,"return"!==t.method&&(t.method="next",t.arg=void 0),t.delegate=null,d):i:(t.method="throw",t.arg=new TypeError("iterator result is not an object"),t.delegate=null,d)}function _(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function E(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function O(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(_,this),this.reset(!0)}function j(e){if(e){var t=e[a];if(t)return t.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var r=-1,o=function t(){for(;++r<e.length;)if(n.call(e,r))return t.value=e[r],t.done=!1,t;return t.value=void 0,t.done=!0,t};return o.next=o}}return{next:k}}function k(){return{value:void 0,done:!0}}return g.prototype=v,r(b,"constructor",{value:v,configurable:!0}),r(v,"constructor",{value:g,configurable:!0}),g.displayName=l(v,u,"GeneratorFunction"),e.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===g||"GeneratorFunction"===(t.displayName||t.name))},e.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,v):(e.__proto__=v,l(e,u,"GeneratorFunction")),e.prototype=Object.create(b),e},e.awrap=function(e){return{__await:e}},S(L.prototype),l(L.prototype,s,(function(){return this})),e.AsyncIterator=L,e.async=function(t,n,r,o,i){void 0===i&&(i=Promise);var a=new L(h(t,n,r,o),i);return e.isGeneratorFunction(n)?a:a.next().then((function(e){return e.done?e.value:a.next()}))},S(b),l(b,u,"Generator"),l(b,a,(function(){return this})),l(b,"toString",(function(){return"[object Generator]"})),e.keys=function(e){var t=Object(e),n=[];for(var r in t)n.push(r);return n.reverse(),function e(){for(;n.length;){var r=n.pop();if(r in t)return e.value=r,e.done=!1,e}return e.done=!0,e}},e.values=j,O.prototype={constructor:O,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,this.tryEntries.forEach(E),!e)for(var t in this)"t"===t.charAt(0)&&n.call(this,t)&&!isNaN(+t.slice(1))&&(this[t]=void 0)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var t=this;function r(n,r){return a.type="throw",a.arg=e,t.next=n,r&&(t.method="next",t.arg=void 0),!!r}for(var o=this.tryEntries.length-1;o>=0;--o){var i=this.tryEntries[o],a=i.completion;if("root"===i.tryLoc)return r("end");if(i.tryLoc<=this.prev){var s=n.call(i,"catchLoc"),c=n.call(i,"finallyLoc");if(s&&c){if(this.prev<i.catchLoc)return r(i.catchLoc,!0);if(this.prev<i.finallyLoc)return r(i.finallyLoc)}else if(s){if(this.prev<i.catchLoc)return r(i.catchLoc,!0)}else{if(!c)throw new Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return r(i.finallyLoc)}}}},abrupt:function(e,t){for(var r=this.tryEntries.length-1;r>=0;--r){var o=this.tryEntries[r];if(o.tryLoc<=this.prev&&n.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var i=o;break}}i&&("break"===e||"continue"===e)&&i.tryLoc<=t&&t<=i.finallyLoc&&(i=null);var a=i?i.completion:{};return a.type=e,a.arg=t,i?(this.method="next",this.next=i.finallyLoc,d):this.complete(a)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),d},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),E(n),d}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.tryLoc===e){var r=n.completion;if("throw"===r.type){var o=r.arg;E(n)}return o}}throw new Error("illegal catch attempt")},delegateYield:function(e,t,n){return this.delegate={iterator:j(e),resultName:t,nextLoc:n},"next"===this.method&&(this.arg=void 0),d}},e}function u(e,t,n,r,o,i,a){try{var s=e[i](a),c=s.value}catch(e){return void n(e)}s.done?t(c):Promise.resolve(c).then(r,o)}function l(e){return function(){var t=this,n=arguments;return new Promise((function(r,o){var i=e.apply(t,n);function a(e){u(i,r,o,a,s,"next",e)}function s(e){u(i,r,o,a,s,"throw",e)}a(void 0)}))}}var h=Shopware,f=h.Component,d=h.License,p=h.Mixin,g=h.Context;t.default=f.wrapComponentConfig({template:'<sw-page\n    class="sw-settings-search-view-aisearch"\n    v-if="showAISearchView"\n>\n    \n    {% block sw_settings_aisearch_header %}\n        <template #smart-bar-header>\n            <h2>{{ $tc(\'sw-settings.index.title\') }} <sw-icon\n                name="regular-chevron-right-xs"\n                small\n            /> {{ $tc(\'sw-settings-aisearch.general.title\') }} </h2>\n        </template>\n    {% endblock %}\n\n    \n    {% block sw_settings_aisearch_language_switch %}\n        <template #language-switch>\n            <sw-language-switch @on-change="onLanguageChange" />\n        </template>\n    {% endblock %}\n\n    \n    {% block sw_settings_aisearch_smart_bar_actions %}\n        <template #smart-bar-actions>\n            \n            {% block sw_settings_reviews_actions_save %}\n                <sw-button-process\n                        class="sw-settings-aisearch__save-action"\n                        :is-loading="isLoading || saveLoading"\n                        :disabled="isLoading || saveLoading"\n                        variant="primary"\n                        :process-success="saveSuccess"\n                        {% if VUE3 %}\n                            @update:processSuccess="saveFinish"\n                        {% else %}\n                            @process-finish="saveFinish"\n                        {% endif %}\n                        @click="onSave"\n                >\n                    {{ $tc(\'sw-settings-translator.detail.buttonSave\') }}\n                </sw-button-process>\n            {% endblock %}\n        </template>\n    {% endblock %}\n\n    {% block sw_settings_aisearch_content %}\n        <template #content>\n            <sw-card-view>\n                <template v-if="isLoading || !currentSalesChannelId">\n                    <sw-skeleton variant="detail-bold" />\n                    <sw-skeleton />\n                </template>\n\n                <sw-sales-channel-switch-aisearch\n                    v-show="!isLoading && currentSalesChannelId"\n                    class="sw-settings-search-view-aisearch__sales-channel-switch"\n                    :label="$tc(\'sw-settings.system-config.labelSalesChannelSelect\')"\n                    :sales-channel-id="currentSalesChannelId"\n                    @change-sales-channel-id="onSalesChannelChanged"\n                />\n\n                <sw-settings-search-aisearch\n                    v-if="!isLoading && configSchema && currentSalesChannelId"\n                    domain="AISearch.naturalLanguageSearch"\n                    :configFields="getConfigSchema(\'AISearch.naturalLanguageSearch\')"\n                    :currentSalesChannelId="currentSalesChannelId"\n                    :currentLanguageId="currentLanguageId"\n                    @save-success="onSaveSuccess"\n                    @save-finish="saveFinish"\n                />\n            </sw-card-view>\n        </template>\n    {% endblock %}\n\n</sw-page>\n',inject:["systemConfigApiService"],mixins:[p.getByName("notification")],data:function(){return{currentSalesChannelId:null,configSchema:null,isLoading:!1,domains:["AISearch.naturalLanguageSearch"],currentLanguageId:g.api.languageId,saveSuccess:!1,saveLoading:!1}},computed:{showAISearchView:function(){return d.get(r.c)}},created:function(){this.createdComponent()},methods:{createdComponent:function(){var e=this;return l(c().mark((function t(){return c().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,e.loadConfigSchema();case 2:case"end":return t.stop()}}),t)})))()},loadConfigSchema:function(){var e=this;this.isLoading=!0,this.domains.forEach(function(){var t=l(c().mark((function t(n){var r,o;return c().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.prev=0,t.next=3,e.systemConfigApiService.getConfig(n);case 3:if(r=t.sent,null!=(o=r[0])&&o.hasOwnProperty("elements")&&null!=o&&o.hasOwnProperty("title")){t.next=8;break}return e.createNotificationError({message:e.$tc("sw-settings-aisearch.notification.schemaErrorMessage")}),t.abrupt("return");case 8:e.configSchema={},e.configSchema[n]=a(a({},o),{},{elements:e.reduceConfigFields(o)}),t.next=15;break;case 12:t.prev=12,t.t0=t.catch(0),e.createNotificationError({message:e.$tc("sw-settings-aisearch.notification.schemaErrorMessage")});case 15:return t.prev=15,e.isLoading=!1,t.finish(15);case 18:case"end":return t.stop()}}),t,null,[[0,12,15,18]])})));return function(e){return t.apply(this,arguments)}}())},onSalesChannelChanged:function(e){this.currentSalesChannelId=e},onLanguageChange:function(e){this.currentLanguageId=e},reduceConfigFields:function(e){var t;return null==e||null===(t=e.elements)||void 0===t?void 0:t.map((function(e){var t=e;return t.config.hasOwnProperty("options")?(t.config.options=e.config.options.reduce((function(e,t){return a(a({},e),{},s({},t.id,t))}),{}),e):t}))},getConfigSchema:function(e){return this.configSchema[e]},onSave:function(){this.saveSuccess=!1,this.saveLoading=!0,this.$root.$emit(r.a)},onSaveSuccess:function(){this.saveLoading=!1,this.saveSuccess=!0},saveFinish:function(){this.saveSuccess=!1,this.saveLoading=!1}}})},"ub+m":function(e,t,n){var r=n("9+zH");r.__esModule&&(r=r.default),"string"==typeof r&&(r=[[e.i,r,""]]),r.locals&&(e.exports=r.locals);(0,n("P8hj").default)("9942f252",r,!0,{})}}]);
//# sourceMappingURL=0.js.map
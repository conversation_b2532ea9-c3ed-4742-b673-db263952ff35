/*! For license information please see 1.js.LICENSE.txt */
(this["webpackJsonpPlugina-i-search"]=this["webpackJsonpPlugina-i-search"]||[]).push([[1],{P8hj:function(t,e,n){"use strict";function r(t,e){for(var n=[],r={},i=0;i<e.length;i++){var o=e[i],a=o[0],c={id:t+":"+i,css:o[1],media:o[2],sourceMap:o[3]};r[a]?r[a].parts.push(c):n.push(r[a]={id:a,parts:[c]})}return n}n.r(e),n.d(e,"default",(function(){return d}));var i="undefined"!=typeof document;if("undefined"!=typeof DEBUG&&DEBUG&&!i)throw new Error("vue-style-loader cannot be used in a non-browser environment. Use { target: 'node' } in your Webpack config to indicate a server-rendering environment.");var o={},a=i&&(document.head||document.getElementsByTagName("head")[0]),c=null,s=0,u=!1,l=function(){},f=null,h="data-vue-ssr-id",p="undefined"!=typeof navigator&&/msie [6-9]\b/.test(navigator.userAgent.toLowerCase());function d(t,e,n,i){u=n,f=i||{};var a=r(t,e);return g(a),function(e){for(var n=[],i=0;i<a.length;i++){var c=a[i];(s=o[c.id]).refs--,n.push(s)}e?g(a=r(t,e)):a=[];for(i=0;i<n.length;i++){var s;if(0===(s=n[i]).refs){for(var u=0;u<s.parts.length;u++)s.parts[u]();delete o[s.id]}}}}function g(t){for(var e=0;e<t.length;e++){var n=t[e],r=o[n.id];if(r){r.refs++;for(var i=0;i<r.parts.length;i++)r.parts[i](n.parts[i]);for(;i<n.parts.length;i++)r.parts.push(m(n.parts[i]));r.parts.length>n.parts.length&&(r.parts.length=n.parts.length)}else{var a=[];for(i=0;i<n.parts.length;i++)a.push(m(n.parts[i]));o[n.id]={id:n.id,refs:1,parts:a}}}}function v(){var t=document.createElement("style");return t.type="text/css",a.appendChild(t),t}function m(t){var e,n,r=document.querySelector("style["+h+'~="'+t.id+'"]');if(r){if(u)return l;r.parentNode.removeChild(r)}if(p){var i=s++;r=c||(c=v()),e=w.bind(null,r,i,!1),n=w.bind(null,r,i,!0)}else r=v(),e=S.bind(null,r),n=function(){r.parentNode.removeChild(r)};return e(t),function(r){if(r){if(r.css===t.css&&r.media===t.media&&r.sourceMap===t.sourceMap)return;e(t=r)}else n()}}var y,b=(y=[],function(t,e){return y[t]=e,y.filter(Boolean).join("\n")});function w(t,e,n,r){var i=n?"":r.css;if(t.styleSheet)t.styleSheet.cssText=b(e,i);else{var o=document.createTextNode(i),a=t.childNodes;a[e]&&t.removeChild(a[e]),a.length?t.insertBefore(o,a[e]):t.appendChild(o)}}function S(t,e){var n=e.css,r=e.media,i=e.sourceMap;if(r&&t.setAttribute("media",r),f.ssrId&&t.setAttribute(h,e.id),i&&(n+="\n/*# sourceURL="+i.sources[0]+" */",n+="\n/*# sourceMappingURL=data:application/json;base64,"+btoa(unescape(encodeURIComponent(JSON.stringify(i))))+" */"),t.styleSheet)t.styleSheet.cssText=n;else{for(;t.firstChild;)t.removeChild(t.firstChild);t.appendChild(document.createTextNode(n))}}},XgoX:function(t,e,n){"use strict";n.r(e);var r=n("uzim");n("hpKP");function i(t){return(i="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function o(){o=function(){return t};var t={},e=Object.prototype,n=e.hasOwnProperty,r=Object.defineProperty||function(t,e,n){t[e]=n.value},a="function"==typeof Symbol?Symbol:{},c=a.iterator||"@@iterator",s=a.asyncIterator||"@@asyncIterator",u=a.toStringTag||"@@toStringTag";function l(t,e,n){return Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{l({},"")}catch(t){l=function(t,e,n){return t[e]=n}}function f(t,e,n,i){var o=e&&e.prototype instanceof d?e:d,a=Object.create(o.prototype),c=new x(i||[]);return r(a,"_invoke",{value:C(t,n,c)}),a}function h(t,e,n){try{return{type:"normal",arg:t.call(e,n)}}catch(t){return{type:"throw",arg:t}}}t.wrap=f;var p={};function d(){}function g(){}function v(){}var m={};l(m,c,(function(){return this}));var y=Object.getPrototypeOf,b=y&&y(y(D([])));b&&b!==e&&n.call(b,c)&&(m=b);var w=v.prototype=d.prototype=Object.create(m);function S(t){["next","throw","return"].forEach((function(e){l(t,e,(function(t){return this._invoke(e,t)}))}))}function L(t,e){function o(r,a,c,s){var u=h(t[r],t,a);if("throw"!==u.type){var l=u.arg,f=l.value;return f&&"object"==i(f)&&n.call(f,"__await")?e.resolve(f.__await).then((function(t){o("next",t,c,s)}),(function(t){o("throw",t,c,s)})):e.resolve(f).then((function(t){l.value=t,c(l)}),(function(t){return o("throw",t,c,s)}))}s(u.arg)}var a;r(this,"_invoke",{value:function(t,n){function r(){return new e((function(e,r){o(t,n,e,r)}))}return a=a?a.then(r,r):r()}})}function C(t,e,n){var r="suspendedStart";return function(i,o){if("executing"===r)throw new Error("Generator is already running");if("completed"===r){if("throw"===i)throw o;return _()}for(n.method=i,n.arg=o;;){var a=n.delegate;if(a){var c=O(a,n);if(c){if(c===p)continue;return c}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if("suspendedStart"===r)throw r="completed",n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);r="executing";var s=h(t,e,n);if("normal"===s.type){if(r=n.done?"completed":"suspendedYield",s.arg===p)continue;return{value:s.arg,done:n.done}}"throw"===s.type&&(r="completed",n.method="throw",n.arg=s.arg)}}}function O(t,e){var n=e.method,r=t.iterator[n];if(void 0===r)return e.delegate=null,"throw"===n&&t.iterator.return&&(e.method="return",e.arg=void 0,O(t,e),"throw"===e.method)||"return"!==n&&(e.method="throw",e.arg=new TypeError("The iterator does not provide a '"+n+"' method")),p;var i=h(r,t.iterator,e.arg);if("throw"===i.type)return e.method="throw",e.arg=i.arg,e.delegate=null,p;var o=i.arg;return o?o.done?(e[t.resultName]=o.value,e.next=t.nextLoc,"return"!==e.method&&(e.method="next",e.arg=void 0),e.delegate=null,p):o:(e.method="throw",e.arg=new TypeError("iterator result is not an object"),e.delegate=null,p)}function E(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function j(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function x(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(E,this),this.reset(!0)}function D(t){if(t){var e=t[c];if(e)return e.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var r=-1,i=function e(){for(;++r<t.length;)if(n.call(t,r))return e.value=t[r],e.done=!1,e;return e.value=void 0,e.done=!0,e};return i.next=i}}return{next:_}}function _(){return{value:void 0,done:!0}}return g.prototype=v,r(w,"constructor",{value:v,configurable:!0}),r(v,"constructor",{value:g,configurable:!0}),g.displayName=l(v,u,"GeneratorFunction"),t.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===g||"GeneratorFunction"===(e.displayName||e.name))},t.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,v):(t.__proto__=v,l(t,u,"GeneratorFunction")),t.prototype=Object.create(w),t},t.awrap=function(t){return{__await:t}},S(L.prototype),l(L.prototype,s,(function(){return this})),t.AsyncIterator=L,t.async=function(e,n,r,i,o){void 0===o&&(o=Promise);var a=new L(f(e,n,r,i),o);return t.isGeneratorFunction(n)?a:a.next().then((function(t){return t.done?t.value:a.next()}))},S(w),l(w,u,"Generator"),l(w,c,(function(){return this})),l(w,"toString",(function(){return"[object Generator]"})),t.keys=function(t){var e=Object(t),n=[];for(var r in e)n.push(r);return n.reverse(),function t(){for(;n.length;){var r=n.pop();if(r in e)return t.value=r,t.done=!1,t}return t.done=!0,t}},t.values=D,x.prototype={constructor:x,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,this.tryEntries.forEach(j),!t)for(var e in this)"t"===e.charAt(0)&&n.call(this,e)&&!isNaN(+e.slice(1))&&(this[e]=void 0)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var e=this;function r(n,r){return a.type="throw",a.arg=t,e.next=n,r&&(e.method="next",e.arg=void 0),!!r}for(var i=this.tryEntries.length-1;i>=0;--i){var o=this.tryEntries[i],a=o.completion;if("root"===o.tryLoc)return r("end");if(o.tryLoc<=this.prev){var c=n.call(o,"catchLoc"),s=n.call(o,"finallyLoc");if(c&&s){if(this.prev<o.catchLoc)return r(o.catchLoc,!0);if(this.prev<o.finallyLoc)return r(o.finallyLoc)}else if(c){if(this.prev<o.catchLoc)return r(o.catchLoc,!0)}else{if(!s)throw new Error("try statement without catch or finally");if(this.prev<o.finallyLoc)return r(o.finallyLoc)}}}},abrupt:function(t,e){for(var r=this.tryEntries.length-1;r>=0;--r){var i=this.tryEntries[r];if(i.tryLoc<=this.prev&&n.call(i,"finallyLoc")&&this.prev<i.finallyLoc){var o=i;break}}o&&("break"===t||"continue"===t)&&o.tryLoc<=e&&e<=o.finallyLoc&&(o=null);var a=o?o.completion:{};return a.type=t,a.arg=e,o?(this.method="next",this.next=o.finallyLoc,p):this.complete(a)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),p},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var n=this.tryEntries[e];if(n.finallyLoc===t)return this.complete(n.completion,n.afterLoc),j(n),p}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var n=this.tryEntries[e];if(n.tryLoc===t){var r=n.completion;if("throw"===r.type){var i=r.arg;j(n)}return i}}throw new Error("illegal catch attempt")},delegateYield:function(t,e,n){return this.delegate={iterator:D(t),resultName:e,nextLoc:n},"next"===this.method&&(this.arg=void 0),p}},t}function a(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var n=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=n){var r,i,o,a,c=[],s=!0,u=!1;try{if(o=(n=n.call(t)).next,0===e){if(Object(n)!==n)return;s=!1}else for(;!(s=(r=o.call(n)).done)&&(c.push(r.value),c.length!==e);s=!0);}catch(t){u=!0,i=t}finally{try{if(!s&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(u)throw i}}return c}}(t,e)||function(t,e){if(!t)return;if("string"==typeof t)return c(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);"Object"===n&&t.constructor&&(n=t.constructor.name);if("Map"===n||"Set"===n)return Array.from(t);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return c(t,e)}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function c(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=new Array(e);n<e;n++)r[n]=t[n];return r}function s(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,r)}return n}function u(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?s(Object(n),!0).forEach((function(e){l(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):s(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}function l(t,e,n){return(e=function(t){var e=function(t,e){if("object"!==i(t)||null===t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var r=n.call(t,e||"default");if("object"!==i(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"===i(e)?e:String(e)}(e))in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function f(t,e,n,r,i,o,a){try{var c=t[o](a),s=c.value}catch(t){return void n(t)}c.done?e(s):Promise.resolve(s).then(r,i)}var h=Shopware.Component.getComponentHelper().mapSystemConfigErrors,p=Shopware,d=p.Component,g=p.State,v=p.Context,m=p.Mixin,y=Shopware.Utils.string;e.default=d.wrapComponentConfig({template:'<div class="sw-settings-search-aisearch">\n    <sw-card\n        position-identifier="sw-settings-search-aisearch-content-natlang"\n        class="sw-settings-search-aisearch__form-card"\n        :is-loading="isLoading"\n        :title="$tc(\'sw-settings-aisearch.component.naturalLanguage.card.title\')"\n        :subtitle="$tc(\'sw-settings-aisearch.component.naturalLanguage.card.subtitle\')"\n        ai-badge\n    >\n        <sw-alert variant="neutral">\n            {{ $tc(\'sw-settings-aisearch.component.naturalLanguage.alertText\') }}\n        </sw-alert>\n\n        <template v-if="isValidData">\n            <template v-for="element in configFields.elements">\n                <template v-if="hasHeadingData(element)">\n                    <div class="sw-settings-search-aisearch__form-heading">\n                        <div class="sw-card__title">\n                            {{ element.config.options.sectionTitle.name[currentLocale] }}\n                        </div>\n                        <div class="sw-card__subtitle">\n                            {{ element.config.options.sectionSubtitle.name[currentLocale] }}\n                        </div>\n                    </div>\n                </template>\n\n                <sw-form-field-renderer\n                    :value="configData[element.name]"\n                    :type="element.type"\n                    :config="element.config"\n                    :error="getFieldError(element.name)"\n                    :name="element.name"\n                    :disabled="!acl.can(\'system_config.editor\') || isLoading"\n                    @update="onChangeValue(element.name, $event)"\n                />\n            </template>\n\n            <slot name="card-element-last"></slot>\n        </template>\n    </sw-card>\n</div>\n',inject:["systemConfigApiService","acl"],mixins:[m.getByName("notification")],props:{domain:{type:String,required:!0},configFields:{type:Object,required:!0},currentSalesChannelId:{type:[String,null],default:""},currentLanguageId:{type:String,required:!0}},data:function(){return{isLoading:!1,configData:{},configValue:{},isValidData:!1}},computed:{currentLocale:function(){return g.get("session").currentLocale||v.app.fallbackLocale}},created:function(){this.getCurrentConfigData()},mounted:function(){this.$root.$on(r.a,this.saveData)},beforeDestroy:function(){this.beforeDestroyComponent()},watch:{currentSalesChannelId:function(){this.getCurrentConfigData()},currentLanguageId:function(){this.getCurrentConfigData()}},methods:{getCurrentConfigData:function(){var t,e=this;return(t=o().mark((function t(){var n,r;return o().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return e.isLoading=!0,e.configValue={},e.configData=null===(n=e.configFields)||void 0===n||null===(r=n.elements)||void 0===r?void 0:r.reduce((function(t,e){return u(u({},t),{},l({},e.name,null))}),{}),t.prev=3,t.next=6,e.systemConfigApiService.getValues(e.domain,e.currentSalesChannelId);case 6:if(e.configValue=t.sent,e.isValidData=e.validateConfigData(),e.isValidData){t.next=11;break}return e.createNotificationError({message:e.$tc("sw-settings-aisearch.notification.invalidData")}),t.abrupt("return");case 11:Object.entries(e.configValue).forEach((function(t){var n=a(t,2),r=n[0],i=n[1];r.startsWith("AISearch.naturalLanguageSearch.searchSuggestions")?e.configData[r]=i[e.currentLanguageId]:e.configData[r]=i}));case 12:return t.prev=12,e.isLoading=!1,t.finish(12);case 15:case"end":return t.stop()}}),t,null,[[3,,12,15]])})),function(){var e=this,n=arguments;return new Promise((function(r,i){var o=t.apply(e,n);function a(t){f(o,r,i,a,c,"next",t)}function c(t){f(o,r,i,a,c,"throw",t)}a(void 0)}))})()},saveData:function(){var t=this;if(Shopware.License.get(r.b))return this.sendToggleKey(r.b);if(Shopware.License.get(r.d))return this.sendToggleKey(r.d);this.isLoading=!0;var e=u({},this.configData);return Object.entries(e).forEach((function(n){var r=a(n,2),i=r[0],o=r[1],c="string"==typeof o?o.trim():o;if(i.startsWith("AISearch.naturalLanguageSearch.searchSuggestions"))return e[i]=u(u({},t.configValue[i]),{},l({},t.currentLanguageId,c)),void(t.configData[i]=c);e[i]=c,t.configData[i]=c})),this.systemConfigApiService.batchSave(l({},this.currentSalesChannelId,e)).then((function(){return t.$emit("save-success"),t.getCurrentConfigData()})).catch((function(){t.createNotificationError({message:t.$tc("sw-settings-aisearch.notification.configSaveErrorMessage")})})).finally((function(){t.isLoading=!1,t.$emit("save-finish")}))},hasHeadingData:function(t){return t.config.hasOwnProperty("options")&&t.config.options.hasOwnProperty("sectionTitle")&&t.config.options.hasOwnProperty("sectionSubtitle")},validateConfigData:function(){var t;if(!Array.isArray(null===(t=this.configFields)||void 0===t?void 0:t.elements))return!1;var e=this.configFields.elements.map((function(t){return t.name}));return 0===Object.keys(this.configValue).filter((function(t){return!e.includes(t)})).length},getFieldError:function(t){var e=this.currentSalesChannelId?y.camelCase(this.currentSalesChannelId):null,n=h("SYSTEM_CONFIG",e,"selfLink"),r=h("SYSTEM_CONFIG",e,t);return r&&n?(r.selfLink=n,r):null},onChangeValue:function(t,e){this.configData[t]=e},sendToggleKey:function(t){return Shopware.Application.getContainer("init").httpClient.get("_info/config",{headers:{Accept:"application/vnd.api+json",Authorization:"Bearer ".concat(Shopware.Service("loginService").getToken()),"Content-Type":"application/json","sw-license-toggle":t}})},beforeDestroyComponent:function(){this.$root.$off(r.a,this.saveData);var t=this.currentSalesChannelId?y.camelCase(this.currentSalesChannelId):null;g.commit("error/removeApiError",{expression:"SYSTEM_CONFIG.".concat(t)})}}})},hpKP:function(t,e,n){var r=n("mTE0");r.__esModule&&(r=r.default),"string"==typeof r&&(r=[[t.i,r,""]]),r.locals&&(t.exports=r.locals);(0,n("P8hj").default)("1e5b1f69",r,!0,{})},mTE0:function(t,e,n){}}]);
//# sourceMappingURL=1.js.map
{"version": 3, "sources": ["webpack:////tmp/extension2400992207/SwagCommercial/src/AISearch/Resources/app/administration/src/module/sw-settings-aisearch/component/sw-sales-channel-switch-aisearch/sw-sales-channel-switch-aisearch.html.twig", "webpack:////tmp/extension2400992207/SwagCommercial/src/AISearch/Resources/app/administration/src/module/sw-settings-aisearch/component/sw-sales-channel-switch-aisearch/index.ts"], "names": ["Component", "Shopware", "Criteria", "Data", "wrapComponentConfig", "template", "inject", "props", "salesChannelId", "type", "String", "default", "data", "salesChannels", "computed", "salesChannelRepository", "this", "repositoryFactory", "create", "created", "fetchSalesChannels", "methods", "onChange", "$emit", "_this", "search", "then", "response", "_response$", "defaultSalesChannelId", "id", "undefined"], "mappings": "sIAAe,ICSPA,EAAcC,SAAdD,UACAE,EAAaD,SAASE,KAAtBD,SAEOF,YAAUI,oBAAoB,CACzCC,SDbW,ofCeXC,OAAQ,CACJ,qBAGJC,MAAO,CACHC,eAAgB,CACZC,KAAM,CAACC,OAAQ,MACfC,QAAS,OAIjBC,KAAI,WAGA,MAAO,CACHC,cAAe,KAIvBC,SAAU,CACNC,uBAAsB,WAClB,OAAOC,KAAKC,kBAAkBC,OAAO,mBAI7CC,QAAO,WACHH,KAAKI,sBAGTC,QAAS,CACLC,SAAQ,SAACd,GACAA,GAILQ,KAAKO,MAAM,0BAA2Bf,IAG1CY,mBAAkB,WAAmB,IAADI,EAAA,KAChC,OAAOR,KAAKD,uBAAuBU,OAAO,IAAIvB,EAAS,EAAG,KACrDwB,MAAK,SAACC,GAAiD,IAADC,EAC7CC,EAAmC,QAAdD,EAAGD,EAAS,UAAE,IAAAC,OAAA,EAAXA,EAAaE,GACtCH,QAAsCI,IAA1BF,IAIjBL,EAAKX,cAAgBc,EACrBH,EAAKD,MAAM,0BAA2BM", "file": "static/js/2.js", "sourcesContent": ["export default \"<sw-single-select\\n    class=\\\"sw-settings-search-live-search__sales-channel-select\\\"\\n    value-property=\\\"id\\\"\\n    label-property=\\\"translated.name\\\"\\n    :placeholder=\\\"$tc('sw-settings-search.liveSearchTab.textPlaceholderSalesChannel')\\\"\\n    :label=\\\"$tc('sw-settings-search.liveSearchTab.labelSalesChannelSelect')\\\"\\n    :value=\\\"salesChannelId\\\"\\n    :options=\\\"salesChannels\\\"\\n    {% if VUE3 %}\\n        @update:value=\\\"onChange\\\"\\n    {% else %}\\n        @change=\\\"onChange\\\"\\n    {% endif %}\\n/>\\n\\n\";", "/**\n * @package system-settings\n * @experimental stableVersion:v6.7.0 feature:NaturalLanguageSearch\n */\n\nimport type RepositoryType from '@administration/core/data/repository.data';\nimport type EntityCollection from '@shopware-ag/admin-extension-sdk/es/data/_internals/EntityCollection';\nimport template from './sw-sales-channel-switch-aisearch.html.twig';\n\nconst { Component } = Shopware;\nconst { Criteria } = Shopware.Data;\n\nexport default Component.wrapComponentConfig({\n    template,\n\n    inject: [\n        'repositoryFactory',\n    ],\n\n    props: {\n        salesChannelId: {\n            type: [String, null],\n            default: null,\n        },\n    },\n\n    data(): {\n        salesChannels: EntityCollection<'sales_channel'>,\n        } {\n        return {\n            salesChannels: [],\n        };\n    },\n\n    computed: {\n        salesChannelRepository(): RepositoryType<'sales_channel'> {\n            return this.repositoryFactory.create('sales_channel');\n        },\n    },\n\n    created() {\n        this.fetchSalesChannels();\n    },\n\n    methods: {\n        onChange(salesChannelId: string): void {\n            if (!salesChannelId) {\n                return;\n            }\n\n            this.$emit('change-sales-channel-id', salesChannelId);\n        },\n\n        fetchSalesChannels(): Promise<void> {\n            return this.salesChannelRepository.search(new Criteria(1, 25))\n                .then((response: EntityCollection<'sales_channel'>) => {\n                    const defaultSalesChannelId = response[0]?.id;\n                    if (!response || defaultSalesChannelId === undefined) {\n                        return;\n                    }\n\n                    this.salesChannels = response;\n                    this.$emit('change-sales-channel-id', defaultSalesChannelId);\n                });\n        },\n    },\n});\n"], "sourceRoot": ""}
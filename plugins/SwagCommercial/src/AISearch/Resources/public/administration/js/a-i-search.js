!function(e){function t(t){for(var r,n,i=t[0],o=t[1],s=0,u=[];s<i.length;s++)n=i[s],Object.prototype.hasOwnProperty.call(a,n)&&a[n]&&u.push(a[n][0]),a[n]=0;for(r in o)Object.prototype.hasOwnProperty.call(o,r)&&(e[r]=o[r]);for(c&&c(t);u.length;)u.shift()()}var r={},n={"a-i-search":0},a={"a-i-search":0};function i(t){if(r[t])return r[t].exports;var n=r[t]={i:t,l:!1,exports:{}};return e[t].call(n.exports,n,n.exports,i),n.l=!0,n.exports}i.e=function(e){var t=[];n[e]?t.push(n[e]):0!==n[e]&&{0:1,1:1}[e]&&t.push(n[e]=new Promise((function(t,r){for(var a="static/css/"+({}[e]||e)+".css",o=i.p+a,s=document.getElementsByTagName("link"),u=0;u<s.length;u++){var c=(h=s[u]).getAttribute("data-href")||h.getAttribute("href");if("stylesheet"===h.rel&&(c===a||c===o))return t()}var l=document.getElementsByTagName("style");for(u=0;u<l.length;u++){var h;if((c=(h=l[u]).getAttribute("data-href"))===a||c===o)return t()}var f=document.createElement("link");f.rel="stylesheet",f.type="text/css";f.onerror=f.onload=function(a){if(f.onerror=f.onload=null,"load"===a.type)t();else{var i=a&&("load"===a.type?"missing":a.type),s=a&&a.target&&a.target.href||o,u=new Error("Loading CSS chunk "+e+" failed.\n("+s+")");u.code="CSS_CHUNK_LOAD_FAILED",u.type=i,u.request=s,delete n[e],f.parentNode.removeChild(f),r(u)}},f.href=o,document.head.appendChild(f)})).then((function(){n[e]=0})));var r=a[e];if(0!==r)if(r)t.push(r[2]);else{var o=new Promise((function(t,n){r=a[e]=[t,n]}));t.push(r[2]=o);var s,u=document.createElement("script");u.charset="utf-8",u.timeout=120,i.nc&&u.setAttribute("nonce",i.nc),u.src=function(e){return i.p+"static/js/"+({}[e]||e)+".js"}(e);var c=new Error;s=function(t){u.onerror=u.onload=null,clearTimeout(l);var r=a[e];if(0!==r){if(r){var n=t&&("load"===t.type?"missing":t.type),i=t&&t.target&&t.target.src;c.message="Loading chunk "+e+" failed.\n("+n+": "+i+")",c.name="ChunkLoadError",c.type=n,c.request=i,r[1](c)}a[e]=void 0}};var l=setTimeout((function(){s({type:"timeout",target:u})}),12e4);u.onerror=u.onload=s,document.head.appendChild(u)}return Promise.all(t)},i.m=e,i.c=r,i.d=function(e,t,r){i.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:r})},i.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},i.t=function(e,t){if(1&t&&(e=i(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var r=Object.create(null);if(i.r(r),Object.defineProperty(r,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var n in e)i.d(r,n,function(t){return e[t]}.bind(null,n));return r},i.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return i.d(t,"a",t),t},i.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},i.p=(window.__sw__.assetPath + '/bundles/aisearch/'),i.oe=function(e){throw console.error(e),e};var o=this["webpackJsonpPlugina-i-search"]=this["webpackJsonpPlugina-i-search"]||[],s=o.push.bind(o);o.push=t,o=o.slice();for(var u=0;u<o.length;u++)t(o[u]);var c=s;i(i.s="yFSZ")}({uzim:function(e,t,r){"use strict";r.d(t,"c",(function(){return n})),r.d(t,"b",(function(){return a})),r.d(t,"d",(function(){return i})),r.d(t,"a",(function(){return o}));var n="NATURAL_LANGUAGE_SEARCH-9467395",a="NATURAL_LANGUAGE_SEARCH-1783023",i="NATURAL_LANGUAGE_SEARCH-9622170",o="sw-settings-aisearch-save"},yFSZ:function(e,t,r){"use strict";r.r(t);var n=r("uzim"),a=Shopware.Component;Shopware.License.get(n.c)&&(a.register("sw-settings-search-aisearch",(function(){return r.e(1).then(r.bind(null,"XgoX"))})),a.register("sw-settings-search-view-aisearch",(function(){return r.e(0).then(r.bind(null,"mAb1"))})),a.register("sw-sales-channel-switch-aisearch",(function(){return r.e(2).then(r.bind(null,"9RuO"))}))),Shopware.License.get(n.c)&&Shopware.Module.register("sw-settings-aisearch",{type:"plugin",name:"sw-settings-aisearch",title:"sw-settings-aisearch.general.title",icon:"regular-cog",color:"#9AA8B5",routes:{index:{component:"sw-settings-search-view-aisearch",path:"index",meta:{parentPath:"sw.settings.index"}}},settingsItem:{group:"shop",to:"sw.settings.aisearch.index",icon:"regular-search",name:"aisearch.general.mainMenuItemGeneral"}})}});
//# sourceMappingURL=a-i-search.js.map
/**
 * @package system-settings
 * @experimental stableVersion:v6.7.0 feature:NaturalLanguageSearch
 */

import template from './sw-settings-search-view-aisearch.html.twig';
import './sw-settings-search-view-aisearch.scss';

import { SETTINGS_AISEARCH_SAVE_EVENT, TOGGLE_KEY_9467395 } from '../../../../config';
import {
    SchemaResponseItem,
    SchemaResponseItemElement,
    ConfigSchema,
    ConfigElementOption,
    ConfigSchemaDomain,
    ConfigSchemaElement,
} from '../../types.d';

const { Component, License, Mixin, Context } = Shopware;

export default Component.wrapComponentConfig({
    template,

    inject: ['systemConfigApiService'],

    mixins: [
        Mixin.getByName('notification'),
    ],

    data(): {
        currentSalesChannelId: string|null,
        configSchema: ConfigSchemaDomain|null,
        isLoading: boolean,
        domains: string[],
        currentLanguageId: string,
        saveSuccess: boolean,
        saveLoading: boolean,
        } {
        return {
            currentSalesChannelId: null,
            configSchema: null,
            isLoading: false,
            domains: ['AISearch.naturalLanguageSearch'],
            currentLanguageId: Context.api.languageId,
            saveSuccess: false,
            saveLoading: false,
        };
    },

    computed: {
        showAISearchView(): boolean {
            return License.get(TOGGLE_KEY_9467395);
        },
    },

    created() {
        this.createdComponent();
    },

    methods: {
        async createdComponent(): Promise<void> {
            await this.loadConfigSchema();
        },

        loadConfigSchema(): void {
            this.isLoading = true;

            this.domains.forEach(async (domain: string) => {
                try {
                    const responseData = await this.systemConfigApiService.getConfig(domain);
                    const config = responseData[0] as SchemaResponseItem;

                    if (!config?.hasOwnProperty('elements') || !config?.hasOwnProperty('title')
                    ) {
                        this.createNotificationError({
                            message: this.$tc('sw-settings-aisearch.notification.schemaErrorMessage'),
                        });

                        return;
                    }

                    this.configSchema = {};

                    this.configSchema[domain] = {
                        ...config,
                        elements: this.reduceConfigFields(config),
                    };
                } catch {
                    this.createNotificationError({
                        message: this.$tc('sw-settings-aisearch.notification.schemaErrorMessage'),
                    });
                } finally {
                    this.isLoading = false;
                }
            });
        },

        onSalesChannelChanged(salesChannelId: string|null): void {
            this.currentSalesChannelId = salesChannelId;
        },

        onLanguageChange(languageId: string): void {
            this.currentLanguageId = languageId;
        },

        reduceConfigFields(configSchema: SchemaResponseItem): ConfigSchemaElement[] {
            return configSchema?.elements?.map((element: SchemaResponseItemElement) => {
                const reduceOptions = (
                    obj: {[key:string]: ConfigElementOption},
                    configElement: ConfigElementOption,
                ) => {
                    return { ...obj, [configElement.id]: configElement };
                };

                // Add variable to account for type switch between parameter and return
                const newElement: ConfigSchemaElement = element;
                if (!newElement.config.hasOwnProperty('options')) {
                    return newElement;
                }

                newElement.config.options = element.config.options.reduce(reduceOptions, {});

                return element;
            });
        },

        getConfigSchema(domain: string): ConfigSchema {
            return this.configSchema[domain];
        },

        onSave(): void {
            this.saveSuccess = false;
            this.saveLoading = true;

            this.$root.$emit(SETTINGS_AISEARCH_SAVE_EVENT);
        },

        onSaveSuccess(): void {
            this.saveLoading = false;
            this.saveSuccess = true;
        },

        saveFinish(): void {
            this.saveSuccess = false;
            this.saveLoading = false;
        },
    },
});

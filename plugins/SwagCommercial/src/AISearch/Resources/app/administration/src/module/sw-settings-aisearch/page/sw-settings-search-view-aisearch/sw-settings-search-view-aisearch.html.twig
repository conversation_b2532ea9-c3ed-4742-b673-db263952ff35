<sw-page
    class="sw-settings-search-view-aisearch"
    v-if="showAISearchView"
>
    <!-- eslint-disable-next-line sw-deprecation-rules/no-twigjs-blocks -->
    {% block sw_settings_aisearch_header %}
        <template #smart-bar-header>
            <h2>{{ $tc('sw-settings.index.title') }} <sw-icon
                name="regular-chevron-right-xs"
                small
            /> {{ $tc('sw-settings-aisearch.general.title') }} </h2>
        </template>
    {% endblock %}

    <!-- eslint-disable-next-line sw-deprecation-rules/no-twigjs-blocks -->
    {% block sw_settings_aisearch_language_switch %}
        <template #language-switch>
            <sw-language-switch @on-change="onLanguageChange" />
        </template>
    {% endblock %}

    <!-- eslint-disable-next-line sw-deprecation-rules/no-twigjs-blocks -->
    {% block sw_settings_aisearch_smart_bar_actions %}
        <template #smart-bar-actions>
            <!-- eslint-disable-next-line sw-deprecation-rules/no-twigjs-blocks -->
            {% block sw_settings_reviews_actions_save %}
                <sw-button-process
                        class="sw-settings-aisearch__save-action"
                        :is-loading="isLoading || saveLoading"
                        :disabled="isLoading || saveLoading"
                        variant="primary"
                        :process-success="saveSuccess"
                        {% if VUE3 %}
                            @update:processSuccess="saveFinish"
                        {% else %}
                            @process-finish="saveFinish"
                        {% endif %}
                        @click="onSave"
                >
                    {{ $tc('sw-settings-translator.detail.buttonSave') }}
                </sw-button-process>
            {% endblock %}
        </template>
    {% endblock %}

    {% block sw_settings_aisearch_content %}
        <template #content>
            <sw-card-view>
                <template v-if="isLoading || !currentSalesChannelId">
                    <sw-skeleton variant="detail-bold" />
                    <sw-skeleton />
                </template>

                <sw-sales-channel-switch-aisearch
                    v-show="!isLoading && currentSalesChannelId"
                    class="sw-settings-search-view-aisearch__sales-channel-switch"
                    :label="$tc('sw-settings.system-config.labelSalesChannelSelect')"
                    :sales-channel-id="currentSalesChannelId"
                    @change-sales-channel-id="onSalesChannelChanged"
                />

                <sw-settings-search-aisearch
                    v-if="!isLoading && configSchema && currentSalesChannelId"
                    domain="AISearch.naturalLanguageSearch"
                    :configFields="getConfigSchema('AISearch.naturalLanguageSearch')"
                    :currentSalesChannelId="currentSalesChannelId"
                    :currentLanguageId="currentLanguageId"
                    @save-success="onSaveSuccess"
                    @save-finish="saveFinish"
                />
            </sw-card-view>
        </template>
    {% endblock %}

</sw-page>

/**
 * @package system-settings
 * @experimental stableVersion:v6.7.0 feature:NaturalLanguageSearch
 */
import { TOGGLE_KEY_9467395 } from './config';

import './module/sw-settings-aisearch';

if (Shopware.License.get(TOGGLE_KEY_9467395)) {
    Shopware.Module.register('sw-settings-aisearch', {
        type: 'plugin',
        name: 'sw-settings-aisearch',
        title: 'sw-settings-aisearch.general.title',
        icon: 'regular-cog',
        color: '#9AA8B5',

        routes: {
            index: {
                component: 'sw-settings-search-view-aisearch',
                path: 'index',
                meta: {
                    parentPath: 'sw.settings.index',
                },
            },
        },

        settingsItem: {
            group: 'shop',
            to: 'sw.settings.aisearch.index',
            icon: 'regular-search',
            name: 'aisearch.general.mainMenuItemGeneral',
        },
    });
}

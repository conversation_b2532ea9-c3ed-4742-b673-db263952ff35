import { TOGGLE_KEY_9467395 } from '../../config';

const { Component } = Shopware;

/**
 * @package system-settings
 * @experimental stableVersion:v6.7.0 feature:NaturalLanguageSearch
 * @private
 */

if (Shopware.License.get(TOGGLE_KEY_9467395)) {
    Component.register('sw-settings-search-aisearch', () => import('./view/sw-settings-search-aisearch'));
    Component.register('sw-settings-search-view-aisearch', () => import('./page/sw-settings-search-view-aisearch'));

    // TODO: Will be removed in NEXT-31422
    Component.register('sw-sales-channel-switch-aisearch', () => import('./component/sw-sales-channel-switch-aisearch'));
}

/**
 * @package inventory
 */
@import "~scss/variables";
@import "~scss/mixins";

$sw-card-section-margin-bottom: 40px;

.sw-settings-search-aisearch {
    &__form-heading {
        margin-bottom: 20px;
    }

    .sw-card__header {
        .sw-card-title {
            font-family: $font-family-default;
            font-weight: $font-weight-semi-bold;
            font-size: $font-size-m;
            line-height: 26px;
            color: $color-darkgray-800
        }

        .sw-card__subtitle {
            font-family: 'Inter', 'Inter var', sans-serif;
        }
    }

    &__form-card {
        .sw-card__title {
            font-family: $font-family-variables;
            font-size: 16px;
            line-height: 25px;
            margin-bottom: 4px;
        }

        .sw-field--default:not(.sw-field--switch) {
            margin-bottom: 16px;

            + .sw-settings-search-aisearch__form-heading {
                margin-top: 40px;
            }
        }
    }

    &__switch {
        margin: 25px 0;
    }

}

/**
 * @package system-settings
 * @experimental stableVersion:v6.7.0 feature:NaturalLanguageSearch
 */

import type RepositoryType from '@administration/core/data/repository.data';
import type EntityCollection from '@shopware-ag/admin-extension-sdk/es/data/_internals/EntityCollection';
import template from './sw-sales-channel-switch-aisearch.html.twig';

const { Component } = Shopware;
const { Criteria } = Shopware.Data;

export default Component.wrapComponentConfig({
    template,

    inject: [
        'repositoryFactory',
    ],

    props: {
        salesChannelId: {
            type: [String, null],
            default: null,
        },
    },

    data(): {
        salesChannels: EntityCollection<'sales_channel'>,
        } {
        return {
            salesChannels: [],
        };
    },

    computed: {
        salesChannelRepository(): RepositoryType<'sales_channel'> {
            return this.repositoryFactory.create('sales_channel');
        },
    },

    created() {
        this.fetchSalesChannels();
    },

    methods: {
        onChange(salesChannelId: string): void {
            if (!salesChannelId) {
                return;
            }

            this.$emit('change-sales-channel-id', salesChannelId);
        },

        fetchSalesChannels(): Promise<void> {
            return this.salesChannelRepository.search(new Criteria(1, 25))
                .then((response: EntityCollection<'sales_channel'>) => {
                    const defaultSalesChannelId = response[0]?.id;
                    if (!response || defaultSalesChannelId === undefined) {
                        return;
                    }

                    this.salesChannels = response;
                    this.$emit('change-sales-channel-id', defaultSalesChannelId);
                });
        },
    },
});

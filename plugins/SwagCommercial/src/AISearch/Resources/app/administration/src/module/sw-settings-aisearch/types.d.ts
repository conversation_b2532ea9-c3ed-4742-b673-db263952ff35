/**
 * @package system-settings
 */

 type FieldError = {
    _code: string,
    _detail: string,
    _id: string,
    _status: string,
    _parameters: any,
    selfLink: string,
}

interface ConfigLocaleData {
    [key:string]: string;
}

interface ConfigElementOption {
    id: string;
    name: ConfigLocaleData;
}

interface ConfigSchemaElementConfig {
    label?: ConfigLocaleData;
    helpText?: ConfigLocaleData;
    options?: {
        [key:string]: ConfigElementOption;
    };
}
interface ConfigSchemaElement {
    name: string;
    type: string;
    config: ConfigSchemaElementConfig;
}

interface ConfigSchema {
    title: ConfigLocaleData;
    name?: string|null;
    elements: ConfigSchemaElement[];
}

interface ConfigSchemaDomain {
    [key:string]: ConfigSchema;
}

interface SchemaResponseElementConfig extends ConfigSchemaElementConfig {
    options?: ConfigElementOption[];
}

interface SchemaResponseItemElement extends ConfigSchemaElement {
    config: SchemaResponseElementConfig;
}

interface SchemaResponseItem extends ConfigSchema {
    elements: SchemaResponseItemElement[];
}

export {
    SchemaResponseItem,
    SchemaResponseItemElement,
    ConfigSchema,
    ConfigElementOption,
    ConfigSchemaDomain,
    ConfigSchemaElement,
    FieldError,
};

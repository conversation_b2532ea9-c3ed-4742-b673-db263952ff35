/**
 * @package system-settings
 * @experimental stableVersion:v6.7.0 feature:NaturalLanguageSearch
 */

import type { PropType } from 'vue';
import template from './sw-settings-search-aisearch.html.twig';

import {
    ConfigSchema,
    ConfigSchemaElement,
    FieldError,
} from '../../types.d';

import { TOGGLE_KEY_1783023, TOGGLE_KEY_9622170, SETTINGS_AISEARCH_SAVE_EVENT } from '../../../../config';

import './sw-settings-search-aisearch.scss';

const { mapSystemConfigErrors } = Shopware.Component.getComponentHelper();
const { Component, State, Context, Mixin } = Shopware;
const { string } = Shopware.Utils;

export default Component.wrapComponentConfig({
    template,

    inject: ['systemConfigApiService', 'acl'],

    mixins: [
        Mixin.getByName('notification'),
    ],

    props: {
        domain: {
            type: String,
            required: true,
        },
        configFields: {
            type: Object as PropType<ConfigSchema>,
            required: true,
        },
        currentSalesChannelId: {
            type: [String, null],
            default: '',
        },
        currentLanguageId: {
            type: String,
            required: true,
        },
    },

    data(): {
        isLoading: boolean,
        configData: object,
        configValue: object,
        isValidData: boolean,
        } {
        return {
            isLoading: false,
            configData: {},
            configValue: {},
            isValidData: false,
        };
    },

    computed: {
        currentLocale(): string {
            return State.get('session').currentLocale || Context.app.fallbackLocale;
        },
    },

    created() {
        this.getCurrentConfigData();
    },

    mounted() {
        this.$root.$on(SETTINGS_AISEARCH_SAVE_EVENT, this.saveData);
    },

    beforeDestroy() {
        this.beforeDestroyComponent();
    },

    watch: {
        currentSalesChannelId(): void {
            this.getCurrentConfigData();
        },

        currentLanguageId(): void {
            this.getCurrentConfigData();
        },
    },

    methods: {
        async getCurrentConfigData(): Promise<void> {
            this.isLoading = true;

            // Reset data
            this.configValue = {};
            // Ensure that all config fields in provided schema are mapped to configData
            this.configData = this.configFields?.elements?.reduce(
                (acc, element) => { return { ...acc, [element.name]: null }; },
                {},
            );

            try {
                // Stored original data
                this.configValue = await this.systemConfigApiService.getValues(this.domain, this.currentSalesChannelId);
                this.isValidData = this.validateConfigData();

                if (!this.isValidData) {
                    this.createNotificationError({
                        message: this.$tc('sw-settings-aisearch.notification.invalidData'),
                    });

                    return;
                }

                // Data for from currentLanguageId
                Object.entries(this.configValue).forEach(([key, value]) => {
                    if (key.startsWith('AISearch.naturalLanguageSearch.searchSuggestions')) {
                        this.configData[key] = value[this.currentLanguageId];
                        return;
                    }

                    this.configData[key] = value;
                });
            } finally {
                this.isLoading = false;
            }
        },

        saveData(): Promise<void> {
            if (Shopware.License.get(TOGGLE_KEY_1783023)) {
                return this.sendToggleKey(TOGGLE_KEY_1783023);
            }

            if (Shopware.License.get(TOGGLE_KEY_9622170)) {
                return this.sendToggleKey(TOGGLE_KEY_9622170);
            }

            this.isLoading = true;

            const configData = { ...this.configData };

            Object.entries(configData).forEach(([key, value]) => {
                const trimmedValue = typeof value === 'string' ? value.trim() : value;

                if (key.startsWith('AISearch.naturalLanguageSearch.searchSuggestions')) {
                    configData[key] = {
                        ...this.configValue[key],
                        [this.currentLanguageId]: trimmedValue,
                    };

                    this.configData[key] = trimmedValue;
                    return;
                }

                configData[key] = trimmedValue;
                this.configData[key] = trimmedValue;
            });


            return this.systemConfigApiService
                .batchSave({ [this.currentSalesChannelId]: configData })
                .then(() => {
                    this.$emit('save-success');
                    return this.getCurrentConfigData();
                })
                .catch(() => {
                    this.createNotificationError({
                        message: this.$tc('sw-settings-aisearch.notification.configSaveErrorMessage'),
                    });
                })
                .finally(() => {
                    this.isLoading = false;
                    this.$emit('save-finish');
                });
        },

        hasHeadingData(element: ConfigSchemaElement): boolean {
            return element.config.hasOwnProperty('options')
                && element.config.options.hasOwnProperty('sectionTitle')
                && element.config.options.hasOwnProperty('sectionSubtitle');
        },

        validateConfigData(): boolean {
            if (!Array.isArray(this.configFields?.elements)) {
                return false;
            }

            const configFieldsMap = this.configFields.elements.map((element) => element.name);
            // Create a 'set difference' between names in configFields and received data keys
            const missingKeys = Object.keys(this.configValue).filter((key) => !configFieldsMap.includes(key));
            return missingKeys.length === 0;
        },

        getFieldError(fieldName: string): FieldError {
            const salesChannelId = this.currentSalesChannelId
                ? string.camelCase(this.currentSalesChannelId)
                : null;

            const selfLink = mapSystemConfigErrors('SYSTEM_CONFIG', salesChannelId, 'selfLink');
            const fieldNameData = mapSystemConfigErrors('SYSTEM_CONFIG', salesChannelId, fieldName);

            if (fieldNameData && selfLink) {
                fieldNameData.selfLink = selfLink;
                return fieldNameData;
            }

            return null;
        },

        onChangeValue(elementName: string, value: string|boolean): void {
            this.configData[elementName] = value;
        },

        sendToggleKey(toggleKey: string): Promise<void> {
            const initContainer = Shopware.Application.getContainer('init');

            return initContainer.httpClient.get(
                '_info/config',
                {
                    headers: {
                        Accept: 'application/vnd.api+json',
                        Authorization: `Bearer ${Shopware.Service('loginService').getToken()}`,
                        'Content-Type': 'application/json',
                        'sw-license-toggle': toggleKey,
                    },
                },
            );
        },

        beforeDestroyComponent(): void {
            this.$root.$off(SETTINGS_AISEARCH_SAVE_EVENT, this.saveData);

            const salesChannelId = this.currentSalesChannelId
                ? string.camelCase(this.currentSalesChannelId)
                : null;

            State.commit('error/removeApiError', {
                expression: `SYSTEM_CONFIG.${salesChannelId}`,
            });
        },
    },
});


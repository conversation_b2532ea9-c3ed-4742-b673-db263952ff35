{"sw-settings-aisearch": {"general": {"title": "Search by context"}, "component": {"naturalLanguage": {"card": {"title": "Search by context", "subtitle": "Allows the customer to describe what they are looking for using natural language and find products more easily."}, "alertText": "This function has a daily limit of 300 requests."}}, "page": {"aisearchTab": "AI Copilot"}, "notification": {"schemaErrorMessage": "Failed to load system configuration", "configSaveErrorMessage": "Failed to save AISearch config", "missingDescriptionErrorMessage": "Failed to save AISearch config: Sales Channel Description cannot be blank", "invalidData": "Failed to fetch AISearch config data: incorrect data response"}}, "sw-settings-search": {"notification": {"saveError": "An error occurred while saving your search-settings configuration."}}}
<div class="sw-settings-search-aisearch">
    <sw-card
        position-identifier="sw-settings-search-aisearch-content-natlang"
        class="sw-settings-search-aisearch__form-card"
        :is-loading="isLoading"
        :title="$tc('sw-settings-aisearch.component.naturalLanguage.card.title')"
        :subtitle="$tc('sw-settings-aisearch.component.naturalLanguage.card.subtitle')"
        ai-badge
    >
        <sw-alert variant="neutral">
            {{ $tc('sw-settings-aisearch.component.naturalLanguage.alertText') }}
        </sw-alert>

        <template v-if="isValidData">
            <template v-for="element in configFields.elements">
                <template v-if="hasHeadingData(element)">
                    <div class="sw-settings-search-aisearch__form-heading">
                        <div class="sw-card__title">
                            {{ element.config.options.sectionTitle.name[currentLocale] }}
                        </div>
                        <div class="sw-card__subtitle">
                            {{ element.config.options.sectionSubtitle.name[currentLocale] }}
                        </div>
                    </div>
                </template>

                <sw-form-field-renderer
                    :value="configData[element.name]"
                    :type="element.type"
                    :config="element.config"
                    :error="getFieldError(element.name)"
                    :name="element.name"
                    :disabled="!acl.can('system_config.editor') || isLoading"
                    @update="onChangeValue(element.name, $event)"
                />
            </template>

            <slot name="card-element-last"></slot>
        </template>
    </sw-card>
</div>

{"name": "AISearch Administration", "version": "1.0.0", "description": "", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "lint": "eslint --ext .js,.ts,.vue,.html,.html.twig src", "lint:fix": "npm run lint -- --fix"}, "keywords": [], "license": "ISC", "dependencies": {"@typescript-eslint/parser": "5.57.0"}, "devDependencies": {"@babel/eslint-parser": "^7.21.3", "@shopware-ag/eslint-config-base": "^2.0.0", "@typescript-eslint/eslint-plugin": "^5.57.0", "eslint-import-resolver-webpack": "^0.13.2", "eslint-plugin-html": "^7.1.0", "eslint-plugin-jest": "^27.2.1", "eslint-plugin-vue": "9.14.1", "eslint-plugin-inclusive-language": "2.2.0", "eslint-plugin-file-progress": "1.3.0", "eslint-plugin-filename-rules": "^1.3.1", "eslint-plugin-vuejs-accessibility": "2.1.0"}}
/**
 * @package system-settings
 *
 * @experimental stableVersion:v6.7.0 feature:NaturalLanguageSearch
 */
import Plugin from 'src/plugin-system/plugin.class';
import DomAccess from 'src/helper/dom-access.helper';
import HttpClient from 'src/service/http-client.service';

declare var window: StorefrontWindow;
export default class NaturalLanguageSearch extends Plugin {

    private el: HTMLElement;
    private inputField: HTMLInputElement;
    private inputCharCount: HTMLInputElement;
    private submitButton: HTMLElement;
    private searchExamples: HTMLElement;
    private searchResults: HTMLElement;
    private titlePrimary: HTMLElement;
    private errorText: HTMLElement;
    private loadingSpinner: HTMLElement;
    private client: any;

    public static options = {
        fieldSelector: {
            searchInput: '.nls-search-input',
            searchCharCounter: '.nls-input-counter',
            searchButton: '.nls-search-bar-button',
            primaryTitle: '.nls-title-primary',
            secondaryTitle: '.nls-title-secondary',
            searchExamples: '.nls-example',
            searchExampleItem: '.nls-example-item',
            searchResults: '.nls-search-results',
            loadingSpinner: '.nls-loading-spinner',
            errorText: '.nls-search-error'
        }
    };

    init(): void {
        this.inputField = DomAccess.querySelector(this.el, this.options.fieldSelector.searchInput);
        this.inputCharCount = DomAccess.querySelector(this.el, this.options.fieldSelector.searchCharCounter);
        this.submitButton = DomAccess.querySelector(this.el, this.options.fieldSelector.searchButton);
        this.searchExamples = DomAccess.querySelector(this.el, this.options.fieldSelector.searchExamples);
        this.searchResults = DomAccess.querySelector(this.el, this.options.fieldSelector.searchResults);
        this.titlePrimary = DomAccess.querySelector(this.el, this.options.fieldSelector.primaryTitle);
        this.loadingSpinner = DomAccess.querySelector(this.el, this.options.fieldSelector.loadingSpinner);
        this.errorText = DomAccess.querySelector(this.el, this.options.fieldSelector.errorText);

        this.client = new HttpClient();
        this._registerEvents();

        if (this.inputField.value.trim()) {
            this.inputCharCount.innerText = this.inputField.value.length.toString().trim();
            this._searchTerms();
        }
    }

    private _registerEvents(): void {
        this.inputField.addEventListener('keydown', this._onKeyDown.bind(this));
        this.inputField.addEventListener('input', this._onInput.bind(this));
        this.submitButton.addEventListener('click', this._onButtonClick.bind(this));
        this.searchExamples.querySelectorAll(this.options.fieldSelector.searchExampleItem).forEach((element: HTMLElement) => {
            element.addEventListener('click', this._onSearchExampleClick.bind(this));
        });
    }

    private _onKeyDown(event: KeyboardEvent): void {
        if (event.key === 'Enter') {
            event.preventDefault();
            event.stopPropagation();

            this._searchTerms();
        }
    }

    private _onInput(event: Event): void {
        event.preventDefault();
        event.stopPropagation();

        this.inputCharCount.innerText = this.inputField.value.length.toString().trim();
    }

    private _onButtonClick(event: Event): void {
        event.preventDefault();
        event.stopPropagation();

        this._searchTerms();
    }

    private _onSearchExampleClick(event: Event): void {
        event.preventDefault();
        event.stopPropagation();

        this.inputField.value = (event.target as HTMLElement).innerHTML;
    }

    private _searchTerms(): void {
        const value: string = this.inputField.value.trim();

        if (value === '') {
            return;
        }

        const url = new URL(window.location.toString());
        url.searchParams.set('searchQuery', value);
        window.history.pushState({}, '', url);

        this._showState('loading');

        this.client.abort();
        this.client.post(this.options.searchTermsUrl , JSON.stringify({ query: value }), (responseText: string, request: XMLHttpRequest) => {
            if (request.status !== 200) {
                this._showState('error');
                return;
            }

            const productSearchResults: Promise<string>[] = []
            const searchTerms = JSON.parse(responseText);

            if (searchTerms.length === 0) {
                this._showState('empty');
                return;
            }

            for(const searchTerm of searchTerms) {
                productSearchResults.push(this._searchProducts(searchTerm));
            }

            Promise.all(productSearchResults).then((productLists: string[]): void => {
                this.searchResults.innerHTML = productLists.join('');

                if (this.searchResults.innerHTML === '') {
                    this._showState('empty');
                    return;
                }

                window.PluginManager.initializePlugins();

                this._showState('finish');
            }).catch(() => {
                this._showState('error');
            });
        });
    }

    private _searchProducts(value: NaturalLanguageSearchTermsResult): Promise<string> {
        return new Promise((resolve, reject) => {
            this.client.post(
                this.options.searchProductsUrl,
                JSON.stringify({ term: value.term, reason: value.reason }),
                (responseText: string, request: XMLHttpRequest) => {
                    if (request.status !== 200) {
                        reject();
                        return;
                    }

                    resolve(responseText.trim());
                }
            );
        });
    }

    private _showState(state: string): void {
        const secondaryTitle = DomAccess.querySelector(this.el, this.options.fieldSelector.secondaryTitle, false);
        if (secondaryTitle instanceof HTMLElement) {
            secondaryTitle.remove();
        }

        switch (state) {
            case 'loading':
                this.searchResults.innerHTML = '';
                this.errorText.classList.add('d-none');
                this.searchExamples.classList.add('d-none');
                this.loadingSpinner.classList.remove('d-none');
                this.titlePrimary.innerText = this.options.snippets.loadingTitle;
                break;

            case 'error':
                this.loadingSpinner.classList.add('d-none');
                this.errorText.classList.remove('d-none');

                break;

            case 'empty':
                this.loadingSpinner.classList.add('d-none');
                this.searchExamples.classList.remove('d-none');
                this.titlePrimary.innerText = this.options.snippets.noResultsTitle;
                this.titlePrimary.insertAdjacentHTML('afterend',
                    `<h2 class="nls-title-secondary align-self-stretch text-primary m-0">${this.options.snippets.noResultsSubTitle}</h2>`
                );

                break;

            case 'finish':
                this.loadingSpinner.classList.add('d-none');
                this.titlePrimary.innerText = this.options.snippets.resultTitle;
                break;
        }
    }
}

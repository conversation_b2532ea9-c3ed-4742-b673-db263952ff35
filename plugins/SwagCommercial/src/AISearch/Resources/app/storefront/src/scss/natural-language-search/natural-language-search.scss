/**
 * @experimental stableVersion:v6.7.0 feature:NaturalLanguageSearch
 */

.header-search {
    display: flex;
}

.nls-search-page {
    padding-top: 80px;
    gap: 60px;
}

.nls-search-container {
    gap: 80px;
}

.nls-search-bar {
    padding: 4px 0px;
    border-bottom: 2px solid $border-color;

    &:focus-within {
        border-bottom: 2px solid $primary;
    }
}

.nls-search-error {
    font-size: $font-size-sm;

    .icon-exclamation-circle {
        width: 12px;
        height: 12px;
    }
}

.icon-ai-primary {
    color: $primary;
}

.nls-example {
    .nls-example-item {
        --#{$prefix}btn-border-radius: 4px;
        --#{$prefix}btn-font-weight: --#{$font-weight-normal};
    }

    .nls-example-description,
    .nls-example-item,
    .nls-example-suggestion {
        font-size: $font-size-sm;
    }
}

.nls-search-results {
    gap: 48px;
}

@keyframes sparkleAnimation3 {
    0% {
        transform: translateY(0%);
    }

    40% {
        opacity: 1;
    }

    100% {
        transform: translateY(-25%);
    }
}

@keyframes sparkleAnimation2 {
    0% {
        transform: translateY(5%);
    }

    40% {
        opacity: 1;
    }

    100% {
        transform: translateY(-25%);
    }
}

@keyframes sparkleAnimation1 {
    0% {
        transform: translateY(10%);
    }

    40% {
        opacity: 1;
    }

    100% {
        transform: translateY(-25%);
    }
}


.nls-loading-spinner svg {
    > path {
        opacity: 0;
    }

    > path:nth-child(1) {
        animation: sparkleAnimation1 3s infinite ease-in-out;;
        animation-delay: 0s;
    }

    > path:nth-child(2) {
        animation: sparkleAnimation2 3s infinite ease-in-out;;
        animation-delay: 0.2s;
    }

    > path:nth-child(3) {
        animation: sparkleAnimation3 3s infinite ease-in-out;;
        animation-delay: 0.4s;
    }
}

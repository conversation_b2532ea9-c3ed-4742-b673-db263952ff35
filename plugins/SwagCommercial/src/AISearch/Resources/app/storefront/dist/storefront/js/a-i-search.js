"use strict";(self.webpackChunk=self.webpackChunk||[]).push([["a-i-search"],{7524:(e,t,i)=>{var s,r,n,o=i(6285),a=i(3206),l=i(8254);class h extends o.Z{init(){this.inputField=a.Z.querySelector(this.el,this.options.fieldSelector.searchInput),this.inputCharCount=a.Z.querySelector(this.el,this.options.fieldSelector.searchCharCounter),this.submitButton=a.Z.querySelector(this.el,this.options.fieldSelector.searchButton),this.searchExamples=a.Z.querySelector(this.el,this.options.fieldSelector.searchExamples),this.searchResults=a.Z.querySelector(this.el,this.options.fieldSelector.searchResults),this.titlePrimary=a.Z.querySelector(this.el,this.options.fieldSelector.primaryTitle),this.loadingSpinner=a.Z.querySelector(this.el,this.options.fieldSelector.loadingSpinner),this.errorText=a.Z.querySelector(this.el,this.options.fieldSelector.errorText),this.client=new l.Z,this._registerEvents(),this.inputField.value.trim()&&(this.inputCharCount.innerText=this.inputField.value.length.toString().trim(),this._searchTerms())}_registerEvents(){this.inputField.addEventListener("keydown",this._onKeyDown.bind(this)),this.inputField.addEventListener("input",this._onInput.bind(this)),this.submitButton.addEventListener("click",this._onButtonClick.bind(this)),this.searchExamples.querySelectorAll(this.options.fieldSelector.searchExampleItem).forEach((e=>{e.addEventListener("click",this._onSearchExampleClick.bind(this))}))}_onKeyDown(e){"Enter"===e.key&&(e.preventDefault(),e.stopPropagation(),this._searchTerms())}_onInput(e){e.preventDefault(),e.stopPropagation(),this.inputCharCount.innerText=this.inputField.value.length.toString().trim()}_onButtonClick(e){e.preventDefault(),e.stopPropagation(),this._searchTerms()}_onSearchExampleClick(e){e.preventDefault(),e.stopPropagation(),this.inputField.value=e.target.innerHTML}_searchTerms(){const e=this.inputField.value.trim();if(""===e)return;const t=new URL(window.location.toString());t.searchParams.set("searchQuery",e),window.history.pushState({},"",t),this._showState("loading"),this.client.abort(),this.client.post(this.options.searchTermsUrl,JSON.stringify({query:e}),((e,t)=>{if(200!==t.status)return void this._showState("error");const i=[],s=JSON.parse(e);if(0!==s.length){for(const e of s)i.push(this._searchProducts(e));Promise.all(i).then((e=>{this.searchResults.innerHTML=e.join(""),""!==this.searchResults.innerHTML?(window.PluginManager.initializePlugins(),this._showState("finish")):this._showState("empty")})).catch((()=>{this._showState("error")}))}else this._showState("empty")}))}_searchProducts(e){return new Promise(((t,i)=>{this.client.post(this.options.searchProductsUrl,JSON.stringify({term:e.term,reason:e.reason}),((e,s)=>{200===s.status?t(e.trim()):i()}))}))}_showState(e){const t=a.Z.querySelector(this.el,this.options.fieldSelector.secondaryTitle,!1);switch(t instanceof HTMLElement&&t.remove(),e){case"loading":this.searchResults.innerHTML="",this.errorText.classList.add("d-none"),this.searchExamples.classList.add("d-none"),this.loadingSpinner.classList.remove("d-none"),this.titlePrimary.innerText=this.options.snippets.loadingTitle;break;case"error":this.loadingSpinner.classList.add("d-none"),this.errorText.classList.remove("d-none");break;case"empty":this.loadingSpinner.classList.add("d-none"),this.searchExamples.classList.remove("d-none"),this.titlePrimary.innerText=this.options.snippets.noResultsTitle,this.titlePrimary.insertAdjacentHTML("afterend",`<h2 class="nls-title-secondary align-self-stretch text-primary m-0">${this.options.snippets.noResultsSubTitle}</h2>`);break;case"finish":this.loadingSpinner.classList.add("d-none"),this.titlePrimary.innerText=this.options.snippets.resultTitle}}}s=h,n={fieldSelector:{searchInput:".nls-search-input",searchCharCounter:".nls-input-counter",searchButton:".nls-search-bar-button",primaryTitle:".nls-title-primary",secondaryTitle:".nls-title-secondary",searchExamples:".nls-example",searchExampleItem:".nls-example-item",searchResults:".nls-search-results",loadingSpinner:".nls-loading-spinner",errorText:".nls-search-error"}},(r=function(e){var t=function(e,t){if("object"!=typeof e||null===e)return e;var i=e[Symbol.toPrimitive];if(void 0!==i){var s=i.call(e,t||"default");if("object"!=typeof s)return s;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:String(t)}(r="options"))in s?Object.defineProperty(s,r,{value:n,enumerable:!0,configurable:!0,writable:!0}):s[r]=n;window.PluginManager.register("NaturalLanguageSearch",h,"[data-natural-language-search]")}},e=>{e.O(0,["vendor-node","vendor-shared"],(()=>{return t=7524,e(e.s=t);var t}));e.O()}]);
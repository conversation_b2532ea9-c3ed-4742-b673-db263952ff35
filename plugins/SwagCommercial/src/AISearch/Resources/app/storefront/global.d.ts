/**
 * @package inventory
 */

interface StorefrontWindow extends Window {
    PluginManager: {
        register: (pluginName: string, plugin: any, selector: string) => void;
        initializePlugins : () => void;
    };
}

declare module "src/plugin-system/plugin.class" {
    const Plugin:any;
    export = Plugin;
}

declare module "src/service/http-client.service" {
    const HttpClient:any;
    export = HttpClient;
}

declare module "src/helper/dom-access.helper" {
   const DomAccess:any;
   export = DomAccess;
}

declare module "src/plugin-system/plugin.manager" {
   const PluginManager:any;
   export = PluginManager;
}
interface NaturalLanguageSearchTermsResult {
    term: string,
    reason: string,
    extensions: object,
}

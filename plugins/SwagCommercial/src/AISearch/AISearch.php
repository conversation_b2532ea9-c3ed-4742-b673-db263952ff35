<?php declare(strict_types=1);

namespace Shopware\Commercial\AISearch;

use Shopware\Commercial\AISearch\DependencyInjection\AISearchExtension;
use Shopware\Commercial\CommercialBundle;
use Shopware\Core\Framework\Log\Package;
use Symfony\Component\Config\FileLocator;
use Symfony\Component\Config\Loader\DelegatingLoader;
use Symfony\Component\Config\Loader\LoaderResolver;
use Symfony\Component\DependencyInjection\ContainerBuilder;
use Symfony\Component\DependencyInjection\Extension\ExtensionInterface;
use Symfony\Component\DependencyInjection\Loader\GlobFileLoader;
use Symfony\Component\DependencyInjection\Loader\YamlFileLoader;

/**
 * @internal
 *
 * @phpstan-import-type Feature from CommercialBundle
 */
#[Package('inventory')]
class AISearch extends CommercialBundle
{
    /**
     * @var string
     */
    protected $name = 'AISearch';

    /**
     * @return list<Feature>
     */
    public function describeFeatures(): array
    {
        return [
            [
                'code' => 'NATURAL_LANGUAGE_SEARCH',
                'name' => 'Search by context',
                'description' => 'Allows the customer to describe what they are looking for using natural language and find products more easily.',
            ],
        ];
    }

    public function build(ContainerBuilder $container): void
    {
        parent::build($container);

        $this->buildConfig($container);
    }

    protected function createContainerExtension(): ?ExtensionInterface
    {
        return new AISearchExtension();
    }

    private function buildConfig(ContainerBuilder $container): void
    {
        $locator = new FileLocator('Resources/config');

        $resolver = new LoaderResolver([
            new YamlFileLoader($container, $locator),
            new GlobFileLoader($container, $locator),
        ]);

        $loader = new DelegatingLoader($resolver);

        $configDir = $this->getPath() . '/Resources/config';

        $loader->load($configDir . '/{packages}/*.yaml', 'glob');
    }
}

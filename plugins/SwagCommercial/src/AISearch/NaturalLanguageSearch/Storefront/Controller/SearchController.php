<?php declare(strict_types=1);

namespace Shopware\Commercial\AISearch\NaturalLanguageSearch\Storefront\Controller;

use Shopware\Commercial\AISearch\NaturalLanguageSearch\Event\SearchTerm\SearchTermRouteRequestEvent;
use Shopware\Commercial\AISearch\NaturalLanguageSearch\Exception\SearchTermException;
use Shopware\Commercial\AISearch\NaturalLanguageSearch\SalesChannel\Product\SearchTerm\AbstractSearchTermRoute;
use Shopware\Commercial\AISearch\NaturalLanguageSearch\Storefront\Search\Page\SearchPageHook;
use Shopware\Commercial\AISearch\NaturalLanguageSearch\Storefront\Search\Page\SearchPageLoader;
use Shopware\Core\Content\Product\ProductCollection;
use Shopware\Core\Content\Product\ProductEntity;
use Shopware\Core\Content\Product\SalesChannel\Listing\ProductListingResult;
use Shopware\Core\Content\Product\SalesChannel\Search\AbstractProductSearchRoute;
use Shopware\Core\Content\Product\SalesChannel\Search\ProductSearchRouteResponse;
use Shopware\Core\Framework\DataAbstractionLayer\Search\Criteria;
use Shopware\Core\Framework\Log\Package;
use Shopware\Core\Framework\Routing\RoutingException;
use Shopware\Core\System\SalesChannel\SalesChannelContext;
use Shopware\Core\System\SystemConfig\SystemConfigService;
use Shopware\Storefront\Controller\StorefrontController;
use Symfony\Component\EventDispatcher\EventDispatcherInterface;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

/**
 * @internal
 *
 * @experimental stableVersion:v6.7.0 feature:NaturalLanguageSearch
 */
#[Route(defaults: ['_routeScope' => ['storefront']])]
#[Package('system-settings')]
class SearchController extends StorefrontController
{
    final public const CONFIG_ENABLED_KEY = 'AISearch.naturalLanguageSearch.enabled';

    /**
     * @internal
     */
    public function __construct(
        private readonly SearchPageLoader $searchPageLoader,
        private readonly SystemConfigService $systemConfigService,
        private readonly EventDispatcherInterface $eventDispatcher,
        private readonly AbstractSearchTermRoute $searchTermRoute,
        private readonly AbstractProductSearchRoute $productSearchRoute
    ) {
    }

    #[Route(
        path: '/natural-language-search',
        name: 'frontend.natlang.page',
        defaults: ['_httpCache' => true],
        methods: ['GET'],
        condition: 'service(\'license\').check(\'NATURAL_LANGUAGE_SEARCH-5828669\')'
    )]
    public function index(Request $request, SalesChannelContext $context): Response
    {
        $salesChannelId = $context->getSalesChannelId();

        if (!$this->systemConfigService->get(self::CONFIG_ENABLED_KEY, $salesChannelId)) {
            return $this->redirectToRoute('frontend.home.page');
        }

        $naturalLanguageSearchPage = $this->searchPageLoader->load($request, $context);

        $this->hook(new SearchPageHook($naturalLanguageSearchPage, $context));

        return $this->renderStorefront('@AISearch/storefront/page/natural-language-search/index.html.twig', [
            'page' => $naturalLanguageSearchPage,
        ]);
    }

    #[Route(
        path: '/natural-language-search/search-terms',
        name: 'frontend.natlang.search.terms',
        defaults: ['_httpCache' => true, 'XmlHttpRequest' => true],
        methods: ['POST'],
        condition: 'service(\'license\').check(\'NATURAL_LANGUAGE_SEARCH-5828669\')'
    )]
    public function searchTerms(Request $request, SalesChannelContext $context): JsonResponse
    {
        $query = $request->request->get('query');

        $searchRouteRequest = new Request();
        $searchRouteRequest->request->set('query', $query);

        $event = new SearchTermRouteRequestEvent($request, $searchRouteRequest, $context);
        $this->eventDispatcher->dispatch($event);

        try {
            $response = $this->searchTermRoute->getSearchTerms($event->getStoreApiRequest(), $context);
        } catch (SearchTermException $exception) {
            if ($exception->getErrorCode() === SearchTermException::INVALID_RESPONSE) {
                return new JsonResponse([]);
            }

            throw $exception;
        }

        return new JsonResponse($response->getObject());
    }

    #[Route(
        path: '/natural-language-search/search-products',
        name: 'frontend.natlang.search.products',
        defaults: ['_httpCache' => true, 'XmlHttpRequest' => true],
        methods: ['POST'],
        condition: 'service(\'license\').check(\'NATURAL_LANGUAGE_SEARCH-5828669\')'
    )]
    public function searchProducts(Request $request, SalesChannelContext $salesChannelContext): Response
    {
        $searchRouteRequest = new Request();
        $searchRouteRequest->request->set('no-aggregations', true);
        $searchRouteRequest->request->set('search', $request->request->get('term'));
        $criteria = new Criteria();

        try {
            $results = $this->productSearchRoute->load($searchRouteRequest, $salesChannelContext, $criteria);
        } catch (RoutingException) {
            $results = new ProductSearchRouteResponse(
                new ProductListingResult(
                    ProductEntity::class,
                    0,
                    new ProductCollection(),
                    null,
                    $criteria,
                    $salesChannelContext->getContext()
                )
            );
        }

        return $this->renderStorefront(
            '@AISearch/storefront/component/natural-language-search/product/product-slider.html.twig',
            [
                'products' => $results->getListingResult()->getElements(),
                'searchTerm' => $request->request->get('term'),
                'searchReason' => $request->request->get('reason') ?? '',
            ]
        );
    }
}

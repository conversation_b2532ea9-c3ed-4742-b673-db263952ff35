<?php declare(strict_types=1);

namespace Shopware\Commercial\AISearch\NaturalLanguageSearch\Storefront\Search\Page;

use Shopware\Core\Framework\Log\Package;
use Shopware\Core\System\SalesChannel\SalesChannelContext;
use Shopware\Storefront\Page\PageLoadedHook;

/**
 * @internal
 */
#[Package('system-settings')]
class SearchPageHook extends PageLoadedHook
{
    final public const HOOK_NAME = 'natural-language-search-page-loaded';

    public function __construct(
        private readonly SearchPage $naturalLanguageSearchPage,
        private readonly SalesChannelContext $salesChannelContext
    ) {
        parent::__construct($salesChannelContext->getContext());
    }

    /**
     * {@inheritDoc}
     */
    public function getName(): string
    {
        return static::HOOK_NAME;
    }

    public function getSalesChannelContext(): SalesChannelContext
    {
        return $this->salesChannelContext;
    }

    public function getNaturalLanguageSearchPage(): SearchPage
    {
        return $this->naturalLanguageSearchPage;
    }
}

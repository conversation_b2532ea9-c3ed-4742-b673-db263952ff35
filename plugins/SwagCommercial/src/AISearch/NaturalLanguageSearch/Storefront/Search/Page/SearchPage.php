<?php declare(strict_types=1);

namespace Shopware\Commercial\AISearch\NaturalLanguageSearch\Storefront\Search\Page;

use Shopware\Core\Framework\Log\Package;
use Shopware\Storefront\Page\Page;

#[Package('system-settings')]
class SearchPage extends Page
{
    protected ?string $searchQuery;

    public function getSearchQuery(): ?string
    {
        return $this->searchQuery;
    }

    public function setSearchQuery(?string $searchQuery): void
    {
        $this->searchQuery = $searchQuery;
    }
}

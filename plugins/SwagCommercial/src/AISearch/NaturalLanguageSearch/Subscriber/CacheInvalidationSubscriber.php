<?php declare(strict_types=1);

namespace Shopware\Commercial\AISearch\NaturalLanguageSearch\Subscriber;

use Shopware\Core\Content\Product\Events\ProductIndexerEvent;
use Shopware\Core\Content\Product\Events\ProductNoLongerAvailableEvent;
use Shopware\Core\Framework\Adapter\Cache\CacheInvalidator;
use Shopware\Core\Framework\Log\Package;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;

/**
 * @internal
 *
 * @experimental stableVersion:v6.7.0 feature:NaturalLanguageSearch
 */
#[Package('system-settings')]
class CacheInvalidationSubscriber implements EventSubscriberInterface
{
    public function __construct(private readonly CacheInvalidator $cacheInvalidator)
    {
    }

    /**
     * @return array<string, string|array{0: string, 1: int}|list<array{0: string, 1?: int}>>
     */
    public static function getSubscribedEvents(): array
    {
        return [
            ProductIndexerEvent::class => [
                ['invalidateSearch', 2002],
            ],
            ProductNoLongerAvailableEvent::class => [
                ['invalidateSearch', 2002],
            ],
            'product_search_config.written' => [
                ['invalidateSearch', 2002],
            ],
        ];
    }

    public function invalidateSearch(): void
    {
        $this->cacheInvalidator->invalidate([
            'natural-language-search-term-route',
        ]);
    }
}

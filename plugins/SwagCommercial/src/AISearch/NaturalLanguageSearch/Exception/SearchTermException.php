<?php declare(strict_types=1);

namespace Shopware\Commercial\AISearch\NaturalLanguageSearch\Exception;

use Shopware\Core\Framework\HttpException;
use Shopware\Core\Framework\Log\Package;
use Symfony\Component\HttpFoundation\Response;

/**
 * @internal
 *
 * @experimental stableVersion:v6.7.0 feature:NaturalLanguageSearch
 */
#[Package('system-settings')]
class SearchTermException extends HttpException
{
    final public const LOCALE_NOT_FOUND = 'NATURAL_LANGUAGE_SEARCH__LOCALE_NOT_FOUND';

    final public const QUERY_TOO_LONG = 'NATURAL_LANGUAGE_SEARCH__QUERY_TOO_LONG';

    final public const EMPTY_QUERY = 'NATURAL_LANGUAGE_SEARCH__EMPTY_QUERY';

    final public const INVALID_RESPONSE = 'NATURAL_LANGUAGE_SEARCH__INVALID_RESPONSE';

    public static function localeNotFound(string $languageId): self
    {
        return new self(
            Response::HTTP_BAD_REQUEST,
            self::LOCALE_NOT_FOUND,
            'Locale for languageId "{{ languageId }}" not found',
            ['languageId' => $languageId]
        );
    }

    public static function queryTooLong(int $maxLength): self
    {
        return new self(
            Response::HTTP_BAD_REQUEST,
            self::QUERY_TOO_LONG,
            'Query is too long. Max length is {{ maxLength }}',
            ['maxLength' => $maxLength]
        );
    }

    public static function emptyQuery(): self
    {
        return new self(
            Response::HTTP_BAD_REQUEST,
            self::EMPTY_QUERY,
            'A query parameter was provided, but was empty'
        );
    }

    public static function invalidResponse(): self
    {
        return new self(
            Response::HTTP_BAD_REQUEST,
            self::INVALID_RESPONSE,
            'Invalid response from AI service'
        );
    }
}

<?php declare(strict_types=1);

namespace Shopware\Commercial\AISearch\NaturalLanguageSearch\Domain\Product\SearchTerm;

use Doctrine\DBAL\Connection;
use Shopware\Commercial\AISearch\NaturalLanguageSearch\Exception\SearchTermException;
use Shopware\Commercial\Licensing\Exception\LicenseExpiredException;
use Shopware\Commercial\Licensing\License;
use Shopware\Core\Framework\Log\Package;
use Shopware\Core\Framework\Uuid\Uuid;
use Shopware\Core\System\SalesChannel\SalesChannelContext;

/**
 * @experimental stableVersion:v6.7.0 feature:NaturalLanguageSearch
 *
 * @phpstan-import-type SearchTermRequestResponse from SearchTermRequest
 */
#[Package('system-settings')]
class SearchTermBuilder
{
    /**
     * @internal
     */
    public function __construct(
        private readonly SearchTermRequest $request,
        private readonly Connection $connection
    ) {
    }

    public function build(SalesChannelContext $context, string $query): SearchTermCollection
    {
        if (!License::get('NATURAL_LANGUAGE_SEARCH-6923702')) {
            throw new LicenseExpiredException();
        }

        /** @var SearchTermRequestResponse $response */
        $response = $this->request->request(
            $query,
            $this->getLocale($context->getContext()->getLanguageId()),
            $context->getSalesChannelId()
        );

        if (!isset($response['terms'])) {
            return new SearchTermCollection();
        }

        return new SearchTermCollection(
            \array_reduce(
                $response['terms'],
                [$this, 'reduceSearchResults'],
                []
            )
        );
    }

    /**
     * @param array<string, SearchTerm> $accum
     * @param array{term: string, reason: string} $item
     *
     * @return array<string, SearchTerm>
     */
    private static function reduceSearchResults(array $accum, array $item): array
    {
        $term = $item['term'];
        $accum[$term] = new SearchTerm($term, $item['reason']);

        return $accum;
    }

    private function getLocale(string $languageId): string
    {
        /** @var string|null $locale */
        $locale = $this->connection->fetchOne(
            'SELECT locale.code FROM locale INNER JOIN language ON locale.id = language.locale_id  WHERE language.id = :languageId',
            ['languageId' => Uuid::fromHexToBytes($languageId)]
        );

        if ($locale === null) {
            throw SearchTermException::localeNotFound($languageId);
        }

        return $locale;
    }
}

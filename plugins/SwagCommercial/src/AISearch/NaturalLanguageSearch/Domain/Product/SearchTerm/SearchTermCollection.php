<?php declare(strict_types=1);

namespace Shopware\Commercial\AISearch\NaturalLanguageSearch\Domain\Product\SearchTerm;

use Shopware\Core\Framework\Log\Package;
use Shopware\Core\Framework\Struct\Collection;

/**
 * @extends Collection<SearchTerm>
 *
 * @experimental stableVersion:v6.7.0 feature:NaturalLanguageSearch
 */
#[Package('system-settings')]
class SearchTermCollection extends Collection
{
    public function getApiAlias(): string
    {
        return 'product_natural_language_search_term_collection';
    }

    protected function getExpectedClass(): ?string
    {
        return SearchTerm::class;
    }
}

<?php declare(strict_types=1);

namespace Shopware\Commercial\AISearch\NaturalLanguageSearch\Domain\Product\SearchTerm;

use GuzzleHttp\Client;
use Shopware\Commercial\AISearch\NaturalLanguageSearch\Exception\SearchTermException;
use Shopware\Commercial\Licensing\Exception\LicenseExpiredException;
use Shopware\Commercial\Licensing\License;
use Shopware\Core\Framework\Log\Package;
use Shopware\Core\System\SystemConfig\SystemConfigService;
use Symfony\Component\HttpFoundation\Request;

/**
 * @experimental stableVersion:v6.7.0 feature:NaturalLanguageSearch
 *
 * @phpstan-type SearchTermRequestResponse array{terms: array<int, array{term: string, reason: string}>}
 */
#[Package('system-settings')]
class SearchTermRequest
{
    final public const CONFIG_CONTEXT_KEY = 'AISearch.naturalLanguageSearch.salesChannelDescription';

    private const SEARCH_URL = 'https://ai-services.apps.shopware.io/api/product-search/natural-language';

    /**
     * @internal
     */
    public function __construct(
        private readonly Client $client,
        private readonly SystemConfigService $systemConfigService,
    ) {
    }

    /**
     * @return SearchTermRequestResponse
     */
    public function request(
        string $query,
        string $locale,
        string $salesChannelId
    ): array {
        if (!License::get('NATURAL_LANGUAGE_SEARCH-6923702')) {
            throw new LicenseExpiredException();
        }

        $originalResponse = $this->client->request(Request::METHOD_POST, self::SEARCH_URL, [
            'json' => [
                'context' => $this->systemConfigService->get(self::CONFIG_CONTEXT_KEY, $salesChannelId),
                'query' => $query,
                'locale' => $locale,
            ],
            'headers' => [
                'Authorization' => $this->systemConfigService->getString(License::CONFIG_STORE_LICENSE_KEY),
            ],
        ]);

        /** @var SearchTermRequestResponse $response */
        $response = \json_decode($originalResponse->getBody()->getContents(), true);

        if (json_last_error() !== \JSON_ERROR_NONE) {
            throw SearchTermException::invalidResponse();
        }

        return $response;
    }
}

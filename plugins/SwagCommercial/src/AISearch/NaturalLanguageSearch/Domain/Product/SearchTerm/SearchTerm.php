<?php declare(strict_types=1);

namespace Shopware\Commercial\AISearch\NaturalLanguageSearch\Domain\Product\SearchTerm;

use Shopware\Core\Framework\Log\Package;
use Shopware\Core\Framework\Struct\Struct;

/**
 * @experimental stableVersion:v6.7.0 feature:NaturalLanguageSearch
 */
#[Package('system-settings')]
class SearchTerm extends Struct
{
    protected string $term;

    protected string $reason;

    public function __construct(string $term, string $reason)
    {
        $this->term = $term;
        $this->reason = $reason;
    }

    public function getTerm(): string
    {
        return $this->term;
    }

    public function getReason(): string
    {
        return $this->reason;
    }

    public function getApiAlias(): string
    {
        return 'product_natural_language_search_term';
    }
}

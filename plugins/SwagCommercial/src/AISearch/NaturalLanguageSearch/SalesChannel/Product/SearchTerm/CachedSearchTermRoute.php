<?php declare(strict_types=1);

namespace Shopware\Commercial\AISearch\NaturalLanguageSearch\SalesChannel\Product\SearchTerm;

use Shopware\Commercial\AISearch\NaturalLanguageSearch\Event\SearchTerm\SearchTermRouteCacheKeyEvent;
use Shopware\Commercial\AISearch\NaturalLanguageSearch\Event\SearchTerm\SearchTermRouteCacheTagsEvent;
use Shopware\Core\Framework\Adapter\Cache\AbstractCacheTracer;
use Shopware\Core\Framework\Adapter\Cache\CacheValueCompressor;
use Shopware\Core\Framework\Log\Package;
use Shopware\Core\Framework\Util\Json;
use Shopware\Core\System\SalesChannel\SalesChannelContext;
use Shopware\Core\System\SalesChannel\StoreApiResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Contracts\Cache\CacheInterface;
use Symfony\Contracts\Cache\ItemInterface;
use Symfony\Contracts\EventDispatcher\EventDispatcherInterface;

/**
 * @experimental stableVersion:v6.7.0 feature:NaturalLanguageSearch
 */
#[Route(defaults: ['_routeScope' => ['store-api']])]
#[Package('system-settings')]
class CachedSearchTermRoute extends AbstractSearchTermRoute
{
    private const NAME = 'natural-language-search-term-route';

    /**
     * @internal
     *
     * @param AbstractCacheTracer<SearchTermRouteResponse> $tracer
     * @param array<string> $states
     */
    public function __construct(
        private readonly AbstractSearchTermRoute $decorated,
        private readonly CacheInterface $cache,
        private readonly AbstractCacheTracer $tracer,
        private readonly EventDispatcherInterface $dispatcher,
        private readonly array $states
    ) {
    }

    public function getDecorated(): AbstractSearchTermRoute
    {
        return $this->decorated;
    }

    #[Route(
        path: '/store-api/product/natural-language/search-term',
        name: 'store-api.product.natural-language.search-term',
        methods: ['POST'],
        condition: 'service(\'license\').check(\'NATURAL_LANGUAGE_SEARCH-5828669\')'
    )]
    public function getSearchTerms(Request $request, SalesChannelContext $context): SearchTermRouteResponse
    {
        if ($context->hasState(...$this->states)) {
            return $this->getDecorated()->getSearchTerms($request, $context);
        }

        $key = $this->generateKey($request, $context);

        if ($key === null) {
            return $this->getDecorated()->getSearchTerms($request, $context);
        }

        $value = $this->cache->get($key, function (ItemInterface $item) use ($request, $context) {
            $response = $this->tracer->trace(self::NAME, fn () => $this->getDecorated()->getSearchTerms($request, $context));

            $item->tag($this->generateTags($request, $response, $context));

            return CacheValueCompressor::compress($response);
        });

        /** @var SearchTermRouteResponse $response */
        $response = CacheValueCompressor::uncompress($value);

        return $response;
    }

    private function generateKey(Request $request, SalesChannelContext $context): ?string
    {
        $parts = [
            $context->getSalesChannelId(),
            $context->getContext()->getLanguageId(),
            $request->get('query'),
        ];

        $event = new SearchTermRouteCacheKeyEvent($parts, $request, $context, null);
        $this->dispatcher->dispatch($event);

        if (!$event->shouldCache()) {
            return null;
        }

        return self::NAME . '-' . md5(Json::encode($event->getParts()));
    }

    /**
     * @return array<string>
     */
    private function generateTags(Request $request, StoreApiResponse $response, SalesChannelContext $context): array
    {
        $tags = array_merge(
            $this->tracer->get(self::NAME),
            [self::NAME],
        );

        $event = new SearchTermRouteCacheTagsEvent($tags, $request, $response, $context, null);
        $this->dispatcher->dispatch($event);

        return array_unique(array_filter($event->getTags()));
    }
}

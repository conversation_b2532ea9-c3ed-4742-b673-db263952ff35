<?php
declare(strict_types=1);

namespace Shopware\Commercial\AISearch\NaturalLanguageSearch\SalesChannel\Product\SearchTerm;

use Shopware\Core\Framework\Log\Package;
use Shopware\Core\System\SalesChannel\SalesChannelContext;
use Symfony\Component\HttpFoundation\Request;

/**
 * @experimental stableVersion:v6.7.0 feature:NaturalLanguageSearch
 */
#[Package('system-settings')]
abstract class AbstractSearchTermRoute
{
    abstract public function getSearchTerms(Request $request, SalesChannelContext $context): SearchTermRouteResponse;

    abstract public function getDecorated(): AbstractSearchTermRoute;
}

<?php declare(strict_types=1);

namespace Shopware\Commercial\AISearch\NaturalLanguageSearch\SalesChannel\Product\SearchTerm;

use Shopware\Commercial\AISearch\NaturalLanguageSearch\Domain\Product\SearchTerm\SearchTermCollection;
use Shopware\Core\Framework\Log\Package;
use Shopware\Core\System\SalesChannel\StoreApiResponse;

/**
 * @experimental stableVersion:v6.7.0 feature:NaturalLanguageSearch
 */
#[Package('system-settings')]
class SearchTermRouteResponse extends StoreApiResponse
{
    /**
     * @var SearchTermCollection
     */
    protected $object;

    public function __construct(SearchTermCollection $collection)
    {
        parent::__construct($collection);
    }

    public function getSearchTerms(): SearchTermCollection
    {
        return $this->object;
    }
}

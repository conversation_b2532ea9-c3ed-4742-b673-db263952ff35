<?php declare(strict_types=1);

namespace Shopware\Commercial\AISearch\NaturalLanguageSearch\SalesChannel\Product\SearchTerm;

use Shopware\Commercial\AISearch\NaturalLanguageSearch\Domain\Product\SearchTerm\SearchTermBuilder;
use Shopware\Commercial\AISearch\NaturalLanguageSearch\Exception\SearchTermException;
use Shopware\Core\Framework\Log\Package;
use Shopware\Core\Framework\Plugin\Exception\DecorationPatternException;
use Shopware\Core\Framework\Routing\RoutingException;
use Shopware\Core\System\SalesChannel\SalesChannelContext;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;

/**
 * @experimental stableVersion:v6.7.0 feature:NaturalLanguageSearch
 */
#[Route(defaults: ['_routeScope' => ['store-api']])]
#[Package('system-settings')]
class SearchTermRoute extends AbstractSearchTermRoute
{
    final public const QUERY_MAX_LENGTH = 200;

    /**
     * @internal
     */
    public function __construct(private readonly SearchTermBuilder $searchTermBuilder)
    {
    }

    public function getDecorated(): AbstractSearchTermRoute
    {
        throw new DecorationPatternException(self::class);
    }

    #[Route(
        path: '/store-api/product/natural-language/search-term',
        name: 'store-api.product.natural-language.search-term',
        methods: ['POST'],
        condition: 'service(\'license\').check(\'NATURAL_LANGUAGE_SEARCH-5828669\')'
    )]
    public function getSearchTerms(Request $request, SalesChannelContext $context): SearchTermRouteResponse
    {
        $query = $request->request->get('query');

        if (!\is_string($query)) {
            throw RoutingException::invalidRequestParameter('query');
        }

        if (empty($query)) {
            throw SearchTermException::emptyQuery();
        }

        if (\strlen($query) > self::QUERY_MAX_LENGTH) {
            throw SearchTermException::queryTooLong(self::QUERY_MAX_LENGTH);
        }

        $terms = $this->searchTermBuilder->build($context, $query);

        return new SearchTermRouteResponse($terms);
    }
}

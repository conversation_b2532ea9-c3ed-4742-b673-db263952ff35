<?php declare(strict_types=1);

namespace Shopware\Commercial\AISearch\DependencyInjection;

use Shopware\Core\Framework\Log\Package;
use Symfony\Component\Config\Definition\ConfigurationInterface;
use Symfony\Component\DependencyInjection\ContainerBuilder;
use Symfony\Component\DependencyInjection\Extension\Extension;

/**
 * @experimental stableVersion:v6.7.0 feature:NaturalLanguageSearch
 */
#[Package('system-settings')]
class AISearchExtension extends Extension
{
    private const ALIAS = 'ai_search';

    /**
     * {@inheritdoc}
     */
    public function getAlias(): string
    {
        return self::ALIAS;
    }

    /**
     * @param array<array<string, array<string, mixed>|bool|string|int|float|\UnitEnum|null>> $configs
     */
    public function load(array $configs, ContainerBuilder $container): void
    {
        $config = $this->processConfiguration($this->getConfiguration($configs, $container), $configs);
        $this->addConfig($container, $this->getAlias(), $config);
    }

    /**
     * @param array<array<string, array<string, mixed>|bool|string|int|float|\UnitEnum|null>> $config
     */
    public function getConfiguration(array $config, ContainerBuilder $container): ConfigurationInterface
    {
        return new Configuration();
    }

    /**
     * @param array<string, array<string, mixed>|bool|float|int|string|\UnitEnum|null> $options
     */
    private function addConfig(ContainerBuilder $container, string $alias, array $options): void
    {
        /** @var array<string, array<string, mixed>|bool|float|int|string|\UnitEnum|null> $option */
        foreach ($options as $key => $option) {
            $parameterKey = $alias . '.' . $key;
            $container->setParameter($parameterKey, $option);

            if (\is_array($option)) {
                $this->addConfig($container, $parameterKey, $option);
            }
        }
    }
}

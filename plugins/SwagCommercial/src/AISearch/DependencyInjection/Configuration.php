<?php declare(strict_types=1);

namespace Shopware\Commercial\AISearch\DependencyInjection;

use Shopware\Core\Framework\Log\Package;
use Symfony\Component\Config\Definition\Builder\TreeBuilder;
use Symfony\Component\Config\Definition\ConfigurationInterface;

/**
 * @experimental stableVersion:v6.7.0 feature:NaturalLanguageSearch
 */
#[Package('system-settings')]
class Configuration implements ConfigurationInterface
{
    public function getConfigTreeBuilder(): TreeBuilder
    {
        $builder = new TreeBuilder('ai_search');

        $rootNode = $builder->getRootNode();
        $rootNode
            ->children()
                ->arrayNode('cache')
                    ->children()
                        ->arrayNode('invalidation')
                            ->children()
                                ->arrayNode('search_term_route')
                                    ->performNoDeepMerging()
                                    ->scalarPrototype()->end()
                                ->end()
                            ->end()
                        ->end()
                    ->end()
                ->end()
            ->end();

        return $builder;
    }
}

{% block component_captcha_friendly_captcha %}
    {% set language = app.request.locale|split('-')|first %}

    {% set options = {
        siteKey: config('core.basicInformation.activeCaptchasV2.friendlyCaptcha.config.siteKey'),
        startMode: 'none',
        language,
        solutionFieldName: constant('Shopware\\Commercial\\Captcha\\Storefront\\Framework\\Captcha\\FriendlyCaptcha::CAPTCHA_REQUEST_PARAMETER'),
    } %}

    <div data-friendly-captcha="true" data-friendly-captcha-options="{{ options|json_encode }}">
        <div
            class="frc-captcha"
            data-sitekey="{{ options.siteKey }}"
            data-start="{{ options.startMode }}"
            data-puzzle-endpoint="{{ constant('Shopware\\Commercial\\Captcha\\Storefront\\Framework\\Captcha\\FriendlyCaptcha::FRC_CAPTCHA_PUZZLE_ENDPOINT') }}"
            data-solution-field-name="{{ options.solutionFieldName }}"
            data-lang="{{ options.language }}"></div>
    </div>
{% endblock %}

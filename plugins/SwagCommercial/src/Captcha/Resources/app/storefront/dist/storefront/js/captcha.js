"use strict";(self.webpackChunk=self.webpackChunk||[]).push([["captcha"],{3501:(t,e,i)=>{var s,r,n,o=i(6285);class a extends o.Z{init(){this._getForm(),this._form&&this.options.siteKey&&(this.frcContainerSelector=this.el.querySelector(this.options.frcContainerSelector),this.friendlyChallenge=window.friendlyChallenge,this._formSubmitting=!1,this.formPluginInstances=window.PluginManager.getPluginInstancesFromElement(this._form),this._registerEvents(),this._render())}onFormSubmit(){this.widget&&this.widget.valid?this._submitInvisibleForm():(this.frcContainerSelector.classList.add(this.options.frcHasErrorClassSelector),this._formSubmitting=!0)}_render(){this.friendlyChallenge&&(this.widget&&this.widget.valid||(this.widget=new this.friendlyChallenge.WidgetInstance(this.frcContainerSelector,{siteKey:this.options.siteKey,startMode:this.options.startMode,language:this.options.language,doneCallback:this._onCaptchaTokenResponse.bind(this),readyCallback:this._onCaptchaReadyCallback.bind(this)})))}_onCaptchaTokenResponse(t){t?(this.frcContainerSelector.classList.remove(this.options.frcHasErrorClassSelector),this.frcContainerSelector.classList.add(this.options.frcHasSuccessClassSelector)):this._formSubmitting=!0}_onCaptchaReadyCallback(){this.frcInputSelector=this.el.querySelector(`[name="${this.options.solutionFieldName}"]`),this.frcInputSelector.value="",this.frcInputSelector.setAttribute("data-skip-report-validity",!0),this.frcInputSelector.setAttribute("required",!0),this.frcInputSelector.setAttribute("type","text")}_registerEvents(){this.formPluginInstances?this.formPluginInstances.forEach((t=>{t.$emitter.subscribe("beforeSubmit",this._onFormSubmitCallback.bind(this))})):this._form.addEventListener("submit",this._onFormSubmitCallback.bind(this))}_onFormSubmitCallback(){this._formSubmitting||(this._formSubmitting=!0,this.onFormSubmit())}_getForm(){return this.el&&"FORM"===this.el.nodeName?(this._form=this.el,!0):(this._form=this.el.closest("form"),this._form)}_submitInvisibleForm(){if(!this._form.checkValidity())return;let t=!1;this.formPluginInstances.forEach((e=>{"function"==typeof e.sendAjaxFormSubmit&&!1!==e.options.useAjax&&(t=!0,e.sendAjaxFormSubmit())})),t||this._form.submit()}}s=a,n={siteKey:null,startMode:null,language:null,frcContainerSelector:".frc-captcha",solutionFieldName:"frc-captcha-solution",frcHasErrorClassSelector:"has-error",frcHasSuccessClassSelector:"has-success"},(r=function(t){var e=function(t,e){if("object"!=typeof t||null===t)return t;var i=t[Symbol.toPrimitive];if(void 0!==i){var s=i.call(t,e||"default");if("object"!=typeof s)return s;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==typeof e?e:String(e)}(r="options"))in s?Object.defineProperty(s,r,{value:n,enumerable:!0,configurable:!0,writable:!0}):s[r]=n;const l=window.PluginManager;window.friendlyCaptchaActive&&l.register("FriendlyCaptcha",a,"[data-friendly-captcha]")}},t=>{t.O(0,["vendor-node","vendor-shared"],(()=>{return e=3501,t(t.s=e);var e}));t.O()}]);
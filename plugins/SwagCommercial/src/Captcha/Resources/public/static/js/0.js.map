{"version": 3, "sources": ["webpack:////tmp/extension2400992207/SwagCommercial/src/Captcha/Resources/app/administration/src/core/modules/sw-settings-basic-information/component/sw-settings-captcha-select-v2/sw-settings-captcha-select-v2.html.twig", "webpack:////tmp/extension2400992207/SwagCommercial/src/Captcha/Resources/app/administration/src/core/modules/sw-settings-basic-information/component/sw-settings-captcha-select-v2/index.ts"], "names": ["Component", "Shopware", "wrapComponentConfig", "template"], "mappings": "wHAAe,ICEPA,EAAcC,SAAdD,UAKOA,YAAUE,oBAAoB,CACzCC,SDRW", "file": "static/js/0.js", "sourcesContent": ["export default \"\\n{% block sw_settings_captcha_select_v2 %}\\n    <sw-container class=\\\"sw-settings-captcha-select-v2\\\">\\n        {% parent %}\\n\\n        <sw-container\\n            v-if=\\\"currentValue.friendlyCaptcha.isActive\\\"\\n            class=\\\"sw-settings-captcha-select-v2__friendly-captcha\\\"\\n        >\\n            <p class=\\\"sw-settings-captcha-select-v2__description sw-settings-captcha-select-v2__friendly-captcha-description\\\">\\n                {{ $tc('sw-settings-basic-information.captcha.label.friendlyCaptchaDescription') }}\\n            </p>\\n\\n            <sw-text-field\\n                {% if VUE3 %}\\n                v-model:value=\\\"currentValue.friendlyCaptcha.config.siteKey\\\"\\n                {% else %}\\n                v-model=\\\"currentValue.friendlyCaptcha.config.siteKey\\\"\\n                {% endif %}\\n                name=\\\"googleReCaptchaV2SiteKey\\\"\\n                :label=\\\"$tc('sw-settings-basic-information.captcha.label.friendlyCaptchaSiteKey')\\\"\\n            />\\n\\n            <sw-text-field\\n                {% if VUE3 %}\\n                v-model:value=\\\"currentValue.friendlyCaptcha.config.secretKey\\\"\\n                {% else %}\\n                v-model=\\\"currentValue.friendlyCaptcha.config.secretKey\\\"\\n                {% endif %}\\n                name=\\\"googleReCaptchaV2SecretKey\\\"\\n                :label=\\\"$tc('sw-settings-basic-information.captcha.label.friendlyCaptchaSecretKey')\\\"\\n            />\\n        </sw-container>\\n    </sw-container>\\n{% endblock %}\\n\";", "import template from './sw-settings-captcha-select-v2.html.twig';\n\nconst { Component } = Shopware;\n\n/**\n * @package checkout\n */\nexport default Component.wrapComponentConfig({\n    template,\n});\n"], "sourceRoot": ""}
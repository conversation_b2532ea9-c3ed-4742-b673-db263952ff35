!function(e){function t(t){for(var r,o,u=t[0],a=t[1],i=0,l=[];i<u.length;i++)o=u[i],Object.prototype.hasOwnProperty.call(n,o)&&n[o]&&l.push(n[o][0]),n[o]=0;for(r in a)Object.prototype.hasOwnProperty.call(a,r)&&(e[r]=a[r]);for(c&&c(t);l.length;)l.shift()()}var r={},n={captcha:0};function o(t){if(r[t])return r[t].exports;var n=r[t]={i:t,l:!1,exports:{}};return e[t].call(n.exports,n,n.exports,o),n.l=!0,n.exports}o.e=function(e){var t=[],r=n[e];if(0!==r)if(r)t.push(r[2]);else{var u=new Promise((function(t,o){r=n[e]=[t,o]}));t.push(r[2]=u);var a,i=document.createElement("script");i.charset="utf-8",i.timeout=120,o.nc&&i.setAttribute("nonce",o.nc),i.src=function(e){return o.p+"static/js/"+({}[e]||e)+".js"}(e);var c=new Error;a=function(t){i.onerror=i.onload=null,clearTimeout(l);var r=n[e];if(0!==r){if(r){var o=t&&("load"===t.type?"missing":t.type),u=t&&t.target&&t.target.src;c.message="Loading chunk "+e+" failed.\n("+o+": "+u+")",c.name="ChunkLoadError",c.type=o,c.request=u,r[1](c)}n[e]=void 0}};var l=setTimeout((function(){a({type:"timeout",target:i})}),12e4);i.onerror=i.onload=a,document.head.appendChild(i)}return Promise.all(t)},o.m=e,o.c=r,o.d=function(e,t,r){o.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:r})},o.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},o.t=function(e,t){if(1&t&&(e=o(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var r=Object.create(null);if(o.r(r),Object.defineProperty(r,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var n in e)o.d(r,n,function(t){return e[t]}.bind(null,n));return r},o.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return o.d(t,"a",t),t},o.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},o.p=(window.__sw__.assetPath + '/bundles/captcha/'),o.oe=function(e){throw console.error(e),e};var u=this.webpackJsonpPlugincaptcha=this.webpackJsonpPlugincaptcha||[],a=u.push.bind(u);u.push=t,u=u.slice();for(var i=0;i<u.length;i++)t(u[i]);var c=a;o(o.s="w9tX")}({w9tX:function(e,t,r){Shopware.License.get("CAPTCHA-3581792")&&Shopware.Component.override("sw-settings-captcha-select-v2",(function(){return r.e(0).then(r.bind(null,"uXzo"))}))}});
//# sourceMappingURL=captcha.js.map
{
    "compilerOptions": {
        "types": ["html-loader.d.ts", "node"],
        "target": "es2020",                                     /* Set the JavaScript language version for emitted JavaScript and include compatible library declarations. */
        "lib": ["ES2020", "DOM"],
        "baseUrl": "../../../../../../../../platform/src/Administration/Resources/app/administration",
        "moduleResolution": "node"
    },
    "files": [
        "../../../../../../../../platform/src/Administration/Resources/app/administration/src/global.types.ts"
    ],
    "include": [
        "**/*.ts"
    ],
}

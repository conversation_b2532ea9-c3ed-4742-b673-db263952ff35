<div class="sw-flow-delay-tab">
    <sw-card class="sw-flow-delay-tab__card" position-identifier="sw-flow-delay-tab-card" :is-loading="isLoading">
        <template #grid>
            <div class="sw-flow-delay-tab__container">
                <div v-if="isShowWarningAlert">
                    <sw-alert
                        variant="warning"
                        class="sw-flow-delay-tab__warning-unknow-trigger"
                    >
                        <p>{{ $tc('sw-flow.flowNotification.messageUnknownTriggerWarning') }}</p>
                        <p>{{ $tc('sw-flow.flowNotification.textIntroduce') }}</p>
                        <ul>
                            <li>{{ $tc('sw-flow.flowNotification.textGuide1') }}</li>
                            <li>{{ $tc('sw-flow.flowNotification.textGuide2') }}</li>
                            <li>{{ $tc('sw-flow.flowNotification.textGuide3') }}</li>
                        </ul>
                    </sw-alert>

                    <sw-alert
                        variant="warning"
                        class="sw-flow-delay-tab__warning-box"
                    >
                        {{ $tc('sw-flow-delay.delay.warningText') }}
                    </sw-alert>
                </div>

                <sw-card-section secondary>
                    <sw-container columns="1fr 90px" gap="15px">
                        <sw-simple-search-field
                            {% if VUE3 %}
                            v-model:value="searchTerms"
                            {% else %}
                            v-model="searchTerms"
                            {% endif %}
                            class="sw-flow-delay-tab__search-field"
                            size="medium"
                            variant="form"
                            :placeholder="$tc('sw-flow-delay.delay.list.placeholderSearch')"
                            :delay="500"
                            @search-term-change="onSearchTermChange"
                        />

                        <sw-context-button
                            menu-horizontal-align="left"
                            :menu-width="250"
                            :auto-close="false"
                            :auto-close-outside-click="true"
                            :z-index="1000"
                        >
                            <template #button>
                                <sw-button
                                    class="sw-flow-delay-tab__filter-menu-trigger"
                                    size="small"
                                >
                                    <sw-icon
                                        name="regular-filter-s"
                                        size="16"
                                    />
                                    {{ $tc('sw-flow-delay.delay.list.filter') }}
                                </sw-button>
                                <i
                                    v-if="delayedActionsFilter.length > 0"
                                    class="filter-badge"
                                >
                                    {{ delayedActionsFilter.length }}
                                </i>
                            </template>

                            <h3>{{ $tc('sw-flow-delay.delay.list.filter') }}</h3>

                            <sw-context-menu-divider />

                            <sw-multi-select
                                {% if VUE3 %}
                                v-model:value="delayedActionsFilter"
                                {% else %}
                                v-model="delayedActionsFilter"
                                {% endif %}
                                class="sw-flow-delay-tab__filter-action-list-select"
                                :label="$tc('sw-flow-delay.delay.list.filterLabel')"
                                :label-property="filterItems.label"
                                :value-property="filterItems.value"
                                :options="filterItems"
                                {% if VUE3 %}
                                @update:value="getList"
                                {% else %}
                                @change="getList"
                                {% endif %}
                            />

                            <div class="sw-flow-delay-tab__filter-footer">
                                <a
                                    href="#"
                                    @click.prevent="resetFilters"
                                >
                                    {{ $tc('sw-flow-delay.delay.list.resetFilter') }}
                                </a>
                            </div>
                        </sw-context-button>
                    </sw-container>
                </sw-card-section>

                <sw-card-section divider="top">
                    <sw-entity-listing
                        v-if="!isLoading && delayedActions.length > 0"
                        ref="delayedActionsGrid"
                        class="sw-flow-delay-tab-actions-list"
                        :show-settings="true"
                        :show-actions="true"
                        :show-selection="true"
                        :full-page="false"
                        :is-loading="isLoading"
                        :repository="delayedActionsRepository"
                        :items="delayedActions"
                        :columns="delayedActionColumns"
                        :sort-by="sortBy"
                        :sort-direction="sortDirection"
                        @column-sort="onSortColumn"
                        @items-delete-finish="getList"
                    >
                        <template #bulk-additional="{ selection }">
                            <!-- eslint-disable-next-line vuejs-accessibility/click-events-have-key-events -->
                            <a
                                class="sw-flow-delay-tab__execute-all-link"
                                @click="showExecuteAllDelayed = true"
                            >
                                {{ $tc('sw-flow-delay.delay.list.buttonExecute') }}
                            </a>
                        </template>

                        <template #bulk-modals-additional="{ selection, ids }">
                            <sw-modal
                                v-if="showExecuteAllDelayed"
                                variant="small"
                                :title="$tc('global.default.warning')"
                                @modal-close="onCloseModal"
                            >
                                <sw-alert variant="warning">
                                    {{ $tc('sw-flow-delay.delay.list.executeAllWarningMessage') }}
                                </sw-alert>

                                <template #modal-footer>
                                    <sw-button size="small" @click="onCloseModal">
                                        {{ $tc('global.default.cancel') }}
                                    </sw-button>

                                    <sw-button
                                        class="sw-flow-delay-tab__execute-all-button"
                                        size="small"
                                        variant="primary"
                                        @click="onExecuteAll(ids)">
                                        {{ $tc('sw-flow-delay.delay.list.buttonExecute') }}
                                    </sw-button>
                                </template>
                            </sw-modal>
                        </template>

                        <template #column-order.orderNumber="{ item }">
                            <router-link v-if="item.orderId" :to="{ name: 'sw.order.detail', params: { id: item.orderId } }">
                                {{ item.order.orderNumber }}
                            </router-link>
                            <span v-else>
                                {{ $tc('sw-flow-delay.delay.list.notAvailable') }}
                            </span>
                        </template>

                        <template #column-customer.firstName="{ item }">
                            <router-link
                                v-if="item.customerId"
                                :to="{
                                    name: 'sw.customer.detail',
                                    params: { id: item.customerId },
                                }"
                            >
                                {{ item.customer.lastName || item.order.orderCustomer.lastName }},
                                {{ item.customer.firstName || item.order.orderCustomer.firstName }}
                            </router-link>
                            <span v-else>
                                {{ $tc('sw-flow-delay.delay.list.notAvailable') }}
                            </span>
                        </template>

                        <template #column-name="{ item }">
                            <sw-flow-sequence-label
                                :sequence="getSequence(item)"
                                :app-flow-actions="appActions"
                                @click="detailActionsModal(item)"
                            />
                        </template>

                        <template #column-executionTime="{ item }">
                            {{ remainingTime(item.executionTime) }}
                        </template>

                        <template #column-scheduledFor="{ item }">
                            {{ getScheduledFor(item.executionTime) }}
                        </template>

                        <template #pagination>
                            <sw-pagination
                                :page="page"
                                :limit="limit"
                                :total="total"
                                :total-visible="25"
                                :auto-hide="false"
                                @page-change="onPageChange"
                            />
                        </template>

                        <template #actions="{ item }">
                            <sw-context-menu-item class="sw-flow-delay-tab-actions-list__execute-action" @click="onAction(item.id, 'EXECUTE')">
                                {{ $tc('sw-flow-delay.delay.list.actionExecuteDelayedAction') }}
                            </sw-context-menu-item>

                            <sw-context-menu-item class="sw-flow-delay-tab-actions-list__cancel-action" variant="danger" @click="onAction(item.id, 'DELETE')">
                                {{ $tc('sw-flow-delay.delay.list.actionCancelDelayedAction') }}
                            </sw-context-menu-item>
                        </template>

                        <template #action-modals="{ item }">
                            <sw-modal
                                v-if="showModal === item.id"
                                variant="small"
                                :title="$tc('global.default.warning')"
                                @modal-close="onCloseModal"
                            >
                                <p v-if="actionType ==='DELETE'"
                                   class="sw-flow-list__confirm-delete-text">
                                    {{ $tc('sw-flow-delay.delay.list.cancelWarningMessage') }}
                                </p>

                                <p v-else
                                   class="sw-flow-list__confirm-delete-text">
                                    <sw-alert variant="warning">
                                        {{ $tc('sw-flow-delay.delay.list.confirmExecuteActionText') }}
                                    </sw-alert>
                                </p>

                                <template #modal-footer>
                                    <sw-button size="small" class="sw-flow-delay-tab__button_cancel" @click="onCloseModal">
                                        {{ $tc('global.default.cancel') }}
                                    </sw-button>

                                    <sw-button
                                        v-if="actionType ==='DELETE'"
                                        size="small"
                                        variant="danger"
                                        class="sw-flow-delay-tab__button_delete"
                                        @click="onConfirmAction(item.id)">
                                        {{ $tc('global.default.delete') }}
                                    </sw-button>
                                    <sw-button
                                        v-else
                                        size="small"
                                        variant="primary"
                                        class="sw-flow-delay-tab__button_execute"
                                        @click="onConfirmAction(item.id)">
                                        {{ $tc('sw-flow-delay.delay.list.buttonExecute') }}
                                    </sw-button>
                                </template>
                            </sw-modal>

                            <sw-flow-action-detail-modal
                                v-if="showDetailActionsModal === item.id"
                                :sequence="item"
                                :app-flow-actions="appActions"
                                @modal-close="showDetailActionsModal = null"
                            />
                        </template>
                    </sw-entity-listing>

                </sw-card-section>
            </div>

            <sw-container v-if="!isLoading && !delayedActions.length" rows="auto 400px">
                <sw-empty-state class="sw-flow-delay-tab-actions__empty-state" :show-description="false" :title="$tc('sw-flow-delay.delay.list.emptyStateTitle')">
                    <template #icon>
                        <img :alt="$tc('sw-flow-delay.delay.list.emptyStateTitle')" :src="'/administration/static/img/empty-states/settings-empty-state.svg' | asset">
                    </template>
                </sw-empty-state>
            </sw-container>
        </template>
    </sw-card>
</div>

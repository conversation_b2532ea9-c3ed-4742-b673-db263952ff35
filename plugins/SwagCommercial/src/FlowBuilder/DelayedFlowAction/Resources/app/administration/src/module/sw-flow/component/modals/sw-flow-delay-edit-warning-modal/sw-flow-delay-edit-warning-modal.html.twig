<sw-modal
    class="sw-flow-delay-edit-warning-modal"
    :title="titleModal"
    :closable="false"
    variant="small"
    @modal-close="$emit('modal-cancel')"
>
    <sw-alert :variant="warningContent.type">
        {{ warningContent.text }}
    </sw-alert>

    <template #modal-footer>
        <div class="sw-flow-delay-edit-warning-modal__footer-content">
            <sw-checkbox-field
                {% if VUE3 %}
                v-model:value="dontRemindSelection"
                {% else %}
                v-model="dontRemindSelection"
                {% endif %}
                class="sw-flow-delay-edit-warning-modal__reminder"
                :label="$tc('sw-flow-delay.detail.sequence.labelDontRemind')"
            />

            <div>
                <sw-button
                    class="sw-flow-delay-edit-warning-modal__cancel-button"
                    size="small"
                    @click="$emit('modal-cancel')"
                >
                    {{ $tc('global.default.cancel') }}
                </sw-button>

                <sw-button
                    v-if="type === 'delay_action' && actionType === 'DELETE'"
                    class="sw-flow-delay-edit-warning-modal__delete-button"
                    size="small"
                    variant="danger"
                    @click="handleCloseModal"
                >
                    {{ $tc('global.default.delete') }}
                </sw-button>

                <sw-button
                    v-else
                    class="sw-flow-delay-edit-warning-modal__continue-button"
                    size="small"
                    variant="primary"
                    @click="handleCloseModal"
                >
                    {{ $tc('sw-flow-delay.detail.sequence.continueButton') }}
                </sw-button>
            </div>
        </div>
    </template>
</sw-modal>

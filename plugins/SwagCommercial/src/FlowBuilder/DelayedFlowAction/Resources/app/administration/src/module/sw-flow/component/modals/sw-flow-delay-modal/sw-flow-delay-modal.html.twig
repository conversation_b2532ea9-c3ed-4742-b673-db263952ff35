<sw-modal
    class="sw-flow-delay-modal"
    variant="small"
    :closable="false"
    :title="$tc('sw-flow-delay.modal.titleDelayAction')"
    @modal-close="onClose"
>
    <div
        v-if="isUpdateDelay"
    >
        <sw-single-select
            class="sw-flow-delay-modal__type-selection"
            :value="type"
            :label="$tc('sw-flow-delay.modal.typeDelayAction')"
            :options="actionDelayOptions"
            :popover-classes="['sw-flow-delay-action__popover']"
            :error="typeError"
            {% if VUE3 %}
            @update:value="onSelectDelay"
            {% else %}
            @change="onSelectDelay"
            {% endif %}
        >
        </sw-single-select>
    </div>

    <sw-number-field
        v-if="type !== CUSTOM_TIME"
        {% if VUE3 %}
        v-model:value="time"
        {% else %}
        v-model="time"
        {% endif %}
        class="sw-flow-delay-modal__time"
        required
        :min="1"
        :label="getTimeLabel(type)"
        :placeholder="getTimePlaceholder(type)"
        :error="timeError"
    />

    <sw-text-field
        v-else
        {% if VUE3 %}
        v-model:value="time"
        {% else %}
        v-model="time"
        {% endif %}
        class="sw-flow-delay-modal__custom-time"
        required
        :helpText="$tc('sw-flow-delay.modal.helpTextCustomTime')"
        :label="$tc('sw-flow-delay.modal.labelCustom')"
        :placeholder="$tc('sw-flow-delay.modal.placeholderCustomTime')"
        :error="customTimeError"
    />
    <template #modal-footer>
        <sw-button
            class="sw-flow-delay-modal__cancel-button"
            size="small"
            @click="onClose"
        >
            {{ $tc('global.default.cancel') }}
        </sw-button>

        <sw-button
            class="sw-flow-delay-modal__save-button"
            variant="primary"
            size="small"
            @click="onSaveDelay"
        >
            {{ $tc('global.default.save') }}
        </sw-button>
    </template>
</sw-modal>

{"sw-flow-delay": {"tabDelay": "Geplante Aktionen", "labelDelayedAction": "Verzögerte Aktionen", "action": {"contextButton": {"editDelayAction": "Verzögerung bearbeiten", "deleteDelayAction": "Verzögerung löschen"}}, "modal": {"titleDelayAction": "Verzögerungsdauer", "typeDelayAction": "<PERSON><PERSON>", "labelTime": "{type}", "labelHour": "Stunde | Stunden", "labelDay": "Tag | Tage", "labelWeek": "Woche | Wochen", "labelMonth": "Monat | Monate", "labelCustom": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "placeholderTime": "<PERSON>ib e<PERSON> {type} an", "placeholderCustomTime": "Gib eine benutzerdefinierte Verzögerung ein MM:WW:TT:SS ...", "helpTextCustomTime": "Benutzerdefinierte Verzögerungen müssen genau im folgenden Format (Monate):(Wochen):(Tage):(Stunden) eingegeben werden. <br/> Beispiel: 01:03:00:00 verzögert Deinen Flow um einen Monat und drei Wochen.", "customTimeInvalid": "Entspricht nicht dem erforderlichen Format MM:WW:TT:hh."}, "trigger": {"modal": {"textConfirmChangeTrigger": "Dieser Flow enthält geplante Aktionen. Durch Ändern des Auslösers wird der Flow zurückgesetzt und alle geplante Aktionen werden entfernt."}}, "delay": {"list": {"emptyStateTitle": "<PERSON>ine verzögerten Aktionen vorhanden", "actionCancelDelayedAction": "Abbrechen", "placeholderSearch": "<PERSON><PERSON>, Kunden...", "columnOrderNumber": "Bestellnummer", "columnCustomer": "Kunde", "columnName": "Verzögerte Aktion oder Bedingung", "columnRemainingTime": "Verbleibende Zeit", "columnScheduleFor": "<PERSON><PERSON><PERSON>", "fetchErrorMessage": "Beim Abrufen der Daten ist ein Fehler aufgetreten.", "expiredAction": "Abgelaufen", "notAvailable": "n.v.", "day": "Tag | Tage", "hour": "Stunde | Stunden", "minute": "Minute | Minuten", "actionExecuteDelayedAction": "Jetzt ausführen", "executeActionErrorMessage": "Bei der Ausführung der verzögerten Aktion ist ein Fehler aufgetreten.", "buttonExecute": "Ausführen", "confirmExecuteActionText": "Möchtest Du diese Aktion wirklich ausführen? Diese Aktion kann nicht rückgängig gemacht werden.", "cancelWarningMessage": "Möchtest Du diese verzögerte Aktion wirklich löschen?", "btnExecuteAll": "Alle ausführen", "executeAllWarningMessage": "Möchtest Du alle diese verzögerten Aktionen wirklich ausführen? Diese Aktion kann nicht rückgängig gemacht werden.", "inactivateWarningMessage": "Dieser Flow enthält verzögerte Aktionen. Wenn Du diesen Flow deaktivieren möchtest, schließe vorher bitte alle verzögerten Aktionen ab.", "filterLabel": "Verzögerte Aktion oder Bedingung", "filter": "Filter", "resetFilter": "Z<PERSON>ücksetzen"}, "itemDetail": {"details": "Details", "actions": "Aktionen", "delay": "Verzögerung", "condition": "Bedingung", "conditionIf": "Wenn <PERSON>ung ist", "delayed": "Verzögert"}, "warningText": "Geplante Aktionen werden nur ausgeführt, wenn der Auslöser vor Ablauf der verbleibenden Zeit wieder verfügbar ist."}, "detail": {"sequence": {"selectorDelayAction": "Verzögerung (WARTEN) hinzufügen", "noDelayAction": "<PERSON>ine Verzögerung eingestellt", "delayActionTitle": "Verzögerung (WARTEN)", "delayActionExplainsTitle": "Eine Verzögerung festlegen", "delayActionExplainsDescription": "Verwende Verzögerungen, um definierte Zeiträume der Inaktivität in Deinen Flow einzufügen. Abhä<PERSON><PERSON> von Deinen Einstellungen können diese Zeiträume wenige Stunden oder mehrere Monate lang sein.", "labelThen": "<PERSON><PERSON>", "warningText": "<PERSON><PERSON><PERSON> diesen Flow sind Aktionen geplant. Änderungen an diesem Flow wirken sich nicht auf bereits geplante Aktionen aus.", "continueButton": "<PERSON><PERSON>", "labelDontRemind": "<PERSON><PERSON> Nachricht nicht mehr anzeigen", "labelChangingAction": "Änderungen an dieser Aktion, wirken sich unmittelbar auf geplante verzögerte Aktionen aus.", "labelChangingCondition": "Änderungen an dieser Bedingung wirken sich unmittelbar auf geplante verzögerte Bedingungen aus.", "labelChangingDelay": "Änderungen an dieser Verzögerung wirken sich nicht auf geplante Verzögerungen aus.", "labelDeletingDelay": "Das Löschen dieser Verzögerung beendet alle abhängig geplanten, verzögerten Aktionen und Bedingungen."}}, "list": {"sequence": {"warningText": "Dieser Flow enthält verzögerte Aktionen. Wenn Du diesen Ablauf löschen möchtest, schließe vorher bitte alle verzögerten Aktionen ab."}}, "actions": {"group": {"swagflowbuilder": "Swag Flow Builder"}}}}
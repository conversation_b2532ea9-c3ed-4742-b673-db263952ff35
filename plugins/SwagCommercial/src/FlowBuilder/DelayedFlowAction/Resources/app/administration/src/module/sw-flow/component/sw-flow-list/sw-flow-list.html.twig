{% block sw_flow_list_grid_action_modal_confirm_delete_text %}
    <p v-if="showDelayWarning">
    {% block sw_flow_list_grid_action_modal_confirm_alert %}
        <sw-alert variant="warning">
        {{ $tc('sw-flow-delay.list.sequence.warningText') }}
        <ul>
            <li v-for="(item, index) in flowsDelayedName" :key="index">
                {{ item }}
            </li>
        </ul>
        </sw-alert>
    {% endblock %}
    </p>
    <p
        v-else
        class="sw-flow-list__confirm-delete-text"
    >
        <sw-alert variant="warning">
            {{ deleteWarningMessage() }}
        </sw-alert>
    </p>
{% endblock %}

{% block sw_flow_list_grid_bulk_modal_delete_confirm_text %}
    <template #bulk-modal-delete-confirm-text="{ selectionCount }">
        <p v-if="showDelayWarning">
            {% block sw_flow_list_grid_action_modal_confirm_alert %}
            <sw-alert variant="warning">
                {{ $tc('sw-flow-delay.list.sequence.warningText') }}
                <ul>
                    <li v-for="(item, index) in flowsDelayedName" :key="index">
                        {{ item }}
                    </li>
                </ul>
            </sw-alert>
            {% endblock %}
        </p>
        <p
            v-else
            class="sw-flow-list__confirm-delete-text"
        >
            <sw-alert variant="warning">
                {{ bulkDeleteWarningMessage(selectionCount) }}
            </sw-alert>
        </p>
    </template>
{% endblock %}

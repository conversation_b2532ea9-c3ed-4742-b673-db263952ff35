!function(e){function t(t){for(var n,r,i=t[0],l=t[1],a=0,u=[];a<i.length;a++)r=i[a],Object.prototype.hasOwnProperty.call(o,r)&&o[r]&&u.push(o[r][0]),o[r]=0;for(n in l)Object.prototype.hasOwnProperty.call(l,n)&&(e[n]=l[n]);for(c&&c(t);u.length;)u.shift()()}var n={},r={"delayed-flow-action":0},o={"delayed-flow-action":0};function i(t){if(n[t])return n[t].exports;var r=n[t]={i:t,l:!1,exports:{}};return e[t].call(r.exports,r,r.exports,i),r.l=!0,r.exports}i.e=function(e){var t=[];r[e]?t.push(r[e]):0!==r[e]&&{0:1,1:1,2:1,3:1,4:1,5:1,6:1}[e]&&t.push(r[e]=new Promise((function(t,n){for(var o="static/css/"+({}[e]||e)+".css",l=i.p+o,a=document.getElementsByTagName("link"),u=0;u<a.length;u++){var c=(s=a[u]).getAttribute("data-href")||s.getAttribute("href");if("stylesheet"===s.rel&&(c===o||c===l))return t()}var f=document.getElementsByTagName("style");for(u=0;u<f.length;u++){var s;if((c=(s=f[u]).getAttribute("data-href"))===o||c===l)return t()}var d=document.createElement("link");d.rel="stylesheet",d.type="text/css";d.onerror=d.onload=function(o){if(d.onerror=d.onload=null,"load"===o.type)t();else{var i=o&&("load"===o.type?"missing":o.type),a=o&&o.target&&o.target.href||l,u=new Error("Loading CSS chunk "+e+" failed.\n("+a+")");u.code="CSS_CHUNK_LOAD_FAILED",u.type=i,u.request=a,delete r[e],d.parentNode.removeChild(d),n(u)}},d.href=l,document.head.appendChild(d)})).then((function(){r[e]=0})));var n=o[e];if(0!==n)if(n)t.push(n[2]);else{var l=new Promise((function(t,r){n=o[e]=[t,r]}));t.push(n[2]=l);var a,u=document.createElement("script");u.charset="utf-8",u.timeout=120,i.nc&&u.setAttribute("nonce",i.nc),u.src=function(e){return i.p+"static/js/"+({}[e]||e)+".js"}(e);var c=new Error;a=function(t){u.onerror=u.onload=null,clearTimeout(f);var n=o[e];if(0!==n){if(n){var r=t&&("load"===t.type?"missing":t.type),i=t&&t.target&&t.target.src;c.message="Loading chunk "+e+" failed.\n("+r+": "+i+")",c.name="ChunkLoadError",c.type=r,c.request=i,n[1](c)}o[e]=void 0}};var f=setTimeout((function(){a({type:"timeout",target:u})}),12e4);u.onerror=u.onload=a,document.head.appendChild(u)}return Promise.all(t)},i.m=e,i.c=n,i.d=function(e,t,n){i.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:n})},i.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},i.t=function(e,t){if(1&t&&(e=i(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var n=Object.create(null);if(i.r(n),Object.defineProperty(n,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var r in e)i.d(n,r,function(t){return e[t]}.bind(null,r));return n},i.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return i.d(t,"a",t),t},i.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},i.p=(window.__sw__.assetPath + '/bundles/delayedflowaction/'),i.oe=function(e){throw console.error(e),e};var l=this["webpackJsonpPlugindelayed-flow-action"]=this["webpackJsonpPlugindelayed-flow-action"]||[],a=l.push.bind(l);l.push=t,l=l.slice();for(var u=0;u<l.length;u++)t(l[u]);var c=a;i(i.s="qlJj")}({qlJj:function(e,t,n){"use strict";n.r(t);Shopware.Utils.array.uniqBy;function r(e){return(r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function o(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function i(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?o(Object(n),!0).forEach((function(t){l(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):o(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function l(e,t,n){return(t=c(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function a(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function u(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,c(r.key),r)}}function c(e){var t=function(e,t){if("object"!==r(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var o=n.call(e,t||"default");if("object"!==r(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===r(t)?t:String(t)}function f(e,t){return(f=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function s(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=p(e);if(t){var o=p(this).constructor;n=Reflect.construct(r,arguments,o)}else n=r.apply(this,arguments);return d(this,n)}}function d(e,t){if(t&&("object"===r(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}function p(e){return(p=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}var w=Shopware.Classes.ApiService,h=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&f(e,t)}(l,e);var t,n,r,o=s(l);function l(e,t){var n,r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"flow-builder";return a(this,l),(n=o.call(this,e,t,r)).name="swFlowDelayService",n}return t=l,(n=[{key:"delayedExecute",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},r=t,o=this.getBasicHeaders(n);return Shopware.License.get("FLOW_BUILDER-1475275")?this.httpClient.get("api/_info/me",{headers:i(i({},o),{},{"sw-license-toggle":"FLOW_BUILDER-1475275"})}):this.httpClient.post("/_admin/flow-builder/delayed/execute",{ids:e},{params:r,headers:o}).then((function(e){return w.handleResponse(e)}))}}])&&u(t.prototype,n),r&&u(t,r),Object.defineProperty(t,"prototype",{writable:!1}),l}(w),y=Shopware.Application,b=y.getContainer("init");y.addServiceProvider("swFlowDelayService",(function(e){return new h(b.httpClient,e.loginService)})),Shopware.Component.override("sw-flow-list",(function(){return n.e(12).then(n.bind(null,"Rh3V"))})),Shopware.Component.override("sw-flow-detail",(function(){return n.e(11).then(n.bind(null,"PaJh"))})),Shopware.Component.override("sw-flow-detail-flow",(function(){return n.e(7).then(n.bind(null,"Ydm+"))})),Shopware.Component.override("sw-flow-sequence",(function(){return n.e(13).then(n.bind(null,"yd2g"))})),Shopware.Component.override("sw-flow-sequence-selector",(function(){return n.e(5).then(n.bind(null,"6OqF"))})),Shopware.Component.override("sw-flow-sequence-action",(function(){return n.e(8).then(n.bind(null,"VLOg"))})),Shopware.Component.override("sw-flow-sequence-condition",(function(){return n.e(9).then(n.bind(null,"I5w2"))})),Shopware.Component.extend("sw-flow-delay-action","sw-flow-sequence-action",(function(){return n.e(2).then(n.bind(null,"SZPS"))})),Shopware.Component.register("sw-flow-sequence-label",(function(){return n.e(4).then(n.bind(null,"3UEb"))})),Shopware.Component.register("sw-flow-delay-tab",(function(){return n.e(6).then(n.bind(null,"2GBf"))})),Shopware.Component.register("sw-flow-delay-modal",(function(){return n.e(1).then(n.bind(null,"X9Kk"))})),Shopware.Component.register("sw-flow-action-detail-modal",(function(){return n.e(3).then(n.bind(null,"CTNz"))})),Shopware.Component.register("sw-flow-delay-edit-warning-modal",(function(){return n.e(0).then(n.bind(null,"919+"))})),Shopware.Component.override("sw-flow-event-change-confirm-modal",(function(){return n.e(10).then(n.bind(null,"qq5Y"))}));var v=Shopware,m=v.State,g=v.Module;Shopware.Service().register("delayedFlowActionService",(function(){var e=Shopware.Application.getContainer("init");return new h(e.httpClient,Shopware.Service("loginService"))})),m.registerModule("swFlowDelay",{namespaced:!0,state:function(){return{showWarningModal:{enabled:!1,type:"",id:"",name:""}}},mutations:{setShowWarningModal:function(e,t){e.showWarningModal=t}}}),g.register("sw-flow-delay",{routeMiddleware:function(e,t){"sw.flow.detail"===t.name&&t.children.push({component:"sw-flow-delay-tab",name:"sw.flow.detail.delay",isChildren:!0,path:"/sw/flow/detail/:id/delay",meta:{parentPath:"sw.flow.index",privilege:"flow.viewer"}}),e(t)}}),Shopware.Service("flowBuilderService").addIcons({delay:"regular-hourglass"}),Shopware.Service("flowBuilderService").addLabels({delay:"sw-flow-delay.detail.sequence.delayActionTitle"}),Shopware.Service("flowBuilderService").addActionNames({DELAY:"action.delay"})}});
//# sourceMappingURL=delayed-flow-action.js.map
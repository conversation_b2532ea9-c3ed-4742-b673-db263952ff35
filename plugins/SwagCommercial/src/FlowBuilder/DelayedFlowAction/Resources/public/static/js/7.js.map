{"version": 3, "sources": ["webpack:////tmp/extension2400992207/SwagCommercial/src/FlowBuilder/DelayedFlowAction/Resources/app/administration/src/constant/sw-flow-delay.constant.ts", "webpack:////tmp/extension2400992207/SwagCommercial/src/FlowBuilder/DelayedFlowAction/Resources/app/administration/src/module/sw-flow/component/sw-flow-detail-flow/sw-flow-detail-flow.html.twig", "webpack:////tmp/extension2400992207/SwagCommercial/src/FlowBuilder/DelayedFlowAction/Resources/app/administration/src/module/sw-flow/component/sw-flow-detail-flow/index.ts"], "names": ["SEQUENCE_TYPES", "DELAY_OPTIONS", "value", "label", "CUSTOM_TIME", "_Shopware", "Shopware", "Component", "State", "mapState", "getComponentHelper", "wrapComponentConfig", "template", "computed", "_objectSpread", "enableWarningModal", "_this$showWarningModa", "this", "showWarningModal", "type", "enabled", "actionType", "DELAY_ACTION", "localStorage", "getItem", "methods", "onCloseEditModal", "commit", "name", "onCancelEditModal", "id"], "mappings": "+IAAA,sGAAO,IAAWA,EAAc,SAAdA,GAAc,OAAdA,EAAc,gBAAdA,EAAc,sBAAdA,EAAc,4BAAdA,EAAc,KAMnBC,EAAgB,CACzB,CACIC,MAAO,OACPC,MAAO,iCAEX,CACID,MAAO,MACPC,MAAO,gCAEX,CACID,MAAO,OACPC,MAAO,iCAEX,CACID,MAAO,QACPC,MAAO,kCAEX,CACID,MAAO,SACPC,MAAO,oCAIFC,EAAc,U,2CC7BZ,I,+uCCGf,IAAAC,EAA6BC,SAArBC,EAASF,EAATE,UAAWC,EAAKH,EAALG,MACXC,EAAaF,EAAUG,qBAAvBD,SAKOF,YAAUI,oBAAoB,CACzCC,SDVW,qUCYXC,SAAQC,IAAA,GACDL,EAAS,cAAe,CAAC,sBAAoB,IAEhDM,mBAAkB,WACd,IAAAC,EAAsCC,KAAKC,iBAAnCC,EAAIH,EAAJG,KAAMC,EAAOJ,EAAPI,QACd,MAAmB,WADcJ,EAAVK,YACQF,IAASnB,IAAesB,aAC5CF,GAAqD,SAA1CG,aAAaC,QAAQ,iBAGpCJ,GAA0C,SAA/BG,aAAaC,QAAQL,MAI/CM,QAAS,CACLC,iBAAgB,WACZlB,EAAMmB,OAAO,kCAAiCb,IAAA,GAAOG,KAAKC,kBAAgB,IAAEU,KAAMX,KAAKC,iBAAiBC,KAAMC,SAAS,MAG3HS,kBAAiB,WACbrB,EAAMmB,OAAO,kCAAmC,CAAER,KAAM,GAAIS,KAAM,GAAIR,SAAS,EAAOU,GAAI", "file": "static/js/7.js", "sourcesContent": ["export const enum SEQUENCE_TYPES {\n    ACTION = 'action',\n    CONDITION = 'condition',\n    DELAY_ACTION = 'delay_action',\n}\n\nexport const DELAY_OPTIONS = [\n    {\n        value: 'hour',\n        label: 'sw-flow-delay.modal.labelHour'\n    },\n    {\n        value: 'day',\n        label: 'sw-flow-delay.modal.labelDay'\n    },\n    {\n        value: 'week',\n        label: 'sw-flow-delay.modal.labelWeek'\n    },\n    {\n        value: 'month',\n        label: 'sw-flow-delay.modal.labelMonth'\n    },\n    {\n        value: 'custom',\n        label: 'sw-flow-delay.modal.labelCustom'\n    },\n] as const;\n\nexport const CUSTOM_TIME = 'custom' as const;\nexport const GENERAL_GROUP = 'general' as const;\n", "export default \"{% block sw_flow_detail_flow_modal_extension %}\\n    <sw-flow-delay-edit-warning-modal\\n        v-if=\\\"enableWarningModal\\\"\\n        :action-type=\\\"showWarningModal.actionType\\\"\\n        :type=\\\"showWarningModal.type\\\"\\n        @modal-close=\\\"onCloseEditModal\\\"\\n        @modal-cancel=\\\"onCancelEditModal\\\"\\n    />\\n{% endblock %}\\n\";", "import template from './sw-flow-detail-flow.html.twig';\nimport {SEQUENCE_TYPES} from '../../../../constant/sw-flow-delay.constant';\n\nconst { Component, State } = Shopware;\nconst { mapState } = Component.getComponentHelper();\n\n/**\n * @package services-settings\n */\nexport default Component.wrapComponentConfig({\n    template,\n\n    computed: {\n        ...mapState('swFlowDelay', ['showWarningModal']),\n\n        enableWarningModal(): boolean {\n            const { type, enabled, actionType } = this.showWarningModal;\n            if (actionType === 'DELETE' && type === SEQUENCE_TYPES.DELAY_ACTION) {\n                return enabled && localStorage.getItem('delay_deleted') !== 'true'\n            }\n\n            return enabled && localStorage.getItem(type) !== 'true'\n        },\n    },\n\n    methods: {\n        onCloseEditModal(): void {\n            State.commit('swFlowDelay/setShowWarningModal', { ...this.showWarningModal, name: this.showWarningModal.type, enabled: false });\n        },\n\n        onCancelEditModal(): void {\n            State.commit('swFlowDelay/setShowWarningModal', { type: '', name: '', enabled: false, id: '' });\n        },\n    },\n});\n"], "sourceRoot": ""}
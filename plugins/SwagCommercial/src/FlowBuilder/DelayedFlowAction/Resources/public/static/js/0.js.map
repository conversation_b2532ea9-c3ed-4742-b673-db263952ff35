{"version": 3, "sources": ["webpack:////tmp/extension2400992207/SwagCommercial/src/FlowBuilder/DelayedFlowAction/Resources/app/administration/src/module/sw-flow/component/modals/sw-flow-delay-edit-warning-modal/sw-flow-delay-edit-warning-modal.html.twig", "webpack:////tmp/extension2400992207/SwagCommercial/src/FlowBuilder/DelayedFlowAction/Resources/app/administration/src/module/sw-flow/component/modals/sw-flow-delay-edit-warning-modal/index.ts", "webpack:////tmp/extension2400992207/SwagCommercial/src/FlowBuilder/DelayedFlowAction/Resources/app/administration/src/module/sw-flow/component/modals/sw-flow-delay-edit-warning-modal/sw-flow-delay-edit-warning-modal.scss", "webpack:////tmp/extension2400992207/SwagCommercial/src/FlowBuilder/DelayedFlowAction/Resources/app/administration/src/constant/sw-flow-delay.constant.ts", "webpack:///./node_modules/vue-style-loader/lib/listToStyles.js", "webpack:///./node_modules/vue-style-loader/lib/addStylesClient.js"], "names": ["Component", "Shopware", "wrapComponentConfig", "template", "data", "dontRemindSelection", "computed", "titleModal", "this", "type", "SEQUENCE_TYPES", "ACTION", "CONDITION", "DELAY_ACTION", "actionType", "$tc", "warningContent", "text", "props", "String", "default", "methods", "handleCloseModal", "localStorage", "setItem", "$emit", "content", "__esModule", "module", "i", "locals", "exports", "add", "DELAY_OPTIONS", "value", "label", "CUSTOM_TIME", "listToStyles", "parentId", "list", "styles", "newStyles", "length", "item", "id", "part", "css", "media", "sourceMap", "parts", "push", "hasDocument", "document", "DEBUG", "Error", "stylesInDom", "head", "getElementsByTagName", "singletonElement", "singletonCounter", "isProduction", "noop", "options", "ssrIdKey", "isOldIE", "navigator", "test", "userAgent", "toLowerCase", "addStylesClient", "_isProduction", "_options", "addStylesToDom", "newList", "<PERSON><PERSON><PERSON><PERSON>", "domStyle", "refs", "j", "addStyle", "createStyleElement", "styleElement", "createElement", "append<PERSON><PERSON><PERSON>", "obj", "update", "remove", "querySelector", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "styleIndex", "applyToSingletonTag", "bind", "applyToTag", "newObj", "textStore", "replaceText", "index", "replacement", "filter", "Boolean", "join", "styleSheet", "cssText", "cssNode", "createTextNode", "childNodes", "insertBefore", "setAttribute", "ssrId", "sources", "btoa", "unescape", "encodeURIComponent", "JSON", "stringify", "<PERSON><PERSON><PERSON><PERSON>"], "mappings": "kKAAe,I,YCMPA,EAAcC,SAAdD,UAKOA,YAAUE,oBAAoB,CACzCC,SDZW,k6DCcXC,KAAI,WAGA,MAAO,CACHC,qBAAqB,IAI7BC,SAAU,CACNC,WAAU,WACN,OAAIC,KAAKC,OAASC,IAAeC,QAAUH,KAAKC,OAASC,IAAeE,WAIpEJ,KAAKC,OAASC,IAAeG,cAAoC,WAApBL,KAAKM,WAH3CN,KAAKO,IAAI,0BAObP,KAAKO,IAAI,wBAGpBC,eAAc,WACV,OAAIR,KAAKC,OAASC,IAAeC,OACtB,CACHM,KAAMT,KAAKO,IAAI,qDACfN,KAAM,WAIVD,KAAKC,OAASC,IAAeE,UACtB,CACHK,KAAMT,KAAKO,IAAI,wDACfN,KAAM,WAIVD,KAAKC,OAASC,IAAeG,cAAoC,WAApBL,KAAKM,WAC3C,CACHG,KAAMT,KAAKO,IAAI,oDACfN,KAAM,QAIP,CACHQ,KAAMT,KAAKO,IAAI,oDACfN,KAAM,aAKlBS,MAAO,CACHJ,WAAY,CACRL,KAAMU,OACNC,QAAS,UAGbX,KAAM,CACFA,KAAMU,OACNC,QAASV,IAAeC,SAIhCU,QAAS,CACLC,iBAAgB,WACZ,GAAId,KAAKH,qBAAuBG,KAAKC,OAASC,IAAeG,cAAoC,WAApBL,KAAKM,WAG9E,OAFAS,aAAaC,QAAQ,gBAAiB,aACtChB,KAAKiB,MAAM,eAIXjB,KAAKH,qBACLkB,aAAaC,QAAQhB,KAAKC,KAAM,QAGpCD,KAAKiB,MAAM,oB,uBCrFvB,IAAIC,EAAU,EAAQ,QACnBA,EAAQC,aAAYD,EAAUA,EAAQN,SACnB,iBAAZM,IAAsBA,EAAU,CAAC,CAACE,EAAOC,EAAIH,EAAS,MAC7DA,EAAQI,SAAQF,EAAOG,QAAUL,EAAQI,SAG/BE,EADH,EAAQ,QAA2LZ,SAC5L,WAAYM,GAAS,EAAM,K,kCCT5C,sGAAO,IAAWhB,EAAc,SAAdA,GAAc,OAAdA,EAAc,gBAAdA,EAAc,sBAAdA,EAAc,4BAAdA,EAAc,KAMnBuB,EAAgB,CACzB,CACIC,MAAO,OACPC,MAAO,iCAEX,CACID,MAAO,MACPC,MAAO,gCAEX,CACID,MAAO,OACPC,MAAO,iCAEX,CACID,MAAO,QACPC,MAAO,kCAEX,CACID,MAAO,SACPC,MAAO,oCAIFC,EAAc,U,kCCzBZ,SAASC,EAAcC,EAAUC,GAG9C,IAFA,IAAIC,EAAS,GACTC,EAAY,GACPZ,EAAI,EAAGA,EAAIU,EAAKG,OAAQb,IAAK,CACpC,IAAIc,EAAOJ,EAAKV,GACZe,EAAKD,EAAK,GAIVE,EAAO,CACTD,GAAIN,EAAW,IAAMT,EACrBiB,IALQH,EAAK,GAMbI,MALUJ,EAAK,GAMfK,UALcL,EAAK,IAOhBF,EAAUG,GAGbH,EAAUG,GAAIK,MAAMC,KAAKL,GAFzBL,EAAOU,KAAKT,EAAUG,GAAM,CAAEA,GAAIA,EAAIK,MAAO,CAACJ,KAKlD,OAAOL,E,+CCjBT,IAAIW,EAAkC,oBAAbC,SAEzB,GAAqB,oBAAVC,OAAyBA,QAC7BF,EACH,MAAM,IAAIG,MACV,2JAkBJ,IAAIC,EAAc,GAQdC,EAAOL,IAAgBC,SAASI,MAAQJ,SAASK,qBAAqB,QAAQ,IAC9EC,EAAmB,KACnBC,EAAmB,EACnBC,GAAe,EACfC,EAAO,aACPC,EAAU,KACVC,EAAW,kBAIXC,EAA+B,oBAAdC,WAA6B,eAAeC,KAAKD,UAAUE,UAAUC,eAE3E,SAASC,EAAiB/B,EAAUC,EAAM+B,EAAeC,GACtEX,EAAeU,EAEfR,EAAUS,GAAY,GAEtB,IAAI/B,EAASH,EAAaC,EAAUC,GAGpC,OAFAiC,EAAehC,GAER,SAAiBiC,GAEtB,IADA,IAAIC,EAAY,GACP7C,EAAI,EAAGA,EAAIW,EAAOE,OAAQb,IAAK,CACtC,IAAIc,EAAOH,EAAOX,IACd8C,EAAWpB,EAAYZ,EAAKC,KACvBgC,OACTF,EAAUxB,KAAKyB,GAEbF,EAEFD,EADAhC,EAASH,EAAaC,EAAUmC,IAGhCjC,EAAS,GAEX,IAASX,EAAI,EAAGA,EAAI6C,EAAUhC,OAAQb,IAAK,CACzC,IAAI8C,EACJ,GAAsB,KADlBA,EAAWD,EAAU7C,IACZ+C,KAAY,CACvB,IAAK,IAAIC,EAAI,EAAGA,EAAIF,EAAS1B,MAAMP,OAAQmC,IACzCF,EAAS1B,MAAM4B,YAEVtB,EAAYoB,EAAS/B,OAMpC,SAAS4B,EAAgBhC,GACvB,IAAK,IAAIX,EAAI,EAAGA,EAAIW,EAAOE,OAAQb,IAAK,CACtC,IAAIc,EAAOH,EAAOX,GACd8C,EAAWpB,EAAYZ,EAAKC,IAChC,GAAI+B,EAAU,CACZA,EAASC,OACT,IAAK,IAAIC,EAAI,EAAGA,EAAIF,EAAS1B,MAAMP,OAAQmC,IACzCF,EAAS1B,MAAM4B,GAAGlC,EAAKM,MAAM4B,IAE/B,KAAOA,EAAIlC,EAAKM,MAAMP,OAAQmC,IAC5BF,EAAS1B,MAAMC,KAAK4B,EAASnC,EAAKM,MAAM4B,KAEtCF,EAAS1B,MAAMP,OAASC,EAAKM,MAAMP,SACrCiC,EAAS1B,MAAMP,OAASC,EAAKM,MAAMP,YAEhC,CACL,IAAIO,EAAQ,GACZ,IAAS4B,EAAI,EAAGA,EAAIlC,EAAKM,MAAMP,OAAQmC,IACrC5B,EAAMC,KAAK4B,EAASnC,EAAKM,MAAM4B,KAEjCtB,EAAYZ,EAAKC,IAAM,CAAEA,GAAID,EAAKC,GAAIgC,KAAM,EAAG3B,MAAOA,KAK5D,SAAS8B,IACP,IAAIC,EAAe5B,SAAS6B,cAAc,SAG1C,OAFAD,EAAavE,KAAO,WACpB+C,EAAK0B,YAAYF,GACVA,EAGT,SAASF,EAAUK,GACjB,IAAIC,EAAQC,EACRL,EAAe5B,SAASkC,cAAc,SAAWvB,EAAW,MAAQoB,EAAIvC,GAAK,MAEjF,GAAIoC,EAAc,CAChB,GAAIpB,EAGF,OAAOC,EAOPmB,EAAaO,WAAWC,YAAYR,GAIxC,GAAIhB,EAAS,CAEX,IAAIyB,EAAa9B,IACjBqB,EAAetB,IAAqBA,EAAmBqB,KACvDK,EAASM,EAAoBC,KAAK,KAAMX,EAAcS,GAAY,GAClEJ,EAASK,EAAoBC,KAAK,KAAMX,EAAcS,GAAY,QAGlET,EAAeD,IACfK,EAASQ,EAAWD,KAAK,KAAMX,GAC/BK,EAAS,WACPL,EAAaO,WAAWC,YAAYR,IAMxC,OAFAI,EAAOD,GAEA,SAAsBU,GAC3B,GAAIA,EAAQ,CACV,GAAIA,EAAO/C,MAAQqC,EAAIrC,KACnB+C,EAAO9C,QAAUoC,EAAIpC,OACrB8C,EAAO7C,YAAcmC,EAAInC,UAC3B,OAEFoC,EAAOD,EAAMU,QAEbR,KAKN,IACMS,EADFC,GACED,EAAY,GAET,SAAUE,EAAOC,GAEtB,OADAH,EAAUE,GAASC,EACZH,EAAUI,OAAOC,SAASC,KAAK,QAI1C,SAASV,EAAqBV,EAAcgB,EAAOX,EAAQF,GACzD,IAAIrC,EAAMuC,EAAS,GAAKF,EAAIrC,IAE5B,GAAIkC,EAAaqB,WACfrB,EAAaqB,WAAWC,QAAUP,EAAYC,EAAOlD,OAChD,CACL,IAAIyD,EAAUnD,SAASoD,eAAe1D,GAClC2D,EAAazB,EAAayB,WAC1BA,EAAWT,IAAQhB,EAAaQ,YAAYiB,EAAWT,IACvDS,EAAW/D,OACbsC,EAAa0B,aAAaH,EAASE,EAAWT,IAE9ChB,EAAaE,YAAYqB,IAK/B,SAASX,EAAYZ,EAAcG,GACjC,IAAIrC,EAAMqC,EAAIrC,IACVC,EAAQoC,EAAIpC,MACZC,EAAYmC,EAAInC,UAiBpB,GAfID,GACFiC,EAAa2B,aAAa,QAAS5D,GAEjCe,EAAQ8C,OACV5B,EAAa2B,aAAa5C,EAAUoB,EAAIvC,IAGtCI,IAGFF,GAAO,mBAAqBE,EAAU6D,QAAQ,GAAK,MAEnD/D,GAAO,uDAAyDgE,KAAKC,SAASC,mBAAmBC,KAAKC,UAAUlE,MAAgB,OAG9HgC,EAAaqB,WACfrB,EAAaqB,WAAWC,QAAUxD,MAC7B,CACL,KAAOkC,EAAamC,YAClBnC,EAAaQ,YAAYR,EAAamC,YAExCnC,EAAaE,YAAY9B,SAASoD,eAAe1D,O", "file": "static/js/0.js", "sourcesContent": ["export default \"<sw-modal\\n    class=\\\"sw-flow-delay-edit-warning-modal\\\"\\n    :title=\\\"titleModal\\\"\\n    :closable=\\\"false\\\"\\n    variant=\\\"small\\\"\\n    @modal-close=\\\"$emit('modal-cancel')\\\"\\n>\\n    <sw-alert :variant=\\\"warningContent.type\\\">\\n        {{ warningContent.text }}\\n    </sw-alert>\\n\\n    <template #modal-footer>\\n        <div class=\\\"sw-flow-delay-edit-warning-modal__footer-content\\\">\\n            <sw-checkbox-field\\n                {% if VUE3 %}\\n                v-model:value=\\\"dontRemindSelection\\\"\\n                {% else %}\\n                v-model=\\\"dontRemindSelection\\\"\\n                {% endif %}\\n                class=\\\"sw-flow-delay-edit-warning-modal__reminder\\\"\\n                :label=\\\"$tc('sw-flow-delay.detail.sequence.labelDontRemind')\\\"\\n            />\\n\\n            <div>\\n                <sw-button\\n                    class=\\\"sw-flow-delay-edit-warning-modal__cancel-button\\\"\\n                    size=\\\"small\\\"\\n                    @click=\\\"$emit('modal-cancel')\\\"\\n                >\\n                    {{ $tc('global.default.cancel') }}\\n                </sw-button>\\n\\n                <sw-button\\n                    v-if=\\\"type === 'delay_action' && actionType === 'DELETE'\\\"\\n                    class=\\\"sw-flow-delay-edit-warning-modal__delete-button\\\"\\n                    size=\\\"small\\\"\\n                    variant=\\\"danger\\\"\\n                    @click=\\\"handleCloseModal\\\"\\n                >\\n                    {{ $tc('global.default.delete') }}\\n                </sw-button>\\n\\n                <sw-button\\n                    v-else\\n                    class=\\\"sw-flow-delay-edit-warning-modal__continue-button\\\"\\n                    size=\\\"small\\\"\\n                    variant=\\\"primary\\\"\\n                    @click=\\\"handleCloseModal\\\"\\n                >\\n                    {{ $tc('sw-flow-delay.detail.sequence.continueButton') }}\\n                </sw-button>\\n            </div>\\n        </div>\\n    </template>\\n</sw-modal>\\n\";", "import type {PropType} from 'vue';\nimport template from './sw-flow-delay-edit-warning-modal.html.twig';\nimport './sw-flow-delay-edit-warning-modal.scss';\nimport {SEQUENCE_TYPES} from '../../../../../constant/sw-flow-delay.constant';\nimport {NotificationType} from \"../../../../../type/types\";\n\nconst { Component } = Shopware;\n\n/**\n * @package services-settings\n */\nexport default Component.wrapComponentConfig({\n    template,\n\n    data(): {\n        dontRemindSelection: boolean\n    } {\n        return {\n            dontRemindSelection: false,\n        }\n    },\n\n    computed: {\n        titleModal(): string {\n            if (this.type === SEQUENCE_TYPES.ACTION || this.type === SEQUENCE_TYPES.CONDITION) {\n                return this.$tc('global.default.warning');\n            }\n\n            if (this.type === SEQUENCE_TYPES.DELAY_ACTION && this.actionType === 'DELETE') {\n                return this.$tc('global.default.warning');\n            }\n\n            return this.$tc('global.default.info');\n        },\n\n        warningContent(): NotificationType {\n            if (this.type === SEQUENCE_TYPES.ACTION) {\n                return {\n                    text: this.$tc('sw-flow-delay.detail.sequence.labelChangingAction'),\n                    type: 'warning'\n                }\n            }\n\n            if (this.type === SEQUENCE_TYPES.CONDITION) {\n                return {\n                    text: this.$tc('sw-flow-delay.detail.sequence.labelChangingCondition'),\n                    type: 'warning'\n                }\n            }\n\n            if (this.type === SEQUENCE_TYPES.DELAY_ACTION && this.actionType !== 'DELETE') {\n                return {\n                    text: this.$tc('sw-flow-delay.detail.sequence.labelChangingDelay'),\n                    type: 'info'\n                }\n            }\n\n            return {\n                text: this.$tc('sw-flow-delay.detail.sequence.labelDeletingDelay'),\n                type: 'warning'\n            }\n        },\n    },\n\n    props: {\n        actionType: {\n            type: String,\n            default: 'DELETE',\n        },\n\n        type: {\n            type: String as PropType<SEQUENCE_TYPES.ACTION>,\n            default: SEQUENCE_TYPES.ACTION,\n        },\n    },\n\n    methods: {\n        handleCloseModal(): void {\n            if (this.dontRemindSelection && this.type === SEQUENCE_TYPES.DELAY_ACTION && this.actionType === 'DELETE') {\n                localStorage.setItem('delay_deleted', 'true');\n                this.$emit('modal-close');\n                return;\n            }\n\n            if (this.dontRemindSelection) {\n                localStorage.setItem(this.type, 'true');\n            }\n\n            this.$emit('modal-close');\n        },\n    }\n});\n", "// style-loader: Adds some css to the DOM by adding a <style> tag\n\n// load the styles\nvar content = require(\"!!../../../../../../../../../../../../../../../builds/shopware/6/product/commercial/src/Administration/Resources/app/administration/node_modules/mini-css-extract-plugin/dist/loader.js??ref--15-1!../../../../../../../../../../../../../../../builds/shopware/6/product/commercial/src/Administration/Resources/app/administration/node_modules/css-loader/dist/cjs.js??ref--15-2!../../../../../../../../../../../../../../../builds/shopware/6/product/commercial/src/Administration/Resources/app/administration/node_modules/sass-loader/dist/cjs.js??ref--15-3!./sw-flow-delay-edit-warning-modal.scss\");\nif(content.__esModule) content = content.default;\nif(typeof content === 'string') content = [[module.id, content, '']];\nif(content.locals) module.exports = content.locals;\n// add the styles to the DOM\nvar add = require(\"!../../../../../../../../../../../../../../../builds/shopware/6/product/commercial/src/Administration/Resources/app/administration/node_modules/vue-style-loader/lib/addStylesClient.js\").default\nvar update = add(\"10314691\", content, true, {});", "export const enum SEQUENCE_TYPES {\n    ACTION = 'action',\n    CONDITION = 'condition',\n    DELAY_ACTION = 'delay_action',\n}\n\nexport const DELAY_OPTIONS = [\n    {\n        value: 'hour',\n        label: 'sw-flow-delay.modal.labelHour'\n    },\n    {\n        value: 'day',\n        label: 'sw-flow-delay.modal.labelDay'\n    },\n    {\n        value: 'week',\n        label: 'sw-flow-delay.modal.labelWeek'\n    },\n    {\n        value: 'month',\n        label: 'sw-flow-delay.modal.labelMonth'\n    },\n    {\n        value: 'custom',\n        label: 'sw-flow-delay.modal.labelCustom'\n    },\n] as const;\n\nexport const CUSTOM_TIME = 'custom' as const;\nexport const GENERAL_GROUP = 'general' as const;\n", "/**\n * Translates the list format produced by css-loader into something\n * easier to manipulate.\n */\nexport default function listToStyles (parentId, list) {\n  var styles = []\n  var newStyles = {}\n  for (var i = 0; i < list.length; i++) {\n    var item = list[i]\n    var id = item[0]\n    var css = item[1]\n    var media = item[2]\n    var sourceMap = item[3]\n    var part = {\n      id: parentId + ':' + i,\n      css: css,\n      media: media,\n      sourceMap: sourceMap\n    }\n    if (!newStyles[id]) {\n      styles.push(newStyles[id] = { id: id, parts: [part] })\n    } else {\n      newStyles[id].parts.push(part)\n    }\n  }\n  return styles\n}\n", "/*\n  MIT License http://www.opensource.org/licenses/mit-license.php\n  Author <PERSON> @sokra\n  Modified by <PERSON> @yyx990803\n*/\n\nimport listToStyles from './listToStyles'\n\nvar hasDocument = typeof document !== 'undefined'\n\nif (typeof DEBUG !== 'undefined' && DEBUG) {\n  if (!hasDocument) {\n    throw new Error(\n    'vue-style-loader cannot be used in a non-browser environment. ' +\n    \"Use { target: 'node' } in your Webpack config to indicate a server-rendering environment.\"\n  ) }\n}\n\n/*\ntype StyleObject = {\n  id: number;\n  parts: Array<StyleObjectPart>\n}\n\ntype StyleObjectPart = {\n  css: string;\n  media: string;\n  sourceMap: ?string\n}\n*/\n\nvar stylesInDom = {/*\n  [id: number]: {\n    id: number,\n    refs: number,\n    parts: Array<(obj?: StyleObjectPart) => void>\n  }\n*/}\n\nvar head = hasDocument && (document.head || document.getElementsByTagName('head')[0])\nvar singletonElement = null\nvar singletonCounter = 0\nvar isProduction = false\nvar noop = function () {}\nvar options = null\nvar ssrIdKey = 'data-vue-ssr-id'\n\n// Force single-tag solution on IE6-9, which has a hard limit on the # of <style>\n// tags it will allow on a page\nvar isOldIE = typeof navigator !== 'undefined' && /msie [6-9]\\b/.test(navigator.userAgent.toLowerCase())\n\nexport default function addStylesClient (parentId, list, _isProduction, _options) {\n  isProduction = _isProduction\n\n  options = _options || {}\n\n  var styles = listToStyles(parentId, list)\n  addStylesToDom(styles)\n\n  return function update (newList) {\n    var mayRemove = []\n    for (var i = 0; i < styles.length; i++) {\n      var item = styles[i]\n      var domStyle = stylesInDom[item.id]\n      domStyle.refs--\n      mayRemove.push(domStyle)\n    }\n    if (newList) {\n      styles = listToStyles(parentId, newList)\n      addStylesToDom(styles)\n    } else {\n      styles = []\n    }\n    for (var i = 0; i < mayRemove.length; i++) {\n      var domStyle = mayRemove[i]\n      if (domStyle.refs === 0) {\n        for (var j = 0; j < domStyle.parts.length; j++) {\n          domStyle.parts[j]()\n        }\n        delete stylesInDom[domStyle.id]\n      }\n    }\n  }\n}\n\nfunction addStylesToDom (styles /* Array<StyleObject> */) {\n  for (var i = 0; i < styles.length; i++) {\n    var item = styles[i]\n    var domStyle = stylesInDom[item.id]\n    if (domStyle) {\n      domStyle.refs++\n      for (var j = 0; j < domStyle.parts.length; j++) {\n        domStyle.parts[j](item.parts[j])\n      }\n      for (; j < item.parts.length; j++) {\n        domStyle.parts.push(addStyle(item.parts[j]))\n      }\n      if (domStyle.parts.length > item.parts.length) {\n        domStyle.parts.length = item.parts.length\n      }\n    } else {\n      var parts = []\n      for (var j = 0; j < item.parts.length; j++) {\n        parts.push(addStyle(item.parts[j]))\n      }\n      stylesInDom[item.id] = { id: item.id, refs: 1, parts: parts }\n    }\n  }\n}\n\nfunction createStyleElement () {\n  var styleElement = document.createElement('style')\n  styleElement.type = 'text/css'\n  head.appendChild(styleElement)\n  return styleElement\n}\n\nfunction addStyle (obj /* StyleObjectPart */) {\n  var update, remove\n  var styleElement = document.querySelector('style[' + ssrIdKey + '~=\"' + obj.id + '\"]')\n\n  if (styleElement) {\n    if (isProduction) {\n      // has SSR styles and in production mode.\n      // simply do nothing.\n      return noop\n    } else {\n      // has SSR styles but in dev mode.\n      // for some reason Chrome can't handle source map in server-rendered\n      // style tags - source maps in <style> only works if the style tag is\n      // created and inserted dynamically. So we remove the server rendered\n      // styles and inject new ones.\n      styleElement.parentNode.removeChild(styleElement)\n    }\n  }\n\n  if (isOldIE) {\n    // use singleton mode for IE9.\n    var styleIndex = singletonCounter++\n    styleElement = singletonElement || (singletonElement = createStyleElement())\n    update = applyToSingletonTag.bind(null, styleElement, styleIndex, false)\n    remove = applyToSingletonTag.bind(null, styleElement, styleIndex, true)\n  } else {\n    // use multi-style-tag mode in all other cases\n    styleElement = createStyleElement()\n    update = applyToTag.bind(null, styleElement)\n    remove = function () {\n      styleElement.parentNode.removeChild(styleElement)\n    }\n  }\n\n  update(obj)\n\n  return function updateStyle (newObj /* StyleObjectPart */) {\n    if (newObj) {\n      if (newObj.css === obj.css &&\n          newObj.media === obj.media &&\n          newObj.sourceMap === obj.sourceMap) {\n        return\n      }\n      update(obj = newObj)\n    } else {\n      remove()\n    }\n  }\n}\n\nvar replaceText = (function () {\n  var textStore = []\n\n  return function (index, replacement) {\n    textStore[index] = replacement\n    return textStore.filter(Boolean).join('\\n')\n  }\n})()\n\nfunction applyToSingletonTag (styleElement, index, remove, obj) {\n  var css = remove ? '' : obj.css\n\n  if (styleElement.styleSheet) {\n    styleElement.styleSheet.cssText = replaceText(index, css)\n  } else {\n    var cssNode = document.createTextNode(css)\n    var childNodes = styleElement.childNodes\n    if (childNodes[index]) styleElement.removeChild(childNodes[index])\n    if (childNodes.length) {\n      styleElement.insertBefore(cssNode, childNodes[index])\n    } else {\n      styleElement.appendChild(cssNode)\n    }\n  }\n}\n\nfunction applyToTag (styleElement, obj) {\n  var css = obj.css\n  var media = obj.media\n  var sourceMap = obj.sourceMap\n\n  if (media) {\n    styleElement.setAttribute('media', media)\n  }\n  if (options.ssrId) {\n    styleElement.setAttribute(ssrIdKey, obj.id)\n  }\n\n  if (sourceMap) {\n    // https://developer.chrome.com/devtools/docs/javascript-debugging\n    // this makes source maps inside style tags work properly in Chrome\n    css += '\\n/*# sourceURL=' + sourceMap.sources[0] + ' */'\n    // http://stackoverflow.com/a/26603875\n    css += '\\n/*# sourceMappingURL=data:application/json;base64,' + btoa(unescape(encodeURIComponent(JSON.stringify(sourceMap)))) + ' */'\n  }\n\n  if (styleElement.styleSheet) {\n    styleElement.styleSheet.cssText = css\n  } else {\n    while (styleElement.firstChild) {\n      styleElement.removeChild(styleElement.firstChild)\n    }\n    styleElement.appendChild(document.createTextNode(css))\n  }\n}\n"], "sourceRoot": ""}
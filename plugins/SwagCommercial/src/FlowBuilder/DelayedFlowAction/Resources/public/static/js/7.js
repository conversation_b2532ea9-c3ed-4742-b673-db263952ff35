(this["webpackJsonpPlugindelayed-flow-action"]=this["webpackJsonpPlugindelayed-flow-action"]||[]).push([[7],{EFTm:function(e,t,o){"use strict";o.d(t,"c",(function(){return n})),o.d(t,"b",(function(){return l})),o.d(t,"a",(function(){return a}));var n=function(e){return e.ACTION="action",e.CONDITION="condition",e.DELAY_ACTION="delay_action",e}({}),l=[{value:"hour",label:"sw-flow-delay.modal.labelHour"},{value:"day",label:"sw-flow-delay.modal.labelDay"},{value:"week",label:"sw-flow-delay.modal.labelWeek"},{value:"month",label:"sw-flow-delay.modal.labelMonth"},{value:"custom",label:"sw-flow-delay.modal.labelCustom"}],a="custom"},"Ydm+":function(e,t,o){"use strict";o.r(t);var n=o("EFTm");function l(e){return(l="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function a(e,t){var o=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),o.push.apply(o,n)}return o}function r(e){for(var t=1;t<arguments.length;t++){var o=null!=arguments[t]?arguments[t]:{};t%2?a(Object(o),!0).forEach((function(t){i(e,t,o[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(o)):a(Object(o)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(o,t))}))}return e}function i(e,t,o){return(t=function(e){var t=function(e,t){if("object"!==l(e)||null===e)return e;var o=e[Symbol.toPrimitive];if(void 0!==o){var n=o.call(e,t||"default");if("object"!==l(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===l(t)?t:String(t)}(t))in e?Object.defineProperty(e,t,{value:o,enumerable:!0,configurable:!0,writable:!0}):e[t]=o,e}var c=Shopware,u=c.Component,d=c.State,s=u.getComponentHelper().mapState;t.default=u.wrapComponentConfig({template:'{% block sw_flow_detail_flow_modal_extension %}\n    <sw-flow-delay-edit-warning-modal\n        v-if="enableWarningModal"\n        :action-type="showWarningModal.actionType"\n        :type="showWarningModal.type"\n        @modal-close="onCloseEditModal"\n        @modal-cancel="onCancelEditModal"\n    />\n{% endblock %}\n',computed:r(r({},s("swFlowDelay",["showWarningModal"])),{},{enableWarningModal:function(){var e=this.showWarningModal,t=e.type,o=e.enabled;return"DELETE"===e.actionType&&t===n.c.DELAY_ACTION?o&&"true"!==localStorage.getItem("delay_deleted"):o&&"true"!==localStorage.getItem(t)}}),methods:{onCloseEditModal:function(){d.commit("swFlowDelay/setShowWarningModal",r(r({},this.showWarningModal),{},{name:this.showWarningModal.type,enabled:!1}))},onCancelEditModal:function(){d.commit("swFlowDelay/setShowWarningModal",{type:"",name:"",enabled:!1,id:""})}}})}}]);
//# sourceMappingURL=7.js.map
{"version": 3, "sources": ["webpack:////tmp/extension2400992207/SwagCommercial/src/FlowBuilder/DelayedFlowAction/Resources/app/administration/src/module/sw-flow/component/modals/sw-flow-event-change-confirm-modal/index.ts", "webpack:////tmp/extension2400992207/SwagCommercial/src/FlowBuilder/DelayedFlowAction/Resources/app/administration/src/module/sw-flow/component/modals/sw-flow-event-change-confirm-modal/sw-flow-event-change-confirm-modal.html.twig"], "names": ["Component", "Shopware", "_Component$getCompone", "getComponentHelper", "mapState", "mapGetters", "wrapComponentConfig", "template", "computed", "_objectSpread", "hasDelayedActions", "this", "sequences", "some", "item", "actionName", "isShowConfirmOverride", "flow", "active"], "mappings": "03CAGA,IAAQA,EAAcC,SAAdD,UACRE,EAAiCF,EAAUG,qBAAnCC,EAAQF,EAARE,SAAUC,EAAUH,EAAVG,WAKHL,YAAUM,oBAAoB,CACzCC,SCVW,gUDYXC,SAAQC,MAAA,GACDL,EAAS,cAAe,CAAC,UACzBC,EAAW,cAAe,CAAC,eAAa,IAG3CK,kBAAiB,WACb,OAAOC,KAAKC,UAAUC,MAAK,SAAAC,GAAI,MAAwB,iBAApBA,EAAKC,eAG5CC,sBAAqB,WACjB,OAAQL,KAAKM,KAAKC,QAAUP,KAAKD", "file": "static/js/10.js", "sourcesContent": ["// @ts-ignore\nimport template from './sw-flow-event-change-confirm-modal.html.twig';\n\nconst { Component } = Shopware;\nconst { mapState, mapGetters } = Component.getComponentHelper();\n\n/**\n * @package services-settings\n */\nexport default Component.wrapComponentConfig({\n    template,\n\n    computed: {\n        ...mapState('swFlowState', ['flow']),\n        ...mapGetters('swFlowState', ['sequences']),\n\n\n        hasDelayedActions(): boolean {\n            return this.sequences.some(item => item.actionName === 'action.delay');\n        },\n\n        isShowConfirmOverride(): boolean {\n            return !this.flow.active && this.hasDelayedActions;\n        },\n    },\n\n});\n", "export default \"{% block sw_flow_event_change_confirm_modal_text_confirmation %}\\n    <sw-alert\\n        v-if=\\\"isShowConfirmOverride\\\"\\n        variant=\\\"warning\\\"\\n    >\\n        {{ $tc('sw-flow-delay.trigger.modal.textConfirmChangeTrigger') }}\\n    </sw-alert>\\n\\n    <div v-else>\\n        {% parent %}\\n    </div>\\n{% endblock %}\\n\";"], "sourceRoot": ""}
{"version": 3, "sources": ["webpack:////tmp/extension2400992207/SwagCommercial/src/FlowBuilder/DelayedFlowAction/Resources/app/administration/src/module/sw-flow/component/sw-flow-list/sw-flow-list.html.twig", "webpack:////tmp/extension2400992207/SwagCommercial/src/FlowBuilder/DelayedFlowAction/Resources/app/administration/src/module/sw-flow/component/sw-flow-list/index.ts"], "names": ["_Shopware", "Shopware", "Component", "Criteria", "Data", "wrapComponentConfig", "template", "data", "flowsDelayedName", "showDelayWarning", "computed", "flowCriteria", "criteria", "this", "page", "limit", "setTerm", "term", "addAssociation", "addSorting", "sort", "sortBy", "sortDirection", "delayedActionsRepository", "repositoryFactory", "create", "watch", "currentFlow", "value", "findDelayAction", "selectedItems", "values", "_this", "length", "for<PERSON>ach", "item", "methods", "getLicense", "toggle", "License", "get", "_this2", "id", "Application", "getContainer", "httpClient", "headers", "Accept", "Authorization", "concat", "Service", "getToken", "addFilter", "equalsAny", "not", "equals", "search", "then", "result", "push", "name", "catch"], "mappings": "uJAAe,ICKfA,EAA0CC,SAAlCC,EAASF,EAATE,UAAmBC,EAAQH,EAAhBI,KAAQD,SAKZD,YAAUG,oBAAoB,CACzCC,SDXW,uhDCaXC,KAAI,WAIA,MAAO,CACHC,iBAAkB,GAClBC,kBAAkB,IAI1BC,SAAU,CACNC,aAAY,WACR,IAAMC,EAAW,IAAIT,EAASU,KAAKC,KAAMD,KAAKE,OAO9C,OANAH,EAASI,QAAQH,KAAKI,MACtBL,EACKM,eAAe,aACfC,WAAWhB,EAASiB,KAAKP,KAAKQ,OAAQR,KAAKS,gBAC3CH,WAAWhB,EAASiB,KAAK,YAAa,SAEpCR,GAGXW,yBAAwB,WACpB,OAAOV,KAAKW,kBAAkBC,OAAO,uBAI7CC,MAAO,CACHC,YAAW,SAACC,GACRf,KAAKL,iBAAmB,GACxBK,KAAKgB,gBAAgBD,GACrBf,KAAKJ,kBAAmB,GAG5BqB,cAAa,SAACC,GAA2B,IAADC,EAAA,KACd,IAAlBD,EAAOE,SAIXpB,KAAKL,iBAAmB,GACxBuB,EAAOG,SAAQ,SAAAC,GACXH,EAAKH,gBAAgBM,MAGzBtB,KAAKJ,kBAAmB,KAIhC2B,QAAS,CACLC,WAAU,SAACC,GACP,OAAOrC,SAASsC,QAAQC,IAAIF,IAGhCT,gBAAe,SAACM,GAAuB,IAADM,EAAA,KAClC,GAAKN,EAAKO,GAIV,GAAI7B,KAAKwB,WAAW,wBAApB,CAC0BpC,SAAS0C,YAAYC,aAAa,QAC1CC,WAAWL,IACrB,mBACA,CACIM,QAAS,CACLC,OAAQ,2BACRC,cAAc,UAADC,OAAYhD,SAASiD,QAAQ,gBAAgBC,YAC1D,eAAgB,mBAChB,oBAAqB,8BATrC,CAgBA,IAAMvC,EAAW,IAAIT,EACrBS,EAASwC,UAAUjD,EAASkD,UAAU,SAAU,CAAClB,EAAKO,MACtD9B,EAASwC,UAAUjD,EAASmD,IAAI,MAAO,CAACnD,EAASoD,OAAO,uBAAwB,SAEhF1C,KAAKU,yBAAyBiC,OAAO5C,GAAU6C,MAAK,SAAAC,GAC5CA,EAAOzB,OAAS,IAChBQ,EAAKhC,kBAAmB,EACxBgC,EAAKjC,iBAAiBmD,KAAKxB,EAAKyB,UAErCC,OAAM,WACLpB,EAAKhC,kBAAmB", "file": "static/js/12.js", "sourcesContent": ["export default \"{% block sw_flow_list_grid_action_modal_confirm_delete_text %}\\n    <p v-if=\\\"showDelayWarning\\\">\\n    {% block sw_flow_list_grid_action_modal_confirm_alert %}\\n        <sw-alert variant=\\\"warning\\\">\\n        {{ $tc('sw-flow-delay.list.sequence.warningText') }}\\n        <ul>\\n            <li v-for=\\\"(item, index) in flowsDelayedName\\\" :key=\\\"index\\\">\\n                {{ item }}\\n            </li>\\n        </ul>\\n        </sw-alert>\\n    {% endblock %}\\n    </p>\\n    <p\\n        v-else\\n        class=\\\"sw-flow-list__confirm-delete-text\\\"\\n    >\\n        <sw-alert variant=\\\"warning\\\">\\n            {{ deleteWarningMessage() }}\\n        </sw-alert>\\n    </p>\\n{% endblock %}\\n\\n{% block sw_flow_list_grid_bulk_modal_delete_confirm_text %}\\n    <template #bulk-modal-delete-confirm-text=\\\"{ selectionCount }\\\">\\n        <p v-if=\\\"showDelayWarning\\\">\\n            {% block sw_flow_list_grid_action_modal_confirm_alert %}\\n            <sw-alert variant=\\\"warning\\\">\\n                {{ $tc('sw-flow-delay.list.sequence.warningText') }}\\n                <ul>\\n                    <li v-for=\\\"(item, index) in flowsDelayedName\\\" :key=\\\"index\\\">\\n                        {{ item }}\\n                    </li>\\n                </ul>\\n            </sw-alert>\\n            {% endblock %}\\n        </p>\\n        <p\\n            v-else\\n            class=\\\"sw-flow-list__confirm-delete-text\\\"\\n        >\\n            <sw-alert variant=\\\"warning\\\">\\n                {{ bulkDeleteWarningMessage(selectionCount) }}\\n            </sw-alert>\\n        </p>\\n    </template>\\n{% endblock %}\\n\";", "import template from './sw-flow-list.html.twig';\nimport type RepositoryType from 'src/core/data/repository.data';\nimport type CriteriaType from 'src/core/data/criteria.data';\nimport {Sequence} from \"../../../../type/types\";\n\nconst { Component, Data: { Criteria } } = Shopware;\n\n/**\n * @package services-settings\n */\nexport default Component.wrapComponentConfig({\n    template,\n\n    data(): {\n        flowsDelayedName: [],\n        showDelayWarning: boolean\n    } {\n        return {\n            flowsDelayedName: [],\n            showDelayWarning: false,\n        };\n    },\n\n    computed: {\n        flowCriteria():CriteriaType {\n            const criteria = new Criteria(this.page, this.limit);\n            criteria.setTerm(this.term);\n            criteria\n                .addAssociation('sequences')\n                .addSorting(Criteria.sort(this.sortBy, this.sortDirection))\n                .addSorting(Criteria.sort('updatedAt', 'DESC'));\n\n            return criteria;\n        },\n\n        delayedActionsRepository(): RepositoryType {\n            return this.repositoryFactory.create('swag_delay_action');\n        }\n    },\n\n    watch: {\n        currentFlow(value: Sequence): void {\n            this.flowsDelayedName = [];\n            this.findDelayAction(value);\n            this.showDelayWarning = false;\n        },\n\n        selectedItems(values: Sequence[]): void {\n            if (values.length === 0) {\n                return;\n            }\n\n            this.flowsDelayedName = [];\n            values.forEach(item => {\n                this.findDelayAction(item);\n            });\n\n            this.showDelayWarning = false;\n        },\n    },\n\n    methods: {\n        getLicense(toggle: string): boolean {\n            return Shopware.License.get(toggle);\n        },\n\n        findDelayAction(item: Sequence): void {\n            if (!item.id) {\n                return;\n            }\n\n            if (this.getLicense('FLOW_BUILDER-8415866')) {\n                const initContainer = Shopware.Application.getContainer('init');\n                initContainer.httpClient.get(\n                    'api/_info/config',\n                    {\n                        headers: {\n                            Accept: 'application/vnd.api+json',\n                            Authorization: `Bearer ${Shopware.Service('loginService').getToken()}`,\n                            'Content-Type': 'application/json',\n                            'sw-license-toggle': 'FLOW_BUILDER-8415866',\n                        },\n                    },\n                );\n                return;\n            }\n\n            const criteria = new Criteria();\n            criteria.addFilter(Criteria.equalsAny('flowId', [item.id]));\n            criteria.addFilter(Criteria.not('and', [Criteria.equals('sequence.children.id', null)]));\n\n            this.delayedActionsRepository.search(criteria).then(result => {\n                if (result.length > 0) {\n                    this.showDelayWarning = true;\n                    this.flowsDelayedName.push(item.name);\n                }\n            }).catch(() => {\n                this.showDelayWarning = false;\n            });\n        },\n    }\n});\n"], "sourceRoot": ""}
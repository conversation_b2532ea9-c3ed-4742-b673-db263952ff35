(this["webpackJsonpPlugindelayed-flow-action"]=this["webpackJsonpPlugindelayed-flow-action"]||[]).push([[12],{Rh3V:function(e,n,t){"use strict";t.r(n);var i=Shopware,a=i.Component,l=i.Data.Criteria;n.default=a.wrapComponentConfig({template:'{% block sw_flow_list_grid_action_modal_confirm_delete_text %}\n    <p v-if="showDelayWarning">\n    {% block sw_flow_list_grid_action_modal_confirm_alert %}\n        <sw-alert variant="warning">\n        {{ $tc(\'sw-flow-delay.list.sequence.warningText\') }}\n        <ul>\n            <li v-for="(item, index) in flowsDelayedName" :key="index">\n                {{ item }}\n            </li>\n        </ul>\n        </sw-alert>\n    {% endblock %}\n    </p>\n    <p\n        v-else\n        class="sw-flow-list__confirm-delete-text"\n    >\n        <sw-alert variant="warning">\n            {{ deleteWarningMessage() }}\n        </sw-alert>\n    </p>\n{% endblock %}\n\n{% block sw_flow_list_grid_bulk_modal_delete_confirm_text %}\n    <template #bulk-modal-delete-confirm-text="{ selectionCount }">\n        <p v-if="showDelayWarning">\n            {% block sw_flow_list_grid_action_modal_confirm_alert %}\n            <sw-alert variant="warning">\n                {{ $tc(\'sw-flow-delay.list.sequence.warningText\') }}\n                <ul>\n                    <li v-for="(item, index) in flowsDelayedName" :key="index">\n                        {{ item }}\n                    </li>\n                </ul>\n            </sw-alert>\n            {% endblock %}\n        </p>\n        <p\n            v-else\n            class="sw-flow-list__confirm-delete-text"\n        >\n            <sw-alert variant="warning">\n                {{ bulkDeleteWarningMessage(selectionCount) }}\n            </sw-alert>\n        </p>\n    </template>\n{% endblock %}\n',data:function(){return{flowsDelayedName:[],showDelayWarning:!1}},computed:{flowCriteria:function(){var e=new l(this.page,this.limit);return e.setTerm(this.term),e.addAssociation("sequences").addSorting(l.sort(this.sortBy,this.sortDirection)).addSorting(l.sort("updatedAt","DESC")),e},delayedActionsRepository:function(){return this.repositoryFactory.create("swag_delay_action")}},watch:{currentFlow:function(e){this.flowsDelayedName=[],this.findDelayAction(e),this.showDelayWarning=!1},selectedItems:function(e){var n=this;0!==e.length&&(this.flowsDelayedName=[],e.forEach((function(e){n.findDelayAction(e)})),this.showDelayWarning=!1)}},methods:{getLicense:function(e){return Shopware.License.get(e)},findDelayAction:function(e){var n=this;if(e.id)if(this.getLicense("FLOW_BUILDER-8415866")){Shopware.Application.getContainer("init").httpClient.get("api/_info/config",{headers:{Accept:"application/vnd.api+json",Authorization:"Bearer ".concat(Shopware.Service("loginService").getToken()),"Content-Type":"application/json","sw-license-toggle":"FLOW_BUILDER-8415866"}})}else{var t=new l;t.addFilter(l.equalsAny("flowId",[e.id])),t.addFilter(l.not("and",[l.equals("sequence.children.id",null)])),this.delayedActionsRepository.search(t).then((function(t){t.length>0&&(n.showDelayWarning=!0,n.flowsDelayedName.push(e.name))})).catch((function(){n.showDelayWarning=!1}))}}}})}}]);
//# sourceMappingURL=12.js.map
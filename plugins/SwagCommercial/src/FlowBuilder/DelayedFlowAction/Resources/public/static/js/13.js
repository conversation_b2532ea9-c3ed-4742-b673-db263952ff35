(this["webpackJsonpPlugindelayed-flow-action"]=this["webpackJsonpPlugindelayed-flow-action"]||[]).push([[13],{yd2g:function(e,n,t){"use strict";t.r(n);var i=Shopware.Component;n.default=i.wrapComponentConfig({inject:["flowBuilderService"],template:'{% block sw_flow_sequence_extension %}\n    <sw-flow-delay-action\n        v-if="isDelayAction"\n        :sequence="sequenceData"\n        :disabled="disabled"\n    />\n{% endblock %}\n',computed:{delayConstant:function(){return this.flowBuilderService.getActionName("DELAY")},isDelayAction:function(){return this.sequenceData.actionName===this.delayConstant},isActionSequence:function(){return!this.isSelectorSequence&&!this.isConditionSequence&&this.sequenceData.actionName!==this.delayConstant}}})}}]);
//# sourceMappingURL=13.js.map
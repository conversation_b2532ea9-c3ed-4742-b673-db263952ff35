(this["webpackJsonpPlugindelayed-flow-action"]=this["webpackJsonpPlugindelayed-flow-action"]||[]).push([[8],{EFTm:function(e,t,n){"use strict";n.d(t,"c",(function(){return i})),n.d(t,"b",(function(){return o})),n.d(t,"a",(function(){return r}));var i=function(e){return e.ACTION="action",e.CONDITION="condition",e.DELAY_ACTION="delay_action",e}({}),o=[{value:"hour",label:"sw-flow-delay.modal.labelHour"},{value:"day",label:"sw-flow-delay.modal.labelDay"},{value:"week",label:"sw-flow-delay.modal.labelWeek"},{value:"month",label:"sw-flow-delay.modal.labelMonth"},{value:"custom",label:"sw-flow-delay.modal.labelCustom"}],r="custom"},VLOg:function(e,t,n){"use strict";n.r(t);var i=n("EFTm");function o(e){return(o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function r(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);t&&(i=i.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,i)}return n}function a(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?r(Object(n),!0).forEach((function(t){c(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):r(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function c(e,t,n){return(t=function(e){var t=function(e,t){if("object"!==o(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var i=n.call(e,t||"default");if("object"!==o(i))return i;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===o(t)?t:String(t)}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var s=Shopware,l=s.Component,u=s.State,d=l.getComponentHelper(),h=d.mapState,p=d.mapGetters;t.default=l.wrapComponentConfig({inject:["flowBuilderService"],data:function(){return{customSelectedAction:null,sequenceId:null}},computed:a(a(a({delayConstant:function(){return this.flowBuilderService.getActionName("DELAY")},filterActionOptions:function(){var e=this,t=[];return this.$super("actionOptions").filter((function(t){return t&&t.value!==e.delayConstant})).forEach((function(n){var i=e.triggerActions.find((function(e){return n.value===e.name}));i.requirements.length?i.delayable&&t.push(n):t.push(n)})),t},actionOptions:function(){var e=this,t=this.sequence.parentId||this.getParentId(this.sequence);return this.hasDelay(this.sequences,t)?this.filterActionOptions:this.$super("actionOptions").filter((function(t){return t&&t.value!==e.delayConstant}))}},h("swFlowState",["triggerActions"])),p("swFlowState",["sequences","actionGroups"])),h("swFlowDelay",["showWarningModal"])),watch:{showWarningModal:function(e){var t=this.sequence.id||this.getFirstKey(this.sequence);e.name===i.c.ACTION&&t===e.id&&"ADD"===e.actionType&&(this.selectedAction=this.customSelectedAction,u.commit("swFlowDelay/setShowWarningModal",{type:"",name:"",enabled:!1,id:""}),this.$super("openDynamicModal",this.customSelectedAction)),e.name===i.c.ACTION&&"EDIT"===e.actionType&&this.$super("onEditAction",this.customSelectedAction,e.clickableOption.target,e.clickableOption.key),e.name===i.c.ACTION&&"DELETE"===e.actionType&&this.$super("removeAction",e.id),e.name===i.c.ACTION&&e.id===t&&"DELETE_ALL"===e.actionType&&this.$super("removeActionContainer")}},methods:{hasDelay:function(e,t){var n=e.find((function(e){return e.id===t}));return!!n&&(n.actionName===this.delayConstant||this.hasDelay(e,n.parentId))},getParentId:function(e){var t;return(null===(t=e[Object.keys(e)[Object.keys(e).length-1]])||void 0===t?void 0:t.parentId)||""},getFirstKey:function(e){return Object.keys(e)[0]},openDynamicModal:function(e){if(e)if("action.stop.flow"!==e){var t=this.sequence.parentId||this.getParentId(this.sequence);if(!this.hasDelay(this.sequences,t)||"true"===localStorage.getItem("action"))return this.selectedAction=e,void this.$super("openDynamicModal",e);u.commit("swFlowDelay/setShowWarningModal",{actionType:"ADD",type:i.c.ACTION,name:"",enabled:!0,id:this.sequence.id||this.getFirstKey(this.sequence)}),this.customSelectedAction=e}else this.$super("openDynamicModal",e)},onEditAction:function(e,t,n){if(!e.actionName||"action.stop.flow"!==e.actionName){var o=e.parentId||this.getParentId(this.sequence);this.hasDelay(this.sequences,o)&&"true"!==localStorage.getItem("action")?(u.commit("swFlowDelay/setShowWarningModal",{actionType:"EDIT",type:i.c.ACTION,name:"",enabled:!0,id:e.id,clickableOption:{target:t,key:n}}),this.customSelectedAction=e):this.$super("onEditAction",e,t,n)}},removeAction:function(e){var t=this.sequences.find((function(t){return t.id===e})).parentId;this.hasDelay(this.sequences,t)&&"true"!==localStorage.getItem("action")?u.commit("swFlowDelay/setShowWarningModal",{actionType:"DELETE",type:i.c.ACTION,name:"",enabled:!0,id:e}):this.$super("removeAction",e)},removeActionContainer:function(){var e=this.sequence.parentId||this.getParentId(this.sequence);this.hasDelay(this.sequences,e)&&"true"!==localStorage.getItem("action")?u.commit("swFlowDelay/setShowWarningModal",{actionType:"DELETE_ALL",type:i.c.ACTION,name:"",enabled:!0,id:this.sequence.id||this.getFirstKey(this.sequence)}):this.$super("removeActionContainer")}}})}}]);
//# sourceMappingURL=8.js.map
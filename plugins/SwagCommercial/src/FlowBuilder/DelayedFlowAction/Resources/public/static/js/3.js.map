{"version": 3, "sources": ["webpack:////tmp/extension2400992207/SwagCommercial/src/FlowBuilder/DelayedFlowAction/Resources/app/administration/src/module/sw-flow/component/modals/sw-flow-action-detail-modal/index.ts", "webpack:////tmp/extension2400992207/SwagCommercial/src/FlowBuilder/DelayedFlowAction/Resources/app/administration/src/module/sw-flow/component/modals/sw-flow-action-detail-modal/sw-flow-action-detail-modal.html.twig", "webpack:///./node_modules/vue-style-loader/lib/listToStyles.js", "webpack:///./node_modules/vue-style-loader/lib/addStylesClient.js", "webpack:////tmp/extension2400992207/SwagCommercial/src/FlowBuilder/DelayedFlowAction/Resources/app/administration/src/module/sw-flow/component/modals/sw-flow-action-detail-modal/sw-flow-action-detail-modal.scss"], "names": ["_Shopware", "Shopware", "Component", "Mixin", "Criteria", "Data", "_Component$getCompone", "getComponentHelper", "mapGetters", "mapState", "wrapComponentConfig", "template", "inject", "mixins", "getByName", "props", "sequence", "type", "Object", "default", "appFlowActions", "Array", "data", "sequenceTree", "actionsTrueCase", "actionsFalseCase", "isLoading", "created", "this", "createdComponent", "computed", "_objectSpread", "flowSequenceRepository", "repositoryFactory", "create", "flowSequenceCriteria", "criteria", "addAssociation", "addSorting", "sort", "getAssociation", "flowSequenceCriteriaChildren", "parentCriteria", "fromCriteria", "setLimit", "associations", "push", "association", "delayConstant", "flowBuilderService", "getActionName", "isActionDetail", "_this$sequenceTree", "length", "actionName", "conditionName", "_this$sequenceTree2", "_this$sequenceTree$fi", "first", "rule", "name", "getActionTitle", "_this$sequenceTree3", "$tc", "methods", "getDetail", "_this", "get", "delaySequenceId", "Context", "api", "then", "result", "children", "catch", "createNotificationError", "message", "finally", "actionCases", "_this$sequenceTree4", "_this$sequenceTree$", "trueCase", "arguments", "undefined", "filter", "action", "getActionDescriptions", "config", "getDelayDescription", "appActions", "customerGroups", "customFieldSets", "customFields", "stateMachineState", "documentTypes", "mailTemplates", "unit", "getTimeLabel", "value", "concat", "number", "onCloseModal", "$emit", "listToStyles", "parentId", "list", "styles", "newStyles", "i", "item", "id", "part", "css", "media", "sourceMap", "parts", "hasDocument", "document", "DEBUG", "Error", "stylesInDom", "head", "getElementsByTagName", "singletonElement", "singletonCounter", "isProduction", "noop", "options", "ssrIdKey", "isOldIE", "navigator", "test", "userAgent", "toLowerCase", "addStylesClient", "_isProduction", "_options", "addStylesToDom", "newList", "<PERSON><PERSON><PERSON><PERSON>", "domStyle", "refs", "j", "addStyle", "createStyleElement", "styleElement", "createElement", "append<PERSON><PERSON><PERSON>", "obj", "update", "remove", "querySelector", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "styleIndex", "applyToSingletonTag", "bind", "applyToTag", "newObj", "textStore", "replaceText", "index", "replacement", "Boolean", "join", "styleSheet", "cssText", "cssNode", "createTextNode", "childNodes", "insertBefore", "setAttribute", "ssrId", "sources", "btoa", "unescape", "encodeURIComponent", "JSON", "stringify", "<PERSON><PERSON><PERSON><PERSON>", "content", "__esModule", "module", "locals", "exports", "add"], "mappings": "45CAQA,IAAAA,EAA6BC,SAArBC,EAASF,EAATE,UAAWC,EAAKH,EAALG,MACXC,EAAaH,SAASI,KAAtBD,SACRE,EAAiCJ,EAAUK,qBAAnCC,EAAUF,EAAVE,WAAYC,EAAQH,EAARG,SAKLP,YAAUQ,oBAAoB,CACzCC,SChBW,o4IDkBXC,OAAQ,CAAC,qBAAsB,qBAE/BC,OAAQ,CACJV,EAAMW,UAAU,gBAChBX,EAAMW,UAAU,sBAGpBC,MAAO,CACHC,SAAU,CACNC,KAAMC,OACNC,QAAS,MAGbC,eAAgB,CACZH,KAAMI,MACNF,QAAS,KAIjBG,KAAI,WAMA,MAAO,CACHC,aAAc,GACdC,gBAAiB,GACjBC,iBAAkB,GAClBC,WAAW,IAInBC,QAAO,WACHC,KAAKC,oBAGTC,SAAQC,IAAA,CACJC,uBAAsB,WAClB,OAAOJ,KAAKK,kBAAkBC,OAAO,kBAGzCC,qBAAoB,WAChB,IAAMC,EAAW,IAAIhC,EAOrB,OANAgC,EAASC,eAAe,YACxBD,EAASC,eAAe,QACxBD,EAASE,WAAWlC,EAASmC,KAAK,WAAY,QAC9CH,EAASI,eAAe,YAAYH,eAAe,QACnDD,EAASI,eAAe,YAAYF,WAAWlC,EAASmC,KAAK,WAAY,QAElEH,GAGXK,6BAA4B,WACxB,IAAMC,EAAiBtC,EAASuC,aAAaf,KAAKO,sBAAsBS,SAAS,GAOjF,OANAF,EAAeJ,WAAWlC,EAASmC,KAAK,WAAY,QACpDG,EAAeG,aAAaC,KAAK,CAC7BC,YAAa,WACbX,SAAUhC,EAASuC,aAAaf,KAAKO,wBAGlCO,GAGXM,cAAa,WACT,OAAOpB,KAAKqB,mBAAmBC,cAAc,UAGjDC,eAAc,WAAa,IAADC,EACtB,OAAwB,QAAjBA,EAAAxB,KAAKL,oBAAY,IAAA6B,OAAA,EAAjBA,EAAmBC,SAAU,GAAKzB,KAAKL,aAAa,GAAG+B,YAGlEC,cAAa,WAAY,IAADC,EAAAC,EACpB,OAAsB,QAAlBD,EAAC5B,KAAKL,oBAAY,IAAAiC,GAAjBA,EAAmBH,SAIa,QAA9BI,EAAA7B,KAAKL,aAAamC,QAAQC,YAAI,IAAAF,OAAA,EAA9BA,EAAgCG,OAH5B,IAMfC,eAAc,WAAY,IAADC,EACrB,OAAsB,QAAlBA,EAAClC,KAAKL,oBAAY,IAAAuC,GAAjBA,EAAmBT,OAIFzB,KAAKL,aAAamC,QACtBJ,aAAe1B,KAAKoB,cAC3BpB,KAAKmC,IAAI,wCAGbnC,KAAKmC,IAAI,0CARL,KAWZtD,EACC,cACA,CACI,oBACA,gBACA,gBACA,iBACA,kBACA,kBAGLD,EACC,cAAe,CAAC,gBAIxBwD,QAAS,CACLnC,iBAAgB,WACZD,KAAKqC,aAGTA,UAAS,WAAU,IAADC,EAAA,KACdtC,KAAKF,WAAY,EACjBE,KAAKI,uBAAuBmC,IAAIvC,KAAKZ,SAASoD,gBAAiBnE,SAASoE,QAAQC,IAAK1C,KAAKa,8BACrF8B,MAAK,SAACC,GACHN,EAAK3C,aAAeiD,aAAM,EAANA,EAAQC,YAC7BC,OAAM,WACTR,EAAKS,wBAAwB,CACzBC,QAASV,EAAKH,IAAI,mDAEvBc,SAAQ,WACPX,EAAKxC,WAAY,MAIzBoD,YAAW,WAAuB,IAADC,EAAAC,EAArBC,IAAQC,UAAA7B,OAAA,QAAA8B,IAAAD,UAAA,KAAAA,UAAA,GAChB,OAAsB,QAAlBH,EAACnD,KAAKL,oBAAY,IAAAwD,GAAjBA,EAAmB1B,QAA+B,QAArB2B,EAACpD,KAAKL,aAAa,UAAE,IAAAyD,GAApBA,EAAsBP,SAClD7C,KAAKL,aAAa,GAAGkD,SAASW,QAAO,SAAAC,GAAM,OAAIA,EAAOJ,WAAaA,KADA,IAI9EK,sBAAqB,SAACtE,GAClB,IAAQsC,EAAuBtC,EAAvBsC,WAAYiC,EAAWvE,EAAXuE,OACpB,GAAIjC,IAAe1B,KAAKoB,cACpB,OAAOpB,KAAK4D,oBAAoBD,GAGpC,IAAMjE,EAAO,CACTmE,WAAY7D,KAAK6D,WACjBC,eAAgB9D,KAAK8D,eACrBC,gBAAiB/D,KAAK+D,gBACtBC,aAAchE,KAAKgE,aACnBC,kBAAmBjE,KAAKiE,kBACxBC,cAAelE,KAAKkE,cACpBC,cAAenE,KAAKmE,eAGxB,OAAOnE,KAAKqB,mBAAmBqC,sBAAsBhE,EAAMN,EAAUY,OAGzE4D,oBAAmB,SAACD,GAChB,IAAMS,EAAOpE,KAAKqE,aAAaV,EAAc,MAAE,GAAGtE,KAAMsE,EAAc,MAAE,GAAGW,OAC3E,MAAM,GAANC,OAAUvE,KAAKmC,IAAI,0CAAyC,MAAAoC,OAAKZ,EAAc,MAAE,GAAGW,MAAK,KAAAC,OAAIH,IAGjGC,aAAY,SAAChF,EAAiBmF,GAC1B,OAAQnF,GAEJ,IAAK,OACD,OAAOW,KAAKmC,IAAI,gCAAiCqC,GAGrD,IAAK,MACD,OAAOxE,KAAKmC,IAAI,+BAAgCqC,GAGpD,IAAK,OACD,OAAOxE,KAAKmC,IAAI,gCAAiCqC,GAGrD,IAAK,QACD,OAAOxE,KAAKmC,IAAI,iCAAkCqC,GAGtD,QAAS,MAAO,KAIxBC,aAAY,WACRzE,KAAK0E,MAAM,oB,kCEnMR,SAASC,EAAcC,EAAUC,GAG9C,IAFA,IAAIC,EAAS,GACTC,EAAY,GACPC,EAAI,EAAGA,EAAIH,EAAKpD,OAAQuD,IAAK,CACpC,IAAIC,EAAOJ,EAAKG,GACZE,EAAKD,EAAK,GAIVE,EAAO,CACTD,GAAIN,EAAW,IAAMI,EACrBI,IALQH,EAAK,GAMbI,MALUJ,EAAK,GAMfK,UALcL,EAAK,IAOhBF,EAAUG,GAGbH,EAAUG,GAAIK,MAAMrE,KAAKiE,GAFzBL,EAAO5D,KAAK6D,EAAUG,GAAM,CAAEA,GAAIA,EAAIK,MAAO,CAACJ,KAKlD,OAAOL,E,+CCjBT,IAAIU,EAAkC,oBAAbC,SAEzB,GAAqB,oBAAVC,OAAyBA,QAC7BF,EACH,MAAM,IAAIG,MACV,2JAkBJ,IAAIC,EAAc,GAQdC,EAAOL,IAAgBC,SAASI,MAAQJ,SAASK,qBAAqB,QAAQ,IAC9EC,EAAmB,KACnBC,EAAmB,EACnBC,GAAe,EACfC,EAAO,aACPC,EAAU,KACVC,EAAW,kBAIXC,EAA+B,oBAAdC,WAA6B,eAAeC,KAAKD,UAAUE,UAAUC,eAE3E,SAASC,EAAiB9B,EAAUC,EAAM8B,EAAeC,GACtEX,EAAeU,EAEfR,EAAUS,GAAY,GAEtB,IAAI9B,EAASH,EAAaC,EAAUC,GAGpC,OAFAgC,EAAe/B,GAER,SAAiBgC,GAEtB,IADA,IAAIC,EAAY,GACP/B,EAAI,EAAGA,EAAIF,EAAOrD,OAAQuD,IAAK,CACtC,IAAIC,EAAOH,EAAOE,IACdgC,EAAWpB,EAAYX,EAAKC,KACvB+B,OACTF,EAAU7F,KAAK8F,GAEbF,EAEFD,EADA/B,EAASH,EAAaC,EAAUkC,IAGhChC,EAAS,GAEX,IAASE,EAAI,EAAGA,EAAI+B,EAAUtF,OAAQuD,IAAK,CACzC,IAAIgC,EACJ,GAAsB,KADlBA,EAAWD,EAAU/B,IACZiC,KAAY,CACvB,IAAK,IAAIC,EAAI,EAAGA,EAAIF,EAASzB,MAAM9D,OAAQyF,IACzCF,EAASzB,MAAM2B,YAEVtB,EAAYoB,EAAS9B,OAMpC,SAAS2B,EAAgB/B,GACvB,IAAK,IAAIE,EAAI,EAAGA,EAAIF,EAAOrD,OAAQuD,IAAK,CACtC,IAAIC,EAAOH,EAAOE,GACdgC,EAAWpB,EAAYX,EAAKC,IAChC,GAAI8B,EAAU,CACZA,EAASC,OACT,IAAK,IAAIC,EAAI,EAAGA,EAAIF,EAASzB,MAAM9D,OAAQyF,IACzCF,EAASzB,MAAM2B,GAAGjC,EAAKM,MAAM2B,IAE/B,KAAOA,EAAIjC,EAAKM,MAAM9D,OAAQyF,IAC5BF,EAASzB,MAAMrE,KAAKiG,EAASlC,EAAKM,MAAM2B,KAEtCF,EAASzB,MAAM9D,OAASwD,EAAKM,MAAM9D,SACrCuF,EAASzB,MAAM9D,OAASwD,EAAKM,MAAM9D,YAEhC,CACL,IAAI8D,EAAQ,GACZ,IAAS2B,EAAI,EAAGA,EAAIjC,EAAKM,MAAM9D,OAAQyF,IACrC3B,EAAMrE,KAAKiG,EAASlC,EAAKM,MAAM2B,KAEjCtB,EAAYX,EAAKC,IAAM,CAAEA,GAAID,EAAKC,GAAI+B,KAAM,EAAG1B,MAAOA,KAK5D,SAAS6B,IACP,IAAIC,EAAe5B,SAAS6B,cAAc,SAG1C,OAFAD,EAAahI,KAAO,WACpBwG,EAAK0B,YAAYF,GACVA,EAGT,SAASF,EAAUK,GACjB,IAAIC,EAAQC,EACRL,EAAe5B,SAASkC,cAAc,SAAWvB,EAAW,MAAQoB,EAAItC,GAAK,MAEjF,GAAImC,EAAc,CAChB,GAAIpB,EAGF,OAAOC,EAOPmB,EAAaO,WAAWC,YAAYR,GAIxC,GAAIhB,EAAS,CAEX,IAAIyB,EAAa9B,IACjBqB,EAAetB,IAAqBA,EAAmBqB,KACvDK,EAASM,EAAoBC,KAAK,KAAMX,EAAcS,GAAY,GAClEJ,EAASK,EAAoBC,KAAK,KAAMX,EAAcS,GAAY,QAGlET,EAAeD,IACfK,EAASQ,EAAWD,KAAK,KAAMX,GAC/BK,EAAS,WACPL,EAAaO,WAAWC,YAAYR,IAMxC,OAFAI,EAAOD,GAEA,SAAsBU,GAC3B,GAAIA,EAAQ,CACV,GAAIA,EAAO9C,MAAQoC,EAAIpC,KACnB8C,EAAO7C,QAAUmC,EAAInC,OACrB6C,EAAO5C,YAAckC,EAAIlC,UAC3B,OAEFmC,EAAOD,EAAMU,QAEbR,KAKN,IACMS,EADFC,GACED,EAAY,GAET,SAAUE,EAAOC,GAEtB,OADAH,EAAUE,GAASC,EACZH,EAAU3E,OAAO+E,SAASC,KAAK,QAI1C,SAAST,EAAqBV,EAAcgB,EAAOX,EAAQF,GACzD,IAAIpC,EAAMsC,EAAS,GAAKF,EAAIpC,IAE5B,GAAIiC,EAAaoB,WACfpB,EAAaoB,WAAWC,QAAUN,EAAYC,EAAOjD,OAChD,CACL,IAAIuD,EAAUlD,SAASmD,eAAexD,GAClCyD,EAAaxB,EAAawB,WAC1BA,EAAWR,IAAQhB,EAAaQ,YAAYgB,EAAWR,IACvDQ,EAAWpH,OACb4F,EAAayB,aAAaH,EAASE,EAAWR,IAE9ChB,EAAaE,YAAYoB,IAK/B,SAASV,EAAYZ,EAAcG,GACjC,IAAIpC,EAAMoC,EAAIpC,IACVC,EAAQmC,EAAInC,MACZC,EAAYkC,EAAIlC,UAiBpB,GAfID,GACFgC,EAAa0B,aAAa,QAAS1D,GAEjCc,EAAQ6C,OACV3B,EAAa0B,aAAa3C,EAAUoB,EAAItC,IAGtCI,IAGFF,GAAO,mBAAqBE,EAAU2D,QAAQ,GAAK,MAEnD7D,GAAO,uDAAyD8D,KAAKC,SAASC,mBAAmBC,KAAKC,UAAUhE,MAAgB,OAG9H+B,EAAaoB,WACfpB,EAAaoB,WAAWC,QAAUtD,MAC7B,CACL,KAAOiC,EAAakC,YAClBlC,EAAaQ,YAAYR,EAAakC,YAExClC,EAAaE,YAAY9B,SAASmD,eAAexD,O,qBCxNrD,IAAIoE,EAAU,EAAQ,QACnBA,EAAQC,aAAYD,EAAUA,EAAQjK,SACnB,iBAAZiK,IAAsBA,EAAU,CAAC,CAACE,EAAO1E,EAAIwE,EAAS,MAC7DA,EAAQG,SAAQD,EAAOE,QAAUJ,EAAQG,SAG/BE,EADH,EAAQ,QAA2LtK,SAC5L,WAAYiK,GAAS,EAAM", "file": "static/js/3.js", "sourcesContent": ["import type {PropType} from 'vue';\nimport template from './sw-flow-action-detail-modal.html.twig';\nimport './sw-flow-action-detail-modal.scss';\nimport {ActionConfig, ActionOption, DelayType} from '../../../../../type/types';\nimport type RepositoryType from 'src/core/data/repository.data';\nimport type CriteriaType from 'src/core/data/criteria.data';\nimport type {Entity} from '@shopware-ag/admin-extension-sdk/es/data/_internals/Entity';\n\nconst { Component, Mixin } = Shopware;\nconst { Criteria } = Shopware.Data;\nconst { mapGetters, mapState } = Component.getComponentHelper();\n\n/**\n * @package services-settings\n */\nexport default Component.wrapComponentConfig({\n    template,\n\n    inject: ['flowBuilderService', 'repositoryFactory'],\n\n    mixins: [\n        Mixin.getByName('notification'),\n        Mixin.getByName('sw-inline-snippet'),\n    ],\n\n    props: {\n        sequence: {\n            type: Object as PropType<Entity<'flow_sequence'>>,\n            default: null\n        },\n\n        appFlowActions: {\n            type: Array as PropType<ActionOption>,\n            default: [],\n        },\n    },\n\n    data(): {\n        sequenceTree: [],\n        actionsTrueCase: [],\n        actionsFalseCase: [],\n        isLoading: boolean,\n    } {\n        return {\n            sequenceTree: [],\n            actionsTrueCase: [],\n            actionsFalseCase: [],\n            isLoading: false,\n        };\n    },\n\n    created() {\n        this.createdComponent();\n    },\n\n    computed: {\n        flowSequenceRepository(): RepositoryType<'flow_sequence'> {\n            return this.repositoryFactory.create('flow_sequence');\n        },\n\n        flowSequenceCriteria(): CriteriaType {\n            const criteria = new Criteria();\n            criteria.addAssociation('children');\n            criteria.addAssociation('rule');\n            criteria.addSorting(Criteria.sort('position', 'ASC'));\n            criteria.getAssociation('children').addAssociation('rule');\n            criteria.getAssociation('children').addSorting(Criteria.sort('position', 'ASC'));\n\n            return criteria;\n        },\n\n        flowSequenceCriteriaChildren(): CriteriaType {\n            const parentCriteria = Criteria.fromCriteria(this.flowSequenceCriteria).setLimit(1);\n            parentCriteria.addSorting(Criteria.sort('position', 'ASC'));\n            parentCriteria.associations.push({\n                association: 'children',\n                criteria: Criteria.fromCriteria(this.flowSequenceCriteria),\n            });\n\n            return parentCriteria;\n        },\n\n        delayConstant() {\n            return this.flowBuilderService.getActionName('DELAY');\n        },\n\n        isActionDetail(): boolean {\n            return this.sequenceTree?.length >= 1 && this.sequenceTree[0].actionName;\n        },\n\n        conditionName(): string {\n            if (!this.sequenceTree?.length) {\n                return '';\n            }\n\n            return this.sequenceTree.first().rule?.name || '';\n        },\n\n        getActionTitle(): string {\n            if (!this.sequenceTree?.length) {\n                return '';\n            }\n\n            const firstSequence = this.sequenceTree.first();\n            if (firstSequence.actionName === this.delayConstant) {\n                return this.$tc('sw-flow-delay.delay.itemDetail.delay')\n            }\n\n            return this.$tc('sw-flow-delay.delay.itemDetail.actions');\n        },\n\n        ...mapState(\n            'swFlowState',\n            [\n                'stateMachineState',\n                'documentTypes',\n                'mailTemplates',\n                'customerGroups',\n                'customFieldSets',\n                'customFields',\n            ],\n        ),\n        ...mapGetters(\n            'swFlowState', ['appActions'],\n        ),\n    },\n\n    methods: {\n        createdComponent(): void {\n            this.getDetail();\n        },\n\n        getDetail(): void {\n            this.isLoading = true;\n            this.flowSequenceRepository.get(this.sequence.delaySequenceId, Shopware.Context.api, this.flowSequenceCriteriaChildren)\n                .then((result) => {\n                    this.sequenceTree = result?.children;\n                }).catch(() => {\n                this.createNotificationError({\n                    message: this.$tc('sw-flow-delay.delay.list.fetchErrorMessage'),\n                });\n            }).finally(() => {\n                this.isLoading = false;\n            });\n        },\n\n        actionCases(trueCase = true): [] {\n            if (!this.sequenceTree?.length || !this.sequenceTree[0]?.children) return [];\n            return this.sequenceTree[0].children.filter(action => action.trueCase === trueCase);\n        },\n\n        getActionDescriptions(sequence: Entity<'flow_sequence'>): string {\n            const { actionName, config } = sequence;\n            if (actionName === this.delayConstant) {\n                return this.getDelayDescription(config);\n            }\n\n            const data = {\n                appActions: this.appActions,\n                customerGroups: this.customerGroups,\n                customFieldSets: this.customFieldSets,\n                customFields: this.customFields,\n                stateMachineState: this.stateMachineState,\n                documentTypes: this.documentTypes,\n                mailTemplates: this.mailTemplates,\n            };\n\n            return this.flowBuilderService.getActionDescriptions(data, sequence, this);\n        },\n\n        getDelayDescription(config: ActionConfig): string {\n            const unit = this.getTimeLabel(config['delay'][0].type, config['delay'][0].value);\n            return `${this.$tc('sw-flow-delay.delay.itemDetail.delayed')}: ${config['delay'][0].value} ${unit}`;\n        },\n\n        getTimeLabel(type: DelayType, number: number): string {\n            switch (type) {\n\n                case 'hour': {\n                    return this.$tc('sw-flow-delay.modal.labelHour', number);\n                }\n\n                case 'day': {\n                    return this.$tc('sw-flow-delay.modal.labelDay', number);\n                }\n\n                case 'week':{\n                    return this.$tc('sw-flow-delay.modal.labelWeek', number);\n                }\n\n                case 'month': {\n                    return this.$tc('sw-flow-delay.modal.labelMonth', number);\n                }\n\n                default: return '';\n            }\n        },\n\n        onCloseModal() {\n            this.$emit('modal-close');\n        },\n    },\n});\n", "export default \"<sw-modal\\n    class=\\\"sw-flow-action-detail-modal\\\"\\n    :title=\\\"$tc('sw-flow-delay.delay.itemDetail.details')\\\"\\n    :closable=\\\"false\\\"\\n    :is-loading=\\\"isLoading\\\"\\n    @modal-close=\\\"onCloseModal\\\"\\n>\\n    <sw-container\\n        v-if=\\\"isActionDetail\\\"\\n        class=\\\"sw-flow-action-detail-modal__actions\\\"\\n    >\\n        <ul>\\n            <span class=\\\"sw-flow-action-detail-modal__title\\\">{{ getActionTitle }}</span>\\n            <li v-for=\\\"(item, index) in sequenceTree\\\" :key=\\\"index\\\">\\n                <div class=\\\"sw-flow-action-detail-modal__action-name\\\">\\n                    <sw-flow-sequence-label\\n                        classes=\\\"sw-flow-action-detail-modal__label\\\"\\n                        :sequence=\\\"item\\\"\\n                        :app-flow-actions=\\\"appFlowActions\\\"\\n                    />\\n                </div>\\n                <div class=\\\"sw-flow-action-detail-modal__action-config\\\">\\n                    <span v-html=\\\"getActionDescriptions(item)\\\" />\\n                </div>\\n            </li>\\n        </ul>\\n    </sw-container>\\n\\n    <sw-container\\n        v-else\\n        class=\\\"sw-flow-action-detail-modal__condition\\\"\\n    >\\n        <ul>\\n            <span class=\\\"sw-flow-action-detail-modal__title\\\">\\n                {{ $tc('sw-flow-delay.delay.itemDetail.condition') }}\\n            </span>\\n            <li class=\\\"sw-flow-action-detail-modal__condition-label\\\">\\n                <sw-icon\\n                    class=\\\"sw-flow-action-detail-modal__condition-icon\\\"\\n                    size=\\\"14px\\\"\\n                    name=\\\"regular-rule-s\\\"\\n                />\\n                <span class=\\\"sw-flow-action-detail-modal__condition-name\\\">{{ conditionName }}</span>\\n            </li>\\n        </ul>\\n\\n        <div\\n            v-if=\\\"actionCases(true).length > 0\\\"\\n            class=\\\"sw-flow-action-detail-modal__case\\\"\\n        >\\n            <span class=\\\"sw-flow-action-detail-modal__case-title\\\">\\n                {{ $tc('sw-flow-delay.delay.itemDetail.conditionIf') }}\\n            </span>\\n            <sw-label\\n                appearance=\\\"pill\\\"\\n                size=\\\"medium\\\"\\n                class=\\\"sw-flow-action-detail-modal__true-label\\\"\\n            >\\n                {{ $tc('sw-flow.detail.sequence.labelTrue') }}\\n            </sw-label>\\n\\n            <ul>\\n                <li v-for=\\\"(item, index) in actionCases(true)\\\" :key=\\\"index\\\">\\n                    <sw-flow-sequence-label\\n                        classes=\\\"sw-flow-action-detail-modal__label\\\"\\n                        :sequence=\\\"item\\\"\\n                        :app-flow-actions=\\\"appFlowActions\\\"\\n                        @click=\\\"\\\"\\n                    />\\n\\n                    <div class=\\\"sw-flow-action-detail-modal__action-config\\\">\\n                        <span v-html=\\\"getActionDescriptions(item)\\\" />\\n                    </div>\\n                </li>\\n            </ul>\\n        </div>\\n\\n        <div\\n            v-if=\\\"actionCases(false).length > 0\\\"\\n            class=\\\"sw-flow-action-detail-modal__case\\\"\\n        >\\n            <span class=\\\"sw-flow-action-detail-modal__case-title\\\">\\n                {{ $tc('sw-flow-delay.delay.itemDetail.conditionIf') }}\\n            </span>\\n            <sw-label\\n                appearance=\\\"pill\\\"\\n                size=\\\"medium\\\"\\n                class=\\\"sw-flow-action-detail-modal__false-label\\\"\\n            >\\n                {{ $tc('sw-flow.detail.sequence.labelFalse') }}\\n            </sw-label>\\n\\n            <ul>\\n                <li v-for=\\\"(item, index) in actionCases(false)\\\" :key=\\\"index\\\">\\n                    <sw-flow-sequence-label\\n                        classes=\\\"sw-flow-action-detail-modal__label\\\"\\n                        :sequence=\\\"item\\\"\\n                        :app-flow-actions=\\\"appFlowActions\\\"\\n                        @click=\\\"\\\"\\n                    />\\n\\n                    <div class=\\\"sw-flow-action-detail-modal__action-config\\\">\\n                        <span v-html=\\\"getActionDescriptions(item)\\\" />\\n                    </div>\\n                </li>\\n            </ul>\\n        </div>\\n    </sw-container>\\n\\n    <template #modal-footer>\\n        {% block swag_flow_detail_action_modal_cancel_button %}\\n        <sw-button\\n            size=\\\"small\\\"\\n            variant=\\\"primary\\\"\\n            class=\\\"sw-flow-action-detail-modal__button_close\\\"\\n            @click=\\\"onCloseModal\\\"\\n        >\\n            {{ $tc('global.default.close') }}\\n        </sw-button>\\n        {% endblock %}\\n    </template>\\n</sw-modal>\\n\";", "/**\n * Translates the list format produced by css-loader into something\n * easier to manipulate.\n */\nexport default function listToStyles (parentId, list) {\n  var styles = []\n  var newStyles = {}\n  for (var i = 0; i < list.length; i++) {\n    var item = list[i]\n    var id = item[0]\n    var css = item[1]\n    var media = item[2]\n    var sourceMap = item[3]\n    var part = {\n      id: parentId + ':' + i,\n      css: css,\n      media: media,\n      sourceMap: sourceMap\n    }\n    if (!newStyles[id]) {\n      styles.push(newStyles[id] = { id: id, parts: [part] })\n    } else {\n      newStyles[id].parts.push(part)\n    }\n  }\n  return styles\n}\n", "/*\n  MIT License http://www.opensource.org/licenses/mit-license.php\n  Author <PERSON> @sokra\n  Modified by <PERSON> @yyx990803\n*/\n\nimport listToStyles from './listToStyles'\n\nvar hasDocument = typeof document !== 'undefined'\n\nif (typeof DEBUG !== 'undefined' && DEBUG) {\n  if (!hasDocument) {\n    throw new Error(\n    'vue-style-loader cannot be used in a non-browser environment. ' +\n    \"Use { target: 'node' } in your Webpack config to indicate a server-rendering environment.\"\n  ) }\n}\n\n/*\ntype StyleObject = {\n  id: number;\n  parts: Array<StyleObjectPart>\n}\n\ntype StyleObjectPart = {\n  css: string;\n  media: string;\n  sourceMap: ?string\n}\n*/\n\nvar stylesInDom = {/*\n  [id: number]: {\n    id: number,\n    refs: number,\n    parts: Array<(obj?: StyleObjectPart) => void>\n  }\n*/}\n\nvar head = hasDocument && (document.head || document.getElementsByTagName('head')[0])\nvar singletonElement = null\nvar singletonCounter = 0\nvar isProduction = false\nvar noop = function () {}\nvar options = null\nvar ssrIdKey = 'data-vue-ssr-id'\n\n// Force single-tag solution on IE6-9, which has a hard limit on the # of <style>\n// tags it will allow on a page\nvar isOldIE = typeof navigator !== 'undefined' && /msie [6-9]\\b/.test(navigator.userAgent.toLowerCase())\n\nexport default function addStylesClient (parentId, list, _isProduction, _options) {\n  isProduction = _isProduction\n\n  options = _options || {}\n\n  var styles = listToStyles(parentId, list)\n  addStylesToDom(styles)\n\n  return function update (newList) {\n    var mayRemove = []\n    for (var i = 0; i < styles.length; i++) {\n      var item = styles[i]\n      var domStyle = stylesInDom[item.id]\n      domStyle.refs--\n      mayRemove.push(domStyle)\n    }\n    if (newList) {\n      styles = listToStyles(parentId, newList)\n      addStylesToDom(styles)\n    } else {\n      styles = []\n    }\n    for (var i = 0; i < mayRemove.length; i++) {\n      var domStyle = mayRemove[i]\n      if (domStyle.refs === 0) {\n        for (var j = 0; j < domStyle.parts.length; j++) {\n          domStyle.parts[j]()\n        }\n        delete stylesInDom[domStyle.id]\n      }\n    }\n  }\n}\n\nfunction addStylesToDom (styles /* Array<StyleObject> */) {\n  for (var i = 0; i < styles.length; i++) {\n    var item = styles[i]\n    var domStyle = stylesInDom[item.id]\n    if (domStyle) {\n      domStyle.refs++\n      for (var j = 0; j < domStyle.parts.length; j++) {\n        domStyle.parts[j](item.parts[j])\n      }\n      for (; j < item.parts.length; j++) {\n        domStyle.parts.push(addStyle(item.parts[j]))\n      }\n      if (domStyle.parts.length > item.parts.length) {\n        domStyle.parts.length = item.parts.length\n      }\n    } else {\n      var parts = []\n      for (var j = 0; j < item.parts.length; j++) {\n        parts.push(addStyle(item.parts[j]))\n      }\n      stylesInDom[item.id] = { id: item.id, refs: 1, parts: parts }\n    }\n  }\n}\n\nfunction createStyleElement () {\n  var styleElement = document.createElement('style')\n  styleElement.type = 'text/css'\n  head.appendChild(styleElement)\n  return styleElement\n}\n\nfunction addStyle (obj /* StyleObjectPart */) {\n  var update, remove\n  var styleElement = document.querySelector('style[' + ssrIdKey + '~=\"' + obj.id + '\"]')\n\n  if (styleElement) {\n    if (isProduction) {\n      // has SSR styles and in production mode.\n      // simply do nothing.\n      return noop\n    } else {\n      // has SSR styles but in dev mode.\n      // for some reason Chrome can't handle source map in server-rendered\n      // style tags - source maps in <style> only works if the style tag is\n      // created and inserted dynamically. So we remove the server rendered\n      // styles and inject new ones.\n      styleElement.parentNode.removeChild(styleElement)\n    }\n  }\n\n  if (isOldIE) {\n    // use singleton mode for IE9.\n    var styleIndex = singletonCounter++\n    styleElement = singletonElement || (singletonElement = createStyleElement())\n    update = applyToSingletonTag.bind(null, styleElement, styleIndex, false)\n    remove = applyToSingletonTag.bind(null, styleElement, styleIndex, true)\n  } else {\n    // use multi-style-tag mode in all other cases\n    styleElement = createStyleElement()\n    update = applyToTag.bind(null, styleElement)\n    remove = function () {\n      styleElement.parentNode.removeChild(styleElement)\n    }\n  }\n\n  update(obj)\n\n  return function updateStyle (newObj /* StyleObjectPart */) {\n    if (newObj) {\n      if (newObj.css === obj.css &&\n          newObj.media === obj.media &&\n          newObj.sourceMap === obj.sourceMap) {\n        return\n      }\n      update(obj = newObj)\n    } else {\n      remove()\n    }\n  }\n}\n\nvar replaceText = (function () {\n  var textStore = []\n\n  return function (index, replacement) {\n    textStore[index] = replacement\n    return textStore.filter(Boolean).join('\\n')\n  }\n})()\n\nfunction applyToSingletonTag (styleElement, index, remove, obj) {\n  var css = remove ? '' : obj.css\n\n  if (styleElement.styleSheet) {\n    styleElement.styleSheet.cssText = replaceText(index, css)\n  } else {\n    var cssNode = document.createTextNode(css)\n    var childNodes = styleElement.childNodes\n    if (childNodes[index]) styleElement.removeChild(childNodes[index])\n    if (childNodes.length) {\n      styleElement.insertBefore(cssNode, childNodes[index])\n    } else {\n      styleElement.appendChild(cssNode)\n    }\n  }\n}\n\nfunction applyToTag (styleElement, obj) {\n  var css = obj.css\n  var media = obj.media\n  var sourceMap = obj.sourceMap\n\n  if (media) {\n    styleElement.setAttribute('media', media)\n  }\n  if (options.ssrId) {\n    styleElement.setAttribute(ssrIdKey, obj.id)\n  }\n\n  if (sourceMap) {\n    // https://developer.chrome.com/devtools/docs/javascript-debugging\n    // this makes source maps inside style tags work properly in Chrome\n    css += '\\n/*# sourceURL=' + sourceMap.sources[0] + ' */'\n    // http://stackoverflow.com/a/26603875\n    css += '\\n/*# sourceMappingURL=data:application/json;base64,' + btoa(unescape(encodeURIComponent(JSON.stringify(sourceMap)))) + ' */'\n  }\n\n  if (styleElement.styleSheet) {\n    styleElement.styleSheet.cssText = css\n  } else {\n    while (styleElement.firstChild) {\n      styleElement.removeChild(styleElement.firstChild)\n    }\n    styleElement.appendChild(document.createTextNode(css))\n  }\n}\n", "// style-loader: Adds some css to the DOM by adding a <style> tag\n\n// load the styles\nvar content = require(\"!!../../../../../../../../../../../../../../../builds/shopware/6/product/commercial/src/Administration/Resources/app/administration/node_modules/mini-css-extract-plugin/dist/loader.js??ref--15-1!../../../../../../../../../../../../../../../builds/shopware/6/product/commercial/src/Administration/Resources/app/administration/node_modules/css-loader/dist/cjs.js??ref--15-2!../../../../../../../../../../../../../../../builds/shopware/6/product/commercial/src/Administration/Resources/app/administration/node_modules/sass-loader/dist/cjs.js??ref--15-3!./sw-flow-action-detail-modal.scss\");\nif(content.__esModule) content = content.default;\nif(typeof content === 'string') content = [[module.id, content, '']];\nif(content.locals) module.exports = content.locals;\n// add the styles to the DOM\nvar add = require(\"!../../../../../../../../../../../../../../../builds/shopware/6/product/commercial/src/Administration/Resources/app/administration/node_modules/vue-style-loader/lib/addStylesClient.js\").default\nvar update = add(\"6ec89b94\", content, true, {});"], "sourceRoot": ""}
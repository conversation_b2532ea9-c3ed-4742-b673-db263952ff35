{"version": 3, "sources": ["webpack:////tmp/extension2400992207/SwagCommercial/src/FlowBuilder/DelayedFlowAction/Resources/app/administration/src/constant/sw-flow-delay.constant.ts", "webpack:////tmp/extension2400992207/SwagCommercial/src/FlowBuilder/DelayedFlowAction/Resources/app/administration/src/module/sw-flow/component/sw-flow-sequence-condition/sw-flow-sequence-condition.html.twig", "webpack:////tmp/extension2400992207/SwagCommercial/src/FlowBuilder/DelayedFlowAction/Resources/app/administration/src/module/sw-flow/component/sw-flow-sequence-condition/index.ts"], "names": ["SEQUENCE_TYPES", "DELAY_OPTIONS", "value", "label", "CUSTOM_TIME", "_regeneratorRuntime", "exports", "Op", "Object", "prototype", "hasOwn", "hasOwnProperty", "defineProperty", "obj", "key", "desc", "$Symbol", "Symbol", "iteratorSymbol", "iterator", "asyncIteratorSymbol", "asyncIterator", "toStringTagSymbol", "toStringTag", "define", "enumerable", "configurable", "writable", "err", "wrap", "innerFn", "outerFn", "self", "tryLocsList", "protoGenerator", "Generator", "generator", "create", "context", "Context", "makeInvokeMethod", "tryCatch", "fn", "arg", "type", "call", "ContinueSentinel", "GeneratorFunction", "GeneratorFunctionPrototype", "IteratorPrototype", "getProto", "getPrototypeOf", "NativeIteratorPrototype", "values", "Gp", "defineIteratorMethods", "for<PERSON>ach", "method", "_invoke", "AsyncIterator", "PromiseImpl", "invoke", "resolve", "reject", "record", "result", "_typeof", "__await", "then", "unwrapped", "error", "previousPromise", "callInvokeWithMethodAndArg", "state", "Error", "doneResult", "delegate", "delegate<PERSON><PERSON><PERSON>", "maybeInvokeDelegate", "sent", "_sent", "dispatchException", "abrupt", "done", "methodName", "undefined", "return", "TypeError", "info", "resultName", "next", "nextLoc", "pushTryEntry", "locs", "entry", "tryLoc", "catchLoc", "finallyLoc", "afterLoc", "tryEntries", "push", "resetTryEntry", "completion", "reset", "iterable", "iteratorMethod", "isNaN", "length", "i", "displayName", "isGeneratorFunction", "gen<PERSON>un", "ctor", "constructor", "name", "mark", "setPrototypeOf", "__proto__", "awrap", "async", "Promise", "iter", "keys", "val", "object", "reverse", "pop", "skip<PERSON>emp<PERSON><PERSON><PERSON>", "prev", "char<PERSON>t", "slice", "stop", "rootRecord", "rval", "exception", "handle", "loc", "caught", "hasCatch", "hasFinally", "finallyEntry", "complete", "finish", "catch", "thrown", "<PERSON><PERSON><PERSON>", "asyncGeneratorStep", "gen", "_next", "_throw", "_asyncToGenerator", "args", "arguments", "apply", "ownKeys", "enumerableOnly", "getOwnPropertySymbols", "symbols", "filter", "sym", "getOwnPropertyDescriptor", "_objectSpread", "target", "source", "_defineProperty", "getOwnPropertyDescriptors", "defineProperties", "input", "hint", "prim", "toPrimitive", "res", "String", "Number", "_toPrimitive", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "_Shopware", "Shopware", "Component", "State", "Criteria", "Data", "utils", "Utils", "mapState", "getComponentHelper", "wrapComponentConfig", "template", "inject", "computed", "delayConstant", "this", "flowBuilderService", "getActionName", "delayedActionsRepository", "repositoryFactory", "delayedActionsCriteria", "criteria", "addFilter", "equalsAny", "getDelayIds", "sequence", "id", "watch", "showWarningModal", "CONDITION", "actionType", "$super", "rule", "methods", "addDelayAction", "trueCase", "sequenceRepository", "newSequence", "parentId", "displayGroup", "actionName", "ruleId", "config", "position", "createId", "assign", "commit", "<PERSON><PERSON><PERSON><PERSON>", "sequences", "parentSequence", "find", "item", "_this", "delayedSequences", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "currentId", "arr", "childSequences", "getDelayedActionData", "_this2", "_callee", "_context", "search", "t0", "onRuleChange", "localStorage", "getItem", "enabled", "removeCondition", "_this3", "_callee2", "_context2"], "mappings": ";+IAAA,sGAAO,IAAWA,EAAc,SAAdA,GAAc,OAAdA,EAAc,gBAAdA,EAAc,sBAAdA,EAAc,4BAAdA,EAAc,KAMnBC,EAAgB,CACzB,CACIC,MAAO,OACPC,MAAO,iCAEX,CACID,MAAO,MACPC,MAAO,gCAEX,CACID,MAAO,OACPC,MAAO,iCAEX,CACID,MAAO,QACPC,MAAO,kCAEX,CACID,MAAO,SACPC,MAAO,oCAIFC,EAAc,U,yCC7BZ,I,8PCCfC,EAAA,kBAAAC,GAAA,IAAAA,EAAA,GAAAC,EAAAC,OAAAC,UAAAC,EAAAH,EAAAI,eAAAC,EAAAJ,OAAAI,gBAAA,SAAAC,EAAAC,EAAAC,GAAAF,EAAAC,GAAAC,EAAAb,OAAAc,EAAA,mBAAAC,cAAA,GAAAC,EAAAF,EAAAG,UAAA,aAAAC,EAAAJ,EAAAK,eAAA,kBAAAC,EAAAN,EAAAO,aAAA,yBAAAC,EAAAX,EAAAC,EAAAZ,GAAA,OAAAM,OAAAI,eAAAC,EAAAC,EAAA,CAAAZ,QAAAuB,YAAA,EAAAC,cAAA,EAAAC,UAAA,IAAAd,EAAAC,GAAA,IAAAU,EAAA,aAAAI,GAAAJ,EAAA,SAAAX,EAAAC,EAAAZ,GAAA,OAAAW,EAAAC,GAAAZ,GAAA,SAAA2B,EAAAC,EAAAC,EAAAC,EAAAC,GAAA,IAAAC,EAAAH,KAAAtB,qBAAA0B,EAAAJ,EAAAI,EAAAC,EAAA5B,OAAA6B,OAAAH,EAAAzB,WAAA6B,EAAA,IAAAC,EAAAN,GAAA,WAAArB,EAAAwB,EAAA,WAAAlC,MAAAsC,EAAAV,EAAAE,EAAAM,KAAAF,EAAA,SAAAK,EAAAC,EAAA7B,EAAA8B,GAAA,WAAAC,KAAA,SAAAD,IAAAD,EAAAG,KAAAhC,EAAA8B,IAAA,MAAAf,GAAA,OAAAgB,KAAA,QAAAD,IAAAf,IAAAtB,EAAAuB,OAAA,IAAAiB,EAAA,YAAAX,KAAA,SAAAY,KAAA,SAAAC,KAAA,IAAAC,EAAA,GAAAzB,EAAAyB,EAAA/B,GAAA,8BAAAgC,EAAA1C,OAAA2C,eAAAC,EAAAF,OAAAG,EAAA,MAAAD,OAAA7C,GAAAG,EAAAmC,KAAAO,EAAAlC,KAAA+B,EAAAG,GAAA,IAAAE,EAAAN,EAAAvC,UAAA0B,EAAA1B,UAAAD,OAAA6B,OAAAY,GAAA,SAAAM,EAAA9C,GAAA,0BAAA+C,SAAA,SAAAC,GAAAjC,EAAAf,EAAAgD,GAAA,SAAAd,GAAA,YAAAe,QAAAD,EAAAd,SAAA,SAAAgB,EAAAvB,EAAAwB,GAAA,SAAAC,EAAAJ,EAAAd,EAAAmB,EAAAC,GAAA,IAAAC,EAAAvB,EAAAL,EAAAqB,GAAArB,EAAAO,GAAA,aAAAqB,EAAApB,KAAA,KAAAqB,EAAAD,EAAArB,IAAAzC,EAAA+D,EAAA/D,MAAA,OAAAA,GAAA,UAAAgE,EAAAhE,IAAAQ,EAAAmC,KAAA3C,EAAA,WAAA0D,EAAAE,QAAA5D,EAAAiE,SAAAC,MAAA,SAAAlE,GAAA2D,EAAA,OAAA3D,EAAA4D,EAAAC,MAAA,SAAAnC,GAAAiC,EAAA,QAAAjC,EAAAkC,EAAAC,MAAAH,EAAAE,QAAA5D,GAAAkE,MAAA,SAAAC,GAAAJ,EAAA/D,MAAAmE,EAAAP,EAAAG,MAAA,SAAAK,GAAA,OAAAT,EAAA,QAAAS,EAAAR,EAAAC,QAAAC,EAAArB,KAAA,IAAA4B,EAAA3D,EAAA,gBAAAV,MAAA,SAAAuD,EAAAd,GAAA,SAAA6B,IAAA,WAAAZ,GAAA,SAAAE,EAAAC,GAAAF,EAAAJ,EAAAd,EAAAmB,EAAAC,MAAA,OAAAQ,MAAAH,KAAAI,YAAA,SAAAhC,EAAAV,EAAAE,EAAAM,GAAA,IAAAmC,EAAA,iCAAAhB,EAAAd,GAAA,iBAAA8B,EAAA,UAAAC,MAAA,iDAAAD,EAAA,cAAAhB,EAAA,MAAAd,EAAA,OAAAgC,IAAA,IAAArC,EAAAmB,SAAAnB,EAAAK,QAAA,KAAAiC,EAAAtC,EAAAsC,SAAA,GAAAA,EAAA,KAAAC,EAAAC,EAAAF,EAAAtC,GAAA,GAAAuC,EAAA,IAAAA,IAAA/B,EAAA,gBAAA+B,GAAA,YAAAvC,EAAAmB,OAAAnB,EAAAyC,KAAAzC,EAAA0C,MAAA1C,EAAAK,SAAA,aAAAL,EAAAmB,OAAA,uBAAAgB,EAAA,MAAAA,EAAA,YAAAnC,EAAAK,IAAAL,EAAA2C,kBAAA3C,EAAAK,SAAA,WAAAL,EAAAmB,QAAAnB,EAAA4C,OAAA,SAAA5C,EAAAK,KAAA8B,EAAA,gBAAAT,EAAAvB,EAAAX,EAAAE,EAAAM,GAAA,cAAA0B,EAAApB,KAAA,IAAA6B,EAAAnC,EAAA6C,KAAA,6BAAAnB,EAAArB,MAAAG,EAAA,gBAAA5C,MAAA8D,EAAArB,IAAAwC,KAAA7C,EAAA6C,MAAA,UAAAnB,EAAApB,OAAA6B,EAAA,YAAAnC,EAAAmB,OAAA,QAAAnB,EAAAK,IAAAqB,EAAArB,OAAA,SAAAmC,EAAAF,EAAAtC,GAAA,IAAA8C,EAAA9C,EAAAmB,SAAAmB,EAAAzD,SAAAiE,GAAA,QAAAC,IAAA5B,EAAA,OAAAnB,EAAAsC,SAAA,eAAAQ,GAAAR,EAAAzD,SAAAmE,SAAAhD,EAAAmB,OAAA,SAAAnB,EAAAK,SAAA0C,EAAAP,EAAAF,EAAAtC,GAAA,UAAAA,EAAAmB,SAAA,WAAA2B,IAAA9C,EAAAmB,OAAA,QAAAnB,EAAAK,IAAA,IAAA4C,UAAA,oCAAAH,EAAA,aAAAtC,EAAA,IAAAkB,EAAAvB,EAAAgB,EAAAmB,EAAAzD,SAAAmB,EAAAK,KAAA,aAAAqB,EAAApB,KAAA,OAAAN,EAAAmB,OAAA,QAAAnB,EAAAK,IAAAqB,EAAArB,IAAAL,EAAAsC,SAAA,KAAA9B,EAAA,IAAA0C,EAAAxB,EAAArB,IAAA,OAAA6C,IAAAL,MAAA7C,EAAAsC,EAAAa,YAAAD,EAAAtF,MAAAoC,EAAAoD,KAAAd,EAAAe,QAAA,WAAArD,EAAAmB,SAAAnB,EAAAmB,OAAA,OAAAnB,EAAAK,SAAA0C,GAAA/C,EAAAsC,SAAA,KAAA9B,GAAA0C,GAAAlD,EAAAmB,OAAA,QAAAnB,EAAAK,IAAA,IAAA4C,UAAA,oCAAAjD,EAAAsC,SAAA,KAAA9B,GAAA,SAAA8C,EAAAC,GAAA,IAAAC,EAAA,CAAAC,OAAAF,EAAA,SAAAA,IAAAC,EAAAE,SAAAH,EAAA,SAAAA,IAAAC,EAAAG,WAAAJ,EAAA,GAAAC,EAAAI,SAAAL,EAAA,SAAAM,WAAAC,KAAAN,GAAA,SAAAO,EAAAP,GAAA,IAAA9B,EAAA8B,EAAAQ,YAAA,GAAAtC,EAAApB,KAAA,gBAAAoB,EAAArB,IAAAmD,EAAAQ,WAAAtC,EAAA,SAAAzB,EAAAN,GAAA,KAAAkE,WAAA,EAAAJ,OAAA,SAAA9D,EAAAuB,QAAAoC,EAAA,WAAAW,OAAA,YAAAlD,EAAAmD,GAAA,GAAAA,EAAA,KAAAC,EAAAD,EAAAtF,GAAA,GAAAuF,EAAA,OAAAA,EAAA5D,KAAA2D,GAAA,sBAAAA,EAAAd,KAAA,OAAAc,EAAA,IAAAE,MAAAF,EAAAG,QAAA,KAAAC,GAAA,EAAAlB,EAAA,SAAAA,IAAA,OAAAkB,EAAAJ,EAAAG,QAAA,GAAAjG,EAAAmC,KAAA2D,EAAAI,GAAA,OAAAlB,EAAAxF,MAAAsG,EAAAI,GAAAlB,EAAAP,MAAA,EAAAO,EAAA,OAAAA,EAAAxF,WAAAmF,EAAAK,EAAAP,MAAA,EAAAO,GAAA,OAAAA,UAAA,OAAAA,KAAAf,GAAA,SAAAA,IAAA,OAAAzE,WAAAmF,EAAAF,MAAA,UAAApC,EAAAtC,UAAAuC,EAAApC,EAAA0C,EAAA,eAAApD,MAAA8C,EAAAtB,cAAA,IAAAd,EAAAoC,EAAA,eAAA9C,MAAA6C,EAAArB,cAAA,IAAAqB,EAAA8D,YAAArF,EAAAwB,EAAA1B,EAAA,qBAAAhB,EAAAwG,oBAAA,SAAAC,GAAA,IAAAC,EAAA,mBAAAD,KAAAE,YAAA,QAAAD,QAAAjE,GAAA,uBAAAiE,EAAAH,aAAAG,EAAAE,QAAA5G,EAAA6G,KAAA,SAAAJ,GAAA,OAAAvG,OAAA4G,eAAA5G,OAAA4G,eAAAL,EAAA/D,IAAA+D,EAAAM,UAAArE,EAAAxB,EAAAuF,EAAAzF,EAAA,sBAAAyF,EAAAtG,UAAAD,OAAA6B,OAAAiB,GAAAyD,GAAAzG,EAAAgH,MAAA,SAAA3E,GAAA,OAAAwB,QAAAxB,IAAAY,EAAAI,EAAAlD,WAAAe,EAAAmC,EAAAlD,UAAAW,GAAA,0BAAAd,EAAAqD,gBAAArD,EAAAiH,MAAA,SAAAzF,EAAAC,EAAAC,EAAAC,EAAA2B,QAAA,IAAAA,MAAA4D,SAAA,IAAAC,EAAA,IAAA9D,EAAA9B,EAAAC,EAAAC,EAAAC,EAAAC,GAAA2B,GAAA,OAAAtD,EAAAwG,oBAAA/E,GAAA0F,IAAA/B,OAAAtB,MAAA,SAAAH,GAAA,OAAAA,EAAAkB,KAAAlB,EAAA/D,MAAAuH,EAAA/B,WAAAnC,EAAAD,GAAA9B,EAAA8B,EAAAhC,EAAA,aAAAE,EAAA8B,EAAApC,GAAA,0BAAAM,EAAA8B,EAAA,qDAAAhD,EAAAoH,KAAA,SAAAC,GAAA,IAAAC,EAAApH,OAAAmH,GAAAD,EAAA,WAAA5G,KAAA8G,EAAAF,EAAAtB,KAAAtF,GAAA,OAAA4G,EAAAG,UAAA,SAAAnC,IAAA,KAAAgC,EAAAf,QAAA,KAAA7F,EAAA4G,EAAAI,MAAA,GAAAhH,KAAA8G,EAAA,OAAAlC,EAAAxF,MAAAY,EAAA4E,EAAAP,MAAA,EAAAO,EAAA,OAAAA,EAAAP,MAAA,EAAAO,IAAApF,EAAA+C,SAAAd,EAAA9B,UAAA,CAAAwG,YAAA1E,EAAAgE,MAAA,SAAAwB,GAAA,QAAAC,KAAA,OAAAtC,KAAA,OAAAX,KAAA,KAAAC,WAAAK,EAAA,KAAAF,MAAA,OAAAP,SAAA,UAAAnB,OAAA,YAAAd,SAAA0C,EAAA,KAAAc,WAAA3C,QAAA6C,IAAA0B,EAAA,QAAAb,KAAA,WAAAA,EAAAe,OAAA,IAAAvH,EAAAmC,KAAA,KAAAqE,KAAAR,OAAAQ,EAAAgB,MAAA,WAAAhB,QAAA7B,IAAA8C,KAAA,gBAAAhD,MAAA,MAAAiD,EAAA,KAAAjC,WAAA,GAAAG,WAAA,aAAA8B,EAAAxF,KAAA,MAAAwF,EAAAzF,IAAA,YAAA0F,MAAApD,kBAAA,SAAAqD,GAAA,QAAAnD,KAAA,MAAAmD,EAAA,IAAAhG,EAAA,cAAAiG,EAAAC,EAAAC,GAAA,OAAAzE,EAAApB,KAAA,QAAAoB,EAAArB,IAAA2F,EAAAhG,EAAAoD,KAAA8C,EAAAC,IAAAnG,EAAAmB,OAAA,OAAAnB,EAAAK,SAAA0C,KAAAoD,EAAA,QAAA7B,EAAA,KAAAT,WAAAQ,OAAA,EAAAC,GAAA,IAAAA,EAAA,KAAAd,EAAA,KAAAK,WAAAS,GAAA5C,EAAA8B,EAAAQ,WAAA,YAAAR,EAAAC,OAAA,OAAAwC,EAAA,UAAAzC,EAAAC,QAAA,KAAAiC,KAAA,KAAAU,EAAAhI,EAAAmC,KAAAiD,EAAA,YAAA6C,EAAAjI,EAAAmC,KAAAiD,EAAA,iBAAA4C,GAAAC,EAAA,SAAAX,KAAAlC,EAAAE,SAAA,OAAAuC,EAAAzC,EAAAE,UAAA,WAAAgC,KAAAlC,EAAAG,WAAA,OAAAsC,EAAAzC,EAAAG,iBAAA,GAAAyC,GAAA,QAAAV,KAAAlC,EAAAE,SAAA,OAAAuC,EAAAzC,EAAAE,UAAA,YAAA2C,EAAA,UAAAjE,MAAA,kDAAAsD,KAAAlC,EAAAG,WAAA,OAAAsC,EAAAzC,EAAAG,gBAAAf,OAAA,SAAAtC,EAAAD,GAAA,QAAAiE,EAAA,KAAAT,WAAAQ,OAAA,EAAAC,GAAA,IAAAA,EAAA,KAAAd,EAAA,KAAAK,WAAAS,GAAA,GAAAd,EAAAC,QAAA,KAAAiC,MAAAtH,EAAAmC,KAAAiD,EAAA,oBAAAkC,KAAAlC,EAAAG,WAAA,KAAA2C,EAAA9C,EAAA,OAAA8C,IAAA,UAAAhG,GAAA,aAAAA,IAAAgG,EAAA7C,QAAApD,MAAAiG,EAAA3C,aAAA2C,EAAA,UAAA5E,EAAA4E,IAAAtC,WAAA,UAAAtC,EAAApB,OAAAoB,EAAArB,MAAAiG,GAAA,KAAAnF,OAAA,YAAAiC,KAAAkD,EAAA3C,WAAAnD,GAAA,KAAA+F,SAAA7E,IAAA6E,SAAA,SAAA7E,EAAAkC,GAAA,aAAAlC,EAAApB,KAAA,MAAAoB,EAAArB,IAAA,gBAAAqB,EAAApB,MAAA,aAAAoB,EAAApB,KAAA,KAAA8C,KAAA1B,EAAArB,IAAA,WAAAqB,EAAApB,MAAA,KAAAyF,KAAA,KAAA1F,IAAAqB,EAAArB,IAAA,KAAAc,OAAA,cAAAiC,KAAA,kBAAA1B,EAAApB,MAAAsD,IAAA,KAAAR,KAAAQ,GAAApD,GAAAgG,OAAA,SAAA7C,GAAA,QAAAW,EAAA,KAAAT,WAAAQ,OAAA,EAAAC,GAAA,IAAAA,EAAA,KAAAd,EAAA,KAAAK,WAAAS,GAAA,GAAAd,EAAAG,eAAA,YAAA4C,SAAA/C,EAAAQ,WAAAR,EAAAI,UAAAG,EAAAP,GAAAhD,IAAAiG,MAAA,SAAAhD,GAAA,QAAAa,EAAA,KAAAT,WAAAQ,OAAA,EAAAC,GAAA,IAAAA,EAAA,KAAAd,EAAA,KAAAK,WAAAS,GAAA,GAAAd,EAAAC,WAAA,KAAA/B,EAAA8B,EAAAQ,WAAA,aAAAtC,EAAApB,KAAA,KAAAoG,EAAAhF,EAAArB,IAAA0D,EAAAP,GAAA,OAAAkD,GAAA,UAAAtE,MAAA,0BAAAuE,cAAA,SAAAzC,EAAAf,EAAAE,GAAA,YAAAf,SAAA,CAAAzD,SAAAkC,EAAAmD,GAAAf,aAAAE,WAAA,cAAAlC,SAAA,KAAAd,SAAA0C,GAAAvC,IAAAxC,EAAA,SAAA4I,EAAAC,EAAArF,EAAAC,EAAAqF,EAAAC,EAAAvI,EAAA6B,GAAA,QAAA6C,EAAA2D,EAAArI,GAAA6B,GAAAzC,EAAAsF,EAAAtF,MAAA,MAAAoE,GAAA,YAAAP,EAAAO,GAAAkB,EAAAL,KAAArB,EAAA5D,GAAAsH,QAAA1D,QAAA5D,GAAAkE,KAAAgF,EAAAC,GAAA,SAAAC,EAAA5G,GAAA,sBAAAV,EAAA,KAAAuH,EAAAC,UAAA,WAAAhC,SAAA,SAAA1D,EAAAC,GAAA,IAAAoF,EAAAzG,EAAA+G,MAAAzH,EAAAuH,GAAA,SAAAH,EAAAlJ,GAAAgJ,EAAAC,EAAArF,EAAAC,EAAAqF,EAAAC,EAAA,OAAAnJ,GAAA,SAAAmJ,EAAAzH,GAAAsH,EAAAC,EAAArF,EAAAC,EAAAqF,EAAAC,EAAA,QAAAzH,GAAAwH,OAAA/D,OAAA,SAAAqE,EAAA9B,EAAA+B,GAAA,IAAAjC,EAAAlH,OAAAkH,KAAAE,GAAA,GAAApH,OAAAoJ,sBAAA,KAAAC,EAAArJ,OAAAoJ,sBAAAhC,GAAA+B,IAAAE,IAAAC,QAAA,SAAAC,GAAA,OAAAvJ,OAAAwJ,yBAAApC,EAAAmC,GAAAtI,eAAAiG,EAAAtB,KAAAqD,MAAA/B,EAAAmC,GAAA,OAAAnC,EAAA,SAAAuC,EAAAC,GAAA,QAAAtD,EAAA,EAAAA,EAAA4C,UAAA7C,OAAAC,IAAA,KAAAuD,EAAA,MAAAX,UAAA5C,GAAA4C,UAAA5C,GAAA,GAAAA,EAAA,EAAA8C,EAAAlJ,OAAA2J,IAAA,GAAA3G,SAAA,SAAA1C,GAAAsJ,EAAAF,EAAApJ,EAAAqJ,EAAArJ,OAAAN,OAAA6J,0BAAA7J,OAAA8J,iBAAAJ,EAAA1J,OAAA6J,0BAAAF,IAAAT,EAAAlJ,OAAA2J,IAAA3G,SAAA,SAAA1C,GAAAN,OAAAI,eAAAsJ,EAAApJ,EAAAN,OAAAwJ,yBAAAG,EAAArJ,OAAA,OAAAoJ,EAAA,SAAAE,EAAAvJ,EAAAC,EAAAZ,GAAA,OAAAY,EAAA,SAAA6B,GAAA,IAAA7B,EAAA,SAAAyJ,EAAAC,GAAA,cAAAtG,EAAAqG,IAAA,OAAAA,EAAA,OAAAA,EAAA,IAAAE,EAAAF,EAAAtJ,OAAAyJ,aAAA,QAAArF,IAAAoF,EAAA,KAAAE,EAAAF,EAAA5H,KAAA0H,EAAAC,GAAA,yBAAAtG,EAAAyG,GAAA,OAAAA,EAAA,UAAApF,UAAA,kEAAAiF,EAAAI,OAAAC,QAAAN,GAAAO,CAAAnI,EAAA,2BAAAuB,EAAApD,KAAA8J,OAAA9J,GAAAiK,CAAAjK,MAAAD,EAAAL,OAAAI,eAAAC,EAAAC,EAAA,CAAAZ,QAAAuB,YAAA,EAAAC,cAAA,EAAAC,UAAA,IAAAd,EAAAC,GAAAZ,EAAAW,EAOA,IAAAmK,EAA6BC,SAArBC,EAASF,EAATE,UAAWC,EAAKH,EAALG,MACXC,EAAaH,SAASI,KAAtBD,SACFE,EAAQL,SAASM,MAEfC,EAAaN,EAAUO,qBAAvBD,SAKON,YAAUQ,oBAAoB,CACzCC,SDlBW,wsBCoBXC,OAAQ,CAAC,sBAETC,SAAQ5B,EAAA,CACJ6B,cAAa,WACT,OAAOC,KAAKC,mBAAmBC,cAAc,UAGjDC,yBAAwB,WACpB,OAAOH,KAAKI,kBAAkB9J,OAAO,sBAGzC+J,uBAAsB,WAClB,IAAMC,EAAW,IAAIjB,EAAS,EAAG,GAIjC,OAFAiB,EAASC,UAAUlB,EAASmB,UAAU,kBAAmBR,KAAKS,YAAYT,KAAKU,SAASC,MAEjFL,IAGRb,EAAS,cAAe,CAAC,sBAGhCmB,MAAO,CACHC,iBAAgB,SAAC1M,GACTA,EAAMgH,OAASlH,IAAe6M,WAAad,KAAKU,SAASC,KAAOxM,EAAMwM,IAA2B,QAArBxM,EAAM4M,YAClFf,KAAKgB,OAAO,eAAgB7M,EAAM8M,MAGlC9M,EAAMgH,OAASlH,IAAe6M,WAAad,KAAKU,SAASC,KAAOxM,EAAMwM,IAA2B,WAArBxM,EAAM4M,YAClFf,KAAKgB,OAAO,qBAKxBE,QAAS,CACLC,eAAc,SAACC,GACX,IAAIV,EAAWV,KAAKqB,mBAAmB/K,SACjCgL,EAAWpD,IAAA,GACVwC,GAAQ,IACXa,SAAUvB,KAAKU,SAASC,GACxBa,aAAcxB,KAAKU,SAASc,aAC5BC,WAAY,eACZC,OAAQ,KACRC,OAAQ,GACRC,SAAU,EACVR,SAAUA,EACVT,GAAIpB,EAAMsC,aAGdnB,EAAWjM,OAAOqN,OAAOpB,EAAUY,GACnClC,EAAM2C,OAAO,0BAA2BrB,IAG5CsB,SAAQ,SAACC,EAA8CV,GACnD,IAAMW,EAAiBD,EAAUE,MAAK,SAAAC,GAAI,OAAIA,EAAKzB,KAAOY,KAC1D,QAAKW,IACDA,EAAeT,aAAezB,KAAKD,eAChCC,KAAKgC,SAASC,EAAWC,EAAeX,YAGnDd,YAAW,SAACE,GAA4B,IAAD0B,EAAA,KAC7BC,EAAmB,GA0BzB,OAzBoB,SAAdC,EAAeC,EAAWC,GAC5B,IAAMC,EAAiBL,EAAKJ,UAAUlE,QAAO,SAAA2C,GAAQ,OAAIA,EAASa,WAAaiB,KAC/E,OAAKE,EAAe9H,OAKb8H,EAAejL,SAAQ,SAAA2K,GAC1B,OAAKA,EAAKX,YAAeW,EAAKV,QAI1BU,EAAKV,QAILU,EAAKX,aAAeY,EAAKtC,eACzB0C,EAAIpI,KAAK+H,EAAKzB,IAJP4B,EAAYH,EAAKzB,GAAI8B,IAJrB,MANJ,GAqBfF,CAAY5B,EAAI2B,GACTA,GAGLK,qBAAoB,WAAiB,IAADC,EAAA,YAAArF,EAAAjJ,IAAA8G,MAAA,SAAAyH,IAAA,OAAAvO,IAAAwB,MAAA,SAAAgN,GAAA,cAAAA,EAAA7G,KAAA6G,EAAAnJ,MAAA,UACjCiJ,EAAKnC,YAAYmC,EAAKlC,SAASC,IAAI/F,OAAO,CAADkI,EAAAnJ,KAAA,eAAAmJ,EAAA3J,OAAA,SACnC,IAAE,cAAA2J,EAAA7G,KAAA,EAAA6G,EAAAnJ,KAAA,EAIIiJ,EAAKzC,yBAAyB4C,OAAOH,EAAKvC,wBAAwB,KAAD,SAAAyC,EAAA3J,OAAA,SAAA2J,EAAA9J,MAAA,cAAA8J,EAAA7G,KAAA,EAAA6G,EAAAE,GAAAF,EAAA,SAAAA,EAAA3J,OAAA,SAEvE,IAAE,yBAAA2J,EAAA1G,UAAAyG,EAAA,iBARyBtF,IAY1C0F,aAAY,SAAChC,GACJA,IAGDjB,KAAKgC,SAAShC,KAAKiC,UAAWjC,KAAKU,SAASa,WAAmD,SAAtC2B,aAAaC,QAAQ,aAC9E/D,EAAM2C,OAAO,kCAAmC,CAAEhB,WAAY,MAAOlK,KAAM5C,IAAe6M,UAAWH,GAAIX,KAAKU,SAASC,GAAIyC,SAAS,EAAMnC,SAE1IjB,KAAKgB,OAAO,eAAgBC,KAI9BoC,gBAAe,WAAmB,IAADC,EAAA,YAAA/F,EAAAjJ,IAAA8G,MAAA,SAAAmI,IAAA,OAAAjP,IAAAwB,MAAA,SAAA0N,GAAA,cAAAA,EAAAvH,KAAAuH,EAAA7J,MAAA,cAAA6J,EAAA7J,KAAA,EACT2J,EAAKX,uBAAuB,KAAD,EAApCa,EAAAxK,KAED4B,QAAW0I,EAAKtB,SAASsB,EAAKrB,UAAWqB,EAAK5C,SAASa,WAAmD,SAAtC2B,aAAaC,QAAQ,aACrG/D,EAAM2C,OAAO,kCAAmC,CAAEhB,WAAY,SAAUlK,KAAM5C,IAAe6M,UAAWH,GAAI2C,EAAK5C,SAASC,GAAIyC,SAAS,IAEvIE,EAAKtC,OAAO,mBACf,wBAAAwC,EAAApH,UAAAmH,MAPkChG", "file": "static/js/9.js", "sourcesContent": ["export const enum SEQUENCE_TYPES {\n    ACTION = 'action',\n    CONDITION = 'condition',\n    DELAY_ACTION = 'delay_action',\n}\n\nexport const DELAY_OPTIONS = [\n    {\n        value: 'hour',\n        label: 'sw-flow-delay.modal.labelHour'\n    },\n    {\n        value: 'day',\n        label: 'sw-flow-delay.modal.labelDay'\n    },\n    {\n        value: 'week',\n        label: 'sw-flow-delay.modal.labelWeek'\n    },\n    {\n        value: 'month',\n        label: 'sw-flow-delay.modal.labelMonth'\n    },\n    {\n        value: 'custom',\n        label: 'sw-flow-delay.modal.labelCustom'\n    },\n] as const;\n\nexport const CUSTOM_TIME = 'custom' as const;\nexport const GENERAL_GROUP = 'general' as const;\n", "export default \"{% block sw_flow_sequence_condition_true_arrow_extension_options %}\\n    <sw-context-menu-item\\n        class=\\\"sw-flow-sequence-condition__add-true-action\\\"\\n        :disabled=\\\"disabled\\\"\\n        @click=\\\"addDelayAction(true)\\\"\\n    >\\n        {{ $tc('sw-flow-delay.detail.sequence.selectorDelayAction') }}\\n    </sw-context-menu-item>\\n{% endblock %}\\n\\n{% block sw_flow_sequence_condition_false_arrow_extension_options %}\\n    <sw-context-menu-item\\n        class=\\\"sw-flow-sequence-condition__add-false-action\\\"\\n        :disabled=\\\"disabled\\\"\\n        @click=\\\"addDelayAction(false)\\\"\\n    >\\n        {{ $tc('sw-flow-delay.detail.sequence.selectorDelayAction') }}\\n    </sw-context-menu-item>\\n{% endblock %}\\n\";", "import template from './sw-flow-sequence-condition.html.twig';\nimport {SEQUENCE_TYPES} from '../../../../constant/sw-flow-delay.constant';\nimport type RepositoryType from 'src/core/data/repository.data';\nimport type CriteriaType from 'src/core/data/criteria.data';\nimport {WarningConfig} from \"../../../../type/types\";\nimport type EntityCollection from '@shopware-ag/admin-extension-sdk/es/data/_internals/EntityCollection';\nimport type {Entity} from '@shopware-ag/admin-extension-sdk/es/data/_internals/Entity';\n\nconst { Component, State } = Shopware;\nconst { Criteria } = Shopware.Data;\nconst utils = Shopware.Utils;\n\nconst { mapState } = Component.getComponentHelper();\n\n/**\n * @package services-settings\n */\nexport default Component.wrapComponentConfig({\n    template,\n\n    inject: ['flowBuilderService'],\n\n    computed: {\n        delayConstant() {\n            return this.flowBuilderService.getActionName('DELAY');\n        },\n\n        delayedActionsRepository(): RepositoryType {\n            return this.repositoryFactory.create('swag_delay_action');\n        },\n\n        delayedActionsCriteria(): CriteriaType {\n            const criteria = new Criteria(1, 1);\n\n            criteria.addFilter(Criteria.equalsAny('delaySequenceId', this.getDelayIds(this.sequence.id)));\n\n            return criteria;\n        },\n\n        ...mapState('swFlowDelay', ['showWarningModal']),\n    },\n\n    watch: {\n        showWarningModal(value: WarningConfig): void {\n            if (value.name === SEQUENCE_TYPES.CONDITION && this.sequence.id === value.id && value.actionType === 'ADD') {\n                this.$super('onRuleChange', value.rule);\n            }\n\n            if (value.name === SEQUENCE_TYPES.CONDITION && this.sequence.id === value.id && value.actionType === 'DELETE') {\n                this.$super('removeCondition');\n            }\n        },\n    },\n\n    methods: {\n        addDelayAction(trueCase: boolean): void {\n            let sequence = this.sequenceRepository.create();\n            const newSequence = {\n                ...sequence,\n                parentId: this.sequence.id,\n                displayGroup: this.sequence.displayGroup,\n                actionName: 'action.delay',\n                ruleId: null,\n                config: {},\n                position: 1,\n                trueCase: trueCase,\n                id: utils.createId(),\n            };\n\n            sequence = Object.assign(sequence, newSequence);\n            State.commit('swFlowState/addSequence', sequence);\n        },\n\n        hasDelay(sequences: EntityCollection<'flow_sequence'>, parentId: string): boolean {\n            const parentSequence = sequences.find(item => item.id === parentId);\n            if (!parentSequence) return false;\n            if (parentSequence.actionName === this.delayConstant) return true;\n            return this.hasDelay(sequences, parentSequence.parentId);\n        },\n\n        getDelayIds(id: string): Array<string> {\n            const delayedSequences = [];\n            const getChildren = (currentId, arr) => {\n                const childSequences = this.sequences.filter(sequence => sequence.parentId === currentId);\n                if (!childSequences.length) {\n                    return [];\n                }\n\n\n                return childSequences.forEach(item => {\n                    if (!item.actionName && !item.ruleId) {\n                        return [];\n                    }\n\n                    if (item.ruleId) {\n                        return getChildren(item.id, arr);\n                    }\n\n                    if (item.actionName === this.delayConstant) {\n                        arr.push(item.id);\n                    }\n\n                    return getChildren(item.id, arr);\n                });\n            }\n\n            getChildren(id, delayedSequences);\n            return delayedSequences;\n        },\n\n        async getDelayedActionData(): Promise<[]> {\n            if (!this.getDelayIds(this.sequence.id).length) {\n                return [];\n            }\n\n            try {\n                return await this.delayedActionsRepository.search(this.delayedActionsCriteria);\n            } catch (error) {\n                return [];\n            }\n        },\n\n        onRuleChange(rule: Entity<'rule'>): void {\n            if (!rule) {\n                return;\n            }\n            if (this.hasDelay(this.sequences, this.sequence.parentId) && localStorage.getItem('condition') !== 'true') {\n                State.commit('swFlowDelay/setShowWarningModal', { actionType: 'ADD', type: SEQUENCE_TYPES.CONDITION, id: this.sequence.id, enabled: true, rule });\n            } else {\n                this.$super('onRuleChange', rule);\n            }\n        },\n\n        async removeCondition(): Promise<void> {\n            const delayedData = await this.getDelayedActionData();\n\n            if (delayedData.length || (this.hasDelay(this.sequences, this.sequence.parentId) && localStorage.getItem('condition') !== 'true')) {\n                State.commit('swFlowDelay/setShowWarningModal', { actionType: 'DELETE', type: SEQUENCE_TYPES.CONDITION, id: this.sequence.id, enabled: true });\n            } else {\n                this.$super('removeCondition');\n            }\n        },\n    }\n});\n"], "sourceRoot": ""}
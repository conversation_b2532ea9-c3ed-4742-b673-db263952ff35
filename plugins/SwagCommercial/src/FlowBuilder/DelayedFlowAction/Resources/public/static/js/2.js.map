{"version": 3, "sources": ["webpack:////tmp/extension2400992207/SwagCommercial/src/FlowBuilder/DelayedFlowAction/Resources/app/administration/src/module/sw-flow/component/sw-flow-delay-action/sw-flow-delay-action.scss", "webpack:////tmp/extension2400992207/SwagCommercial/src/FlowBuilder/DelayedFlowAction/Resources/app/administration/src/constant/sw-flow-delay.constant.ts", "webpack:///./node_modules/vue-style-loader/lib/listToStyles.js", "webpack:///./node_modules/vue-style-loader/lib/addStylesClient.js", "webpack:////tmp/extension2400992207/SwagCommercial/src/FlowBuilder/DelayedFlowAction/Resources/app/administration/src/module/sw-flow/component/sw-flow-delay-action/sw-flow-delay-action.html.twig", "webpack:////tmp/extension2400992207/SwagCommercial/src/FlowBuilder/DelayedFlowAction/Resources/app/administration/src/module/sw-flow/component/sw-flow-delay-action/index.ts"], "names": ["content", "__esModule", "default", "module", "i", "locals", "exports", "add", "SEQUENCE_TYPES", "DELAY_OPTIONS", "value", "label", "CUSTOM_TIME", "listToStyles", "parentId", "list", "styles", "newStyles", "length", "item", "id", "part", "css", "media", "sourceMap", "parts", "push", "hasDocument", "document", "DEBUG", "Error", "stylesInDom", "head", "getElementsByTagName", "singletonElement", "singletonCounter", "isProduction", "noop", "options", "ssrIdKey", "isOldIE", "navigator", "test", "userAgent", "toLowerCase", "addStylesClient", "_isProduction", "_options", "addStylesToDom", "newList", "<PERSON><PERSON><PERSON><PERSON>", "domStyle", "refs", "j", "addStyle", "createStyleElement", "styleElement", "createElement", "type", "append<PERSON><PERSON><PERSON>", "obj", "update", "remove", "querySelector", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "styleIndex", "applyToSingletonTag", "bind", "applyToTag", "newObj", "textStore", "replaceText", "index", "replacement", "filter", "Boolean", "join", "styleSheet", "cssText", "cssNode", "createTextNode", "childNodes", "insertBefore", "setAttribute", "ssrId", "sources", "btoa", "unescape", "encodeURIComponent", "JSON", "stringify", "<PERSON><PERSON><PERSON><PERSON>", "_Shopware", "Shopware", "Component", "State", "utils", "Utils", "_Component$getCompone", "getComponentHelper", "mapGetters", "mapState", "wrapComponentConfig", "inject", "template", "data", "showDelayModal", "isUpdateDelay", "showWarningDeleteDelay", "delayType", "watch", "sequence", "handler", "_value$config", "actionName", "this", "delayConstant", "ruleId", "selectorSequence", "sequences", "find", "config", "delay", "trueBlock", "createSequence", "trueCase", "immediate", "showWarningModal", "name", "DELAY_ACTION", "actionType", "commit", "enabled", "onConfirmDeleteDelay", "computed", "_objectSpread", "flowBuilderService", "getActionName", "actionDelayOptions", "_this", "map", "option", "$tc", "showDelayElement", "showCustomDescription", "_this$sequence$config", "customTimeDescription", "convertTimeString", "timeDescription", "_this$delayConfig", "delayConfig", "Object", "values", "methods", "getLicense", "toggle", "License", "get", "unit", "getTimeLabel", "concat", "number", "onEditDelay", "localStorage", "getItem", "onDeleteDelay", "_this2", "for<PERSON>ach", "onSelectDelay", "onChangeType", "onCloseDelayModal", "onSaveDelay", "arrowClasses", "thenCase", "params", "sequenceRepository", "create", "newSequence", "displayGroup", "position", "createId", "assign"], "mappings": "oIAGA,IAAIA,EAAU,EAAQ,QACnBA,EAAQC,aAAYD,EAAUA,EAAQE,SACnB,iBAAZF,IAAsBA,EAAU,CAAC,CAACG,EAAOC,EAAIJ,EAAS,MAC7DA,EAAQK,SAAQF,EAAOG,QAAUN,EAAQK,SAG/BE,EADH,EAAQ,QAAwLL,SACzL,WAAYF,GAAS,EAAM,K,kCCT5C,sGAAO,IAAWQ,EAAc,SAAdA,GAAc,OAAdA,EAAc,gBAAdA,EAAc,sBAAdA,EAAc,4BAAdA,EAAc,KAMnBC,EAAgB,CACzB,CACIC,MAAO,OACPC,MAAO,iCAEX,CACID,MAAO,MACPC,MAAO,gCAEX,CACID,MAAO,OACPC,MAAO,iCAEX,CACID,MAAO,QACPC,MAAO,kCAEX,CACID,MAAO,SACPC,MAAO,oCAIFC,EAAc,U,kCCzBZ,SAASC,EAAcC,EAAUC,GAG9C,IAFA,IAAIC,EAAS,GACTC,EAAY,GACPb,EAAI,EAAGA,EAAIW,EAAKG,OAAQd,IAAK,CACpC,IAAIe,EAAOJ,EAAKX,GACZgB,EAAKD,EAAK,GAIVE,EAAO,CACTD,GAAIN,EAAW,IAAMV,EACrBkB,IALQH,EAAK,GAMbI,MALUJ,EAAK,GAMfK,UALcL,EAAK,IAOhBF,EAAUG,GAGbH,EAAUG,GAAIK,MAAMC,KAAKL,GAFzBL,EAAOU,KAAKT,EAAUG,GAAM,CAAEA,GAAIA,EAAIK,MAAO,CAACJ,KAKlD,OAAOL,E,+CCjBT,IAAIW,EAAkC,oBAAbC,SAEzB,GAAqB,oBAAVC,OAAyBA,QAC7BF,EACH,MAAM,IAAIG,MACV,2JAkBJ,IAAIC,EAAc,GAQdC,EAAOL,IAAgBC,SAASI,MAAQJ,SAASK,qBAAqB,QAAQ,IAC9EC,EAAmB,KACnBC,EAAmB,EACnBC,GAAe,EACfC,EAAO,aACPC,EAAU,KACVC,EAAW,kBAIXC,EAA+B,oBAAdC,WAA6B,eAAeC,KAAKD,UAAUE,UAAUC,eAE3E,SAASC,EAAiB/B,EAAUC,EAAM+B,EAAeC,GACtEX,EAAeU,EAEfR,EAAUS,GAAY,GAEtB,IAAI/B,EAASH,EAAaC,EAAUC,GAGpC,OAFAiC,EAAehC,GAER,SAAiBiC,GAEtB,IADA,IAAIC,EAAY,GACP9C,EAAI,EAAGA,EAAIY,EAAOE,OAAQd,IAAK,CACtC,IAAIe,EAAOH,EAAOZ,IACd+C,EAAWpB,EAAYZ,EAAKC,KACvBgC,OACTF,EAAUxB,KAAKyB,GAEbF,EAEFD,EADAhC,EAASH,EAAaC,EAAUmC,IAGhCjC,EAAS,GAEX,IAASZ,EAAI,EAAGA,EAAI8C,EAAUhC,OAAQd,IAAK,CACzC,IAAI+C,EACJ,GAAsB,KADlBA,EAAWD,EAAU9C,IACZgD,KAAY,CACvB,IAAK,IAAIC,EAAI,EAAGA,EAAIF,EAAS1B,MAAMP,OAAQmC,IACzCF,EAAS1B,MAAM4B,YAEVtB,EAAYoB,EAAS/B,OAMpC,SAAS4B,EAAgBhC,GACvB,IAAK,IAAIZ,EAAI,EAAGA,EAAIY,EAAOE,OAAQd,IAAK,CACtC,IAAIe,EAAOH,EAAOZ,GACd+C,EAAWpB,EAAYZ,EAAKC,IAChC,GAAI+B,EAAU,CACZA,EAASC,OACT,IAAK,IAAIC,EAAI,EAAGA,EAAIF,EAAS1B,MAAMP,OAAQmC,IACzCF,EAAS1B,MAAM4B,GAAGlC,EAAKM,MAAM4B,IAE/B,KAAOA,EAAIlC,EAAKM,MAAMP,OAAQmC,IAC5BF,EAAS1B,MAAMC,KAAK4B,EAASnC,EAAKM,MAAM4B,KAEtCF,EAAS1B,MAAMP,OAASC,EAAKM,MAAMP,SACrCiC,EAAS1B,MAAMP,OAASC,EAAKM,MAAMP,YAEhC,CACL,IAAIO,EAAQ,GACZ,IAAS4B,EAAI,EAAGA,EAAIlC,EAAKM,MAAMP,OAAQmC,IACrC5B,EAAMC,KAAK4B,EAASnC,EAAKM,MAAM4B,KAEjCtB,EAAYZ,EAAKC,IAAM,CAAEA,GAAID,EAAKC,GAAIgC,KAAM,EAAG3B,MAAOA,KAK5D,SAAS8B,IACP,IAAIC,EAAe5B,SAAS6B,cAAc,SAG1C,OAFAD,EAAaE,KAAO,WACpB1B,EAAK2B,YAAYH,GACVA,EAGT,SAASF,EAAUM,GACjB,IAAIC,EAAQC,EACRN,EAAe5B,SAASmC,cAAc,SAAWxB,EAAW,MAAQqB,EAAIxC,GAAK,MAEjF,GAAIoC,EAAc,CAChB,GAAIpB,EAGF,OAAOC,EAOPmB,EAAaQ,WAAWC,YAAYT,GAIxC,GAAIhB,EAAS,CAEX,IAAI0B,EAAa/B,IACjBqB,EAAetB,IAAqBA,EAAmBqB,KACvDM,EAASM,EAAoBC,KAAK,KAAMZ,EAAcU,GAAY,GAClEJ,EAASK,EAAoBC,KAAK,KAAMZ,EAAcU,GAAY,QAGlEV,EAAeD,IACfM,EAASQ,EAAWD,KAAK,KAAMZ,GAC/BM,EAAS,WACPN,EAAaQ,WAAWC,YAAYT,IAMxC,OAFAK,EAAOD,GAEA,SAAsBU,GAC3B,GAAIA,EAAQ,CACV,GAAIA,EAAOhD,MAAQsC,EAAItC,KACnBgD,EAAO/C,QAAUqC,EAAIrC,OACrB+C,EAAO9C,YAAcoC,EAAIpC,UAC3B,OAEFqC,EAAOD,EAAMU,QAEbR,KAKN,IACMS,EADFC,GACED,EAAY,GAET,SAAUE,EAAOC,GAEtB,OADAH,EAAUE,GAASC,EACZH,EAAUI,OAAOC,SAASC,KAAK,QAI1C,SAASV,EAAqBX,EAAciB,EAAOX,EAAQF,GACzD,IAAItC,EAAMwC,EAAS,GAAKF,EAAItC,IAE5B,GAAIkC,EAAasB,WACftB,EAAasB,WAAWC,QAAUP,EAAYC,EAAOnD,OAChD,CACL,IAAI0D,EAAUpD,SAASqD,eAAe3D,GAClC4D,EAAa1B,EAAa0B,WAC1BA,EAAWT,IAAQjB,EAAaS,YAAYiB,EAAWT,IACvDS,EAAWhE,OACbsC,EAAa2B,aAAaH,EAASE,EAAWT,IAE9CjB,EAAaG,YAAYqB,IAK/B,SAASX,EAAYb,EAAcI,GACjC,IAAItC,EAAMsC,EAAItC,IACVC,EAAQqC,EAAIrC,MACZC,EAAYoC,EAAIpC,UAiBpB,GAfID,GACFiC,EAAa4B,aAAa,QAAS7D,GAEjCe,EAAQ+C,OACV7B,EAAa4B,aAAa7C,EAAUqB,EAAIxC,IAGtCI,IAGFF,GAAO,mBAAqBE,EAAU8D,QAAQ,GAAK,MAEnDhE,GAAO,uDAAyDiE,KAAKC,SAASC,mBAAmBC,KAAKC,UAAUnE,MAAgB,OAG9HgC,EAAasB,WACftB,EAAasB,WAAWC,QAAUzD,MAC7B,CACL,KAAOkC,EAAaoC,YAClBpC,EAAaS,YAAYT,EAAaoC,YAExCpC,EAAaG,YAAY/B,SAASqD,eAAe3D,O,mDC3NtC,I,+uCCKf,IAAAuE,EAA6BC,SAArBC,EAASF,EAATE,UAAWC,EAAKH,EAALG,MACbC,EAAQH,SAASI,MACvBC,EAAiCJ,EAAUK,qBAAnCC,EAAUF,EAAVE,WAAYC,EAAQH,EAARG,SAKLP,YAAUQ,oBAAoB,CACzCC,OAAQ,CAAC,sBAETC,SDfW,4/KCiBXC,KAAI,WAMA,MAAO,CACHC,gBAAgB,EAChBC,eAAe,EACfC,wBAAwB,EACxBC,UAAW,SAInBC,MAAO,CACHC,SAAU,CACNC,QAAO,SAACvG,GAAwB,IAADwG,EAC3B,GAAIxG,EAAMyG,aAAeC,KAAKC,gBAAiB3G,EAAM4G,OAArD,CAIA,IAAMC,EAAmBH,KAAKI,UAAUC,MAAK,SAAAtG,GAAI,OAAKA,EAAKL,WAAaJ,EAAMU,MAEzEmG,WAAkBnG,IAAkB,QAAhB8F,EAAIxG,EAAMgH,cAAM,IAAAR,IAAZA,EAAcS,OAAUjH,EAAMkH,WACvDR,KAAKS,eAAe,CAChBV,WAAY,KACZW,UAAU,MAItBC,WAAW,GAGfC,iBAAgB,SAACtH,GACTA,EAAMuH,OAASzH,IAAe0H,cAAqC,SAArBxH,EAAMyH,YAAyBzH,EAAMU,KAAOgG,KAAKJ,SAAS5F,KACxGgG,KAAKT,gBAAiB,EACtBX,EAAMoC,OAAO,kCAAmC,CAAE1E,KAAM,GAAIuE,KAAM,GAAII,SAAS,EAAOjH,GAAI,MAG1FV,EAAMuH,OAASzH,IAAe0H,cAAqC,WAArBxH,EAAMyH,YAA2BzH,EAAMU,KAAOgG,KAAKJ,SAAS5F,IAC1GgG,KAAKkB,yBAKjBC,SAAQC,MAAA,GACDnC,EAAW,cAAe,CAAC,eAC3BC,EAAS,cAAe,CAAC,sBAAoB,IAEhDe,cAAa,WACT,OAAOD,KAAKqB,mBAAmBC,cAAc,UAGjDC,mBAAkB,WAAwB,IAADC,EAAA,KACrC,OAAOnI,IAAcoI,KAAI,SAAAC,GACrB,OAAAN,IAAA,GACOM,GAAM,IACTnI,MAAOiI,EAAKG,IAAID,EAAOnI,aAKnCqI,iBAAgB,WACZ,OAAO5B,KAAKJ,SAASG,aAAeC,KAAKC,eAG7C4B,sBAAqB,WAAa,IAADC,EAC7B,OAAiC,QAA1BA,EAAA9B,KAAKJ,SAASU,OAAOC,aAAK,IAAAuB,OAAA,EAA1BA,EAA4BhI,QAAS,GAGhDiI,sBAAqB,WACjB,IAAQxB,EAAUP,KAAKJ,SAASU,OAAxBC,MAOR,MAAO,CALOP,KAAKgC,kBAAkBzB,EAAM,GAAGjE,KAAMiE,EAAM,GAAGjH,OAChD0G,KAAKgC,kBAAkBzB,EAAM,GAAGjE,KAAMiE,EAAM,GAAGjH,OAChD0G,KAAKgC,kBAAkBzB,EAAM,GAAGjE,KAAMiE,EAAM,GAAGjH,OAC9C0G,KAAKgC,kBAAkBzB,EAAM,GAAGjE,KAAMiE,EAAM,GAAGjH,QAE5BiE,QAAO,SAAAxD,GAAI,OAAIA,KAAM0D,QAGzDwE,gBAAe,WAEX,GADuBjC,KAAKJ,SAApBG,aACWC,KAAKC,cACpB,OAAO,KAGX,IAAAiC,EAAwBlC,KAAKmC,YAArB7F,EAAI4F,EAAJ5F,KAAMhD,EAAK4I,EAAL5I,MACd,OAAO0G,KAAKgC,kBAAkB1F,EAAMhD,IAGxC6I,YAAW,WACP,IAAQ7B,EAAWN,KAAKJ,SAAhBU,OAER,OAAKA,EAAOC,OAAU6B,OAAOC,OAAO/B,EAAOC,OAAOzG,OAOtB,IAAxBwG,EAAOC,MAAMzG,OACN,CACHwC,KAAMgE,EAAOC,MAAM,GAAGjE,KACtBhD,MAAOgH,EAAOC,MAAM,GAAGjH,OAIxB,CACHgD,KAAM9C,IACNF,MAAO,MAfA,CACHgD,KAAM,KACNhD,MAAO,SAkBvBgJ,QAAS,CACLC,WAAU,SAACC,GACP,OAAO9D,SAAS+D,QAAQC,IAAIF,IAGhCR,kBAAiB,SAAC1F,EAAchD,GAC5B,IAAKA,EACD,MAAO,GAGX,IAAMqJ,EAAO3C,KAAK4C,aAAatG,EAAMhD,GACrC,MAAM,IAANuJ,OAAWvJ,EAAK,KAAAuJ,OAAIF,IAGxBC,aAAY,SAACtG,EAAcwG,GACvB,OAAQxG,GAEJ,IAAK,OACD,OAAO0D,KAAK2B,IAAI,gCAAiCmB,GAGrD,IAAK,MACD,OAAO9C,KAAK2B,IAAI,+BAAgCmB,GAGpD,IAAK,OACD,OAAO9C,KAAK2B,IAAI,gCAAiCmB,GAGrD,IAAK,QACD,OAAO9C,KAAK2B,IAAI,iCAAkCmB,GAGtD,QAAS,MAAO,KAIxBC,YAAW,WACP,GAA6C,SAAzCC,aAAaC,QAAQ,gBAIrB,OAHAjD,KAAKN,UAAYM,KAAKmC,YAAY7F,MAAQjD,IAAc,GAAGC,MAC3D0G,KAAKR,eAAgB,OACrBQ,KAAKT,gBAAiB,GAI1BX,EAAMoC,OAAO,kCAAmC,CAAE1E,KAAMlD,IAAe0H,aAAcC,WAAY,OAAQF,KAAM,GAAII,SAAS,EAAMjH,GAAIgG,KAAKJ,SAAS5F,KACpJgG,KAAKN,UAAYM,KAAKmC,YAAY7F,MAAQjD,IAAc,GAAGC,MAC3D0G,KAAKR,eAAgB,GAGzB0D,cAAa,WACqC,SAA1CF,aAAaC,QAAQ,iBAKzBrE,EAAMoC,OAAO,kCAAmC,CAAE1E,KAAMlD,IAAe0H,aAAcC,WAAY,SAAUF,KAAM,GAAII,SAAS,EAAMjH,GAAIgG,KAAKJ,SAAS5F,KAJlJgG,KAAKkB,wBAObA,qBAAoB,WAAU,IAADiC,EAAA,KACRnD,KAAKI,UAAU7C,QAAO,SAAAxD,GAAI,OAAKA,EAAKL,WAAayJ,EAAKvD,SAAS5F,MACvEoJ,SAAQ,SAAArJ,GACb6E,EAAMoC,OAAO,6BAA8B,CACvChH,GAAID,EAAKC,GACTN,SAAUyJ,EAAKvD,SAASlG,SACxBgH,SAAUyC,EAAKvD,SAASc,cAIhC9B,EAAMoC,OAAO,8BAA+B,CAAChB,KAAKJ,SAAS5F,MAG/DqJ,cAAa,SAAC3D,GACLA,IAILM,KAAKT,gBAAiB,EACtBS,KAAKN,UAAYA,IAGrB4D,aAAY,SAAC5D,GACTM,KAAKN,UAAYA,GAGrB6D,kBAAiB,WACbvD,KAAKT,gBAAiB,GAG1BiE,YAAW,SAAClE,GACRV,EAAMoC,OAAO,6BAA8B1B,GAC3CU,KAAKT,gBAAiB,EAEjBS,KAAKR,eACNQ,KAAKS,eAAe,CAChBV,WAAY,KACZW,UAAU,KAKtB+C,aAAY,SAACC,GACT,MAAO,CACH,qBAAsBA,IAI9BjD,eAAc,SAACkD,GACX,IAAI/D,EAAWI,KAAK4D,mBAAmBC,SACjCC,EAAW1C,IAAA,GACVxB,GAAQ,IACXlG,SAAUsG,KAAKJ,SAAS5F,GACxB+J,aAAc/D,KAAKJ,SAASmE,aAC5BhE,WAAY4D,EAAO5D,WACnBG,OAAQ,KACRI,OAAQ,GACR0D,SAAU,EACVtD,SAAUiD,EAAOjD,SACjB1G,GAAI6E,EAAMoF,aAGdrE,EAAWwC,OAAO8B,OAAOtE,EAAUkE,GACnClF,EAAMoC,OAAO,0BAA2BpB,Q", "file": "static/js/2.js", "sourcesContent": ["// style-loader: Adds some css to the DOM by adding a <style> tag\n\n// load the styles\nvar content = require(\"!!../../../../../../../../../../../../../../builds/shopware/6/product/commercial/src/Administration/Resources/app/administration/node_modules/mini-css-extract-plugin/dist/loader.js??ref--15-1!../../../../../../../../../../../../../../builds/shopware/6/product/commercial/src/Administration/Resources/app/administration/node_modules/css-loader/dist/cjs.js??ref--15-2!../../../../../../../../../../../../../../builds/shopware/6/product/commercial/src/Administration/Resources/app/administration/node_modules/sass-loader/dist/cjs.js??ref--15-3!./sw-flow-delay-action.scss\");\nif(content.__esModule) content = content.default;\nif(typeof content === 'string') content = [[module.id, content, '']];\nif(content.locals) module.exports = content.locals;\n// add the styles to the DOM\nvar add = require(\"!../../../../../../../../../../../../../../builds/shopware/6/product/commercial/src/Administration/Resources/app/administration/node_modules/vue-style-loader/lib/addStylesClient.js\").default\nvar update = add(\"1eac22e0\", content, true, {});", "export const enum SEQUENCE_TYPES {\n    ACTION = 'action',\n    CONDITION = 'condition',\n    DELAY_ACTION = 'delay_action',\n}\n\nexport const DELAY_OPTIONS = [\n    {\n        value: 'hour',\n        label: 'sw-flow-delay.modal.labelHour'\n    },\n    {\n        value: 'day',\n        label: 'sw-flow-delay.modal.labelDay'\n    },\n    {\n        value: 'week',\n        label: 'sw-flow-delay.modal.labelWeek'\n    },\n    {\n        value: 'month',\n        label: 'sw-flow-delay.modal.labelMonth'\n    },\n    {\n        value: 'custom',\n        label: 'sw-flow-delay.modal.labelCustom'\n    },\n] as const;\n\nexport const CUSTOM_TIME = 'custom' as const;\nexport const GENERAL_GROUP = 'general' as const;\n", "/**\n * Translates the list format produced by css-loader into something\n * easier to manipulate.\n */\nexport default function listToStyles (parentId, list) {\n  var styles = []\n  var newStyles = {}\n  for (var i = 0; i < list.length; i++) {\n    var item = list[i]\n    var id = item[0]\n    var css = item[1]\n    var media = item[2]\n    var sourceMap = item[3]\n    var part = {\n      id: parentId + ':' + i,\n      css: css,\n      media: media,\n      sourceMap: sourceMap\n    }\n    if (!newStyles[id]) {\n      styles.push(newStyles[id] = { id: id, parts: [part] })\n    } else {\n      newStyles[id].parts.push(part)\n    }\n  }\n  return styles\n}\n", "/*\n  MIT License http://www.opensource.org/licenses/mit-license.php\n  Author <PERSON> @sokra\n  Modified by <PERSON> @yyx990803\n*/\n\nimport listToStyles from './listToStyles'\n\nvar hasDocument = typeof document !== 'undefined'\n\nif (typeof DEBUG !== 'undefined' && DEBUG) {\n  if (!hasDocument) {\n    throw new Error(\n    'vue-style-loader cannot be used in a non-browser environment. ' +\n    \"Use { target: 'node' } in your Webpack config to indicate a server-rendering environment.\"\n  ) }\n}\n\n/*\ntype StyleObject = {\n  id: number;\n  parts: Array<StyleObjectPart>\n}\n\ntype StyleObjectPart = {\n  css: string;\n  media: string;\n  sourceMap: ?string\n}\n*/\n\nvar stylesInDom = {/*\n  [id: number]: {\n    id: number,\n    refs: number,\n    parts: Array<(obj?: StyleObjectPart) => void>\n  }\n*/}\n\nvar head = hasDocument && (document.head || document.getElementsByTagName('head')[0])\nvar singletonElement = null\nvar singletonCounter = 0\nvar isProduction = false\nvar noop = function () {}\nvar options = null\nvar ssrIdKey = 'data-vue-ssr-id'\n\n// Force single-tag solution on IE6-9, which has a hard limit on the # of <style>\n// tags it will allow on a page\nvar isOldIE = typeof navigator !== 'undefined' && /msie [6-9]\\b/.test(navigator.userAgent.toLowerCase())\n\nexport default function addStylesClient (parentId, list, _isProduction, _options) {\n  isProduction = _isProduction\n\n  options = _options || {}\n\n  var styles = listToStyles(parentId, list)\n  addStylesToDom(styles)\n\n  return function update (newList) {\n    var mayRemove = []\n    for (var i = 0; i < styles.length; i++) {\n      var item = styles[i]\n      var domStyle = stylesInDom[item.id]\n      domStyle.refs--\n      mayRemove.push(domStyle)\n    }\n    if (newList) {\n      styles = listToStyles(parentId, newList)\n      addStylesToDom(styles)\n    } else {\n      styles = []\n    }\n    for (var i = 0; i < mayRemove.length; i++) {\n      var domStyle = mayRemove[i]\n      if (domStyle.refs === 0) {\n        for (var j = 0; j < domStyle.parts.length; j++) {\n          domStyle.parts[j]()\n        }\n        delete stylesInDom[domStyle.id]\n      }\n    }\n  }\n}\n\nfunction addStylesToDom (styles /* Array<StyleObject> */) {\n  for (var i = 0; i < styles.length; i++) {\n    var item = styles[i]\n    var domStyle = stylesInDom[item.id]\n    if (domStyle) {\n      domStyle.refs++\n      for (var j = 0; j < domStyle.parts.length; j++) {\n        domStyle.parts[j](item.parts[j])\n      }\n      for (; j < item.parts.length; j++) {\n        domStyle.parts.push(addStyle(item.parts[j]))\n      }\n      if (domStyle.parts.length > item.parts.length) {\n        domStyle.parts.length = item.parts.length\n      }\n    } else {\n      var parts = []\n      for (var j = 0; j < item.parts.length; j++) {\n        parts.push(addStyle(item.parts[j]))\n      }\n      stylesInDom[item.id] = { id: item.id, refs: 1, parts: parts }\n    }\n  }\n}\n\nfunction createStyleElement () {\n  var styleElement = document.createElement('style')\n  styleElement.type = 'text/css'\n  head.appendChild(styleElement)\n  return styleElement\n}\n\nfunction addStyle (obj /* StyleObjectPart */) {\n  var update, remove\n  var styleElement = document.querySelector('style[' + ssrIdKey + '~=\"' + obj.id + '\"]')\n\n  if (styleElement) {\n    if (isProduction) {\n      // has SSR styles and in production mode.\n      // simply do nothing.\n      return noop\n    } else {\n      // has SSR styles but in dev mode.\n      // for some reason Chrome can't handle source map in server-rendered\n      // style tags - source maps in <style> only works if the style tag is\n      // created and inserted dynamically. So we remove the server rendered\n      // styles and inject new ones.\n      styleElement.parentNode.removeChild(styleElement)\n    }\n  }\n\n  if (isOldIE) {\n    // use singleton mode for IE9.\n    var styleIndex = singletonCounter++\n    styleElement = singletonElement || (singletonElement = createStyleElement())\n    update = applyToSingletonTag.bind(null, styleElement, styleIndex, false)\n    remove = applyToSingletonTag.bind(null, styleElement, styleIndex, true)\n  } else {\n    // use multi-style-tag mode in all other cases\n    styleElement = createStyleElement()\n    update = applyToTag.bind(null, styleElement)\n    remove = function () {\n      styleElement.parentNode.removeChild(styleElement)\n    }\n  }\n\n  update(obj)\n\n  return function updateStyle (newObj /* StyleObjectPart */) {\n    if (newObj) {\n      if (newObj.css === obj.css &&\n          newObj.media === obj.media &&\n          newObj.sourceMap === obj.sourceMap) {\n        return\n      }\n      update(obj = newObj)\n    } else {\n      remove()\n    }\n  }\n}\n\nvar replaceText = (function () {\n  var textStore = []\n\n  return function (index, replacement) {\n    textStore[index] = replacement\n    return textStore.filter(Boolean).join('\\n')\n  }\n})()\n\nfunction applyToSingletonTag (styleElement, index, remove, obj) {\n  var css = remove ? '' : obj.css\n\n  if (styleElement.styleSheet) {\n    styleElement.styleSheet.cssText = replaceText(index, css)\n  } else {\n    var cssNode = document.createTextNode(css)\n    var childNodes = styleElement.childNodes\n    if (childNodes[index]) styleElement.removeChild(childNodes[index])\n    if (childNodes.length) {\n      styleElement.insertBefore(cssNode, childNodes[index])\n    } else {\n      styleElement.appendChild(cssNode)\n    }\n  }\n}\n\nfunction applyToTag (styleElement, obj) {\n  var css = obj.css\n  var media = obj.media\n  var sourceMap = obj.sourceMap\n\n  if (media) {\n    styleElement.setAttribute('media', media)\n  }\n  if (options.ssrId) {\n    styleElement.setAttribute(ssrIdKey, obj.id)\n  }\n\n  if (sourceMap) {\n    // https://developer.chrome.com/devtools/docs/javascript-debugging\n    // this makes source maps inside style tags work properly in Chrome\n    css += '\\n/*# sourceURL=' + sourceMap.sources[0] + ' */'\n    // http://stackoverflow.com/a/26603875\n    css += '\\n/*# sourceMappingURL=data:application/json;base64,' + btoa(unescape(encodeURIComponent(JSON.stringify(sourceMap)))) + ' */'\n  }\n\n  if (styleElement.styleSheet) {\n    styleElement.styleSheet.cssText = css\n  } else {\n    while (styleElement.firstChild) {\n      styleElement.removeChild(styleElement.firstChild)\n    }\n    styleElement.appendChild(document.createTextNode(css))\n  }\n}\n", "export default \"{% block sw_flow_sequence_action %}\\n    <div\\n        class=\\\"sw-flow-delay-action__wrapper\\\"\\n        v-tooltip=\\\"{\\n            message: $tc('sw-flow.actions.tooltipActionDisabled'),\\n            disabled: getLicense('FLOW_BUILDER-8478732')\\n        }\\\"\\n        :class=\\\"{'sw-flow-delay-action__disabled': !getLicense('FLOW_BUILDER-8478732')}\\\"\\n    >\\n        <div\\n            class=\\\"sw-flow-sequence-action__card sw-flow-delay-action__delay_card\\\"\\n            :class=\\\"arrowClasses(sequence.config?.delay)\\\"\\n        >\\n            <div class=\\\"sw-flow-sequence-action__header\\\">\\n                <h3 class=\\\"sw-flow-sequence-action__title\\\">\\n                    {{ $tc('sw-flow-delay.detail.sequence.delayActionTitle') }}\\n                </h3>\\n\\n                <sw-context-button class=\\\"sw-flow-sequence-action__context-button\\\">\\n                    <sw-context-menu-item\\n                        v-if=\\\"sequence.config?.delay\\\"\\n                        class=\\\"sw-flow-delay-action__edit\\\"\\n                        @click=\\\"onEditDelay\\\"\\n                    >\\n                        {{ $tc('sw-flow-delay.action.contextButton.editDelayAction') }}\\n                    </sw-context-menu-item>\\n\\n                    <sw-context-menu-item\\n                        variant=\\\"danger\\\"\\n                        class=\\\"sw-flow-delay-action__delete\\\"\\n                        @click=\\\"onDeleteDelay\\\"\\n                    >\\n                        {{ $tc('sw-flow-delay.action.contextButton.deleteDelayAction') }}\\n                    </sw-context-menu-item>\\n                </sw-context-button>\\n            </div>\\n\\n            <div class=\\\"sw-flow-sequence-action__content\\\">\\n                <div class=\\\"sw-flow-sequence-action__actions\\\">\\n                    <div\\n                        v-if=\\\"!sequence.config?.delay\\\"\\n                        class=\\\"sw-flow-sequence-action__actions-empty\\\"\\n                    >\\n                        <sw-icon\\n                            size=\\\"16px\\\"\\n                            name=\\\"regular-hourglass\\\"\\n                        />\\n                        <span class=\\\"sw-flow-sequence-action__no-action\\\">\\n                        {{ $tc('sw-flow-delay.detail.sequence.noDelayAction') }}\\n                    </span>\\n                    </div>\\n\\n                    <div v-else class=\\\"sw-flow-sequence-action__action-list\\\">\\n                        <li class=\\\"sw-flow-sequence-action__action-item\\\">\\n                            <div class=\\\"sw-flow-delay-action__action-header\\\">\\n                                <div class=\\\"sw-flow-delay-action__action-name\\\">\\n                                    <sw-icon\\n                                        size=\\\"16px\\\"\\n                                        name=\\\"regular-hourglass\\\"\\n                                    />\\n\\n                                    <h3 v-if=\\\"showCustomDescription\\\">{{ customTimeDescription }}</h3>\\n                                    <h3 v-else>{{ timeDescription }}</h3>\\n                                </div>\\n                            </div>\\n                        </li>\\n                    </div>\\n                </div>\\n\\n                <div\\n                    v-if=\\\"showDelayElement && !sequence.config?.delay\\\"\\n                    class=\\\"sw-flow-sequence-action__add-action\\\"\\n                >\\n                    <div class=\\\"sw-flow-sequence-action__select\\\">\\n                        <sw-single-select\\n                            class=\\\"sw-flow-sequence-action__selection-action\\\"\\n                            size=\\\"small\\\"\\n                            value=\\\"\\\"\\n                            :placeholder=\\\"$tc('sw-flow.actions.placeholderSelectAction')\\\"\\n                            :options=\\\"actionDelayOptions\\\"\\n                            :popover-classes=\\\"['sw-flow-delay-action__popover']\\\"\\n                            :error=\\\"fieldError\\\"\\n                            {% if VUE3 %}\\n                            @update:value=\\\"onSelectDelay\\\"\\n                            {% else %}\\n                            @change=\\\"onSelectDelay\\\"\\n                            {% endif %}\\n                        >\\n                        </sw-single-select>\\n                    </div>\\n                </div>\\n            </div>\\n        </div>\\n\\n        <div\\n            v-if=\\\"sequence.config?.delay\\\"\\n            class=\\\"sw-flow-delay-action__then-arrow\\\"\\n        >\\n            <div class=\\\"sw-flow-delay-action__then-line\\\"></div>\\n\\n            <div class=\\\"sw-flow-delay-action__then-oval\\\"></div>\\n\\n            <sw-icon\\n                name=\\\"regular-chevron-right-s\\\"\\n                small\\n            />\\n            <sw-label\\n                appearance=\\\"pill\\\"\\n                size=\\\"medium\\\"\\n                class=\\\"sw-flow-delay-action__true-label\\\"\\n            >\\n                {{ $tc('sw-flow-delay.detail.sequence.labelThen') }}\\n            </sw-label>\\n        </div>\\n\\n        <div\\n            v-if=\\\"showDelayElement && !sequence.config?.delay\\\"\\n            class=\\\"sw-flow-delay-action__help-text\\\"\\n        >\\n            <h3>{{ $tc('sw-flow-delay.detail.sequence.delayActionExplainsTitle') }}</h3>\\n\\n            <p v-html=\\\"$tc('sw-flow-delay.detail.sequence.delayActionExplainsDescription')\\\"></p>\\n        </div>\\n\\n        <sw-flow-delay-modal\\n            v-if=\\\"showDelayModal\\\"\\n            :sequence=\\\"sequence\\\"\\n            :type=\\\"delayType\\\"\\n            :is-update-delay=\\\"isUpdateDelay\\\"\\n            @type-change=\\\"onChangeType\\\"\\n            @modal-save=\\\"onSaveDelay\\\"\\n            @modal-close=\\\"onCloseDelayModal\\\"\\n        />\\n    </div>\\n{% endblock %}\\n\";", "import template from './sw-flow-delay-action.html.twig';\nimport './sw-flow-delay-action.scss';\nimport {DELAY_OPTIONS, CUSTOM_TIME, SEQUENCE_TYPES} from '../../../../constant/sw-flow-delay.constant';\nimport {DelayType, DelayAction, DelayConfig, Sequence, WarningConfig} from '../../../../type/types';\n\nconst { Component, State } = Shopware;\nconst utils = Shopware.Utils;\nconst { mapGetters, mapState } = Component.getComponentHelper();\n\n/**\n * @package services-settings\n */\nexport default Component.wrapComponentConfig({\n    inject: ['flowBuilderService'],\n\n    template,\n\n    data():{\n        showDelayModal: boolean,\n        isUpdateDelay: boolean,\n        showWarningDeleteDelay: boolean,\n        delayType: DelayType,\n    } {\n        return {\n            showDelayModal: false,\n            isUpdateDelay: false,\n            showWarningDeleteDelay: false,\n            delayType: 'hour',\n        }\n    },\n\n    watch: {\n        sequence: {\n            handler(value: Sequence): void {\n                if (value.actionName !== this.delayConstant || value.ruleId) {\n                    return;\n                }\n\n                const selectorSequence = this.sequences.find(item => (item.parentId === value.id));\n\n                if (!selectorSequence?.id && value.config?.delay && !value.trueBlock) {\n                    this.createSequence({\n                        actionName: null,\n                        trueCase: true,\n                    })\n                }\n            },\n            immediate: true,\n        },\n\n        showWarningModal(value: WarningConfig): void {\n            if (value.name === SEQUENCE_TYPES.DELAY_ACTION && value.actionType === 'EDIT' && value.id === this.sequence.id) {\n                this.showDelayModal = true;\n                State.commit('swFlowDelay/setShowWarningModal', { type: '', name: '', enabled: false, id: '' });\n            }\n\n            if (value.name === SEQUENCE_TYPES.DELAY_ACTION && value.actionType === 'DELETE' && value.id === this.sequence.id) {\n                this.onConfirmDeleteDelay();\n            }\n        },\n    },\n\n    computed: {\n        ...mapGetters('swFlowState', ['sequences']),\n        ...mapState('swFlowDelay', ['showWarningModal']),\n\n        delayConstant(): string {\n            return this.flowBuilderService.getActionName('DELAY');\n        },\n\n        actionDelayOptions(): Array<DelayAction> {\n            return DELAY_OPTIONS.map(option => {\n                return {\n                    ...option,\n                    label: this.$tc(option.label),\n                }\n            })\n        },\n\n        showDelayElement(): boolean {\n            return this.sequence.actionName === this.delayConstant;\n        },\n\n        showCustomDescription(): boolean {\n            return this.sequence.config.delay?.length > 1;\n        },\n\n        customTimeDescription(): string {\n            const { delay } = this.sequence.config;\n\n            const month = this.convertTimeString(delay[0].type, delay[0].value);\n            const week = this.convertTimeString(delay[1].type, delay[1].value);\n            const day = this.convertTimeString(delay[2].type, delay[2].value);\n            const hour = this.convertTimeString(delay[3].type, delay[3].value);\n\n            return [month, week, day, hour].filter(item => item).join();\n        },\n\n        timeDescription(): string {\n            const { actionName } = this.sequence;\n            if (actionName !== this.delayConstant) {\n                return null;\n            }\n\n            const { type, value } = this.delayConfig;\n            return this.convertTimeString(type, value);\n        },\n\n        delayConfig(): DelayConfig {\n            const { config } = this.sequence;\n\n            if (!config.delay || !Object.values(config.delay).length) {\n                return {\n                    type: null,\n                    value: null,\n                };\n            }\n\n            if (config.delay.length === 1) {\n                return {\n                    type: config.delay[0].type,\n                    value: config.delay[0].value,\n                }\n            }\n\n            return {\n                type: CUSTOM_TIME,\n                value: null,\n            };\n        }\n    },\n\n    methods: {\n        getLicense(toggle: string): boolean {\n            return Shopware.License.get(toggle);\n        },\n\n        convertTimeString(type: string, value: string): string {\n            if (!value) {\n                return '';\n            }\n\n            const unit = this.getTimeLabel(type, value);\n            return ` ${value} ${unit}`;\n        },\n\n        getTimeLabel(type: string, number: number): string {\n            switch (type) {\n\n                case 'hour': {\n                    return this.$tc('sw-flow-delay.modal.labelHour', number);\n                }\n\n                case 'day': {\n                    return this.$tc('sw-flow-delay.modal.labelDay', number);\n                }\n\n                case 'week':{\n                    return this.$tc('sw-flow-delay.modal.labelWeek', number);\n                }\n\n                case 'month': {\n                    return this.$tc('sw-flow-delay.modal.labelMonth', number);\n                }\n\n                default: return '';\n            }\n        },\n\n        onEditDelay(): void {\n            if (localStorage.getItem('delay_action') === 'true') {\n                this.delayType = this.delayConfig.type || DELAY_OPTIONS[0].value;\n                this.isUpdateDelay = true;\n                this.showDelayModal = true;\n                return;\n            }\n\n            State.commit('swFlowDelay/setShowWarningModal', { type: SEQUENCE_TYPES.DELAY_ACTION, actionType: 'EDIT', name: '', enabled: true, id: this.sequence.id });\n            this.delayType = this.delayConfig.type || DELAY_OPTIONS[0].value;\n            this.isUpdateDelay = true;\n        },\n\n        onDeleteDelay(): void {\n            if (localStorage.getItem('delay_deleted') === 'true') {\n                this.onConfirmDeleteDelay();\n                return;\n            }\n\n            State.commit('swFlowDelay/setShowWarningModal', { type: SEQUENCE_TYPES.DELAY_ACTION, actionType: 'DELETE', name: '', enabled: true, id: this.sequence.id });\n        },\n\n        onConfirmDeleteDelay(): void {\n            const children = this.sequences.filter(item => (item.parentId === this.sequence.id))\n            children.forEach(item => {\n                State.commit('swFlowState/updateSequence', {\n                    id: item.id,\n                    parentId: this.sequence.parentId,\n                    trueCase: this.sequence.trueCase\n                });\n            })\n\n            State.commit('swFlowState/removeSequences', [this.sequence.id]);\n        },\n\n        onSelectDelay(delayType: DelayType): void {\n            if (!delayType) {\n                return;\n            }\n\n            this.showDelayModal = true;\n            this.delayType = delayType;\n        },\n\n        onChangeType(delayType: DelayType): void {\n            this.delayType = delayType;\n        },\n\n        onCloseDelayModal(): void {\n            this.showDelayModal = false;\n        },\n\n        onSaveDelay(data: Sequence): void {\n            State.commit('swFlowState/updateSequence', data);\n            this.showDelayModal = false;\n\n            if (!this.isUpdateDelay) {\n                this.createSequence({\n                    actionName: null,\n                    trueCase: true,\n                });\n            }\n        },\n\n        arrowClasses(thenCase: boolean) {\n            return {\n                'has--then-selector': thenCase,\n            };\n        },\n\n        createSequence(params): void {\n            let sequence = this.sequenceRepository.create();\n            const newSequence = {\n                ...sequence,\n                parentId: this.sequence.id,\n                displayGroup: this.sequence.displayGroup,\n                actionName: params.actionName,\n                ruleId: null,\n                config: {},\n                position: 1,\n                trueCase: params.trueCase,\n                id: utils.createId(),\n            };\n\n            sequence = Object.assign(sequence, newSequence);\n            State.commit('swFlowState/addSequence', sequence);\n        },\n    }\n});\n"], "sourceRoot": ""}
/*! For license information please see 9.js.LICENSE.txt */
(this["webpackJsonpPlugindelayed-flow-action"]=this["webpackJsonpPlugindelayed-flow-action"]||[]).push([[9],{EFTm:function(t,e,n){"use strict";n.d(e,"c",(function(){return r})),n.d(e,"b",(function(){return o})),n.d(e,"a",(function(){return i}));var r=function(t){return t.ACTION="action",t.CONDITION="condition",t.DELAY_ACTION="delay_action",t}({}),o=[{value:"hour",label:"sw-flow-delay.modal.labelHour"},{value:"day",label:"sw-flow-delay.modal.labelDay"},{value:"week",label:"sw-flow-delay.modal.labelWeek"},{value:"month",label:"sw-flow-delay.modal.labelMonth"},{value:"custom",label:"sw-flow-delay.modal.labelCustom"}],i="custom"},I5w2:function(t,e,n){"use strict";n.r(e);var r=n("EFTm");function o(t){return(o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function i(){i=function(){return t};var t={},e=Object.prototype,n=e.hasOwnProperty,r=Object.defineProperty||function(t,e,n){t[e]=n.value},a="function"==typeof Symbol?Symbol:{},c=a.iterator||"@@iterator",u=a.asyncIterator||"@@asyncIterator",l=a.toStringTag||"@@toStringTag";function s(t,e,n){return Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{s({},"")}catch(t){s=function(t,e,n){return t[e]=n}}function f(t,e,n,o){var i=e&&e.prototype instanceof p?e:p,a=Object.create(i.prototype),c=new I(o||[]);return r(a,"_invoke",{value:D(t,n,c)}),a}function d(t,e,n){try{return{type:"normal",arg:t.call(e,n)}}catch(t){return{type:"throw",arg:t}}}t.wrap=f;var h={};function p(){}function y(){}function v(){}var m={};s(m,c,(function(){return this}));var w=Object.getPrototypeOf,g=w&&w(w(S([])));g&&g!==e&&n.call(g,c)&&(m=g);var b=v.prototype=p.prototype=Object.create(m);function O(t){["next","throw","return"].forEach((function(e){s(t,e,(function(t){return this._invoke(e,t)}))}))}function x(t,e){function i(r,a,c,u){var l=d(t[r],t,a);if("throw"!==l.type){var s=l.arg,f=s.value;return f&&"object"==o(f)&&n.call(f,"__await")?e.resolve(f.__await).then((function(t){i("next",t,c,u)}),(function(t){i("throw",t,c,u)})):e.resolve(f).then((function(t){s.value=t,c(s)}),(function(t){return i("throw",t,c,u)}))}u(l.arg)}var a;r(this,"_invoke",{value:function(t,n){function r(){return new e((function(e,r){i(t,n,e,r)}))}return a=a?a.then(r,r):r()}})}function D(t,e,n){var r="suspendedStart";return function(o,i){if("executing"===r)throw new Error("Generator is already running");if("completed"===r){if("throw"===o)throw i;return j()}for(n.method=o,n.arg=i;;){var a=n.delegate;if(a){var c=_(a,n);if(c){if(c===h)continue;return c}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if("suspendedStart"===r)throw r="completed",n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);r="executing";var u=d(t,e,n);if("normal"===u.type){if(r=n.done?"completed":"suspendedYield",u.arg===h)continue;return{value:u.arg,done:n.done}}"throw"===u.type&&(r="completed",n.method="throw",n.arg=u.arg)}}}function _(t,e){var n=e.method,r=t.iterator[n];if(void 0===r)return e.delegate=null,"throw"===n&&t.iterator.return&&(e.method="return",e.arg=void 0,_(t,e),"throw"===e.method)||"return"!==n&&(e.method="throw",e.arg=new TypeError("The iterator does not provide a '"+n+"' method")),h;var o=d(r,t.iterator,e.arg);if("throw"===o.type)return e.method="throw",e.arg=o.arg,e.delegate=null,h;var i=o.arg;return i?i.done?(e[t.resultName]=i.value,e.next=t.nextLoc,"return"!==e.method&&(e.method="next",e.arg=void 0),e.delegate=null,h):i:(e.method="throw",e.arg=new TypeError("iterator result is not an object"),e.delegate=null,h)}function E(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function L(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function I(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(E,this),this.reset(!0)}function S(t){if(t){var e=t[c];if(e)return e.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var r=-1,o=function e(){for(;++r<t.length;)if(n.call(t,r))return e.value=t[r],e.done=!1,e;return e.value=void 0,e.done=!0,e};return o.next=o}}return{next:j}}function j(){return{value:void 0,done:!0}}return y.prototype=v,r(b,"constructor",{value:v,configurable:!0}),r(v,"constructor",{value:y,configurable:!0}),y.displayName=s(v,l,"GeneratorFunction"),t.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===y||"GeneratorFunction"===(e.displayName||e.name))},t.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,v):(t.__proto__=v,s(t,l,"GeneratorFunction")),t.prototype=Object.create(b),t},t.awrap=function(t){return{__await:t}},O(x.prototype),s(x.prototype,u,(function(){return this})),t.AsyncIterator=x,t.async=function(e,n,r,o,i){void 0===i&&(i=Promise);var a=new x(f(e,n,r,o),i);return t.isGeneratorFunction(n)?a:a.next().then((function(t){return t.done?t.value:a.next()}))},O(b),s(b,l,"Generator"),s(b,c,(function(){return this})),s(b,"toString",(function(){return"[object Generator]"})),t.keys=function(t){var e=Object(t),n=[];for(var r in e)n.push(r);return n.reverse(),function t(){for(;n.length;){var r=n.pop();if(r in e)return t.value=r,t.done=!1,t}return t.done=!0,t}},t.values=S,I.prototype={constructor:I,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,this.tryEntries.forEach(L),!t)for(var e in this)"t"===e.charAt(0)&&n.call(this,e)&&!isNaN(+e.slice(1))&&(this[e]=void 0)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var e=this;function r(n,r){return a.type="throw",a.arg=t,e.next=n,r&&(e.method="next",e.arg=void 0),!!r}for(var o=this.tryEntries.length-1;o>=0;--o){var i=this.tryEntries[o],a=i.completion;if("root"===i.tryLoc)return r("end");if(i.tryLoc<=this.prev){var c=n.call(i,"catchLoc"),u=n.call(i,"finallyLoc");if(c&&u){if(this.prev<i.catchLoc)return r(i.catchLoc,!0);if(this.prev<i.finallyLoc)return r(i.finallyLoc)}else if(c){if(this.prev<i.catchLoc)return r(i.catchLoc,!0)}else{if(!u)throw new Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return r(i.finallyLoc)}}}},abrupt:function(t,e){for(var r=this.tryEntries.length-1;r>=0;--r){var o=this.tryEntries[r];if(o.tryLoc<=this.prev&&n.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var i=o;break}}i&&("break"===t||"continue"===t)&&i.tryLoc<=e&&e<=i.finallyLoc&&(i=null);var a=i?i.completion:{};return a.type=t,a.arg=e,i?(this.method="next",this.next=i.finallyLoc,h):this.complete(a)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),h},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var n=this.tryEntries[e];if(n.finallyLoc===t)return this.complete(n.completion,n.afterLoc),L(n),h}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var n=this.tryEntries[e];if(n.tryLoc===t){var r=n.completion;if("throw"===r.type){var o=r.arg;L(n)}return o}}throw new Error("illegal catch attempt")},delegateYield:function(t,e,n){return this.delegate={iterator:S(t),resultName:e,nextLoc:n},"next"===this.method&&(this.arg=void 0),h}},t}function a(t,e,n,r,o,i,a){try{var c=t[i](a),u=c.value}catch(t){return void n(t)}c.done?e(u):Promise.resolve(u).then(r,o)}function c(t){return function(){var e=this,n=arguments;return new Promise((function(r,o){var i=t.apply(e,n);function c(t){a(i,r,o,c,u,"next",t)}function u(t){a(i,r,o,c,u,"throw",t)}c(void 0)}))}}function u(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,r)}return n}function l(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?u(Object(n),!0).forEach((function(e){s(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):u(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}function s(t,e,n){return(e=function(t){var e=function(t,e){if("object"!==o(t)||null===t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var r=n.call(t,e||"default");if("object"!==o(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"===o(e)?e:String(e)}(e))in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}var f=Shopware,d=f.Component,h=f.State,p=Shopware.Data.Criteria,y=Shopware.Utils,v=d.getComponentHelper().mapState;e.default=d.wrapComponentConfig({template:'{% block sw_flow_sequence_condition_true_arrow_extension_options %}\n    <sw-context-menu-item\n        class="sw-flow-sequence-condition__add-true-action"\n        :disabled="disabled"\n        @click="addDelayAction(true)"\n    >\n        {{ $tc(\'sw-flow-delay.detail.sequence.selectorDelayAction\') }}\n    </sw-context-menu-item>\n{% endblock %}\n\n{% block sw_flow_sequence_condition_false_arrow_extension_options %}\n    <sw-context-menu-item\n        class="sw-flow-sequence-condition__add-false-action"\n        :disabled="disabled"\n        @click="addDelayAction(false)"\n    >\n        {{ $tc(\'sw-flow-delay.detail.sequence.selectorDelayAction\') }}\n    </sw-context-menu-item>\n{% endblock %}\n',inject:["flowBuilderService"],computed:l({delayConstant:function(){return this.flowBuilderService.getActionName("DELAY")},delayedActionsRepository:function(){return this.repositoryFactory.create("swag_delay_action")},delayedActionsCriteria:function(){var t=new p(1,1);return t.addFilter(p.equalsAny("delaySequenceId",this.getDelayIds(this.sequence.id))),t}},v("swFlowDelay",["showWarningModal"])),watch:{showWarningModal:function(t){t.name===r.c.CONDITION&&this.sequence.id===t.id&&"ADD"===t.actionType&&this.$super("onRuleChange",t.rule),t.name===r.c.CONDITION&&this.sequence.id===t.id&&"DELETE"===t.actionType&&this.$super("removeCondition")}},methods:{addDelayAction:function(t){var e=this.sequenceRepository.create(),n=l(l({},e),{},{parentId:this.sequence.id,displayGroup:this.sequence.displayGroup,actionName:"action.delay",ruleId:null,config:{},position:1,trueCase:t,id:y.createId()});e=Object.assign(e,n),h.commit("swFlowState/addSequence",e)},hasDelay:function(t,e){var n=t.find((function(t){return t.id===e}));return!!n&&(n.actionName===this.delayConstant||this.hasDelay(t,n.parentId))},getDelayIds:function(t){var e=this,n=[];return function t(n,r){var o=e.sequences.filter((function(t){return t.parentId===n}));return o.length?o.forEach((function(n){return n.actionName||n.ruleId?(n.ruleId||n.actionName===e.delayConstant&&r.push(n.id),t(n.id,r)):[]})):[]}(t,n),n},getDelayedActionData:function(){var t=this;return c(i().mark((function e(){return i().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(t.getDelayIds(t.sequence.id).length){e.next=2;break}return e.abrupt("return",[]);case 2:return e.prev=2,e.next=5,t.delayedActionsRepository.search(t.delayedActionsCriteria);case 5:return e.abrupt("return",e.sent);case 8:return e.prev=8,e.t0=e.catch(2),e.abrupt("return",[]);case 11:case"end":return e.stop()}}),e,null,[[2,8]])})))()},onRuleChange:function(t){t&&(this.hasDelay(this.sequences,this.sequence.parentId)&&"true"!==localStorage.getItem("condition")?h.commit("swFlowDelay/setShowWarningModal",{actionType:"ADD",type:r.c.CONDITION,id:this.sequence.id,enabled:!0,rule:t}):this.$super("onRuleChange",t))},removeCondition:function(){var t=this;return c(i().mark((function e(){return i().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,t.getDelayedActionData();case 2:e.sent.length||t.hasDelay(t.sequences,t.sequence.parentId)&&"true"!==localStorage.getItem("condition")?h.commit("swFlowDelay/setShowWarningModal",{actionType:"DELETE",type:r.c.CONDITION,id:t.sequence.id,enabled:!0}):t.$super("removeCondition");case 4:case"end":return e.stop()}}),e)})))()}}})}}]);
//# sourceMappingURL=9.js.map
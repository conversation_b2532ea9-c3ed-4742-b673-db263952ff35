(this["webpackJsonpPlugindelayed-flow-action"]=this["webpackJsonpPlugindelayed-flow-action"]||[]).push([[10],{qq5Y:function(e,t,n){"use strict";n.r(t);function r(e){return(r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function o(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function i(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?o(Object(n),!0).forEach((function(t){c(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):o(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function c(e,t,n){return(t=function(e){var t=function(e,t){if("object"!==r(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var o=n.call(e,t||"default");if("object"!==r(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===r(t)?t:String(t)}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var a=Shopware.Component,l=a.getComponentHelper(),u=l.mapState,f=l.mapGetters;t.default=a.wrapComponentConfig({template:'{% block sw_flow_event_change_confirm_modal_text_confirmation %}\n    <sw-alert\n        v-if="isShowConfirmOverride"\n        variant="warning"\n    >\n        {{ $tc(\'sw-flow-delay.trigger.modal.textConfirmChangeTrigger\') }}\n    </sw-alert>\n\n    <div v-else>\n        {% parent %}\n    </div>\n{% endblock %}\n',computed:i(i(i({},u("swFlowState",["flow"])),f("swFlowState",["sequences"])),{},{hasDelayedActions:function(){return this.sequences.some((function(e){return"action.delay"===e.actionName}))},isShowConfirmOverride:function(){return!this.flow.active&&this.hasDelayedActions}})})}}]);
//# sourceMappingURL=10.js.map
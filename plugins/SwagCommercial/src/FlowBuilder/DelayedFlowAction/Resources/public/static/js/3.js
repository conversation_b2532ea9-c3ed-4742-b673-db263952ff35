(this["webpackJsonpPlugindelayed-flow-action"]=this["webpackJsonpPlugindelayed-flow-action"]||[]).push([[3],{"4Cm0":function(e,t,n){},CTNz:function(e,t,n){"use strict";n.r(t);n("zeIh");function i(e){return(i="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function o(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);t&&(i=i.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,i)}return n}function a(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?o(Object(n),!0).forEach((function(t){l(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):o(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function l(e,t,n){return(t=function(e){var t=function(e,t){if("object"!==i(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var o=n.call(e,t||"default");if("object"!==i(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===i(t)?t:String(t)}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var s=Shopware,r=s.Component,c=s.Mixin,d=Shopware.Data.Criteria,u=r.getComponentHelper(),f=u.mapGetters,p=u.mapState;t.default=r.wrapComponentConfig({template:'<sw-modal\n    class="sw-flow-action-detail-modal"\n    :title="$tc(\'sw-flow-delay.delay.itemDetail.details\')"\n    :closable="false"\n    :is-loading="isLoading"\n    @modal-close="onCloseModal"\n>\n    <sw-container\n        v-if="isActionDetail"\n        class="sw-flow-action-detail-modal__actions"\n    >\n        <ul>\n            <span class="sw-flow-action-detail-modal__title">{{ getActionTitle }}</span>\n            <li v-for="(item, index) in sequenceTree" :key="index">\n                <div class="sw-flow-action-detail-modal__action-name">\n                    <sw-flow-sequence-label\n                        classes="sw-flow-action-detail-modal__label"\n                        :sequence="item"\n                        :app-flow-actions="appFlowActions"\n                    />\n                </div>\n                <div class="sw-flow-action-detail-modal__action-config">\n                    <span v-html="getActionDescriptions(item)" />\n                </div>\n            </li>\n        </ul>\n    </sw-container>\n\n    <sw-container\n        v-else\n        class="sw-flow-action-detail-modal__condition"\n    >\n        <ul>\n            <span class="sw-flow-action-detail-modal__title">\n                {{ $tc(\'sw-flow-delay.delay.itemDetail.condition\') }}\n            </span>\n            <li class="sw-flow-action-detail-modal__condition-label">\n                <sw-icon\n                    class="sw-flow-action-detail-modal__condition-icon"\n                    size="14px"\n                    name="regular-rule-s"\n                />\n                <span class="sw-flow-action-detail-modal__condition-name">{{ conditionName }}</span>\n            </li>\n        </ul>\n\n        <div\n            v-if="actionCases(true).length > 0"\n            class="sw-flow-action-detail-modal__case"\n        >\n            <span class="sw-flow-action-detail-modal__case-title">\n                {{ $tc(\'sw-flow-delay.delay.itemDetail.conditionIf\') }}\n            </span>\n            <sw-label\n                appearance="pill"\n                size="medium"\n                class="sw-flow-action-detail-modal__true-label"\n            >\n                {{ $tc(\'sw-flow.detail.sequence.labelTrue\') }}\n            </sw-label>\n\n            <ul>\n                <li v-for="(item, index) in actionCases(true)" :key="index">\n                    <sw-flow-sequence-label\n                        classes="sw-flow-action-detail-modal__label"\n                        :sequence="item"\n                        :app-flow-actions="appFlowActions"\n                        @click=""\n                    />\n\n                    <div class="sw-flow-action-detail-modal__action-config">\n                        <span v-html="getActionDescriptions(item)" />\n                    </div>\n                </li>\n            </ul>\n        </div>\n\n        <div\n            v-if="actionCases(false).length > 0"\n            class="sw-flow-action-detail-modal__case"\n        >\n            <span class="sw-flow-action-detail-modal__case-title">\n                {{ $tc(\'sw-flow-delay.delay.itemDetail.conditionIf\') }}\n            </span>\n            <sw-label\n                appearance="pill"\n                size="medium"\n                class="sw-flow-action-detail-modal__false-label"\n            >\n                {{ $tc(\'sw-flow.detail.sequence.labelFalse\') }}\n            </sw-label>\n\n            <ul>\n                <li v-for="(item, index) in actionCases(false)" :key="index">\n                    <sw-flow-sequence-label\n                        classes="sw-flow-action-detail-modal__label"\n                        :sequence="item"\n                        :app-flow-actions="appFlowActions"\n                        @click=""\n                    />\n\n                    <div class="sw-flow-action-detail-modal__action-config">\n                        <span v-html="getActionDescriptions(item)" />\n                    </div>\n                </li>\n            </ul>\n        </div>\n    </sw-container>\n\n    <template #modal-footer>\n        {% block swag_flow_detail_action_modal_cancel_button %}\n        <sw-button\n            size="small"\n            variant="primary"\n            class="sw-flow-action-detail-modal__button_close"\n            @click="onCloseModal"\n        >\n            {{ $tc(\'global.default.close\') }}\n        </sw-button>\n        {% endblock %}\n    </template>\n</sw-modal>\n',inject:["flowBuilderService","repositoryFactory"],mixins:[c.getByName("notification"),c.getByName("sw-inline-snippet")],props:{sequence:{type:Object,default:null},appFlowActions:{type:Array,default:[]}},data:function(){return{sequenceTree:[],actionsTrueCase:[],actionsFalseCase:[],isLoading:!1}},created:function(){this.createdComponent()},computed:a(a({flowSequenceRepository:function(){return this.repositoryFactory.create("flow_sequence")},flowSequenceCriteria:function(){var e=new d;return e.addAssociation("children"),e.addAssociation("rule"),e.addSorting(d.sort("position","ASC")),e.getAssociation("children").addAssociation("rule"),e.getAssociation("children").addSorting(d.sort("position","ASC")),e},flowSequenceCriteriaChildren:function(){var e=d.fromCriteria(this.flowSequenceCriteria).setLimit(1);return e.addSorting(d.sort("position","ASC")),e.associations.push({association:"children",criteria:d.fromCriteria(this.flowSequenceCriteria)}),e},delayConstant:function(){return this.flowBuilderService.getActionName("DELAY")},isActionDetail:function(){var e;return(null===(e=this.sequenceTree)||void 0===e?void 0:e.length)>=1&&this.sequenceTree[0].actionName},conditionName:function(){var e,t;return null!==(e=this.sequenceTree)&&void 0!==e&&e.length&&(null===(t=this.sequenceTree.first().rule)||void 0===t?void 0:t.name)||""},getActionTitle:function(){var e;return null!==(e=this.sequenceTree)&&void 0!==e&&e.length?this.sequenceTree.first().actionName===this.delayConstant?this.$tc("sw-flow-delay.delay.itemDetail.delay"):this.$tc("sw-flow-delay.delay.itemDetail.actions"):""}},p("swFlowState",["stateMachineState","documentTypes","mailTemplates","customerGroups","customFieldSets","customFields"])),f("swFlowState",["appActions"])),methods:{createdComponent:function(){this.getDetail()},getDetail:function(){var e=this;this.isLoading=!0,this.flowSequenceRepository.get(this.sequence.delaySequenceId,Shopware.Context.api,this.flowSequenceCriteriaChildren).then((function(t){e.sequenceTree=null==t?void 0:t.children})).catch((function(){e.createNotificationError({message:e.$tc("sw-flow-delay.delay.list.fetchErrorMessage")})})).finally((function(){e.isLoading=!1}))},actionCases:function(){var e,t,n=!(arguments.length>0&&void 0!==arguments[0])||arguments[0];return null!==(e=this.sequenceTree)&&void 0!==e&&e.length&&null!==(t=this.sequenceTree[0])&&void 0!==t&&t.children?this.sequenceTree[0].children.filter((function(e){return e.trueCase===n})):[]},getActionDescriptions:function(e){var t=e.actionName,n=e.config;if(t===this.delayConstant)return this.getDelayDescription(n);var i={appActions:this.appActions,customerGroups:this.customerGroups,customFieldSets:this.customFieldSets,customFields:this.customFields,stateMachineState:this.stateMachineState,documentTypes:this.documentTypes,mailTemplates:this.mailTemplates};return this.flowBuilderService.getActionDescriptions(i,e,this)},getDelayDescription:function(e){var t=this.getTimeLabel(e.delay[0].type,e.delay[0].value);return"".concat(this.$tc("sw-flow-delay.delay.itemDetail.delayed"),": ").concat(e.delay[0].value," ").concat(t)},getTimeLabel:function(e,t){switch(e){case"hour":return this.$tc("sw-flow-delay.modal.labelHour",t);case"day":return this.$tc("sw-flow-delay.modal.labelDay",t);case"week":return this.$tc("sw-flow-delay.modal.labelWeek",t);case"month":return this.$tc("sw-flow-delay.modal.labelMonth",t);default:return""}},onCloseModal:function(){this.$emit("modal-close")}}})},P8hj:function(e,t,n){"use strict";function i(e,t){for(var n=[],i={},o=0;o<t.length;o++){var a=t[o],l=a[0],s={id:e+":"+o,css:a[1],media:a[2],sourceMap:a[3]};i[l]?i[l].parts.push(s):n.push(i[l]={id:l,parts:[s]})}return n}n.r(t),n.d(t,"default",(function(){return m}));var o="undefined"!=typeof document;if("undefined"!=typeof DEBUG&&DEBUG&&!o)throw new Error("vue-style-loader cannot be used in a non-browser environment. Use { target: 'node' } in your Webpack config to indicate a server-rendering environment.");var a={},l=o&&(document.head||document.getElementsByTagName("head")[0]),s=null,r=0,c=!1,d=function(){},u=null,f="data-vue-ssr-id",p="undefined"!=typeof navigator&&/msie [6-9]\b/.test(navigator.userAgent.toLowerCase());function m(e,t,n,o){c=n,u=o||{};var l=i(e,t);return w(l),function(t){for(var n=[],o=0;o<l.length;o++){var s=l[o];(r=a[s.id]).refs--,n.push(r)}t?w(l=i(e,t)):l=[];for(o=0;o<n.length;o++){var r;if(0===(r=n[o]).refs){for(var c=0;c<r.parts.length;c++)r.parts[c]();delete a[r.id]}}}}function w(e){for(var t=0;t<e.length;t++){var n=e[t],i=a[n.id];if(i){i.refs++;for(var o=0;o<i.parts.length;o++)i.parts[o](n.parts[o]);for(;o<n.parts.length;o++)i.parts.push(y(n.parts[o]));i.parts.length>n.parts.length&&(i.parts.length=n.parts.length)}else{var l=[];for(o=0;o<n.parts.length;o++)l.push(y(n.parts[o]));a[n.id]={id:n.id,refs:1,parts:l}}}}function h(){var e=document.createElement("style");return e.type="text/css",l.appendChild(e),e}function y(e){var t,n,i=document.querySelector("style["+f+'~="'+e.id+'"]');if(i){if(c)return d;i.parentNode.removeChild(i)}if(p){var o=r++;i=s||(s=h()),t=b.bind(null,i,o,!1),n=b.bind(null,i,o,!0)}else i=h(),t=_.bind(null,i),n=function(){i.parentNode.removeChild(i)};return t(e),function(i){if(i){if(i.css===e.css&&i.media===e.media&&i.sourceMap===e.sourceMap)return;t(e=i)}else n()}}var v,g=(v=[],function(e,t){return v[e]=t,v.filter(Boolean).join("\n")});function b(e,t,n,i){var o=n?"":i.css;if(e.styleSheet)e.styleSheet.cssText=g(t,o);else{var a=document.createTextNode(o),l=e.childNodes;l[t]&&e.removeChild(l[t]),l.length?e.insertBefore(a,l[t]):e.appendChild(a)}}function _(e,t){var n=t.css,i=t.media,o=t.sourceMap;if(i&&e.setAttribute("media",i),u.ssrId&&e.setAttribute(f,t.id),o&&(n+="\n/*# sourceURL="+o.sources[0]+" */",n+="\n/*# sourceMappingURL=data:application/json;base64,"+btoa(unescape(encodeURIComponent(JSON.stringify(o))))+" */"),e.styleSheet)e.styleSheet.cssText=n;else{for(;e.firstChild;)e.removeChild(e.firstChild);e.appendChild(document.createTextNode(n))}}},zeIh:function(e,t,n){var i=n("4Cm0");i.__esModule&&(i=i.default),"string"==typeof i&&(i=[[e.i,i,""]]),i.locals&&(e.exports=i.locals);(0,n("P8hj").default)("6ec89b94",i,!0,{})}}]);
//# sourceMappingURL=3.js.map
{"version": 3, "sources": ["webpack:////tmp/extension2400992207/SwagCommercial/src/FlowBuilder/DelayedFlowAction/Resources/app/administration/src/constant/sw-flow-delay.constant.ts", "webpack:////tmp/extension2400992207/SwagCommercial/src/FlowBuilder/DelayedFlowAction/Resources/app/administration/src/module/sw-flow/component/sw-flow-sequence-action/index.ts"], "names": ["SEQUENCE_TYPES", "DELAY_OPTIONS", "value", "label", "CUSTOM_TIME", "_Shopware", "Shopware", "Component", "State", "_Component$getCompone", "getComponentHelper", "mapState", "mapGetters", "wrapComponentConfig", "inject", "data", "customSelectedAction", "sequenceId", "computed", "_objectSpread", "delayConstant", "this", "flowBuilderService", "getActionName", "filterActionOptions", "_this", "filterOptions", "$super", "filter", "item", "for<PERSON>ach", "option", "triggerActions", "find", "action", "name", "requirements", "length", "delayable", "push", "actionOptions", "_this2", "parentId", "sequence", "getParentId", "<PERSON><PERSON><PERSON><PERSON>", "sequences", "watch", "showWarningModal", "id", "getFirst<PERSON>ey", "ACTION", "actionType", "selectedAction", "commit", "type", "enabled", "clickableOption", "target", "key", "methods", "parentSequence", "actionName", "_sequence$lastKey", "Object", "keys", "openDynamicModal", "localStorage", "getItem", "onEditAction", "removeAction", "removeActionContainer"], "mappings": "+IAAA,sGAAO,IAAWA,EAAc,SAAdA,GAAc,OAAdA,EAAc,gBAAdA,EAAc,sBAAdA,EAAc,4BAAdA,EAAc,KAMnBC,EAAgB,CACzB,CACIC,MAAO,OACPC,MAAO,iCAEX,CACID,MAAO,MACPC,MAAO,gCAEX,CACID,MAAO,OACPC,MAAO,iCAEX,CACID,MAAO,QACPC,MAAO,kCAEX,CACID,MAAO,SACPC,MAAO,oCAIFC,EAAc,U,4xCCxB3B,IAAAC,EAA6BC,SAArBC,EAASF,EAATE,UAAWC,EAAKH,EAALG,MACnBC,EAAiCF,EAAUG,qBAAnCC,EAAQF,EAARE,SAAUC,EAAUH,EAAVG,WAKHL,YAAUM,oBAAoB,CACzCC,OAAQ,CAAC,sBAETC,KAAI,WAIA,MAAO,CACHC,qBAAsB,KACtBC,WAAY,OAIpBC,SAAQC,MAAA,CACJC,cAAa,WACT,OAAOC,KAAKC,mBAAmBC,cAAc,UAGjDC,oBAAmB,WAAyB,IAADC,EAAA,KACnCC,EAAgB,GAcpB,OAbAL,KAAKM,OAAO,iBAAiBC,QAAO,SAAAC,GAAI,OAAIA,GAAQA,EAAK3B,QAAUuB,EAAKL,iBAAeU,SAAQ,SAAAD,GAC3F,IAAME,EAASN,EAAKO,eAAeC,MAAK,SAAAC,GAAM,OAAIL,EAAK3B,QAAUgC,EAAOC,QAEnEJ,EAAOK,aAAaC,OAKrBN,EAAOO,WACPZ,EAAca,KAAKV,GALnBH,EAAca,KAAKV,MASpBH,GAGXc,cAAa,WAAyB,IAADC,EAAA,KAC3BC,EAAWrB,KAAKsB,SAASD,UAAYrB,KAAKuB,YAAYvB,KAAKsB,UACjE,OAAKtB,KAAKwB,SAASxB,KAAKyB,UAAWJ,GAI5BrB,KAAKG,oBAHDH,KAAKM,OAAO,iBAAiBC,QAAO,SAAAC,GAAI,OAAIA,GAAQA,EAAK3B,QAAUuC,EAAKrB,mBAMpFT,EAAS,cAAe,CAAC,oBACzBC,EAAW,cAAe,CAAC,YAAa,kBACxCD,EAAS,cAAe,CAAC,sBAGhCoC,MAAO,CACHC,iBAAgB,SAAC9C,GACb,IAAMe,EAAaI,KAAKsB,SAASM,IAAM5B,KAAK6B,YAAY7B,KAAKsB,UACzDzC,EAAMiC,OAASnC,IAAemD,QAAUlC,IAAef,EAAM+C,IAA2B,QAArB/C,EAAMkD,aACzE/B,KAAKgC,eAAiBhC,KAAKL,qBAC3BR,EAAM8C,OAAO,kCAAmC,CAAEC,KAAM,GAAIpB,KAAM,GAAIqB,SAAS,EAAOP,GAAI,KAC1F5B,KAAKM,OAAO,mBAAoBN,KAAKL,uBAGrCd,EAAMiC,OAASnC,IAAemD,QAA+B,SAArBjD,EAAMkD,YAC9C/B,KAAKM,OAAO,eAAgBN,KAAKL,qBAAsBd,EAAMuD,gBAAgBC,OAAQxD,EAAMuD,gBAAgBE,KAG3GzD,EAAMiC,OAASnC,IAAemD,QAA+B,WAArBjD,EAAMkD,YAC9C/B,KAAKM,OAAO,eAAgBzB,EAAM+C,IAGlC/C,EAAMiC,OAASnC,IAAemD,QAAUjD,EAAM+C,KAAOhC,GAAmC,eAArBf,EAAMkD,YACzE/B,KAAKM,OAAO,2BAKxBiC,QAAS,CACLf,SAAQ,SAACC,EAA8CJ,GACnD,IAAMmB,EAAiBf,EAAUb,MAAK,SAAAJ,GAAI,OAAIA,EAAKoB,KAAOP,KAC1D,QAAKmB,IACDA,EAAeC,aAAezC,KAAKD,eAChCC,KAAKwB,SAASC,EAAWe,EAAenB,YAGnDE,YAAW,SAACD,GAA4C,IAADoB,EAEnD,OAAwB,QAAjBA,EAAApB,EADSqB,OAAOC,KAAKtB,GAAUqB,OAAOC,KAAKtB,GAAUN,OAAS,WAC7C,IAAA0B,OAAA,EAAjBA,EAAmBrB,WAAY,IAG1CQ,YAAW,SAACP,GACR,OAAOqB,OAAOC,KAAKtB,GAAU,IAGjCuB,iBAAgB,SAAChE,GACb,GAAKA,EAIL,GAAc,qBAAVA,EAAJ,CAKA,IAAMwC,EAAWrB,KAAKsB,SAASD,UAAYrB,KAAKuB,YAAYvB,KAAKsB,UACjE,IAAItB,KAAKwB,SAASxB,KAAKyB,UAAWJ,IAAgD,SAAnCyB,aAAaC,QAAQ,UAKhE,OAFA/C,KAAKgC,eAAiBnD,OACtBmB,KAAKM,OAAO,mBAAoBzB,GAHhCM,EAAM8C,OAAO,kCAAmC,CAAEF,WAAY,MAAOG,KAAMvD,IAAemD,OAAQhB,KAAM,GAAIqB,SAAS,EAAMP,GAAI5B,KAAKsB,SAASM,IAAM5B,KAAK6B,YAAY7B,KAAKsB,YAO7KtB,KAAKL,qBAAuBd,OAbxBmB,KAAKM,OAAO,mBAAoBzB,IAgBxCmE,aAAY,SAAC1B,EAAmCe,EAAQC,GACpD,IAAIhB,EAASmB,YAAsC,qBAAxBnB,EAASmB,WAApC,CAIA,IAAMpB,EAAWC,EAASD,UAAYrB,KAAKuB,YAAYvB,KAAKsB,UACxDtB,KAAKwB,SAASxB,KAAKyB,UAAWJ,IAAgD,SAAnCyB,aAAaC,QAAQ,WAChE5D,EAAM8C,OAAO,kCAAmC,CAC5CF,WAAY,OACZG,KAAMvD,IAAemD,OACrBhB,KAAM,GACNqB,SAAS,EACTP,GAAIN,EAASM,GACbQ,gBAAiB,CACbC,SACAC,SAGRtC,KAAKL,qBAAuB2B,GAE5BtB,KAAKM,OAAO,eAAgBgB,EAAUe,EAAQC,KAItDW,aAAY,SAACrB,GACT,IAAMP,EAAWrB,KAAKyB,UAAUb,MAAK,SAAAJ,GAAI,OAAIA,EAAKoB,KAAOA,KAAIP,SACzDrB,KAAKwB,SAASxB,KAAKyB,UAAWJ,IAAgD,SAAnCyB,aAAaC,QAAQ,UAChE5D,EAAM8C,OAAO,kCAAmC,CAAEF,WAAY,SAAUG,KAAMvD,IAAemD,OAAQhB,KAAM,GAAIqB,SAAS,EAAMP,OAE9H5B,KAAKM,OAAO,eAAgBsB,IAIpCsB,sBAAqB,WACjB,IAAM7B,EAAWrB,KAAKsB,SAASD,UAAYrB,KAAKuB,YAAYvB,KAAKsB,UAC7DtB,KAAKwB,SAASxB,KAAKyB,UAAWJ,IAAgD,SAAnCyB,aAAaC,QAAQ,UAChE5D,EAAM8C,OAAO,kCAAmC,CAAEF,WAAY,aAAcG,KAAMvD,IAAemD,OAAQhB,KAAM,GAAIqB,SAAS,EAAMP,GAAI5B,KAAKsB,SAASM,IAAM5B,KAAK6B,YAAY7B,KAAKsB,YAEhLtB,KAAKM,OAAO", "file": "static/js/8.js", "sourcesContent": ["export const enum SEQUENCE_TYPES {\n    ACTION = 'action',\n    CONDITION = 'condition',\n    DELAY_ACTION = 'delay_action',\n}\n\nexport const DELAY_OPTIONS = [\n    {\n        value: 'hour',\n        label: 'sw-flow-delay.modal.labelHour'\n    },\n    {\n        value: 'day',\n        label: 'sw-flow-delay.modal.labelDay'\n    },\n    {\n        value: 'week',\n        label: 'sw-flow-delay.modal.labelWeek'\n    },\n    {\n        value: 'month',\n        label: 'sw-flow-delay.modal.labelMonth'\n    },\n    {\n        value: 'custom',\n        label: 'sw-flow-delay.modal.labelCustom'\n    },\n] as const;\n\nexport const CUSTOM_TIME = 'custom' as const;\nexport const GENERAL_GROUP = 'general' as const;\n", "import {SEQUENCE_TYPES} from '../../../../constant/sw-flow-delay.constant';\nimport {ActionOption, WarningConfig} from \"../../../../type/types\";\nimport type EntityCollection from '@shopware-ag/admin-extension-sdk/es/data/_internals/EntityCollection';\nimport type {Entity} from '@shopware-ag/admin-extension-sdk/es/data/_internals/Entity';\n\nconst { Component, State } = Shopware;\nconst { mapState, mapGetters } = Component.getComponentHelper();\n\n/**\n * @package services-settings\n */\nexport default Component.wrapComponentConfig({\n    inject: ['flowBuilderService'],\n\n    data(): {\n        customSelectedAction: string,\n        sequenceId: string\n    } {\n        return {\n            customSelectedAction: null,\n            sequenceId: null,\n        };\n    },\n\n    computed: {\n        delayConstant() {\n            return this.flowBuilderService.getActionName('DELAY');\n        },\n\n        filterActionOptions(): Array<ActionOption> {\n            let filterOptions = [];\n            this.$super('actionOptions').filter(item => item && item.value !== this.delayConstant).forEach(item => {\n                const option = this.triggerActions.find(action => item.value === action.name);\n\n                if (!option.requirements.length) {\n                    filterOptions.push(item);\n                    return;\n                }\n\n                if (option.delayable) {\n                    filterOptions.push(item);\n                }\n            })\n\n            return filterOptions;\n        },\n\n        actionOptions(): Array<ActionOption> {\n            const parentId = this.sequence.parentId || this.getParentId(this.sequence);\n            if (!this.hasDelay(this.sequences, parentId)) {\n                return this.$super('actionOptions').filter(item => item && item.value !== this.delayConstant);\n            }\n\n            return this.filterActionOptions;\n        },\n\n        ...mapState('swFlowState', ['triggerActions']),\n        ...mapGetters('swFlowState', ['sequences', 'actionGroups']),\n        ...mapState('swFlowDelay', ['showWarningModal']),\n    },\n\n    watch: {\n        showWarningModal(value: WarningConfig): void {\n            const sequenceId = this.sequence.id || this.getFirstKey(this.sequence);\n            if (value.name === SEQUENCE_TYPES.ACTION && sequenceId === value.id && value.actionType === 'ADD') {\n                this.selectedAction = this.customSelectedAction;\n                State.commit('swFlowDelay/setShowWarningModal', { type: '', name: '', enabled: false, id: '' });\n                this.$super('openDynamicModal', this.customSelectedAction);\n            }\n\n            if (value.name === SEQUENCE_TYPES.ACTION && value.actionType === 'EDIT') {\n                this.$super('onEditAction', this.customSelectedAction, value.clickableOption.target, value.clickableOption.key);\n            }\n\n            if (value.name === SEQUENCE_TYPES.ACTION && value.actionType === 'DELETE') {\n                this.$super('removeAction', value.id);\n            }\n\n            if (value.name === SEQUENCE_TYPES.ACTION && value.id === sequenceId && value.actionType === 'DELETE_ALL') {\n                this.$super('removeActionContainer');\n            }\n        },\n    },\n\n    methods: {\n        hasDelay(sequences: EntityCollection<'flow_sequence'>, parentId: string): boolean {\n            const parentSequence = sequences.find(item => item.id === parentId);\n            if (!parentSequence) return false;\n            if (parentSequence.actionName === this.delayConstant) return true;\n            return this.hasDelay(sequences, parentSequence.parentId);\n        },\n\n        getParentId(sequence: Entity<'flow_sequence'>): string {\n            const lastKey = Object.keys(sequence)[Object.keys(sequence).length - 1];\n            return sequence[lastKey]?.parentId || '';\n        },\n\n        getFirstKey(sequence: Entity<'flow_sequence'>): string {\n            return Object.keys(sequence)[0];\n        },\n\n        openDynamicModal(value: string): void {\n            if (!value) {\n                return\n            }\n\n            if (value === 'action.stop.flow') {\n                this.$super('openDynamicModal', value);\n                return\n            }\n\n            const parentId = this.sequence.parentId || this.getParentId(this.sequence);\n            if (this.hasDelay(this.sequences, parentId) && localStorage.getItem('action') !== 'true') {\n                State.commit('swFlowDelay/setShowWarningModal', { actionType: 'ADD', type: SEQUENCE_TYPES.ACTION, name: '', enabled: true, id: this.sequence.id || this.getFirstKey(this.sequence) });\n            } else {\n                this.selectedAction = value;\n                this.$super('openDynamicModal', value);\n                return\n            }\n\n            this.customSelectedAction = value;\n        },\n\n        onEditAction(sequence: Entity<'flow_sequence'>, target, key): void {\n            if (sequence.actionName && sequence.actionName === 'action.stop.flow') {\n                return;\n            }\n\n            const parentId = sequence.parentId || this.getParentId(this.sequence);\n            if (this.hasDelay(this.sequences, parentId) && localStorage.getItem('action') !== 'true') {\n                State.commit('swFlowDelay/setShowWarningModal', {\n                    actionType: 'EDIT',\n                    type: SEQUENCE_TYPES.ACTION,\n                    name: '',\n                    enabled: true,\n                    id: sequence.id,\n                    clickableOption: {\n                        target,\n                        key\n                    }\n                });\n                this.customSelectedAction = sequence;\n            } else {\n                this.$super('onEditAction', sequence, target, key);\n            }\n        },\n\n        removeAction(id: string): void {\n            const parentId = this.sequences.find(item => item.id === id).parentId;\n            if (this.hasDelay(this.sequences, parentId) && localStorage.getItem('action') !== 'true') {\n                State.commit('swFlowDelay/setShowWarningModal', { actionType: 'DELETE', type: SEQUENCE_TYPES.ACTION, name: '', enabled: true, id });\n            } else {\n                this.$super('removeAction', id);\n            }\n        },\n\n        removeActionContainer(): void {\n            const parentId = this.sequence.parentId || this.getParentId(this.sequence);\n            if (this.hasDelay(this.sequences, parentId) && localStorage.getItem('action') !== 'true') {\n                State.commit('swFlowDelay/setShowWarningModal', { actionType: 'DELETE_ALL', type: SEQUENCE_TYPES.ACTION, name: '', enabled: true, id: this.sequence.id || this.getFirstKey(this.sequence) });\n            } else {\n                this.$super('removeActionContainer');\n            }\n        },\n    },\n});\n"], "sourceRoot": ""}
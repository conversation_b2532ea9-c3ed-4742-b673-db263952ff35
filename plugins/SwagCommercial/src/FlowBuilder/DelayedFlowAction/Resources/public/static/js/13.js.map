{"version": 3, "sources": ["webpack:////tmp/extension2400992207/SwagCommercial/src/FlowBuilder/DelayedFlowAction/Resources/app/administration/src/module/sw-flow/component/sw-flow-sequence/sw-flow-sequence.html.twig", "webpack:////tmp/extension2400992207/SwagCommercial/src/FlowBuilder/DelayedFlowAction/Resources/app/administration/src/module/sw-flow/component/sw-flow-sequence/index.ts"], "names": ["Component", "Shopware", "wrapComponentConfig", "inject", "template", "computed", "delayConstant", "this", "flowBuilderService", "getActionName", "isDelayAction", "sequenceData", "actionName", "isActionSequence", "isSelectorSequence", "isConditionSequence"], "mappings": "uJAAe,ICEPA,EAAcC,SAAdD,UAKOA,YAAUE,oBAAoB,CACzCC,OAAQ,CAAC,sBAETC,SDVW,4LCYXC,SAAU,CACNC,cAAa,WACT,OAAOC,KAAKC,mBAAmBC,cAAc,UAGjDC,cAAa,WACT,OAAOH,KAAKI,aAAaC,aAAeL,KAAKD,eAGjDO,iBAAgB,WACZ,OAAQN,KAAKO,qBACLP,KAAKQ,qBACNR,KAAKI,aAAaC,aAAeL,KAAKD", "file": "static/js/13.js", "sourcesContent": ["export default \"{% block sw_flow_sequence_extension %}\\n    <sw-flow-delay-action\\n        v-if=\\\"isDelayAction\\\"\\n        :sequence=\\\"sequenceData\\\"\\n        :disabled=\\\"disabled\\\"\\n    />\\n{% endblock %}\\n\";", "import template from './sw-flow-sequence.html.twig';\n\nconst { Component } = Shopware;\n\n/**\n * @package services-settings\n */\nexport default Component.wrapComponentConfig({\n    inject: ['flowBuilderService'],\n\n    template,\n\n    computed: {\n        delayConstant() {\n            return this.flowBuilderService.getActionName('DELAY');\n        },\n\n        isDelayAction(): boolean {\n            return this.sequenceData.actionName === this.delayConstant;\n        },\n\n        isActionSequence(): boolean {\n            return !this.isSelectorSequence\n                && !this.isConditionSequence\n                && this.sequenceData.actionName !== this.delayConstant;\n        },\n    },\n});\n"], "sourceRoot": ""}
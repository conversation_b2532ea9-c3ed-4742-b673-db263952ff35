{"version": 3, "sources": ["webpack:////tmp/extension2400992207/SwagCommercial/src/FlowBuilder/DelayedFlowAction/Resources/app/administration/src/module/sw-flow/component/sw-flow-sequence-label/sw-flow-sequence-label.html.twig", "webpack:////tmp/extension2400992207/SwagCommercial/src/FlowBuilder/DelayedFlowAction/Resources/app/administration/src/module/sw-flow/component/sw-flow-sequence-label/index.ts", "webpack:////tmp/extension2400992207/SwagCommercial/src/FlowBuilder/DelayedFlowAction/Resources/app/administration/src/module/sw-flow/component/sw-flow-sequence-label/sw-flow-sequence-label.scss", "webpack:///./node_modules/vue-style-loader/lib/listToStyles.js", "webpack:///./node_modules/vue-style-loader/lib/addStylesClient.js"], "names": ["Component", "Shopware", "wrapComponentConfig", "template", "inject", "props", "sequence", "type", "Object", "default", "appFlowActions", "Array", "classes", "String", "methods", "convertSequence", "_sequence$rule", "_this$flowBuilderServ", "_this$flowBuilderServ2", "_sequence$rule2", "rule", "name", "label", "icon", "_appFlowAction$transl", "appFlowAction", "values", "this", "find", "item", "actionName", "translated", "iconRaw", "swIcon", "concat", "$tc", "flowBuilderService", "getActionTitle", "content", "__esModule", "module", "i", "locals", "exports", "add", "listToStyles", "parentId", "list", "styles", "newStyles", "length", "id", "part", "css", "media", "sourceMap", "parts", "push", "hasDocument", "document", "DEBUG", "Error", "stylesInDom", "head", "getElementsByTagName", "singletonElement", "singletonCounter", "isProduction", "noop", "options", "ssrIdKey", "isOldIE", "navigator", "test", "userAgent", "toLowerCase", "addStylesClient", "_isProduction", "_options", "addStylesToDom", "newList", "<PERSON><PERSON><PERSON><PERSON>", "domStyle", "refs", "j", "addStyle", "createStyleElement", "styleElement", "createElement", "append<PERSON><PERSON><PERSON>", "obj", "update", "remove", "querySelector", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "styleIndex", "applyToSingletonTag", "bind", "applyToTag", "newObj", "textStore", "replaceText", "index", "replacement", "filter", "Boolean", "join", "styleSheet", "cssText", "cssNode", "createTextNode", "childNodes", "insertBefore", "setAttribute", "ssrId", "sources", "btoa", "unescape", "encodeURIComponent", "JSON", "stringify", "<PERSON><PERSON><PERSON><PERSON>"], "mappings": "kKAAe,ICMPA,EAAcC,SAAdD,UAKOA,YAAUE,oBAAoB,CACzCC,SDZW,ioCCcXC,OAAQ,CAAC,sBAETC,MAAO,CACHC,SAAU,CACNC,KAAMC,OACNC,QAAS,IAGbC,eAAgB,CACZH,KAAMI,MACNF,QAAS,IAGbG,QAAS,CACLL,KAAMM,OACNJ,QAAS,KAIjBK,QAAS,CACLC,gBAAe,SAACT,GAAkD,IAADU,EAAAC,EAAAC,EACrCC,EAAxB,GAAiB,QAAjBH,EAAIV,EAASc,YAAI,IAAAJ,GAAbA,EAAeK,KACf,MAAO,CACHC,MAAoB,QAAfH,EAAEb,EAASc,YAAI,IAAAD,OAAA,EAAbA,EAAeE,KACtBE,KAAM,kBAId,IACmBC,EADbC,EAAgBjB,OAAOkB,OAAOC,KAAKjB,gBAAgBkB,MAAK,SAAAC,GAAI,OAAIA,EAAKR,OAASf,EAASwB,cAC7F,OAAIL,EACO,CACHH,OAA+B,QAAxBE,EAAAC,EAAcM,kBAAU,IAAAP,OAAA,EAAxBA,EAA0BF,QAASG,EAAcH,MACxDC,KAAME,EAAcO,SAAWP,EAAcQ,OAC7CD,QAASP,EAAcF,MAKxB,CACHD,MAAM,GAADY,OAAKP,KAAKQ,IAA+D,QAA5DlB,EAACU,KAAKS,mBAAmBC,eAAe/B,EAASwB,mBAAW,IAAAb,OAAA,EAA3DA,EAA6DK,QAChFC,KAAiE,QAA7DL,EAAES,KAAKS,mBAAmBC,eAAe/B,EAASwB,mBAAW,IAAAZ,OAAA,EAA3DA,EAA6DK,W,uBCnDnF,IAAIe,EAAU,EAAQ,QACnBA,EAAQC,aAAYD,EAAUA,EAAQ7B,SACnB,iBAAZ6B,IAAsBA,EAAU,CAAC,CAACE,EAAOC,EAAIH,EAAS,MAC7DA,EAAQI,SAAQF,EAAOG,QAAUL,EAAQI,SAG/BE,EADH,EAAQ,QAAwLnC,SACzL,WAAY6B,GAAS,EAAM,K,kCCL7B,SAASO,EAAcC,EAAUC,GAG9C,IAFA,IAAIC,EAAS,GACTC,EAAY,GACPR,EAAI,EAAGA,EAAIM,EAAKG,OAAQT,IAAK,CACpC,IAAIZ,EAAOkB,EAAKN,GACZU,EAAKtB,EAAK,GAIVuB,EAAO,CACTD,GAAIL,EAAW,IAAML,EACrBY,IALQxB,EAAK,GAMbyB,MALUzB,EAAK,GAMf0B,UALc1B,EAAK,IAOhBoB,EAAUE,GAGbF,EAAUE,GAAIK,MAAMC,KAAKL,GAFzBJ,EAAOS,KAAKR,EAAUE,GAAM,CAAEA,GAAIA,EAAIK,MAAO,CAACJ,KAKlD,OAAOJ,E,+CCjBT,IAAIU,EAAkC,oBAAbC,SAEzB,GAAqB,oBAAVC,OAAyBA,QAC7BF,EACH,MAAM,IAAIG,MACV,2JAkBJ,IAAIC,EAAc,GAQdC,EAAOL,IAAgBC,SAASI,MAAQJ,SAASK,qBAAqB,QAAQ,IAC9EC,EAAmB,KACnBC,EAAmB,EACnBC,GAAe,EACfC,EAAO,aACPC,EAAU,KACVC,EAAW,kBAIXC,EAA+B,oBAAdC,WAA6B,eAAeC,KAAKD,UAAUE,UAAUC,eAE3E,SAASC,EAAiB9B,EAAUC,EAAM8B,EAAeC,GACtEX,EAAeU,EAEfR,EAAUS,GAAY,GAEtB,IAAI9B,EAASH,EAAaC,EAAUC,GAGpC,OAFAgC,EAAe/B,GAER,SAAiBgC,GAEtB,IADA,IAAIC,EAAY,GACPxC,EAAI,EAAGA,EAAIO,EAAOE,OAAQT,IAAK,CACtC,IAAIZ,EAAOmB,EAAOP,IACdyC,EAAWpB,EAAYjC,EAAKsB,KACvBgC,OACTF,EAAUxB,KAAKyB,GAEbF,EAEFD,EADA/B,EAASH,EAAaC,EAAUkC,IAGhChC,EAAS,GAEX,IAASP,EAAI,EAAGA,EAAIwC,EAAU/B,OAAQT,IAAK,CACzC,IAAIyC,EACJ,GAAsB,KADlBA,EAAWD,EAAUxC,IACZ0C,KAAY,CACvB,IAAK,IAAIC,EAAI,EAAGA,EAAIF,EAAS1B,MAAMN,OAAQkC,IACzCF,EAAS1B,MAAM4B,YAEVtB,EAAYoB,EAAS/B,OAMpC,SAAS4B,EAAgB/B,GACvB,IAAK,IAAIP,EAAI,EAAGA,EAAIO,EAAOE,OAAQT,IAAK,CACtC,IAAIZ,EAAOmB,EAAOP,GACdyC,EAAWpB,EAAYjC,EAAKsB,IAChC,GAAI+B,EAAU,CACZA,EAASC,OACT,IAAK,IAAIC,EAAI,EAAGA,EAAIF,EAAS1B,MAAMN,OAAQkC,IACzCF,EAAS1B,MAAM4B,GAAGvD,EAAK2B,MAAM4B,IAE/B,KAAOA,EAAIvD,EAAK2B,MAAMN,OAAQkC,IAC5BF,EAAS1B,MAAMC,KAAK4B,EAASxD,EAAK2B,MAAM4B,KAEtCF,EAAS1B,MAAMN,OAASrB,EAAK2B,MAAMN,SACrCgC,EAAS1B,MAAMN,OAASrB,EAAK2B,MAAMN,YAEhC,CACL,IAAIM,EAAQ,GACZ,IAAS4B,EAAI,EAAGA,EAAIvD,EAAK2B,MAAMN,OAAQkC,IACrC5B,EAAMC,KAAK4B,EAASxD,EAAK2B,MAAM4B,KAEjCtB,EAAYjC,EAAKsB,IAAM,CAAEA,GAAItB,EAAKsB,GAAIgC,KAAM,EAAG3B,MAAOA,KAK5D,SAAS8B,IACP,IAAIC,EAAe5B,SAAS6B,cAAc,SAG1C,OAFAD,EAAahF,KAAO,WACpBwD,EAAK0B,YAAYF,GACVA,EAGT,SAASF,EAAUK,GACjB,IAAIC,EAAQC,EACRL,EAAe5B,SAASkC,cAAc,SAAWvB,EAAW,MAAQoB,EAAIvC,GAAK,MAEjF,GAAIoC,EAAc,CAChB,GAAIpB,EAGF,OAAOC,EAOPmB,EAAaO,WAAWC,YAAYR,GAIxC,GAAIhB,EAAS,CAEX,IAAIyB,EAAa9B,IACjBqB,EAAetB,IAAqBA,EAAmBqB,KACvDK,EAASM,EAAoBC,KAAK,KAAMX,EAAcS,GAAY,GAClEJ,EAASK,EAAoBC,KAAK,KAAMX,EAAcS,GAAY,QAGlET,EAAeD,IACfK,EAASQ,EAAWD,KAAK,KAAMX,GAC/BK,EAAS,WACPL,EAAaO,WAAWC,YAAYR,IAMxC,OAFAI,EAAOD,GAEA,SAAsBU,GAC3B,GAAIA,EAAQ,CACV,GAAIA,EAAO/C,MAAQqC,EAAIrC,KACnB+C,EAAO9C,QAAUoC,EAAIpC,OACrB8C,EAAO7C,YAAcmC,EAAInC,UAC3B,OAEFoC,EAAOD,EAAMU,QAEbR,KAKN,IACMS,EADFC,GACED,EAAY,GAET,SAAUE,EAAOC,GAEtB,OADAH,EAAUE,GAASC,EACZH,EAAUI,OAAOC,SAASC,KAAK,QAI1C,SAASV,EAAqBV,EAAcgB,EAAOX,EAAQF,GACzD,IAAIrC,EAAMuC,EAAS,GAAKF,EAAIrC,IAE5B,GAAIkC,EAAaqB,WACfrB,EAAaqB,WAAWC,QAAUP,EAAYC,EAAOlD,OAChD,CACL,IAAIyD,EAAUnD,SAASoD,eAAe1D,GAClC2D,EAAazB,EAAayB,WAC1BA,EAAWT,IAAQhB,EAAaQ,YAAYiB,EAAWT,IACvDS,EAAW9D,OACbqC,EAAa0B,aAAaH,EAASE,EAAWT,IAE9ChB,EAAaE,YAAYqB,IAK/B,SAASX,EAAYZ,EAAcG,GACjC,IAAIrC,EAAMqC,EAAIrC,IACVC,EAAQoC,EAAIpC,MACZC,EAAYmC,EAAInC,UAiBpB,GAfID,GACFiC,EAAa2B,aAAa,QAAS5D,GAEjCe,EAAQ8C,OACV5B,EAAa2B,aAAa5C,EAAUoB,EAAIvC,IAGtCI,IAGFF,GAAO,mBAAqBE,EAAU6D,QAAQ,GAAK,MAEnD/D,GAAO,uDAAyDgE,KAAKC,SAASC,mBAAmBC,KAAKC,UAAUlE,MAAgB,OAG9HgC,EAAaqB,WACfrB,EAAaqB,WAAWC,QAAUxD,MAC7B,CACL,KAAOkC,EAAamC,YAClBnC,EAAaQ,YAAYR,EAAamC,YAExCnC,EAAaE,YAAY9B,SAASoD,eAAe1D,O", "file": "static/js/4.js", "sourcesContent": ["export default \"<div class=\\\"sw-flow-sequence-label\\\" :class=\\\"classes\\\">\\n    <span>\\n        <a @click=\\\"$emit('click', sequence)\\\" class=\\\"sw-flow-sequence-label__name\\\">\\n            <span v-if=\\\"sequence.actionName\\\" class=\\\"sw-flow-sequence-label__action-name\\\">\\n                {# This is for app icon#}\\n                <img\\n                    v-if=\\\"convertSequence(sequence).iconRaw\\\"\\n                    class=\\\"sw-flow-sequence-label__icon-raw\\\"\\n                    alt=\\\"\\\"\\n                    :src=\\\"`data:image/png;base64, ${convertSequence(sequence).iconRaw}`\\\"\\n                />\\n\\n                <sw-icon\\n                    v-else\\n                    :name=\\\"`${convertSequence(sequence).icon}`\\\"\\n                    size=\\\"14px\\\"\\n                    class=\\\"sw-flow-sequence-label__action-icon\\\"\\n                />\\n            </span>\\n\\n            <sw-icon\\n                v-else\\n                :name=\\\"`${convertSequence(sequence).icon}`\\\"\\n                size=\\\"14px\\\"\\n                class=\\\"sw-flow-sequence-label__condition-icon\\\"\\n            />\\n\\n            <span>{{ convertSequence(sequence).label }}</span>\\n        </a>\\n    </span>\\n</div>\\n\";", "import type {PropType} from 'vue';\nimport template from './sw-flow-sequence-label.html.twig';\nimport './sw-flow-sequence-label.scss';\nimport type {Entity} from '@shopware-ag/admin-extension-sdk/es/data/_internals/Entity';\nimport {ActionOption} from \"../../../../type/types\";\n\nconst { Component } = Shopware;\n\n/**\n * @package services-settings\n */\nexport default Component.wrapComponentConfig({\n    template,\n\n    inject: ['flowBuilderService'],\n\n    props: {\n        sequence: {\n            type: Object as PropType<Entity<'flow_sequence'>>,\n            default: {}\n        },\n\n        appFlowActions: {\n            type: Array as PropType<ActionOption>,\n            default: [],\n        },\n\n        classes: {\n            type: String,\n            default: ''\n        },\n    },\n\n    methods: {\n        convertSequence(sequence: Entity<'flow_sequence'>): ActionOption {\n            if (sequence.rule?.name){\n                return {\n                    label: sequence.rule?.name,\n                    icon: 'regular-rule-s',\n                }\n            }\n\n            const appFlowAction = Object.values(this.appFlowActions).find(item => item.name === sequence.actionName);\n            if (appFlowAction) {\n                return {\n                    label: appFlowAction.translated?.label || appFlowAction.label,\n                    icon: appFlowAction.iconRaw || appFlowAction.swIcon,\n                    iconRaw: appFlowAction.icon,\n                }\n            }\n\n            // for core actions\n            return {\n                label: `${this.$tc(this.flowBuilderService.getActionTitle(sequence.actionName)?.label)}`,\n                icon: this.flowBuilderService.getActionTitle(sequence.actionName)?.icon,\n            }\n        },\n    },\n});\n", "// style-loader: Adds some css to the DOM by adding a <style> tag\n\n// load the styles\nvar content = require(\"!!../../../../../../../../../../../../../../builds/shopware/6/product/commercial/src/Administration/Resources/app/administration/node_modules/mini-css-extract-plugin/dist/loader.js??ref--15-1!../../../../../../../../../../../../../../builds/shopware/6/product/commercial/src/Administration/Resources/app/administration/node_modules/css-loader/dist/cjs.js??ref--15-2!../../../../../../../../../../../../../../builds/shopware/6/product/commercial/src/Administration/Resources/app/administration/node_modules/sass-loader/dist/cjs.js??ref--15-3!./sw-flow-sequence-label.scss\");\nif(content.__esModule) content = content.default;\nif(typeof content === 'string') content = [[module.id, content, '']];\nif(content.locals) module.exports = content.locals;\n// add the styles to the DOM\nvar add = require(\"!../../../../../../../../../../../../../../builds/shopware/6/product/commercial/src/Administration/Resources/app/administration/node_modules/vue-style-loader/lib/addStylesClient.js\").default\nvar update = add(\"518155f8\", content, true, {});", "/**\n * Translates the list format produced by css-loader into something\n * easier to manipulate.\n */\nexport default function listToStyles (parentId, list) {\n  var styles = []\n  var newStyles = {}\n  for (var i = 0; i < list.length; i++) {\n    var item = list[i]\n    var id = item[0]\n    var css = item[1]\n    var media = item[2]\n    var sourceMap = item[3]\n    var part = {\n      id: parentId + ':' + i,\n      css: css,\n      media: media,\n      sourceMap: sourceMap\n    }\n    if (!newStyles[id]) {\n      styles.push(newStyles[id] = { id: id, parts: [part] })\n    } else {\n      newStyles[id].parts.push(part)\n    }\n  }\n  return styles\n}\n", "/*\n  MIT License http://www.opensource.org/licenses/mit-license.php\n  Author <PERSON> @sokra\n  Modified by <PERSON> @yyx990803\n*/\n\nimport listToStyles from './listToStyles'\n\nvar hasDocument = typeof document !== 'undefined'\n\nif (typeof DEBUG !== 'undefined' && DEBUG) {\n  if (!hasDocument) {\n    throw new Error(\n    'vue-style-loader cannot be used in a non-browser environment. ' +\n    \"Use { target: 'node' } in your Webpack config to indicate a server-rendering environment.\"\n  ) }\n}\n\n/*\ntype StyleObject = {\n  id: number;\n  parts: Array<StyleObjectPart>\n}\n\ntype StyleObjectPart = {\n  css: string;\n  media: string;\n  sourceMap: ?string\n}\n*/\n\nvar stylesInDom = {/*\n  [id: number]: {\n    id: number,\n    refs: number,\n    parts: Array<(obj?: StyleObjectPart) => void>\n  }\n*/}\n\nvar head = hasDocument && (document.head || document.getElementsByTagName('head')[0])\nvar singletonElement = null\nvar singletonCounter = 0\nvar isProduction = false\nvar noop = function () {}\nvar options = null\nvar ssrIdKey = 'data-vue-ssr-id'\n\n// Force single-tag solution on IE6-9, which has a hard limit on the # of <style>\n// tags it will allow on a page\nvar isOldIE = typeof navigator !== 'undefined' && /msie [6-9]\\b/.test(navigator.userAgent.toLowerCase())\n\nexport default function addStylesClient (parentId, list, _isProduction, _options) {\n  isProduction = _isProduction\n\n  options = _options || {}\n\n  var styles = listToStyles(parentId, list)\n  addStylesToDom(styles)\n\n  return function update (newList) {\n    var mayRemove = []\n    for (var i = 0; i < styles.length; i++) {\n      var item = styles[i]\n      var domStyle = stylesInDom[item.id]\n      domStyle.refs--\n      mayRemove.push(domStyle)\n    }\n    if (newList) {\n      styles = listToStyles(parentId, newList)\n      addStylesToDom(styles)\n    } else {\n      styles = []\n    }\n    for (var i = 0; i < mayRemove.length; i++) {\n      var domStyle = mayRemove[i]\n      if (domStyle.refs === 0) {\n        for (var j = 0; j < domStyle.parts.length; j++) {\n          domStyle.parts[j]()\n        }\n        delete stylesInDom[domStyle.id]\n      }\n    }\n  }\n}\n\nfunction addStylesToDom (styles /* Array<StyleObject> */) {\n  for (var i = 0; i < styles.length; i++) {\n    var item = styles[i]\n    var domStyle = stylesInDom[item.id]\n    if (domStyle) {\n      domStyle.refs++\n      for (var j = 0; j < domStyle.parts.length; j++) {\n        domStyle.parts[j](item.parts[j])\n      }\n      for (; j < item.parts.length; j++) {\n        domStyle.parts.push(addStyle(item.parts[j]))\n      }\n      if (domStyle.parts.length > item.parts.length) {\n        domStyle.parts.length = item.parts.length\n      }\n    } else {\n      var parts = []\n      for (var j = 0; j < item.parts.length; j++) {\n        parts.push(addStyle(item.parts[j]))\n      }\n      stylesInDom[item.id] = { id: item.id, refs: 1, parts: parts }\n    }\n  }\n}\n\nfunction createStyleElement () {\n  var styleElement = document.createElement('style')\n  styleElement.type = 'text/css'\n  head.appendChild(styleElement)\n  return styleElement\n}\n\nfunction addStyle (obj /* StyleObjectPart */) {\n  var update, remove\n  var styleElement = document.querySelector('style[' + ssrIdKey + '~=\"' + obj.id + '\"]')\n\n  if (styleElement) {\n    if (isProduction) {\n      // has SSR styles and in production mode.\n      // simply do nothing.\n      return noop\n    } else {\n      // has SSR styles but in dev mode.\n      // for some reason Chrome can't handle source map in server-rendered\n      // style tags - source maps in <style> only works if the style tag is\n      // created and inserted dynamically. So we remove the server rendered\n      // styles and inject new ones.\n      styleElement.parentNode.removeChild(styleElement)\n    }\n  }\n\n  if (isOldIE) {\n    // use singleton mode for IE9.\n    var styleIndex = singletonCounter++\n    styleElement = singletonElement || (singletonElement = createStyleElement())\n    update = applyToSingletonTag.bind(null, styleElement, styleIndex, false)\n    remove = applyToSingletonTag.bind(null, styleElement, styleIndex, true)\n  } else {\n    // use multi-style-tag mode in all other cases\n    styleElement = createStyleElement()\n    update = applyToTag.bind(null, styleElement)\n    remove = function () {\n      styleElement.parentNode.removeChild(styleElement)\n    }\n  }\n\n  update(obj)\n\n  return function updateStyle (newObj /* StyleObjectPart */) {\n    if (newObj) {\n      if (newObj.css === obj.css &&\n          newObj.media === obj.media &&\n          newObj.sourceMap === obj.sourceMap) {\n        return\n      }\n      update(obj = newObj)\n    } else {\n      remove()\n    }\n  }\n}\n\nvar replaceText = (function () {\n  var textStore = []\n\n  return function (index, replacement) {\n    textStore[index] = replacement\n    return textStore.filter(Boolean).join('\\n')\n  }\n})()\n\nfunction applyToSingletonTag (styleElement, index, remove, obj) {\n  var css = remove ? '' : obj.css\n\n  if (styleElement.styleSheet) {\n    styleElement.styleSheet.cssText = replaceText(index, css)\n  } else {\n    var cssNode = document.createTextNode(css)\n    var childNodes = styleElement.childNodes\n    if (childNodes[index]) styleElement.removeChild(childNodes[index])\n    if (childNodes.length) {\n      styleElement.insertBefore(cssNode, childNodes[index])\n    } else {\n      styleElement.appendChild(cssNode)\n    }\n  }\n}\n\nfunction applyToTag (styleElement, obj) {\n  var css = obj.css\n  var media = obj.media\n  var sourceMap = obj.sourceMap\n\n  if (media) {\n    styleElement.setAttribute('media', media)\n  }\n  if (options.ssrId) {\n    styleElement.setAttribute(ssrIdKey, obj.id)\n  }\n\n  if (sourceMap) {\n    // https://developer.chrome.com/devtools/docs/javascript-debugging\n    // this makes source maps inside style tags work properly in Chrome\n    css += '\\n/*# sourceURL=' + sourceMap.sources[0] + ' */'\n    // http://stackoverflow.com/a/26603875\n    css += '\\n/*# sourceMappingURL=data:application/json;base64,' + btoa(unescape(encodeURIComponent(JSON.stringify(sourceMap)))) + ' */'\n  }\n\n  if (styleElement.styleSheet) {\n    styleElement.styleSheet.cssText = css\n  } else {\n    while (styleElement.firstChild) {\n      styleElement.removeChild(styleElement.firstChild)\n    }\n    styleElement.appendChild(document.createTextNode(css))\n  }\n}\n"], "sourceRoot": ""}
(this["webpackJsonpPlugindelayed-flow-action"]=this["webpackJsonpPlugindelayed-flow-action"]||[]).push([[1],{EFTm:function(e,t,n){"use strict";n.d(t,"c",(function(){return r})),n.d(t,"b",(function(){return o})),n.d(t,"a",(function(){return l}));var r=function(e){return e.ACTION="action",e.CONDITION="condition",e.DELAY_ACTION="delay_action",e}({}),o=[{value:"hour",label:"sw-flow-delay.modal.labelHour"},{value:"day",label:"sw-flow-delay.modal.labelDay"},{value:"week",label:"sw-flow-delay.modal.labelWeek"},{value:"month",label:"sw-flow-delay.modal.labelMonth"},{value:"custom",label:"sw-flow-delay.modal.labelCustom"}],l="custom"},P8hj:function(e,t,n){"use strict";function r(e,t){for(var n=[],r={},o=0;o<t.length;o++){var l=t[o],a=l[0],i={id:e+":"+o,css:l[1],media:l[2],sourceMap:l[3]};r[a]?r[a].parts.push(i):n.push(r[a]={id:a,parts:[i]})}return n}n.r(t),n.d(t,"default",(function(){return p}));var o="undefined"!=typeof document;if("undefined"!=typeof DEBUG&&DEBUG&&!o)throw new Error("vue-style-loader cannot be used in a non-browser environment. Use { target: 'node' } in your Webpack config to indicate a server-rendering environment.");var l={},a=o&&(document.head||document.getElementsByTagName("head")[0]),i=null,s=0,c=!1,u=function(){},d=null,f="data-vue-ssr-id",m="undefined"!=typeof navigator&&/msie [6-9]\b/.test(navigator.userAgent.toLowerCase());function p(e,t,n,o){c=n,d=o||{};var a=r(e,t);return y(a),function(t){for(var n=[],o=0;o<a.length;o++){var i=a[o];(s=l[i.id]).refs--,n.push(s)}t?y(a=r(e,t)):a=[];for(o=0;o<n.length;o++){var s;if(0===(s=n[o]).refs){for(var c=0;c<s.parts.length;c++)s.parts[c]();delete l[s.id]}}}}function y(e){for(var t=0;t<e.length;t++){var n=e[t],r=l[n.id];if(r){r.refs++;for(var o=0;o<r.parts.length;o++)r.parts[o](n.parts[o]);for(;o<n.parts.length;o++)r.parts.push(v(n.parts[o]));r.parts.length>n.parts.length&&(r.parts.length=n.parts.length)}else{var a=[];for(o=0;o<n.parts.length;o++)a.push(v(n.parts[o]));l[n.id]={id:n.id,refs:1,parts:a}}}}function h(){var e=document.createElement("style");return e.type="text/css",a.appendChild(e),e}function v(e){var t,n,r=document.querySelector("style["+f+'~="'+e.id+'"]');if(r){if(c)return u;r.parentNode.removeChild(r)}if(m){var o=s++;r=i||(i=h()),t=g.bind(null,r,o,!1),n=g.bind(null,r,o,!0)}else r=h(),t=T.bind(null,r),n=function(){r.parentNode.removeChild(r)};return t(e),function(r){if(r){if(r.css===e.css&&r.media===e.media&&r.sourceMap===e.sourceMap)return;t(e=r)}else n()}}var b,w=(b=[],function(e,t){return b[e]=t,b.filter(Boolean).join("\n")});function g(e,t,n,r){var o=n?"":r.css;if(e.styleSheet)e.styleSheet.cssText=w(t,o);else{var l=document.createTextNode(o),a=e.childNodes;a[t]&&e.removeChild(a[t]),a.length?e.insertBefore(l,a[t]):e.appendChild(l)}}function T(e,t){var n=t.css,r=t.media,o=t.sourceMap;if(r&&e.setAttribute("media",r),d.ssrId&&e.setAttribute(f,t.id),o&&(n+="\n/*# sourceURL="+o.sources[0]+" */",n+="\n/*# sourceMappingURL=data:application/json;base64,"+btoa(unescape(encodeURIComponent(JSON.stringify(o))))+" */"),e.styleSheet)e.styleSheet.cssText=n;else{for(;e.firstChild;)e.removeChild(e.firstChild);e.appendChild(document.createTextNode(n))}}},X9Kk:function(e,t,n){"use strict";n.r(t);n("cBxO");var r=n("EFTm");function o(e){return(o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function l(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function a(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?l(Object(n),!0).forEach((function(t){i(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):l(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function i(e,t,n){return(t=function(e){var t=function(e,t){if("object"!==o(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!==o(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===o(t)?t:String(t)}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var s=Shopware.Component,c=Shopware.Classes.ShopwareError;t.default=s.wrapComponentConfig({template:'<sw-modal\n    class="sw-flow-delay-modal"\n    variant="small"\n    :closable="false"\n    :title="$tc(\'sw-flow-delay.modal.titleDelayAction\')"\n    @modal-close="onClose"\n>\n    <div\n        v-if="isUpdateDelay"\n    >\n        <sw-single-select\n            class="sw-flow-delay-modal__type-selection"\n            :value="type"\n            :label="$tc(\'sw-flow-delay.modal.typeDelayAction\')"\n            :options="actionDelayOptions"\n            :popover-classes="[\'sw-flow-delay-action__popover\']"\n            :error="typeError"\n            {% if VUE3 %}\n            @update:value="onSelectDelay"\n            {% else %}\n            @change="onSelectDelay"\n            {% endif %}\n        >\n        </sw-single-select>\n    </div>\n\n    <sw-number-field\n        v-if="type !== CUSTOM_TIME"\n        {% if VUE3 %}\n        v-model:value="time"\n        {% else %}\n        v-model="time"\n        {% endif %}\n        class="sw-flow-delay-modal__time"\n        required\n        :min="1"\n        :label="getTimeLabel(type)"\n        :placeholder="getTimePlaceholder(type)"\n        :error="timeError"\n    />\n\n    <sw-text-field\n        v-else\n        {% if VUE3 %}\n        v-model:value="time"\n        {% else %}\n        v-model="time"\n        {% endif %}\n        class="sw-flow-delay-modal__custom-time"\n        required\n        :helpText="$tc(\'sw-flow-delay.modal.helpTextCustomTime\')"\n        :label="$tc(\'sw-flow-delay.modal.labelCustom\')"\n        :placeholder="$tc(\'sw-flow-delay.modal.placeholderCustomTime\')"\n        :error="customTimeError"\n    />\n    <template #modal-footer>\n        <sw-button\n            class="sw-flow-delay-modal__cancel-button"\n            size="small"\n            @click="onClose"\n        >\n            {{ $tc(\'global.default.cancel\') }}\n        </sw-button>\n\n        <sw-button\n            class="sw-flow-delay-modal__save-button"\n            variant="primary"\n            size="small"\n            @click="onSaveDelay"\n        >\n            {{ $tc(\'global.default.save\') }}\n        </sw-button>\n    </template>\n</sw-modal>\n',props:{sequence:{type:Object,required:!0},type:{type:String,required:!0},isUpdateDelay:{type:Boolean,required:!1}},data:function(){return{time:"",timeError:null,typeError:null,customTimeError:null,CUSTOM_TIME:r.a}},created:function(){this.createdComponent()},computed:{actionDelayOptions:function(){var e=this;return r.b.map((function(t){return a(a({},t),{},{label:e.$tc(t.label)})}))}},watch:{time:function(e){e&&this.timeError&&(this.timeError=null),e&&this.customTimeError&&(this.customTimeError=null)},type:function(e){e&&this.typeError&&(this.typeError=null),this.time=null,this.customTimeError=null,this.timeError=null}},methods:{createdComponent:function(){if(this.time=this.type===r.a?"":null,this.isUpdateDelay){var e=this.sequence.config.delay;1===e.length?this.time=e[0].value:this.time="".concat(e[0].value,":").concat(e[1].value,":").concat(e[2].value,":").concat(e[3].value)}},fieldError:function(e){return e?null:new c({code:"c1051bb4-d103-4f74-8988-acbcafc7fdc3"})},validateCustomTime:function(e){var t=this.convertCustomTime().every((function(e){return 0===e.value}));if(!e||t)return new c({code:"c1051bb4-d103-4f74-8988-acbcafc7fdc3"});return/^(\d+):(\d+):(\d+):(\d+)$/.exec(e)?null:new c({code:"CUSTOM_TIME_INVALID",detail:this.$tc("sw-flow-delay.modal.customTimeInvalid")})},getTimeType:function(e){if(e<0)return"";switch(e){case 0:return"month";case 1:return"week";case 2:return"day";case 3:return"hour";default:return""}},convertCustomTime:function(){var e=this;return this.time.split(":").map((function(t,n){return{value:parseInt(t),type:e.getTimeType(n,t)}}))},onClose:function(){this.$emit("modal-close")},onSelectDelay:function(e){var t=null===e?"":e;this.$emit("type-change",t)},onSaveDelay:function(){if(this.type===r.a?this.customTimeError=this.validateCustomTime(this.time):this.timeError=this.fieldError(this.time),this.typeError=this.fieldError(this.type),this.timeError||this.typeError)return null;if(this.customTimeError||this.typeError)return null;var e=a(a({},this.sequence),{},{config:{delay:[{type:this.type,value:this.time}]}});this.type===r.a&&(e.config={delay:this.convertCustomTime()}),this.$emit("modal-save",e)},getTimeLabel:function(e){switch(e){case"hour":return this.$tc("sw-flow-delay.modal.labelHour");case"day":return this.$tc("sw-flow-delay.modal.labelDay");case"week":return this.$tc("sw-flow-delay.modal.labelWeek");case"month":return this.$tc("sw-flow-delay.modal.labelMonth");default:return""}},getTimePlaceholder:function(e){return this.$tc("sw-flow-delay.modal.placeholderTime",0,{type:this.getTimeLabel(e)})}}})},cBxO:function(e,t,n){var r=n("pcFy");r.__esModule&&(r=r.default),"string"==typeof r&&(r=[[e.i,r,""]]),r.locals&&(e.exports=r.locals);(0,n("P8hj").default)("6572f3fb",r,!0,{})},pcFy:function(e,t,n){}}]);
//# sourceMappingURL=1.js.map
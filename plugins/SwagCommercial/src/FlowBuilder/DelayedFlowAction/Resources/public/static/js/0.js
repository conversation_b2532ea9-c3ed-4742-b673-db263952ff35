(this["webpackJsonpPlugindelayed-flow-action"]=this["webpackJsonpPlugindelayed-flow-action"]||[]).push([[0],{"919+":function(e,t,n){"use strict";n.r(t);n("D/T4");var a=n("EFTm"),l=Shopware.Component;t.default=l.wrapComponentConfig({template:'<sw-modal\n    class="sw-flow-delay-edit-warning-modal"\n    :title="titleModal"\n    :closable="false"\n    variant="small"\n    @modal-close="$emit(\'modal-cancel\')"\n>\n    <sw-alert :variant="warningContent.type">\n        {{ warningContent.text }}\n    </sw-alert>\n\n    <template #modal-footer>\n        <div class="sw-flow-delay-edit-warning-modal__footer-content">\n            <sw-checkbox-field\n                {% if VUE3 %}\n                v-model:value="dontRemindSelection"\n                {% else %}\n                v-model="dontRemindSelection"\n                {% endif %}\n                class="sw-flow-delay-edit-warning-modal__reminder"\n                :label="$tc(\'sw-flow-delay.detail.sequence.labelDontRemind\')"\n            />\n\n            <div>\n                <sw-button\n                    class="sw-flow-delay-edit-warning-modal__cancel-button"\n                    size="small"\n                    @click="$emit(\'modal-cancel\')"\n                >\n                    {{ $tc(\'global.default.cancel\') }}\n                </sw-button>\n\n                <sw-button\n                    v-if="type === \'delay_action\' && actionType === \'DELETE\'"\n                    class="sw-flow-delay-edit-warning-modal__delete-button"\n                    size="small"\n                    variant="danger"\n                    @click="handleCloseModal"\n                >\n                    {{ $tc(\'global.default.delete\') }}\n                </sw-button>\n\n                <sw-button\n                    v-else\n                    class="sw-flow-delay-edit-warning-modal__continue-button"\n                    size="small"\n                    variant="primary"\n                    @click="handleCloseModal"\n                >\n                    {{ $tc(\'sw-flow-delay.detail.sequence.continueButton\') }}\n                </sw-button>\n            </div>\n        </div>\n    </template>\n</sw-modal>\n',data:function(){return{dontRemindSelection:!1}},computed:{titleModal:function(){return this.type===a.c.ACTION||this.type===a.c.CONDITION||this.type===a.c.DELAY_ACTION&&"DELETE"===this.actionType?this.$tc("global.default.warning"):this.$tc("global.default.info")},warningContent:function(){return this.type===a.c.ACTION?{text:this.$tc("sw-flow-delay.detail.sequence.labelChangingAction"),type:"warning"}:this.type===a.c.CONDITION?{text:this.$tc("sw-flow-delay.detail.sequence.labelChangingCondition"),type:"warning"}:this.type===a.c.DELAY_ACTION&&"DELETE"!==this.actionType?{text:this.$tc("sw-flow-delay.detail.sequence.labelChangingDelay"),type:"info"}:{text:this.$tc("sw-flow-delay.detail.sequence.labelDeletingDelay"),type:"warning"}}},props:{actionType:{type:String,default:"DELETE"},type:{type:String,default:a.c.ACTION}},methods:{handleCloseModal:function(){if(this.dontRemindSelection&&this.type===a.c.DELAY_ACTION&&"DELETE"===this.actionType)return localStorage.setItem("delay_deleted","true"),void this.$emit("modal-close");this.dontRemindSelection&&localStorage.setItem(this.type,"true"),this.$emit("modal-close")}}})},"D/T4":function(e,t,n){var a=n("ydCK");a.__esModule&&(a=a.default),"string"==typeof a&&(a=[[e.i,a,""]]),a.locals&&(e.exports=a.locals);(0,n("P8hj").default)("10314691",a,!0,{})},EFTm:function(e,t,n){"use strict";n.d(t,"c",(function(){return a})),n.d(t,"b",(function(){return l})),n.d(t,"a",(function(){return o}));var a=function(e){return e.ACTION="action",e.CONDITION="condition",e.DELAY_ACTION="delay_action",e}({}),l=[{value:"hour",label:"sw-flow-delay.modal.labelHour"},{value:"day",label:"sw-flow-delay.modal.labelDay"},{value:"week",label:"sw-flow-delay.modal.labelWeek"},{value:"month",label:"sw-flow-delay.modal.labelMonth"},{value:"custom",label:"sw-flow-delay.modal.labelCustom"}],o="custom"},P8hj:function(e,t,n){"use strict";function a(e,t){for(var n=[],a={},l=0;l<t.length;l++){var o=t[l],i=o[0],s={id:e+":"+l,css:o[1],media:o[2],sourceMap:o[3]};a[i]?a[i].parts.push(s):n.push(a[i]={id:i,parts:[s]})}return n}n.r(t),n.d(t,"default",(function(){return m}));var l="undefined"!=typeof document;if("undefined"!=typeof DEBUG&&DEBUG&&!l)throw new Error("vue-style-loader cannot be used in a non-browser environment. Use { target: 'node' } in your Webpack config to indicate a server-rendering environment.");var o={},i=l&&(document.head||document.getElementsByTagName("head")[0]),s=null,r=0,d=!1,c=function(){},u=null,f="data-vue-ssr-id",p="undefined"!=typeof navigator&&/msie [6-9]\b/.test(navigator.userAgent.toLowerCase());function m(e,t,n,l){d=n,u=l||{};var i=a(e,t);return h(i),function(t){for(var n=[],l=0;l<i.length;l++){var s=i[l];(r=o[s.id]).refs--,n.push(r)}t?h(i=a(e,t)):i=[];for(l=0;l<n.length;l++){var r;if(0===(r=n[l]).refs){for(var d=0;d<r.parts.length;d++)r.parts[d]();delete o[r.id]}}}}function h(e){for(var t=0;t<e.length;t++){var n=e[t],a=o[n.id];if(a){a.refs++;for(var l=0;l<a.parts.length;l++)a.parts[l](n.parts[l]);for(;l<n.parts.length;l++)a.parts.push(y(n.parts[l]));a.parts.length>n.parts.length&&(a.parts.length=n.parts.length)}else{var i=[];for(l=0;l<n.parts.length;l++)i.push(y(n.parts[l]));o[n.id]={id:n.id,refs:1,parts:i}}}}function w(){var e=document.createElement("style");return e.type="text/css",i.appendChild(e),e}function y(e){var t,n,a=document.querySelector("style["+f+'~="'+e.id+'"]');if(a){if(d)return c;a.parentNode.removeChild(a)}if(p){var l=r++;a=s||(s=w()),t=b.bind(null,a,l,!1),n=b.bind(null,a,l,!0)}else a=w(),t=C.bind(null,a),n=function(){a.parentNode.removeChild(a)};return t(e),function(a){if(a){if(a.css===e.css&&a.media===e.media&&a.sourceMap===e.sourceMap)return;t(e=a)}else n()}}var g,v=(g=[],function(e,t){return g[e]=t,g.filter(Boolean).join("\n")});function b(e,t,n,a){var l=n?"":a.css;if(e.styleSheet)e.styleSheet.cssText=v(t,l);else{var o=document.createTextNode(l),i=e.childNodes;i[t]&&e.removeChild(i[t]),i.length?e.insertBefore(o,i[t]):e.appendChild(o)}}function C(e,t){var n=t.css,a=t.media,l=t.sourceMap;if(a&&e.setAttribute("media",a),u.ssrId&&e.setAttribute(f,t.id),l&&(n+="\n/*# sourceURL="+l.sources[0]+" */",n+="\n/*# sourceMappingURL=data:application/json;base64,"+btoa(unescape(encodeURIComponent(JSON.stringify(l))))+" */"),e.styleSheet)e.styleSheet.cssText=n;else{for(;e.firstChild;)e.removeChild(e.firstChild);e.appendChild(document.createTextNode(n))}}},ydCK:function(e,t,n){}}]);
//# sourceMappingURL=0.js.map
{"version": 3, "sources": ["webpack:////tmp/extension2400992207/SwagCommercial/src/FlowBuilder/DelayedFlowAction/Resources/app/administration/src/module/sw-flow/component/sw-flow-detail/index.ts", "webpack:////tmp/extension2400992207/SwagCommercial/src/FlowBuilder/DelayedFlowAction/Resources/app/administration/src/module/sw-flow/component/sw-flow-detail/sw-flow-detail.html.twig"], "names": ["_regeneratorRuntime", "exports", "Op", "Object", "prototype", "hasOwn", "hasOwnProperty", "defineProperty", "obj", "key", "desc", "value", "$Symbol", "Symbol", "iteratorSymbol", "iterator", "asyncIteratorSymbol", "asyncIterator", "toStringTagSymbol", "toStringTag", "define", "enumerable", "configurable", "writable", "err", "wrap", "innerFn", "outerFn", "self", "tryLocsList", "protoGenerator", "Generator", "generator", "create", "context", "Context", "makeInvokeMethod", "tryCatch", "fn", "arg", "type", "call", "ContinueSentinel", "GeneratorFunction", "GeneratorFunctionPrototype", "IteratorPrototype", "getProto", "getPrototypeOf", "NativeIteratorPrototype", "values", "Gp", "defineIteratorMethods", "for<PERSON>ach", "method", "_invoke", "AsyncIterator", "PromiseImpl", "invoke", "resolve", "reject", "record", "result", "_typeof", "__await", "then", "unwrapped", "error", "previousPromise", "callInvokeWithMethodAndArg", "state", "Error", "doneResult", "delegate", "delegate<PERSON><PERSON><PERSON>", "maybeInvokeDelegate", "sent", "_sent", "dispatchException", "abrupt", "done", "methodName", "undefined", "return", "TypeError", "info", "resultName", "next", "nextLoc", "pushTryEntry", "locs", "entry", "tryLoc", "catchLoc", "finallyLoc", "afterLoc", "tryEntries", "push", "resetTryEntry", "completion", "reset", "iterable", "iteratorMethod", "isNaN", "length", "i", "displayName", "isGeneratorFunction", "gen<PERSON>un", "ctor", "constructor", "name", "mark", "setPrototypeOf", "__proto__", "awrap", "async", "Promise", "iter", "keys", "val", "object", "reverse", "pop", "skip<PERSON>emp<PERSON><PERSON><PERSON>", "prev", "char<PERSON>t", "slice", "stop", "rootRecord", "rval", "exception", "handle", "loc", "caught", "hasCatch", "hasFinally", "finallyEntry", "complete", "finish", "catch", "thrown", "<PERSON><PERSON><PERSON>", "asyncGeneratorStep", "gen", "_next", "_throw", "_asyncToGenerator", "args", "arguments", "apply", "ownKeys", "enumerableOnly", "getOwnPropertySymbols", "symbols", "filter", "sym", "getOwnPropertyDescriptor", "_objectSpread", "target", "source", "_defineProperty", "getOwnPropertyDescriptors", "defineProperties", "input", "hint", "prim", "toPrimitive", "res", "String", "Number", "_toPrimitive", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "_Shopware", "Shopware", "Component", "State", "mapState", "getComponentHelper", "Criteria", "Data", "wrapComponentConfig", "template", "inject", "data", "isOpenWarningModal", "delayedActions", "computed", "delayConstant", "this", "flowBuilderService", "getActionName", "hasDelayedActions", "_this", "sequences", "some", "item", "actionName", "flowCriteria", "criteria", "addAssociation", "getAssociation", "addSorting", "sort", "delayedActionsRepository", "repositoryFactory", "delayedActionCriteria", "page", "limit", "addFilter", "equals", "flow", "id", "methods", "getLicense", "toggle", "License", "get", "getDetailFlow", "_this2", "_callee", "_context", "$super", "isNewFlow", "isTemplate", "search", "validateEmptySequence", "invalidSequences", "sequence", "config", "delay", "commit", "onCloseModal", "active", "onSave", "_this3", "_callee2", "validDelayedActions", "invalidDelayedActions", "_context2", "isNew", "removeAllSelectors", "delayedAction", "parentId", "delaySequenceId", "validDelayedAction", "syncDeleted", "getIds"], "mappings": ";yYACAA,EAAA,kBAAAC,GAAA,IAAAA,EAAA,GAAAC,EAAAC,OAAAC,UAAAC,EAAAH,EAAAI,eAAAC,EAAAJ,OAAAI,gBAAA,SAAAC,EAAAC,EAAAC,GAAAF,EAAAC,GAAAC,EAAAC,OAAAC,EAAA,mBAAAC,cAAA,GAAAC,EAAAF,EAAAG,UAAA,aAAAC,EAAAJ,EAAAK,eAAA,kBAAAC,EAAAN,EAAAO,aAAA,yBAAAC,EAAAZ,EAAAC,EAAAE,GAAA,OAAAR,OAAAI,eAAAC,EAAAC,EAAA,CAAAE,QAAAU,YAAA,EAAAC,cAAA,EAAAC,UAAA,IAAAf,EAAAC,GAAA,IAAAW,EAAA,aAAAI,GAAAJ,EAAA,SAAAZ,EAAAC,EAAAE,GAAA,OAAAH,EAAAC,GAAAE,GAAA,SAAAc,EAAAC,EAAAC,EAAAC,EAAAC,GAAA,IAAAC,EAAAH,KAAAvB,qBAAA2B,EAAAJ,EAAAI,EAAAC,EAAA7B,OAAA8B,OAAAH,EAAA1B,WAAA8B,EAAA,IAAAC,EAAAN,GAAA,WAAAtB,EAAAyB,EAAA,WAAArB,MAAAyB,EAAAV,EAAAE,EAAAM,KAAAF,EAAA,SAAAK,EAAAC,EAAA9B,EAAA+B,GAAA,WAAAC,KAAA,SAAAD,IAAAD,EAAAG,KAAAjC,EAAA+B,IAAA,MAAAf,GAAA,OAAAgB,KAAA,QAAAD,IAAAf,IAAAvB,EAAAwB,OAAA,IAAAiB,EAAA,YAAAX,KAAA,SAAAY,KAAA,SAAAC,KAAA,IAAAC,EAAA,GAAAzB,EAAAyB,EAAA/B,GAAA,8BAAAgC,EAAA3C,OAAA4C,eAAAC,EAAAF,OAAAG,EAAA,MAAAD,OAAA9C,GAAAG,EAAAoC,KAAAO,EAAAlC,KAAA+B,EAAAG,GAAA,IAAAE,EAAAN,EAAAxC,UAAA2B,EAAA3B,UAAAD,OAAA8B,OAAAY,GAAA,SAAAM,EAAA/C,GAAA,0BAAAgD,SAAA,SAAAC,GAAAjC,EAAAhB,EAAAiD,GAAA,SAAAd,GAAA,YAAAe,QAAAD,EAAAd,SAAA,SAAAgB,EAAAvB,EAAAwB,GAAA,SAAAC,EAAAJ,EAAAd,EAAAmB,EAAAC,GAAA,IAAAC,EAAAvB,EAAAL,EAAAqB,GAAArB,EAAAO,GAAA,aAAAqB,EAAApB,KAAA,KAAAqB,EAAAD,EAAArB,IAAA5B,EAAAkD,EAAAlD,MAAA,OAAAA,GAAA,UAAAmD,EAAAnD,IAAAN,EAAAoC,KAAA9B,EAAA,WAAA6C,EAAAE,QAAA/C,EAAAoD,SAAAC,MAAA,SAAArD,GAAA8C,EAAA,OAAA9C,EAAA+C,EAAAC,MAAA,SAAAnC,GAAAiC,EAAA,QAAAjC,EAAAkC,EAAAC,MAAAH,EAAAE,QAAA/C,GAAAqD,MAAA,SAAAC,GAAAJ,EAAAlD,MAAAsD,EAAAP,EAAAG,MAAA,SAAAK,GAAA,OAAAT,EAAA,QAAAS,EAAAR,EAAAC,QAAAC,EAAArB,KAAA,IAAA4B,EAAA5D,EAAA,gBAAAI,MAAA,SAAA0C,EAAAd,GAAA,SAAA6B,IAAA,WAAAZ,GAAA,SAAAE,EAAAC,GAAAF,EAAAJ,EAAAd,EAAAmB,EAAAC,MAAA,OAAAQ,MAAAH,KAAAI,YAAA,SAAAhC,EAAAV,EAAAE,EAAAM,GAAA,IAAAmC,EAAA,iCAAAhB,EAAAd,GAAA,iBAAA8B,EAAA,UAAAC,MAAA,iDAAAD,EAAA,cAAAhB,EAAA,MAAAd,EAAA,OAAAgC,IAAA,IAAArC,EAAAmB,SAAAnB,EAAAK,QAAA,KAAAiC,EAAAtC,EAAAsC,SAAA,GAAAA,EAAA,KAAAC,EAAAC,EAAAF,EAAAtC,GAAA,GAAAuC,EAAA,IAAAA,IAAA/B,EAAA,gBAAA+B,GAAA,YAAAvC,EAAAmB,OAAAnB,EAAAyC,KAAAzC,EAAA0C,MAAA1C,EAAAK,SAAA,aAAAL,EAAAmB,OAAA,uBAAAgB,EAAA,MAAAA,EAAA,YAAAnC,EAAAK,IAAAL,EAAA2C,kBAAA3C,EAAAK,SAAA,WAAAL,EAAAmB,QAAAnB,EAAA4C,OAAA,SAAA5C,EAAAK,KAAA8B,EAAA,gBAAAT,EAAAvB,EAAAX,EAAAE,EAAAM,GAAA,cAAA0B,EAAApB,KAAA,IAAA6B,EAAAnC,EAAA6C,KAAA,6BAAAnB,EAAArB,MAAAG,EAAA,gBAAA/B,MAAAiD,EAAArB,IAAAwC,KAAA7C,EAAA6C,MAAA,UAAAnB,EAAApB,OAAA6B,EAAA,YAAAnC,EAAAmB,OAAA,QAAAnB,EAAAK,IAAAqB,EAAArB,OAAA,SAAAmC,EAAAF,EAAAtC,GAAA,IAAA8C,EAAA9C,EAAAmB,SAAAmB,EAAAzD,SAAAiE,GAAA,QAAAC,IAAA5B,EAAA,OAAAnB,EAAAsC,SAAA,eAAAQ,GAAAR,EAAAzD,SAAAmE,SAAAhD,EAAAmB,OAAA,SAAAnB,EAAAK,SAAA0C,EAAAP,EAAAF,EAAAtC,GAAA,UAAAA,EAAAmB,SAAA,WAAA2B,IAAA9C,EAAAmB,OAAA,QAAAnB,EAAAK,IAAA,IAAA4C,UAAA,oCAAAH,EAAA,aAAAtC,EAAA,IAAAkB,EAAAvB,EAAAgB,EAAAmB,EAAAzD,SAAAmB,EAAAK,KAAA,aAAAqB,EAAApB,KAAA,OAAAN,EAAAmB,OAAA,QAAAnB,EAAAK,IAAAqB,EAAArB,IAAAL,EAAAsC,SAAA,KAAA9B,EAAA,IAAA0C,EAAAxB,EAAArB,IAAA,OAAA6C,IAAAL,MAAA7C,EAAAsC,EAAAa,YAAAD,EAAAzE,MAAAuB,EAAAoD,KAAAd,EAAAe,QAAA,WAAArD,EAAAmB,SAAAnB,EAAAmB,OAAA,OAAAnB,EAAAK,SAAA0C,GAAA/C,EAAAsC,SAAA,KAAA9B,GAAA0C,GAAAlD,EAAAmB,OAAA,QAAAnB,EAAAK,IAAA,IAAA4C,UAAA,oCAAAjD,EAAAsC,SAAA,KAAA9B,GAAA,SAAA8C,EAAAC,GAAA,IAAAC,EAAA,CAAAC,OAAAF,EAAA,SAAAA,IAAAC,EAAAE,SAAAH,EAAA,SAAAA,IAAAC,EAAAG,WAAAJ,EAAA,GAAAC,EAAAI,SAAAL,EAAA,SAAAM,WAAAC,KAAAN,GAAA,SAAAO,EAAAP,GAAA,IAAA9B,EAAA8B,EAAAQ,YAAA,GAAAtC,EAAApB,KAAA,gBAAAoB,EAAArB,IAAAmD,EAAAQ,WAAAtC,EAAA,SAAAzB,EAAAN,GAAA,KAAAkE,WAAA,EAAAJ,OAAA,SAAA9D,EAAAuB,QAAAoC,EAAA,WAAAW,OAAA,YAAAlD,EAAAmD,GAAA,GAAAA,EAAA,KAAAC,EAAAD,EAAAtF,GAAA,GAAAuF,EAAA,OAAAA,EAAA5D,KAAA2D,GAAA,sBAAAA,EAAAd,KAAA,OAAAc,EAAA,IAAAE,MAAAF,EAAAG,QAAA,KAAAC,GAAA,EAAAlB,EAAA,SAAAA,IAAA,OAAAkB,EAAAJ,EAAAG,QAAA,GAAAlG,EAAAoC,KAAA2D,EAAAI,GAAA,OAAAlB,EAAA3E,MAAAyF,EAAAI,GAAAlB,EAAAP,MAAA,EAAAO,EAAA,OAAAA,EAAA3E,WAAAsE,EAAAK,EAAAP,MAAA,EAAAO,GAAA,OAAAA,UAAA,OAAAA,KAAAf,GAAA,SAAAA,IAAA,OAAA5D,WAAAsE,EAAAF,MAAA,UAAApC,EAAAvC,UAAAwC,EAAArC,EAAA2C,EAAA,eAAAvC,MAAAiC,EAAAtB,cAAA,IAAAf,EAAAqC,EAAA,eAAAjC,MAAAgC,EAAArB,cAAA,IAAAqB,EAAA8D,YAAArF,EAAAwB,EAAA1B,EAAA,qBAAAjB,EAAAyG,oBAAA,SAAAC,GAAA,IAAAC,EAAA,mBAAAD,KAAAE,YAAA,QAAAD,QAAAjE,GAAA,uBAAAiE,EAAAH,aAAAG,EAAAE,QAAA7G,EAAA8G,KAAA,SAAAJ,GAAA,OAAAxG,OAAA6G,eAAA7G,OAAA6G,eAAAL,EAAA/D,IAAA+D,EAAAM,UAAArE,EAAAxB,EAAAuF,EAAAzF,EAAA,sBAAAyF,EAAAvG,UAAAD,OAAA8B,OAAAiB,GAAAyD,GAAA1G,EAAAiH,MAAA,SAAA3E,GAAA,OAAAwB,QAAAxB,IAAAY,EAAAI,EAAAnD,WAAAgB,EAAAmC,EAAAnD,UAAAY,GAAA,0BAAAf,EAAAsD,gBAAAtD,EAAAkH,MAAA,SAAAzF,EAAAC,EAAAC,EAAAC,EAAA2B,QAAA,IAAAA,MAAA4D,SAAA,IAAAC,EAAA,IAAA9D,EAAA9B,EAAAC,EAAAC,EAAAC,EAAAC,GAAA2B,GAAA,OAAAvD,EAAAyG,oBAAA/E,GAAA0F,IAAA/B,OAAAtB,MAAA,SAAAH,GAAA,OAAAA,EAAAkB,KAAAlB,EAAAlD,MAAA0G,EAAA/B,WAAAnC,EAAAD,GAAA9B,EAAA8B,EAAAhC,EAAA,aAAAE,EAAA8B,EAAApC,GAAA,0BAAAM,EAAA8B,EAAA,qDAAAjD,EAAAqH,KAAA,SAAAC,GAAA,IAAAC,EAAArH,OAAAoH,GAAAD,EAAA,WAAA7G,KAAA+G,EAAAF,EAAAtB,KAAAvF,GAAA,OAAA6G,EAAAG,UAAA,SAAAnC,IAAA,KAAAgC,EAAAf,QAAA,KAAA9F,EAAA6G,EAAAI,MAAA,GAAAjH,KAAA+G,EAAA,OAAAlC,EAAA3E,MAAAF,EAAA6E,EAAAP,MAAA,EAAAO,EAAA,OAAAA,EAAAP,MAAA,EAAAO,IAAArF,EAAAgD,SAAAd,EAAA/B,UAAA,CAAAyG,YAAA1E,EAAAgE,MAAA,SAAAwB,GAAA,QAAAC,KAAA,OAAAtC,KAAA,OAAAX,KAAA,KAAAC,WAAAK,EAAA,KAAAF,MAAA,OAAAP,SAAA,UAAAnB,OAAA,YAAAd,SAAA0C,EAAA,KAAAc,WAAA3C,QAAA6C,IAAA0B,EAAA,QAAAb,KAAA,WAAAA,EAAAe,OAAA,IAAAxH,EAAAoC,KAAA,KAAAqE,KAAAR,OAAAQ,EAAAgB,MAAA,WAAAhB,QAAA7B,IAAA8C,KAAA,gBAAAhD,MAAA,MAAAiD,EAAA,KAAAjC,WAAA,GAAAG,WAAA,aAAA8B,EAAAxF,KAAA,MAAAwF,EAAAzF,IAAA,YAAA0F,MAAApD,kBAAA,SAAAqD,GAAA,QAAAnD,KAAA,MAAAmD,EAAA,IAAAhG,EAAA,cAAAiG,EAAAC,EAAAC,GAAA,OAAAzE,EAAApB,KAAA,QAAAoB,EAAArB,IAAA2F,EAAAhG,EAAAoD,KAAA8C,EAAAC,IAAAnG,EAAAmB,OAAA,OAAAnB,EAAAK,SAAA0C,KAAAoD,EAAA,QAAA7B,EAAA,KAAAT,WAAAQ,OAAA,EAAAC,GAAA,IAAAA,EAAA,KAAAd,EAAA,KAAAK,WAAAS,GAAA5C,EAAA8B,EAAAQ,WAAA,YAAAR,EAAAC,OAAA,OAAAwC,EAAA,UAAAzC,EAAAC,QAAA,KAAAiC,KAAA,KAAAU,EAAAjI,EAAAoC,KAAAiD,EAAA,YAAA6C,EAAAlI,EAAAoC,KAAAiD,EAAA,iBAAA4C,GAAAC,EAAA,SAAAX,KAAAlC,EAAAE,SAAA,OAAAuC,EAAAzC,EAAAE,UAAA,WAAAgC,KAAAlC,EAAAG,WAAA,OAAAsC,EAAAzC,EAAAG,iBAAA,GAAAyC,GAAA,QAAAV,KAAAlC,EAAAE,SAAA,OAAAuC,EAAAzC,EAAAE,UAAA,YAAA2C,EAAA,UAAAjE,MAAA,kDAAAsD,KAAAlC,EAAAG,WAAA,OAAAsC,EAAAzC,EAAAG,gBAAAf,OAAA,SAAAtC,EAAAD,GAAA,QAAAiE,EAAA,KAAAT,WAAAQ,OAAA,EAAAC,GAAA,IAAAA,EAAA,KAAAd,EAAA,KAAAK,WAAAS,GAAA,GAAAd,EAAAC,QAAA,KAAAiC,MAAAvH,EAAAoC,KAAAiD,EAAA,oBAAAkC,KAAAlC,EAAAG,WAAA,KAAA2C,EAAA9C,EAAA,OAAA8C,IAAA,UAAAhG,GAAA,aAAAA,IAAAgG,EAAA7C,QAAApD,MAAAiG,EAAA3C,aAAA2C,EAAA,UAAA5E,EAAA4E,IAAAtC,WAAA,UAAAtC,EAAApB,OAAAoB,EAAArB,MAAAiG,GAAA,KAAAnF,OAAA,YAAAiC,KAAAkD,EAAA3C,WAAAnD,GAAA,KAAA+F,SAAA7E,IAAA6E,SAAA,SAAA7E,EAAAkC,GAAA,aAAAlC,EAAApB,KAAA,MAAAoB,EAAArB,IAAA,gBAAAqB,EAAApB,MAAA,aAAAoB,EAAApB,KAAA,KAAA8C,KAAA1B,EAAArB,IAAA,WAAAqB,EAAApB,MAAA,KAAAyF,KAAA,KAAA1F,IAAAqB,EAAArB,IAAA,KAAAc,OAAA,cAAAiC,KAAA,kBAAA1B,EAAApB,MAAAsD,IAAA,KAAAR,KAAAQ,GAAApD,GAAAgG,OAAA,SAAA7C,GAAA,QAAAW,EAAA,KAAAT,WAAAQ,OAAA,EAAAC,GAAA,IAAAA,EAAA,KAAAd,EAAA,KAAAK,WAAAS,GAAA,GAAAd,EAAAG,eAAA,YAAA4C,SAAA/C,EAAAQ,WAAAR,EAAAI,UAAAG,EAAAP,GAAAhD,IAAAiG,MAAA,SAAAhD,GAAA,QAAAa,EAAA,KAAAT,WAAAQ,OAAA,EAAAC,GAAA,IAAAA,EAAA,KAAAd,EAAA,KAAAK,WAAAS,GAAA,GAAAd,EAAAC,WAAA,KAAA/B,EAAA8B,EAAAQ,WAAA,aAAAtC,EAAApB,KAAA,KAAAoG,EAAAhF,EAAArB,IAAA0D,EAAAP,GAAA,OAAAkD,GAAA,UAAAtE,MAAA,0BAAAuE,cAAA,SAAAzC,EAAAf,EAAAE,GAAA,YAAAf,SAAA,CAAAzD,SAAAkC,EAAAmD,GAAAf,aAAAE,WAAA,cAAAlC,SAAA,KAAAd,SAAA0C,GAAAvC,IAAAzC,EAAA,SAAA6I,EAAAC,EAAArF,EAAAC,EAAAqF,EAAAC,EAAAxI,EAAA8B,GAAA,QAAA6C,EAAA2D,EAAAtI,GAAA8B,GAAA5B,EAAAyE,EAAAzE,MAAA,MAAAuD,GAAA,YAAAP,EAAAO,GAAAkB,EAAAL,KAAArB,EAAA/C,GAAAyG,QAAA1D,QAAA/C,GAAAqD,KAAAgF,EAAAC,GAAA,SAAAC,EAAA5G,GAAA,sBAAAV,EAAA,KAAAuH,EAAAC,UAAA,WAAAhC,SAAA,SAAA1D,EAAAC,GAAA,IAAAoF,EAAAzG,EAAA+G,MAAAzH,EAAAuH,GAAA,SAAAH,EAAArI,GAAAmI,EAAAC,EAAArF,EAAAC,EAAAqF,EAAAC,EAAA,OAAAtI,GAAA,SAAAsI,EAAAzH,GAAAsH,EAAAC,EAAArF,EAAAC,EAAAqF,EAAAC,EAAA,QAAAzH,GAAAwH,OAAA/D,OAAA,SAAAqE,EAAA9B,EAAA+B,GAAA,IAAAjC,EAAAnH,OAAAmH,KAAAE,GAAA,GAAArH,OAAAqJ,sBAAA,KAAAC,EAAAtJ,OAAAqJ,sBAAAhC,GAAA+B,IAAAE,IAAAC,QAAA,SAAAC,GAAA,OAAAxJ,OAAAyJ,yBAAApC,EAAAmC,GAAAtI,eAAAiG,EAAAtB,KAAAqD,MAAA/B,EAAAmC,GAAA,OAAAnC,EAAA,SAAAuC,EAAAC,GAAA,QAAAtD,EAAA,EAAAA,EAAA4C,UAAA7C,OAAAC,IAAA,KAAAuD,EAAA,MAAAX,UAAA5C,GAAA4C,UAAA5C,GAAA,GAAAA,EAAA,EAAA8C,EAAAnJ,OAAA4J,IAAA,GAAA3G,SAAA,SAAA3C,GAAAuJ,EAAAF,EAAArJ,EAAAsJ,EAAAtJ,OAAAN,OAAA8J,0BAAA9J,OAAA+J,iBAAAJ,EAAA3J,OAAA8J,0BAAAF,IAAAT,EAAAnJ,OAAA4J,IAAA3G,SAAA,SAAA3C,GAAAN,OAAAI,eAAAuJ,EAAArJ,EAAAN,OAAAyJ,yBAAAG,EAAAtJ,OAAA,OAAAqJ,EAAA,SAAAE,EAAAxJ,EAAAC,EAAAE,GAAA,OAAAF,EAAA,SAAA8B,GAAA,IAAA9B,EAAA,SAAA0J,EAAAC,GAAA,cAAAtG,EAAAqG,IAAA,OAAAA,EAAA,OAAAA,EAAA,IAAAE,EAAAF,EAAAtJ,OAAAyJ,aAAA,QAAArF,IAAAoF,EAAA,KAAAE,EAAAF,EAAA5H,KAAA0H,EAAAC,GAAA,yBAAAtG,EAAAyG,GAAA,OAAAA,EAAA,UAAApF,UAAA,kEAAAiF,EAAAI,OAAAC,QAAAN,GAAAO,CAAAnI,EAAA,2BAAAuB,EAAArD,KAAA+J,OAAA/J,GAAAkK,CAAAlK,MAAAD,EAAAL,OAAAI,eAAAC,EAAAC,EAAA,CAAAE,QAAAU,YAAA,EAAAC,cAAA,EAAAC,UAAA,IAAAf,EAAAC,GAAAE,EAAAH,EAKA,IAAAoK,EAA6BC,SAArBC,EAASF,EAATE,UAAWC,EAAKH,EAALG,MACXC,EAAaF,EAAUG,qBAAvBD,SACAE,EAAaL,SAASM,KAAtBD,SAKOJ,YAAUM,oBAAoB,CACzCC,SCdW,uvCDgBXC,OAAQ,CAAC,oBAAqB,UAAW,sBAEzCC,KAAI,WAIA,MAAO,CACHC,oBAAoB,EACpBC,eAAgB,KAIxBC,SAAQ7B,IAAA,GACDmB,EAAS,cAAe,CAAC,UAAQ,IAEpCW,cAAa,WACT,OAAOC,KAAKC,mBAAmBC,cAAc,UAGjDC,kBAAiB,WAAuC,IAADC,EAAA,KACnD,OAAOJ,KAAKK,UAAUC,MAAK,SAAAC,GAAI,OAAIA,EAAKC,aAAeJ,EAAKL,kBAGhEU,aAAY,WACR,IAAMC,EAAW,IAAIpB,EASrB,OAPAoB,EAASC,eAAe,kBACxBD,EAASE,eAAe,aACnBC,WAAWvB,EAASwB,KAAK,eAAgB,QACzCD,WAAWvB,EAASwB,KAAK,WAAY,QACrCD,WAAWvB,EAASwB,KAAK,WAAY,QACrCD,WAAWvB,EAASwB,KAAK,WAAY,QAEnCJ,GAGXK,yBAAwB,WACpB,OAAOf,KAAKgB,kBAAkB3K,OAAO,sBAGzC4K,sBAAqB,WACjB,IAAMP,EAAW,IAAIpB,EAASU,KAAKkB,KAAMlB,KAAKmB,OAG9C,OAFAT,EAASU,UAAU9B,EAAS+B,OAAO,SAAUrB,KAAKsB,KAAKC,KAEhDb,KAIfc,QAAS,CACLC,WAAU,SAACC,GACP,OAAOzC,SAAS0C,QAAQC,IAAIF,IAG1BG,cAAa,WAAmB,IAADC,EAAA,YAAAxE,EAAAlJ,IAAA+G,MAAA,SAAA4G,IAAA,OAAA3N,IAAAyB,MAAA,SAAAmM,GAAA,cAAAA,EAAAhG,KAAAgG,EAAAtI,MAAA,cAAAsI,EAAAtI,KAAA,EAC3BoI,EAAKG,OAAO,iBAAiB,KAAD,KAE7BH,EAAKI,WAAcJ,EAAKK,WAAU,CAAAH,EAAAtI,KAAA,eAAAsI,EAAAtI,KAAA,EACPoI,EAAKf,yBAAyBqB,OAAON,EAAKb,uBAAuB,KAAD,EAA5Fa,EAAKjC,eAAcmC,EAAAjJ,KAAA,wBAAAiJ,EAAA7F,UAAA4F,MAJUzE,IAQrC+E,sBAAqB,WACjB,IAAIC,EAAmBtC,KAAKiC,OAAO,yBAUnC,OARAjC,KAAKK,UAAU7I,SAAQ,SAAC+K,GACO,iBAAxBA,EAAS/B,YAAkC+B,EAASC,OAAOC,OAC1DH,EAAiBlI,KAAKmI,EAAShB,OAIvCpC,EAAMuD,OAAO,kCAAmCJ,GAEzCA,GAGXK,aAAY,WACR3C,KAAKJ,oBAAqB,EAC1BI,KAAKsB,KAAKsB,QAAS,GAGjBC,OAAM,WAAmB,IAADC,EAAA,YAAAxF,EAAAlJ,IAAA+G,MAAA,SAAA4H,IAAA,IAAAC,EAAAC,EAAA,OAAA7O,IAAAyB,MAAA,SAAAqN,GAAA,cAAAA,EAAAlH,KAAAkH,EAAAxJ,MAAA,YACM,mBAApBoJ,EAAKxB,KAAK6B,OAAwBL,EAAKxB,KAAK6B,SAAYL,EAAKX,YAAU,CAAAe,EAAAxJ,KAAA,QACzD,OAAtBoJ,EAAKb,OAAO,UAAUiB,EAAAhK,OAAA,iBAQvB,GAJH4J,EAAKM,wBAECJ,EAAsBF,EAAKjD,eAAe/B,QAAO,SAACuF,GACpD,OAAOP,EAAKzC,UAAUC,MAAK,SAAAiC,GAAQ,OAAIA,EAASe,WAAaD,EAAcE,uBAGvD5I,OAAS,IAAMmI,EAAKxB,KAAKsB,OAAM,CAAAM,EAAAxJ,KAAA,QACpB,OAA/BoJ,EAAKlD,oBAAqB,EAAKsD,EAAAhK,OAAA,wBAAAgK,EAAAxJ,KAAA,GAI7BoJ,EAAKb,OAAO,UAAU,KAAD,IAErBgB,EAAwBH,EAAKjD,eAAe/B,QAAO,SAAAuF,GACrD,OAAQL,EAAoB1C,MAAK,SAAAkD,GAAkB,OAAIA,EAAmBjC,KAAO8B,EAAc9B,UAGzE5G,OAAS,GAC/BmI,EAAK/B,yBAAyB0C,YAAYR,EAAsBS,UACnE,yBAAAR,EAAA/G,UAAA4G,MAzByBzF", "file": "static/js/11.js", "sourcesContent": ["import template from './sw-flow-detail.html.twig';\nimport type RepositoryType from 'src/core/data/repository.data';\nimport type CriteriaType from 'src/core/data/criteria.data';\nimport type {Entity} from '@shopware-ag/admin-extension-sdk/es/data/_internals/Entity';\nimport type EntityCollection from '@shopware-ag/admin-extension-sdk/es/data/_internals/EntityCollection';\n\nconst { Component, State } = Shopware;\nconst { mapState } = Component.getComponentHelper();\nconst { Criteria } = Shopware.Data;\n\n/**\n * @package services-settings\n */\nexport default Component.wrapComponentConfig({\n    template,\n\n    inject: ['repositoryFactory', 'feature', 'flowBuilderService'],\n\n    data(): {\n        isOpenWarningModal: boolean,\n        delayedActions: EntityCollection<'swag_delay_action'>,\n    } {\n        return {\n            isOpenWarningModal: false,\n            delayedActions: [],\n        }\n    },\n\n    computed: {\n        ...mapState('swFlowState', ['flow']),\n\n        delayConstant(): string {\n            return this.flowBuilderService.getActionName('DELAY');\n        },\n\n        hasDelayedActions(): EntityCollection<'flow_sequence'> {\n            return this.sequences.some(item => item.actionName === this.delayConstant);\n        },\n\n        flowCriteria(): CriteriaType {\n            const criteria = new Criteria();\n\n            criteria.addAssociation('sequences.rule');\n            criteria.getAssociation('sequences')\n                .addSorting(Criteria.sort('displayGroup', 'ASC'))\n                .addSorting(Criteria.sort('parentId', 'ASC'))\n                .addSorting(Criteria.sort('trueCase', 'ASC'))\n                .addSorting(Criteria.sort('position', 'ASC'));\n\n            return criteria;\n        },\n\n        delayedActionsRepository(): RepositoryType<'swag_delay_action'> {\n            return this.repositoryFactory.create('swag_delay_action');\n        },\n\n        delayedActionCriteria(): CriteriaType {\n            const criteria = new Criteria(this.page, this.limit);\n            criteria.addFilter(Criteria.equals('flowId', this.flow.id));\n\n            return criteria;\n        },\n    },\n\n    methods: {\n        getLicense(toggle: string): boolean {\n            return Shopware.License.get(toggle);\n        },\n\n        async getDetailFlow(): Promise<void> {\n            await this.$super('getDetailFlow');\n\n            if (!this.isNewFlow && !this.isTemplate) {\n                this.delayedActions = await this.delayedActionsRepository.search(this.delayedActionCriteria);\n            }\n        },\n\n        validateEmptySequence(): EntityCollection<'flow_sequence'> {\n            let invalidSequences = this.$super('validateEmptySequence');\n\n            this.sequences.forEach((sequence) => {\n                if(sequence.actionName === 'action.delay' && !sequence.config.delay) {\n                    invalidSequences.push(sequence.id);\n                }\n            });\n\n            State.commit('swFlowState/setInvalidSequences', invalidSequences);\n\n            return invalidSequences;\n        },\n\n        onCloseModal(): void {\n            this.isOpenWarningModal = false;\n            this.flow.active = true;\n        },\n\n        async onSave(): Promise<void> {\n            if ((typeof this.flow.isNew === 'function' && this.flow.isNew()) || this.isTemplate) {\n                this.$super('onSave');\n                return;\n            }\n\n            this.removeAllSelectors();\n\n            const validDelayedActions = this.delayedActions.filter((delayedAction: Entity<'swag_delay_action'>) => {\n                return this.sequences.some(sequence => sequence.parentId === delayedAction.delaySequenceId);\n            });\n\n            if (validDelayedActions.length > 0 && !this.flow.active) {\n                this.isOpenWarningModal = true;\n                return;\n            }\n\n            await this.$super('onSave');\n\n            const invalidDelayedActions = this.delayedActions.filter(delayedAction => {\n                return !validDelayedActions.some(validDelayedAction => validDelayedAction.id === delayedAction.id);\n            });\n\n            if (invalidDelayedActions.length > 0) {\n                this.delayedActionsRepository.syncDeleted(invalidDelayedActions.getIds());\n            }\n        }\n    }\n});\n", "export default \"{% block sw_flow_tabs_header_extension %}\\n    {% parent %}\\n    <sw-tabs-item\\n        v-if=\\\"!isNewFlow && hasDelayedActions && getLicense('FLOW_BUILDER-8478732')\\\"\\n        class=\\\"sw-flow-detail__tab-delay\\\"\\n        :route=\\\"routeDetailTab('delay')\\\"\\n    >\\n        {{ $tc('sw-flow-delay.tabDelay') }}\\n    </sw-tabs-item>\\n{% endblock %}\\n{% block sw_flow_detail_modal_extension %}\\n    <sw-modal\\n        v-if=\\\"isOpenWarningModal\\\"\\n        class=\\\"sw-flow-detail__modal\\\"\\n        :title=\\\"$tc('global.default.warning')\\\"\\n        @modal-close=\\\"onCloseModal\\\"\\n    >\\n        {% block sw_flow_detail_modal_extension_text_content %}\\n            <sw-alert variant=\\\"warning\\\">\\n                {{ $tc('sw-flow-delay.delay.list.inactivateWarningMessage') }}\\n            </sw-alert>\\n        {% endblock %}\\n\\n        <template #modal-footer>\\n            {% block sw_flow_detail_modal_extension_modal_buttons %}\\n                {% block sw_flow_detail_modal_extension_cancel_button%}\\n                    <sw-button size=\\\"small\\\" @click=\\\"onCloseModal\\\">\\n                        {{ $tc('global.default.cancel') }}\\n                    </sw-button>\\n                {% endblock %}\\n            {% endblock %}\\n        </template>\\n    </sw-modal>\\n{% endblock %}\\n\";"], "sourceRoot": ""}
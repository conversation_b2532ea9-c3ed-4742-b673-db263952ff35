/*! For license information please see 11.js.LICENSE.txt */
(this["webpackJsonpPlugindelayed-flow-action"]=this["webpackJsonpPlugindelayed-flow-action"]||[]).push([[11],{PaJh:function(t,e,n){"use strict";n.r(e);function r(t){return(r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function o(){o=function(){return t};var t={},e=Object.prototype,n=e.hasOwnProperty,i=Object.defineProperty||function(t,e,n){t[e]=n.value},a="function"==typeof Symbol?Symbol:{},c=a.iterator||"@@iterator",u=a.asyncIterator||"@@asyncIterator",l=a.toStringTag||"@@toStringTag";function s(t,e,n){return Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{s({},"")}catch(t){s=function(t,e,n){return t[e]=n}}function f(t,e,n,r){var o=e&&e.prototype instanceof p?e:p,a=Object.create(o.prototype),c=new j(r||[]);return i(a,"_invoke",{value:x(t,n,c)}),a}function d(t,e,n){try{return{type:"normal",arg:t.call(e,n)}}catch(t){return{type:"throw",arg:t}}}t.wrap=f;var h={};function p(){}function y(){}function v(){}var w={};s(w,c,(function(){return this}));var m=Object.getPrototypeOf,g=m&&m(m(k([])));g&&g!==e&&n.call(g,c)&&(w=g);var b=v.prototype=p.prototype=Object.create(w);function _(t){["next","throw","return"].forEach((function(e){s(t,e,(function(t){return this._invoke(e,t)}))}))}function S(t,e){function o(i,a,c,u){var l=d(t[i],t,a);if("throw"!==l.type){var s=l.arg,f=s.value;return f&&"object"==r(f)&&n.call(f,"__await")?e.resolve(f.__await).then((function(t){o("next",t,c,u)}),(function(t){o("throw",t,c,u)})):e.resolve(f).then((function(t){s.value=t,c(s)}),(function(t){return o("throw",t,c,u)}))}u(l.arg)}var a;i(this,"_invoke",{value:function(t,n){function r(){return new e((function(e,r){o(t,n,e,r)}))}return a=a?a.then(r,r):r()}})}function x(t,e,n){var r="suspendedStart";return function(o,i){if("executing"===r)throw new Error("Generator is already running");if("completed"===r){if("throw"===o)throw i;return P()}for(n.method=o,n.arg=i;;){var a=n.delegate;if(a){var c=O(a,n);if(c){if(c===h)continue;return c}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if("suspendedStart"===r)throw r="completed",n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);r="executing";var u=d(t,e,n);if("normal"===u.type){if(r=n.done?"completed":"suspendedYield",u.arg===h)continue;return{value:u.arg,done:n.done}}"throw"===u.type&&(r="completed",n.method="throw",n.arg=u.arg)}}}function O(t,e){var n=e.method,r=t.iterator[n];if(void 0===r)return e.delegate=null,"throw"===n&&t.iterator.return&&(e.method="return",e.arg=void 0,O(t,e),"throw"===e.method)||"return"!==n&&(e.method="throw",e.arg=new TypeError("The iterator does not provide a '"+n+"' method")),h;var o=d(r,t.iterator,e.arg);if("throw"===o.type)return e.method="throw",e.arg=o.arg,e.delegate=null,h;var i=o.arg;return i?i.done?(e[t.resultName]=i.value,e.next=t.nextLoc,"return"!==e.method&&(e.method="next",e.arg=void 0),e.delegate=null,h):i:(e.method="throw",e.arg=new TypeError("iterator result is not an object"),e.delegate=null,h)}function L(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function E(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function j(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(L,this),this.reset(!0)}function k(t){if(t){var e=t[c];if(e)return e.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var r=-1,o=function e(){for(;++r<t.length;)if(n.call(t,r))return e.value=t[r],e.done=!1,e;return e.value=void 0,e.done=!0,e};return o.next=o}}return{next:P}}function P(){return{value:void 0,done:!0}}return y.prototype=v,i(b,"constructor",{value:v,configurable:!0}),i(v,"constructor",{value:y,configurable:!0}),y.displayName=s(v,l,"GeneratorFunction"),t.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===y||"GeneratorFunction"===(e.displayName||e.name))},t.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,v):(t.__proto__=v,s(t,l,"GeneratorFunction")),t.prototype=Object.create(b),t},t.awrap=function(t){return{__await:t}},_(S.prototype),s(S.prototype,u,(function(){return this})),t.AsyncIterator=S,t.async=function(e,n,r,o,i){void 0===i&&(i=Promise);var a=new S(f(e,n,r,o),i);return t.isGeneratorFunction(n)?a:a.next().then((function(t){return t.done?t.value:a.next()}))},_(b),s(b,l,"Generator"),s(b,c,(function(){return this})),s(b,"toString",(function(){return"[object Generator]"})),t.keys=function(t){var e=Object(t),n=[];for(var r in e)n.push(r);return n.reverse(),function t(){for(;n.length;){var r=n.pop();if(r in e)return t.value=r,t.done=!1,t}return t.done=!0,t}},t.values=k,j.prototype={constructor:j,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,this.tryEntries.forEach(E),!t)for(var e in this)"t"===e.charAt(0)&&n.call(this,e)&&!isNaN(+e.slice(1))&&(this[e]=void 0)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var e=this;function r(n,r){return a.type="throw",a.arg=t,e.next=n,r&&(e.method="next",e.arg=void 0),!!r}for(var o=this.tryEntries.length-1;o>=0;--o){var i=this.tryEntries[o],a=i.completion;if("root"===i.tryLoc)return r("end");if(i.tryLoc<=this.prev){var c=n.call(i,"catchLoc"),u=n.call(i,"finallyLoc");if(c&&u){if(this.prev<i.catchLoc)return r(i.catchLoc,!0);if(this.prev<i.finallyLoc)return r(i.finallyLoc)}else if(c){if(this.prev<i.catchLoc)return r(i.catchLoc,!0)}else{if(!u)throw new Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return r(i.finallyLoc)}}}},abrupt:function(t,e){for(var r=this.tryEntries.length-1;r>=0;--r){var o=this.tryEntries[r];if(o.tryLoc<=this.prev&&n.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var i=o;break}}i&&("break"===t||"continue"===t)&&i.tryLoc<=e&&e<=i.finallyLoc&&(i=null);var a=i?i.completion:{};return a.type=t,a.arg=e,i?(this.method="next",this.next=i.finallyLoc,h):this.complete(a)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),h},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var n=this.tryEntries[e];if(n.finallyLoc===t)return this.complete(n.completion,n.afterLoc),E(n),h}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var n=this.tryEntries[e];if(n.tryLoc===t){var r=n.completion;if("throw"===r.type){var o=r.arg;E(n)}return o}}throw new Error("illegal catch attempt")},delegateYield:function(t,e,n){return this.delegate={iterator:k(t),resultName:e,nextLoc:n},"next"===this.method&&(this.arg=void 0),h}},t}function i(t,e,n,r,o,i,a){try{var c=t[i](a),u=c.value}catch(t){return void n(t)}c.done?e(u):Promise.resolve(u).then(r,o)}function a(t){return function(){var e=this,n=arguments;return new Promise((function(r,o){var a=t.apply(e,n);function c(t){i(a,r,o,c,u,"next",t)}function u(t){i(a,r,o,c,u,"throw",t)}c(void 0)}))}}function c(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,r)}return n}function u(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?c(Object(n),!0).forEach((function(e){l(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):c(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}function l(t,e,n){return(e=function(t){var e=function(t,e){if("object"!==r(t)||null===t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var o=n.call(t,e||"default");if("object"!==r(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"===r(e)?e:String(e)}(e))in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}var s=Shopware,f=s.Component,d=s.State,h=f.getComponentHelper().mapState,p=Shopware.Data.Criteria;e.default=f.wrapComponentConfig({template:'{% block sw_flow_tabs_header_extension %}\n    {% parent %}\n    <sw-tabs-item\n        v-if="!isNewFlow && hasDelayedActions && getLicense(\'FLOW_BUILDER-8478732\')"\n        class="sw-flow-detail__tab-delay"\n        :route="routeDetailTab(\'delay\')"\n    >\n        {{ $tc(\'sw-flow-delay.tabDelay\') }}\n    </sw-tabs-item>\n{% endblock %}\n{% block sw_flow_detail_modal_extension %}\n    <sw-modal\n        v-if="isOpenWarningModal"\n        class="sw-flow-detail__modal"\n        :title="$tc(\'global.default.warning\')"\n        @modal-close="onCloseModal"\n    >\n        {% block sw_flow_detail_modal_extension_text_content %}\n            <sw-alert variant="warning">\n                {{ $tc(\'sw-flow-delay.delay.list.inactivateWarningMessage\') }}\n            </sw-alert>\n        {% endblock %}\n\n        <template #modal-footer>\n            {% block sw_flow_detail_modal_extension_modal_buttons %}\n                {% block sw_flow_detail_modal_extension_cancel_button%}\n                    <sw-button size="small" @click="onCloseModal">\n                        {{ $tc(\'global.default.cancel\') }}\n                    </sw-button>\n                {% endblock %}\n            {% endblock %}\n        </template>\n    </sw-modal>\n{% endblock %}\n',inject:["repositoryFactory","feature","flowBuilderService"],data:function(){return{isOpenWarningModal:!1,delayedActions:[]}},computed:u(u({},h("swFlowState",["flow"])),{},{delayConstant:function(){return this.flowBuilderService.getActionName("DELAY")},hasDelayedActions:function(){var t=this;return this.sequences.some((function(e){return e.actionName===t.delayConstant}))},flowCriteria:function(){var t=new p;return t.addAssociation("sequences.rule"),t.getAssociation("sequences").addSorting(p.sort("displayGroup","ASC")).addSorting(p.sort("parentId","ASC")).addSorting(p.sort("trueCase","ASC")).addSorting(p.sort("position","ASC")),t},delayedActionsRepository:function(){return this.repositoryFactory.create("swag_delay_action")},delayedActionCriteria:function(){var t=new p(this.page,this.limit);return t.addFilter(p.equals("flowId",this.flow.id)),t}}),methods:{getLicense:function(t){return Shopware.License.get(t)},getDetailFlow:function(){var t=this;return a(o().mark((function e(){return o().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,t.$super("getDetailFlow");case 2:if(t.isNewFlow||t.isTemplate){e.next=6;break}return e.next=5,t.delayedActionsRepository.search(t.delayedActionCriteria);case 5:t.delayedActions=e.sent;case 6:case"end":return e.stop()}}),e)})))()},validateEmptySequence:function(){var t=this.$super("validateEmptySequence");return this.sequences.forEach((function(e){"action.delay"!==e.actionName||e.config.delay||t.push(e.id)})),d.commit("swFlowState/setInvalidSequences",t),t},onCloseModal:function(){this.isOpenWarningModal=!1,this.flow.active=!0},onSave:function(){var t=this;return a(o().mark((function e(){var n,r;return o().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!("function"==typeof t.flow.isNew&&t.flow.isNew()||t.isTemplate)){e.next=3;break}return t.$super("onSave"),e.abrupt("return");case 3:if(t.removeAllSelectors(),!((n=t.delayedActions.filter((function(e){return t.sequences.some((function(t){return t.parentId===e.delaySequenceId}))}))).length>0)||t.flow.active){e.next=8;break}return t.isOpenWarningModal=!0,e.abrupt("return");case 8:return e.next=10,t.$super("onSave");case 10:(r=t.delayedActions.filter((function(t){return!n.some((function(e){return e.id===t.id}))}))).length>0&&t.delayedActionsRepository.syncDeleted(r.getIds());case 12:case"end":return e.stop()}}),e)})))()}}})}}]);
//# sourceMappingURL=11.js.map
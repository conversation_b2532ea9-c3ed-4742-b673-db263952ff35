(this["webpackJsonpPlugindelayed-flow-action"]=this["webpackJsonpPlugindelayed-flow-action"]||[]).push([[2],{"C/2a":function(e,n,t){var a=t("uuo5");a.__esModule&&(a=a.default),"string"==typeof a&&(a=[[e.i,a,""]]),a.locals&&(e.exports=a.locals);(0,t("P8hj").default)("1eac22e0",a,!0,{})},EFTm:function(e,n,t){"use strict";t.d(n,"c",(function(){return a})),t.d(n,"b",(function(){return o})),t.d(n,"a",(function(){return i}));var a=function(e){return e.ACTION="action",e.CONDITION="condition",e.DELAY_ACTION="delay_action",e}({}),o=[{value:"hour",label:"sw-flow-delay.modal.labelHour"},{value:"day",label:"sw-flow-delay.modal.labelDay"},{value:"week",label:"sw-flow-delay.modal.labelWeek"},{value:"month",label:"sw-flow-delay.modal.labelMonth"},{value:"custom",label:"sw-flow-delay.modal.labelCustom"}],i="custom"},P8hj:function(e,n,t){"use strict";function a(e,n){for(var t=[],a={},o=0;o<n.length;o++){var i=n[o],l=i[0],s={id:e+":"+o,css:i[1],media:i[2],sourceMap:i[3]};a[l]?a[l].parts.push(s):t.push(a[l]={id:l,parts:[s]})}return t}t.r(n),t.d(n,"default",(function(){return w}));var o="undefined"!=typeof document;if("undefined"!=typeof DEBUG&&DEBUG&&!o)throw new Error("vue-style-loader cannot be used in a non-browser environment. Use { target: 'node' } in your Webpack config to indicate a server-rendering environment.");var i={},l=o&&(document.head||document.getElementsByTagName("head")[0]),s=null,c=0,r=!1,u=function(){},d=null,f="data-vue-ssr-id",y="undefined"!=typeof navigator&&/msie [6-9]\b/.test(navigator.userAgent.toLowerCase());function w(e,n,t,o){r=t,d=o||{};var l=a(e,n);return p(l),function(n){for(var t=[],o=0;o<l.length;o++){var s=l[o];(c=i[s.id]).refs--,t.push(c)}n?p(l=a(e,n)):l=[];for(o=0;o<t.length;o++){var c;if(0===(c=t[o]).refs){for(var r=0;r<c.parts.length;r++)c.parts[r]();delete i[c.id]}}}}function p(e){for(var n=0;n<e.length;n++){var t=e[n],a=i[t.id];if(a){a.refs++;for(var o=0;o<a.parts.length;o++)a.parts[o](t.parts[o]);for(;o<t.parts.length;o++)a.parts.push(m(t.parts[o]));a.parts.length>t.parts.length&&(a.parts.length=t.parts.length)}else{var l=[];for(o=0;o<t.parts.length;o++)l.push(m(t.parts[o]));i[t.id]={id:t.id,refs:1,parts:l}}}}function h(){var e=document.createElement("style");return e.type="text/css",l.appendChild(e),e}function m(e){var n,t,a=document.querySelector("style["+f+'~="'+e.id+'"]');if(a){if(r)return u;a.parentNode.removeChild(a)}if(y){var o=c++;a=s||(s=h()),n=b.bind(null,a,o,!1),t=b.bind(null,a,o,!0)}else a=h(),n=D.bind(null,a),t=function(){a.parentNode.removeChild(a)};return n(e),function(a){if(a){if(a.css===e.css&&a.media===e.media&&a.sourceMap===e.sourceMap)return;n(e=a)}else t()}}var v,g=(v=[],function(e,n){return v[e]=n,v.filter(Boolean).join("\n")});function b(e,n,t,a){var o=t?"":a.css;if(e.styleSheet)e.styleSheet.cssText=g(n,o);else{var i=document.createTextNode(o),l=e.childNodes;l[n]&&e.removeChild(l[n]),l.length?e.insertBefore(i,l[n]):e.appendChild(i)}}function D(e,n){var t=n.css,a=n.media,o=n.sourceMap;if(a&&e.setAttribute("media",a),d.ssrId&&e.setAttribute(f,n.id),o&&(t+="\n/*# sourceURL="+o.sources[0]+" */",t+="\n/*# sourceMappingURL=data:application/json;base64,"+btoa(unescape(encodeURIComponent(JSON.stringify(o))))+" */"),e.styleSheet)e.styleSheet.cssText=t;else{for(;e.firstChild;)e.removeChild(e.firstChild);e.appendChild(document.createTextNode(t))}}},SZPS:function(e,n,t){"use strict";t.r(n);t("C/2a");var a=t("EFTm");function o(e){return(o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function i(e,n){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);n&&(a=a.filter((function(n){return Object.getOwnPropertyDescriptor(e,n).enumerable}))),t.push.apply(t,a)}return t}function l(e){for(var n=1;n<arguments.length;n++){var t=null!=arguments[n]?arguments[n]:{};n%2?i(Object(t),!0).forEach((function(n){s(e,n,t[n])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):i(Object(t)).forEach((function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))}))}return e}function s(e,n,t){return(n=function(e){var n=function(e,n){if("object"!==o(e)||null===e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var a=t.call(e,n||"default");if("object"!==o(a))return a;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===n?String:Number)(e)}(e,"string");return"symbol"===o(n)?n:String(n)}(n))in e?Object.defineProperty(e,n,{value:t,enumerable:!0,configurable:!0,writable:!0}):e[n]=t,e}var c=Shopware,r=c.Component,u=c.State,d=Shopware.Utils,f=r.getComponentHelper(),y=f.mapGetters,w=f.mapState;n.default=r.wrapComponentConfig({inject:["flowBuilderService"],template:'{% block sw_flow_sequence_action %}\n    <div\n        class="sw-flow-delay-action__wrapper"\n        v-tooltip="{\n            message: $tc(\'sw-flow.actions.tooltipActionDisabled\'),\n            disabled: getLicense(\'FLOW_BUILDER-8478732\')\n        }"\n        :class="{\'sw-flow-delay-action__disabled\': !getLicense(\'FLOW_BUILDER-8478732\')}"\n    >\n        <div\n            class="sw-flow-sequence-action__card sw-flow-delay-action__delay_card"\n            :class="arrowClasses(sequence.config?.delay)"\n        >\n            <div class="sw-flow-sequence-action__header">\n                <h3 class="sw-flow-sequence-action__title">\n                    {{ $tc(\'sw-flow-delay.detail.sequence.delayActionTitle\') }}\n                </h3>\n\n                <sw-context-button class="sw-flow-sequence-action__context-button">\n                    <sw-context-menu-item\n                        v-if="sequence.config?.delay"\n                        class="sw-flow-delay-action__edit"\n                        @click="onEditDelay"\n                    >\n                        {{ $tc(\'sw-flow-delay.action.contextButton.editDelayAction\') }}\n                    </sw-context-menu-item>\n\n                    <sw-context-menu-item\n                        variant="danger"\n                        class="sw-flow-delay-action__delete"\n                        @click="onDeleteDelay"\n                    >\n                        {{ $tc(\'sw-flow-delay.action.contextButton.deleteDelayAction\') }}\n                    </sw-context-menu-item>\n                </sw-context-button>\n            </div>\n\n            <div class="sw-flow-sequence-action__content">\n                <div class="sw-flow-sequence-action__actions">\n                    <div\n                        v-if="!sequence.config?.delay"\n                        class="sw-flow-sequence-action__actions-empty"\n                    >\n                        <sw-icon\n                            size="16px"\n                            name="regular-hourglass"\n                        />\n                        <span class="sw-flow-sequence-action__no-action">\n                        {{ $tc(\'sw-flow-delay.detail.sequence.noDelayAction\') }}\n                    </span>\n                    </div>\n\n                    <div v-else class="sw-flow-sequence-action__action-list">\n                        <li class="sw-flow-sequence-action__action-item">\n                            <div class="sw-flow-delay-action__action-header">\n                                <div class="sw-flow-delay-action__action-name">\n                                    <sw-icon\n                                        size="16px"\n                                        name="regular-hourglass"\n                                    />\n\n                                    <h3 v-if="showCustomDescription">{{ customTimeDescription }}</h3>\n                                    <h3 v-else>{{ timeDescription }}</h3>\n                                </div>\n                            </div>\n                        </li>\n                    </div>\n                </div>\n\n                <div\n                    v-if="showDelayElement && !sequence.config?.delay"\n                    class="sw-flow-sequence-action__add-action"\n                >\n                    <div class="sw-flow-sequence-action__select">\n                        <sw-single-select\n                            class="sw-flow-sequence-action__selection-action"\n                            size="small"\n                            value=""\n                            :placeholder="$tc(\'sw-flow.actions.placeholderSelectAction\')"\n                            :options="actionDelayOptions"\n                            :popover-classes="[\'sw-flow-delay-action__popover\']"\n                            :error="fieldError"\n                            {% if VUE3 %}\n                            @update:value="onSelectDelay"\n                            {% else %}\n                            @change="onSelectDelay"\n                            {% endif %}\n                        >\n                        </sw-single-select>\n                    </div>\n                </div>\n            </div>\n        </div>\n\n        <div\n            v-if="sequence.config?.delay"\n            class="sw-flow-delay-action__then-arrow"\n        >\n            <div class="sw-flow-delay-action__then-line"></div>\n\n            <div class="sw-flow-delay-action__then-oval"></div>\n\n            <sw-icon\n                name="regular-chevron-right-s"\n                small\n            />\n            <sw-label\n                appearance="pill"\n                size="medium"\n                class="sw-flow-delay-action__true-label"\n            >\n                {{ $tc(\'sw-flow-delay.detail.sequence.labelThen\') }}\n            </sw-label>\n        </div>\n\n        <div\n            v-if="showDelayElement && !sequence.config?.delay"\n            class="sw-flow-delay-action__help-text"\n        >\n            <h3>{{ $tc(\'sw-flow-delay.detail.sequence.delayActionExplainsTitle\') }}</h3>\n\n            <p v-html="$tc(\'sw-flow-delay.detail.sequence.delayActionExplainsDescription\')"></p>\n        </div>\n\n        <sw-flow-delay-modal\n            v-if="showDelayModal"\n            :sequence="sequence"\n            :type="delayType"\n            :is-update-delay="isUpdateDelay"\n            @type-change="onChangeType"\n            @modal-save="onSaveDelay"\n            @modal-close="onCloseDelayModal"\n        />\n    </div>\n{% endblock %}\n',data:function(){return{showDelayModal:!1,isUpdateDelay:!1,showWarningDeleteDelay:!1,delayType:"hour"}},watch:{sequence:{handler:function(e){var n;if(e.actionName===this.delayConstant&&!e.ruleId){var t=this.sequences.find((function(n){return n.parentId===e.id}));null!=t&&t.id||null===(n=e.config)||void 0===n||!n.delay||e.trueBlock||this.createSequence({actionName:null,trueCase:!0})}},immediate:!0},showWarningModal:function(e){e.name===a.c.DELAY_ACTION&&"EDIT"===e.actionType&&e.id===this.sequence.id&&(this.showDelayModal=!0,u.commit("swFlowDelay/setShowWarningModal",{type:"",name:"",enabled:!1,id:""})),e.name===a.c.DELAY_ACTION&&"DELETE"===e.actionType&&e.id===this.sequence.id&&this.onConfirmDeleteDelay()}},computed:l(l(l({},y("swFlowState",["sequences"])),w("swFlowDelay",["showWarningModal"])),{},{delayConstant:function(){return this.flowBuilderService.getActionName("DELAY")},actionDelayOptions:function(){var e=this;return a.b.map((function(n){return l(l({},n),{},{label:e.$tc(n.label)})}))},showDelayElement:function(){return this.sequence.actionName===this.delayConstant},showCustomDescription:function(){var e;return(null===(e=this.sequence.config.delay)||void 0===e?void 0:e.length)>1},customTimeDescription:function(){var e=this.sequence.config.delay;return[this.convertTimeString(e[0].type,e[0].value),this.convertTimeString(e[1].type,e[1].value),this.convertTimeString(e[2].type,e[2].value),this.convertTimeString(e[3].type,e[3].value)].filter((function(e){return e})).join()},timeDescription:function(){if(this.sequence.actionName!==this.delayConstant)return null;var e=this.delayConfig,n=e.type,t=e.value;return this.convertTimeString(n,t)},delayConfig:function(){var e=this.sequence.config;return e.delay&&Object.values(e.delay).length?1===e.delay.length?{type:e.delay[0].type,value:e.delay[0].value}:{type:a.a,value:null}:{type:null,value:null}}}),methods:{getLicense:function(e){return Shopware.License.get(e)},convertTimeString:function(e,n){if(!n)return"";var t=this.getTimeLabel(e,n);return" ".concat(n," ").concat(t)},getTimeLabel:function(e,n){switch(e){case"hour":return this.$tc("sw-flow-delay.modal.labelHour",n);case"day":return this.$tc("sw-flow-delay.modal.labelDay",n);case"week":return this.$tc("sw-flow-delay.modal.labelWeek",n);case"month":return this.$tc("sw-flow-delay.modal.labelMonth",n);default:return""}},onEditDelay:function(){if("true"===localStorage.getItem("delay_action"))return this.delayType=this.delayConfig.type||a.b[0].value,this.isUpdateDelay=!0,void(this.showDelayModal=!0);u.commit("swFlowDelay/setShowWarningModal",{type:a.c.DELAY_ACTION,actionType:"EDIT",name:"",enabled:!0,id:this.sequence.id}),this.delayType=this.delayConfig.type||a.b[0].value,this.isUpdateDelay=!0},onDeleteDelay:function(){"true"!==localStorage.getItem("delay_deleted")?u.commit("swFlowDelay/setShowWarningModal",{type:a.c.DELAY_ACTION,actionType:"DELETE",name:"",enabled:!0,id:this.sequence.id}):this.onConfirmDeleteDelay()},onConfirmDeleteDelay:function(){var e=this;this.sequences.filter((function(n){return n.parentId===e.sequence.id})).forEach((function(n){u.commit("swFlowState/updateSequence",{id:n.id,parentId:e.sequence.parentId,trueCase:e.sequence.trueCase})})),u.commit("swFlowState/removeSequences",[this.sequence.id])},onSelectDelay:function(e){e&&(this.showDelayModal=!0,this.delayType=e)},onChangeType:function(e){this.delayType=e},onCloseDelayModal:function(){this.showDelayModal=!1},onSaveDelay:function(e){u.commit("swFlowState/updateSequence",e),this.showDelayModal=!1,this.isUpdateDelay||this.createSequence({actionName:null,trueCase:!0})},arrowClasses:function(e){return{"has--then-selector":e}},createSequence:function(e){var n=this.sequenceRepository.create(),t=l(l({},n),{},{parentId:this.sequence.id,displayGroup:this.sequence.displayGroup,actionName:e.actionName,ruleId:null,config:{},position:1,trueCase:e.trueCase,id:d.createId()});n=Object.assign(n,t),u.commit("swFlowState/addSequence",n)}}})},uuo5:function(e,n,t){}}]);
//# sourceMappingURL=2.js.map
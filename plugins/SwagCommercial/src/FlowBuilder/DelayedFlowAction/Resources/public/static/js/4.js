(this["webpackJsonpPlugindelayed-flow-action"]=this["webpackJsonpPlugindelayed-flow-action"]||[]).push([[4],{"3UEb":function(e,n,t){"use strict";t.r(n);t("9d6e");var a=Shopware.Component;n.default=a.wrapComponentConfig({template:'<div class="sw-flow-sequence-label" :class="classes">\n    <span>\n        <a @click="$emit(\'click\', sequence)" class="sw-flow-sequence-label__name">\n            <span v-if="sequence.actionName" class="sw-flow-sequence-label__action-name">\n                {# This is for app icon#}\n                <img\n                    v-if="convertSequence(sequence).iconRaw"\n                    class="sw-flow-sequence-label__icon-raw"\n                    alt=""\n                    :src="`data:image/png;base64, ${convertSequence(sequence).iconRaw}`"\n                />\n\n                <sw-icon\n                    v-else\n                    :name="`${convertSequence(sequence).icon}`"\n                    size="14px"\n                    class="sw-flow-sequence-label__action-icon"\n                />\n            </span>\n\n            <sw-icon\n                v-else\n                :name="`${convertSequence(sequence).icon}`"\n                size="14px"\n                class="sw-flow-sequence-label__condition-icon"\n            />\n\n            <span>{{ convertSequence(sequence).label }}</span>\n        </a>\n    </span>\n</div>\n',inject:["flowBuilderService"],props:{sequence:{type:Object,default:{}},appFlowActions:{type:Array,default:[]},classes:{type:String,default:""}},methods:{convertSequence:function(e){var n,t,a,s;if(null!==(n=e.rule)&&void 0!==n&&n.name)return{label:null===(s=e.rule)||void 0===s?void 0:s.name,icon:"regular-rule-s"};var i,o=Object.values(this.appFlowActions).find((function(n){return n.name===e.actionName}));return o?{label:(null===(i=o.translated)||void 0===i?void 0:i.label)||o.label,icon:o.iconRaw||o.swIcon,iconRaw:o.icon}:{label:"".concat(this.$tc(null===(t=this.flowBuilderService.getActionTitle(e.actionName))||void 0===t?void 0:t.label)),icon:null===(a=this.flowBuilderService.getActionTitle(e.actionName))||void 0===a?void 0:a.icon}}}})},"9d6e":function(e,n,t){var a=t("g0IH");a.__esModule&&(a=a.default),"string"==typeof a&&(a=[[e.i,a,""]]),a.locals&&(e.exports=a.locals);(0,t("P8hj").default)("518155f8",a,!0,{})},P8hj:function(e,n,t){"use strict";function a(e,n){for(var t=[],a={},s=0;s<n.length;s++){var i=n[s],o=i[0],r={id:e+":"+s,css:i[1],media:i[2],sourceMap:i[3]};a[o]?a[o].parts.push(r):t.push(a[o]={id:o,parts:[r]})}return t}t.r(n),t.d(n,"default",(function(){return v}));var s="undefined"!=typeof document;if("undefined"!=typeof DEBUG&&DEBUG&&!s)throw new Error("vue-style-loader cannot be used in a non-browser environment. Use { target: 'node' } in your Webpack config to indicate a server-rendering environment.");var i={},o=s&&(document.head||document.getElementsByTagName("head")[0]),r=null,l=0,c=!1,u=function(){},d=null,f="data-vue-ssr-id",p="undefined"!=typeof navigator&&/msie [6-9]\b/.test(navigator.userAgent.toLowerCase());function v(e,n,t,s){c=t,d=s||{};var o=a(e,n);return h(o),function(n){for(var t=[],s=0;s<o.length;s++){var r=o[s];(l=i[r.id]).refs--,t.push(l)}n?h(o=a(e,n)):o=[];for(s=0;s<t.length;s++){var l;if(0===(l=t[s]).refs){for(var c=0;c<l.parts.length;c++)l.parts[c]();delete i[l.id]}}}}function h(e){for(var n=0;n<e.length;n++){var t=e[n],a=i[t.id];if(a){a.refs++;for(var s=0;s<a.parts.length;s++)a.parts[s](t.parts[s]);for(;s<t.parts.length;s++)a.parts.push(g(t.parts[s]));a.parts.length>t.parts.length&&(a.parts.length=t.parts.length)}else{var o=[];for(s=0;s<t.parts.length;s++)o.push(g(t.parts[s]));i[t.id]={id:t.id,refs:1,parts:o}}}}function m(){var e=document.createElement("style");return e.type="text/css",o.appendChild(e),e}function g(e){var n,t,a=document.querySelector("style["+f+'~="'+e.id+'"]');if(a){if(c)return u;a.parentNode.removeChild(a)}if(p){var s=l++;a=r||(r=m()),n=y.bind(null,a,s,!1),t=y.bind(null,a,s,!0)}else a=m(),n=q.bind(null,a),t=function(){a.parentNode.removeChild(a)};return n(e),function(a){if(a){if(a.css===e.css&&a.media===e.media&&a.sourceMap===e.sourceMap)return;n(e=a)}else t()}}var w,b=(w=[],function(e,n){return w[e]=n,w.filter(Boolean).join("\n")});function y(e,n,t,a){var s=t?"":a.css;if(e.styleSheet)e.styleSheet.cssText=b(n,s);else{var i=document.createTextNode(s),o=e.childNodes;o[n]&&e.removeChild(o[n]),o.length?e.insertBefore(i,o[n]):e.appendChild(i)}}function q(e,n){var t=n.css,a=n.media,s=n.sourceMap;if(a&&e.setAttribute("media",a),d.ssrId&&e.setAttribute(f,n.id),s&&(t+="\n/*# sourceURL="+s.sources[0]+" */",t+="\n/*# sourceMappingURL=data:application/json;base64,"+btoa(unescape(encodeURIComponent(JSON.stringify(s))))+" */"),e.styleSheet)e.styleSheet.cssText=t;else{for(;e.firstChild;)e.removeChild(e.firstChild);e.appendChild(document.createTextNode(t))}}},g0IH:function(e,n,t){}}]);
//# sourceMappingURL=4.js.map
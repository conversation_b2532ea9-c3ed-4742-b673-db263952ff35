{"version": 3, "sources": ["webpack:////tmp/extension2400992207/SwagCommercial/src/FlowBuilder/FlowSharing/Resources/app/administration/src/module/sw-flow/component/modals/sw-flow-download-modal/sw-flow-download-modal.scss", "webpack:////tmp/extension2400992207/SwagCommercial/src/FlowBuilder/FlowSharing/Resources/app/administration/src/module/sw-flow/component/modals/sw-flow-download-modal/index.ts", "webpack:////tmp/extension2400992207/SwagCommercial/src/FlowBuilder/FlowSharing/Resources/app/administration/src/module/sw-flow/component/modals/sw-flow-download-modal/sw-flow-download-modal.html.twig", "webpack:///./node_modules/vue-style-loader/lib/listToStyles.js", "webpack:///./node_modules/vue-style-loader/lib/addStylesClient.js"], "names": ["content", "__esModule", "default", "module", "i", "locals", "exports", "add", "State", "Shopware", "template", "inject", "props", "flowId", "type", "String", "required", "data", "flowData", "rules", "mailTemplates", "notSelectedRuleIds", "notSelectedMailIds", "<PERSON><PERSON><PERSON><PERSON>", "computed", "downloadData", "_this$flowData$dataIn", "this", "dataIncluded", "rule", "mail_template", "length", "for<PERSON>ach", "ruleId", "concat", "mailId", "Object", "assign", "keys", "created", "createdComponent", "methods", "getDataIncluded", "onCancel", "$emit", "onDownload", "filename", "flow", "name", "link", "document", "createElement", "href", "encodeURIComponent", "JSON", "stringify", "download", "dispatchEvent", "MouseEvent", "remove", "_this", "flowSharingService", "downloadFlow", "then", "values", "item", "currentLocaleEmail", "find", "email", "locale", "get", "currentLocale", "push", "catch", "handleSelectRule", "checked", "filter", "id", "_toConsumableArray", "handleSelectMail", "listToStyles", "parentId", "list", "styles", "newStyles", "part", "css", "media", "sourceMap", "parts", "hasDocument", "DEBUG", "Error", "stylesInDom", "head", "getElementsByTagName", "singletonElement", "singletonCounter", "isProduction", "noop", "options", "ssrIdKey", "isOldIE", "navigator", "test", "userAgent", "toLowerCase", "addStylesClient", "_isProduction", "_options", "addStylesToDom", "newList", "<PERSON><PERSON><PERSON><PERSON>", "domStyle", "refs", "j", "addStyle", "createStyleElement", "styleElement", "append<PERSON><PERSON><PERSON>", "obj", "update", "querySelector", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "styleIndex", "applyToSingletonTag", "bind", "applyToTag", "newObj", "textStore", "replaceText", "index", "replacement", "Boolean", "join", "styleSheet", "cssText", "cssNode", "createTextNode", "childNodes", "insertBefore", "setAttribute", "ssrId", "sources", "btoa", "unescape", "<PERSON><PERSON><PERSON><PERSON>"], "mappings": "+IAGA,IAAIA,EAAU,EAAQ,QACnBA,EAAQC,aAAYD,EAAUA,EAAQE,SACnB,iBAAZF,IAAsBA,EAAU,CAAC,CAACG,EAAOC,EAAIJ,EAAS,MAC7DA,EAAQK,SAAQF,EAAOG,QAAUN,EAAQK,SAG/BE,EADH,EAAQ,QAA2LL,SAC5L,WAAYF,GAAS,EAAM,K,gzBCD5C,IAAQQ,EAAUC,SAAVD,MAKO,WACXE,SCdW,ooEDgBXC,OAAQ,CACJ,MACA,oBACA,sBAGJC,MAAO,CACHC,OAAQ,CACJC,KAAMC,OACNC,UAAU,IAIlBC,KAAI,WAQA,MAAO,CACHC,SAAU,GACVC,MAAO,GACPC,cAAe,GACfC,mBAAoB,GACpBC,mBAAoB,GACpBC,UAAU,IAIlBC,SAAU,CACNC,aAAY,WAER,IAAAC,EAAgCC,KAAKT,SAASU,aAAtCC,EAAIH,EAAJG,KAAMC,EAAaJ,EAAbI,cAEVD,GAAQF,KAAKN,mBAAmBU,OAAS,GACzCJ,KAAKN,mBAAmBW,SAAQ,SAAAC,UACrBJ,EAAK,GAADK,OAAID,OAKnBH,GAAiBH,KAAKL,mBAAmBS,OAAS,GAClDJ,KAAKL,mBAAmBU,SAAQ,SAAAG,UACrBL,EAAc,GAADI,OAAIC,OAIhC,IAAMV,EAAeW,OAAOC,OAAO,GAAIV,KAAKT,UAgB5C,OAdIW,GAAQO,OAAOE,KAAKT,GAAME,OAAS,EACnCN,EAAaG,aAAaC,KAAOA,SAE1BJ,EAAaG,aAAaC,KAIjCC,GAAiBM,OAAOE,KAAKR,GAAeC,OAAS,EAErDN,EAAaG,aAAaE,cAAgBA,SAEnCL,EAAaG,aAAaE,cAG9BL,IAIfc,QAAO,WACHZ,KAAKa,oBAGTC,QAAS,CACLD,iBAAgB,WACZb,KAAKe,mBAGTC,SAAQ,WACJhB,KAAKiB,MAAM,gBAGfC,WAAU,WACN,GAAIlB,KAAKJ,UAAYI,KAAKT,SAASa,QAAU,EACzCJ,KAAKiB,MAAM,mBAAmB,OADlC,CAMA,IAAME,EAAQ,GAAAZ,OAAMP,KAAKT,SAAS6B,KAAKC,KAAI,SACrCC,EAAOC,SAASC,cAAc,KAEpCF,EAAKG,KAAI,uCAAAlB,OAA0CmB,mBAAmBC,KAAKC,UAAU5B,KAAKF,gBAC1FwB,EAAKO,SAAWV,EAChBG,EAAKQ,cAAc,IAAIC,WAAW,UAClCT,EAAKU,SAELhC,KAAKiB,MAAM,mBAAmB,KAGlCF,gBAAe,WAAU,IAADkB,EAAA,KACpBjC,KAAKkC,mBAAmBC,aAAanC,KAAKd,QAAQkD,MAAK,SAAC9C,IACpD2C,EAAK1C,SAAWD,EAEZA,EAAKW,cAAgBX,EAAKW,aAAaC,OACvC+B,EAAKzC,MAAQiB,OAAO4B,OAAO/C,EAAKW,aAAaC,OAG7CZ,EAAKW,cAAgBX,EAAKW,aAAaE,gBACVM,OAAO4B,OAAO/C,EAAKW,aAAaE,eAExCE,SAAQ,SAAAiC,GACzB,IAAMC,EAAqBD,EAAKE,MAAK,SAAAC,GAAK,OAAIA,EAAMC,SAAW7D,EAAM8D,IAAI,WAAWC,iBAChFL,GACAN,EAAKxC,cAAcoD,KAAKN,SAIrCO,OAAM,WACLb,EAAKrC,UAAW,MAIxBmD,iBAAgB,SAACT,EAAYU,GACzBhD,KAAKN,mBAAqBsD,EACpBhD,KAAKN,mBAAmBuD,QAAO,SAAA3C,GAAM,OAAIA,IAAWgC,EAAKY,MAAG,GAAA3C,OAAA4C,EACxDnD,KAAKN,oBAAkB,CAAE4C,EAAKY,MAG5CE,iBAAgB,SAACd,EAAoBU,GACjChD,KAAKL,mBAAqBqD,EACpBhD,KAAKL,mBAAmBsD,QAAO,SAAAzC,GAAM,OAAIA,IAAW8B,EAAKY,MAAG,GAAA3C,OAAA4C,EACxDnD,KAAKL,oBAAkB,CAAE2C,EAAKY,S,kCEhJrC,SAASG,EAAcC,EAAUC,GAG9C,IAFA,IAAIC,EAAS,GACTC,EAAY,GACPhF,EAAI,EAAGA,EAAI8E,EAAKnD,OAAQ3B,IAAK,CACpC,IAAI6D,EAAOiB,EAAK9E,GACZyE,EAAKZ,EAAK,GAIVoB,EAAO,CACTR,GAAII,EAAW,IAAM7E,EACrBkF,IALQrB,EAAK,GAMbsB,MALUtB,EAAK,GAMfuB,UALcvB,EAAK,IAOhBmB,EAAUP,GAGbO,EAAUP,GAAIY,MAAMjB,KAAKa,GAFzBF,EAAOX,KAAKY,EAAUP,GAAM,CAAEA,GAAIA,EAAIY,MAAO,CAACJ,KAKlD,OAAOF,E,+CCjBT,IAAIO,EAAkC,oBAAbxC,SAEzB,GAAqB,oBAAVyC,OAAyBA,QAC7BD,EACH,MAAM,IAAIE,MACV,2JAkBJ,IAAIC,EAAc,GAQdC,EAAOJ,IAAgBxC,SAAS4C,MAAQ5C,SAAS6C,qBAAqB,QAAQ,IAC9EC,EAAmB,KACnBC,EAAmB,EACnBC,GAAe,EACfC,EAAO,aACPC,EAAU,KACVC,EAAW,kBAIXC,EAA+B,oBAAdC,WAA6B,eAAeC,KAAKD,UAAUE,UAAUC,eAE3E,SAASC,EAAiB1B,EAAUC,EAAM0B,EAAeC,GACtEX,EAAeU,EAEfR,EAAUS,GAAY,GAEtB,IAAI1B,EAASH,EAAaC,EAAUC,GAGpC,OAFA4B,EAAe3B,GAER,SAAiB4B,GAEtB,IADA,IAAIC,EAAY,GACP5G,EAAI,EAAGA,EAAI+E,EAAOpD,OAAQ3B,IAAK,CACtC,IAAI6D,EAAOkB,EAAO/E,IACd6G,EAAWpB,EAAY5B,EAAKY,KACvBqC,OACTF,EAAUxC,KAAKyC,GAEbF,EAEFD,EADA3B,EAASH,EAAaC,EAAU8B,IAGhC5B,EAAS,GAEX,IAAS/E,EAAI,EAAGA,EAAI4G,EAAUjF,OAAQ3B,IAAK,CACzC,IAAI6G,EACJ,GAAsB,KADlBA,EAAWD,EAAU5G,IACZ8G,KAAY,CACvB,IAAK,IAAIC,EAAI,EAAGA,EAAIF,EAASxB,MAAM1D,OAAQoF,IACzCF,EAASxB,MAAM0B,YAEVtB,EAAYoB,EAASpC,OAMpC,SAASiC,EAAgB3B,GACvB,IAAK,IAAI/E,EAAI,EAAGA,EAAI+E,EAAOpD,OAAQ3B,IAAK,CACtC,IAAI6D,EAAOkB,EAAO/E,GACd6G,EAAWpB,EAAY5B,EAAKY,IAChC,GAAIoC,EAAU,CACZA,EAASC,OACT,IAAK,IAAIC,EAAI,EAAGA,EAAIF,EAASxB,MAAM1D,OAAQoF,IACzCF,EAASxB,MAAM0B,GAAGlD,EAAKwB,MAAM0B,IAE/B,KAAOA,EAAIlD,EAAKwB,MAAM1D,OAAQoF,IAC5BF,EAASxB,MAAMjB,KAAK4C,EAASnD,EAAKwB,MAAM0B,KAEtCF,EAASxB,MAAM1D,OAASkC,EAAKwB,MAAM1D,SACrCkF,EAASxB,MAAM1D,OAASkC,EAAKwB,MAAM1D,YAEhC,CACL,IAAI0D,EAAQ,GACZ,IAAS0B,EAAI,EAAGA,EAAIlD,EAAKwB,MAAM1D,OAAQoF,IACrC1B,EAAMjB,KAAK4C,EAASnD,EAAKwB,MAAM0B,KAEjCtB,EAAY5B,EAAKY,IAAM,CAAEA,GAAIZ,EAAKY,GAAIqC,KAAM,EAAGzB,MAAOA,KAK5D,SAAS4B,IACP,IAAIC,EAAepE,SAASC,cAAc,SAG1C,OAFAmE,EAAaxG,KAAO,WACpBgF,EAAKyB,YAAYD,GACVA,EAGT,SAASF,EAAUI,GACjB,IAAIC,EAAQ9D,EACR2D,EAAepE,SAASwE,cAAc,SAAWrB,EAAW,MAAQmB,EAAI3C,GAAK,MAEjF,GAAIyC,EAAc,CAChB,GAAIpB,EAGF,OAAOC,EAOPmB,EAAaK,WAAWC,YAAYN,GAIxC,GAAIhB,EAAS,CAEX,IAAIuB,EAAa5B,IACjBqB,EAAetB,IAAqBA,EAAmBqB,KACvDI,EAASK,EAAoBC,KAAK,KAAMT,EAAcO,GAAY,GAClElE,EAASmE,EAAoBC,KAAK,KAAMT,EAAcO,GAAY,QAGlEP,EAAeD,IACfI,EAASO,EAAWD,KAAK,KAAMT,GAC/B3D,EAAS,WACP2D,EAAaK,WAAWC,YAAYN,IAMxC,OAFAG,EAAOD,GAEA,SAAsBS,GAC3B,GAAIA,EAAQ,CACV,GAAIA,EAAO3C,MAAQkC,EAAIlC,KACnB2C,EAAO1C,QAAUiC,EAAIjC,OACrB0C,EAAOzC,YAAcgC,EAAIhC,UAC3B,OAEFiC,EAAOD,EAAMS,QAEbtE,KAKN,IACMuE,EADFC,GACED,EAAY,GAET,SAAUE,EAAOC,GAEtB,OADAH,EAAUE,GAASC,EACZH,EAAUtD,OAAO0D,SAASC,KAAK,QAI1C,SAAST,EAAqBR,EAAcc,EAAOzE,EAAQ6D,GACzD,IAAIlC,EAAM3B,EAAS,GAAK6D,EAAIlC,IAE5B,GAAIgC,EAAakB,WACflB,EAAakB,WAAWC,QAAUN,EAAYC,EAAO9C,OAChD,CACL,IAAIoD,EAAUxF,SAASyF,eAAerD,GAClCsD,EAAatB,EAAasB,WAC1BA,EAAWR,IAAQd,EAAaM,YAAYgB,EAAWR,IACvDQ,EAAW7G,OACbuF,EAAauB,aAAaH,EAASE,EAAWR,IAE9Cd,EAAaC,YAAYmB,IAK/B,SAASV,EAAYV,EAAcE,GACjC,IAAIlC,EAAMkC,EAAIlC,IACVC,EAAQiC,EAAIjC,MACZC,EAAYgC,EAAIhC,UAiBpB,GAfID,GACF+B,EAAawB,aAAa,QAASvD,GAEjCa,EAAQ2C,OACVzB,EAAawB,aAAazC,EAAUmB,EAAI3C,IAGtCW,IAGFF,GAAO,mBAAqBE,EAAUwD,QAAQ,GAAK,MAEnD1D,GAAO,uDAAyD2D,KAAKC,SAAS7F,mBAAmBC,KAAKC,UAAUiC,MAAgB,OAG9H8B,EAAakB,WACflB,EAAakB,WAAWC,QAAUnD,MAC7B,CACL,KAAOgC,EAAa6B,YAClB7B,EAAaM,YAAYN,EAAa6B,YAExC7B,EAAaC,YAAYrE,SAASyF,eAAerD", "file": "static/js/0.js", "sourcesContent": ["// style-loader: Adds some css to the DOM by adding a <style> tag\n\n// load the styles\nvar content = require(\"!!../../../../../../../../../../../../../../../builds/shopware/6/product/commercial/src/Administration/Resources/app/administration/node_modules/mini-css-extract-plugin/dist/loader.js??ref--15-1!../../../../../../../../../../../../../../../builds/shopware/6/product/commercial/src/Administration/Resources/app/administration/node_modules/css-loader/dist/cjs.js??ref--15-2!../../../../../../../../../../../../../../../builds/shopware/6/product/commercial/src/Administration/Resources/app/administration/node_modules/sass-loader/dist/cjs.js??ref--15-3!./sw-flow-download-modal.scss\");\nif(content.__esModule) content = content.default;\nif(typeof content === 'string') content = [[module.id, content, '']];\nif(content.locals) module.exports = content.locals;\n// add the styles to the DOM\nvar add = require(\"!../../../../../../../../../../../../../../../builds/shopware/6/product/commercial/src/Administration/Resources/app/administration/node_modules/vue-style-loader/lib/addStylesClient.js\").default\nvar update = add(\"49ce0bde\", content, true, {});", "import template from './sw-flow-download-modal.html.twig';\nimport './sw-flow-download-modal.scss';\nimport type {\n    Rule,\n    MailTemplate,\n    FlowData,\n} from '../../../flow.types';\n\nconst { State } = Shopware;\n\n/**\n * @package services-settings\n */\nexport default {\n    template,\n\n    inject: [\n        'acl',\n        'repositoryFactory',\n        'flowSharingService'\n    ],\n\n    props: {\n        flowId: {\n            type: String,\n            required: true,\n        },\n    },\n\n    data(): {\n        flowData: FlowData,\n        rules: Array<Rule>,\n        mailTemplates: Array<MailTemplate>,\n        notSelectedRuleIds: Array<string>,\n        notSelectedMailIds: Array<string>,\n        hasError: boolean,\n    } {\n        return {\n            flowData: {} as FlowData,\n            rules: [],\n            mailTemplates: [],\n            notSelectedRuleIds: [],\n            notSelectedMailIds: [],\n            hasError: false,\n        };\n    },\n\n    computed: {\n        downloadData(): FlowData {\n            // eslint-disable-next-line camelcase\n            const { rule, mail_template } = this.flowData.dataIncluded;\n\n            if (rule && this.notSelectedRuleIds.length > 0) {\n                this.notSelectedRuleIds.forEach(ruleId => {\n                    delete rule[`${ruleId}`];\n                });\n            }\n\n            // eslint-disable-next-line camelcase\n            if (mail_template && this.notSelectedMailIds.length > 0) {\n                this.notSelectedMailIds.forEach(mailId => {\n                    delete mail_template[`${mailId}`];\n                });\n            }\n\n            const downloadData = Object.assign({}, this.flowData);\n\n            if (rule && Object.keys(rule).length > 0) {\n                downloadData.dataIncluded.rule = rule;\n            } else {\n                delete downloadData.dataIncluded.rule;\n            }\n\n            // eslint-disable-next-line camelcase\n            if (mail_template && Object.keys(mail_template).length > 0) {\n                // eslint-disable-next-line camelcase\n                downloadData.dataIncluded.mail_template = mail_template;\n            } else {\n                delete downloadData.dataIncluded.mail_template;\n            }\n\n            return downloadData;\n        },\n    },\n\n    created(): void {\n        this.createdComponent();\n    },\n\n    methods: {\n        createdComponent(): void {\n            this.getDataIncluded();\n        },\n\n        onCancel(): void {\n            this.$emit('modal-close');\n        },\n\n        onDownload(): void {\n            if (this.hasError || this.flowData.length <= 0) {\n                this.$emit('download-finish', false);\n\n                return;\n            }\n\n            const filename = `${this.flowData.flow.name}.json`;\n            const link = document.createElement('a');\n\n            link.href = `data:application/json;charset=utf-8,${encodeURIComponent(JSON.stringify(this.downloadData))}`;\n            link.download = filename;\n            link.dispatchEvent(new MouseEvent('click'));\n            link.remove();\n\n            this.$emit('download-finish', true);\n        },\n\n        getDataIncluded(): void {\n            this.flowSharingService.downloadFlow(this.flowId).then((data) => {\n                this.flowData = data;\n\n                if (data.dataIncluded && data.dataIncluded.rule) {\n                    this.rules = Object.values(data.dataIncluded.rule);\n                }\n\n                if (data.dataIncluded && data.dataIncluded.mail_template) {\n                    const mailTemplateIncluded = Object.values(data.dataIncluded.mail_template) as unknown as Array<Array<MailTemplate>>;\n\n                    mailTemplateIncluded.forEach(item => {\n                        const currentLocaleEmail = item.find(email => email.locale === State.get('session').currentLocale);\n                        if (currentLocaleEmail) {\n                            this.mailTemplates.push(currentLocaleEmail);\n                        }\n                    });\n                }\n            }).catch(() => {\n                this.hasError = true;\n            });\n        },\n\n        handleSelectRule(item: Rule, checked: boolean): void {\n            this.notSelectedRuleIds = checked\n                ? this.notSelectedRuleIds.filter(ruleId => ruleId !== item.id)\n                : [...this.notSelectedRuleIds, item.id];\n        },\n\n        handleSelectMail(item: MailTemplate, checked: boolean): void {\n            this.notSelectedMailIds = checked\n                ? this.notSelectedMailIds.filter(mailId => mailId !== item.id)\n                : [...this.notSelectedMailIds, item.id];\n        },\n    },\n};\n", "export default \"<sw-modal\\n    class=\\\"sw-flow-download-modal\\\"\\n    :title=\\\"$tc('sw-flow-sharing.downloadModal.title')\\\"\\n    @modal-close=\\\"onCancel\\\"\\n>\\n    <div class=\\\"sw-flow-download-modal__description\\\">\\n        <p v-html=\\\"$tc('sw-flow-sharing.downloadModal.description')\\\"></p>\\n    </div>\\n\\n    <div class=\\\"sw-flow-download-modal__included\\\">\\n        <div class=\\\"sw-flow-download-modal__data-included\\\">\\n            <ul>\\n                <li\\n                    v-for=\\\"(rule, index) in rules\\\"\\n                    :key=\\\"index\\\"\\n                >\\n                    <sw-checkbox-field\\n                        class=\\\"sw-flow-download-modal__rule-item\\\"\\n                        :value=\\\"!!rule.id\\\"\\n                        :label=\\\"$tc('sw-flow-sharing.downloadModal.ruleLabel',0, { ruleName: rule.name })\\\"\\n                        @change=\\\"(checked) => handleSelectRule(rule, checked)\\\"\\n                    />\\n                </li>\\n            </ul>\\n\\n            <ul>\\n                <li\\n                    v-for=\\\"(mail, index) in mailTemplates\\\"\\n                    :key=\\\"index\\\"\\n                >\\n                    <sw-checkbox-field\\n                        class=\\\"sw-flow-download-modal__mail-template-item\\\"\\n                        :value=\\\"!!mail.id\\\"\\n                        :label=\\\"$tc('sw-flow-sharing.downloadModal.mailTemplateLabel',0, { mail: mail.mailTemplateTypeName })\\\"\\n                        @change=\\\"(checked) => handleSelectMail(mail, checked)\\\"\\n                    />\\n                </li>\\n            </ul>\\n        </div>\\n    </div>\\n\\n    <template #modal-footer>\\n        <sw-button\\n            class=\\\"sw-flow-download-modal__cancel-button\\\"\\n            size=\\\"small\\\"\\n            @click=\\\"onCancel\\\"\\n        >\\n            {{ $tc('global.default.cancel') }}\\n        </sw-button>\\n\\n        <sw-button\\n            class=\\\"sw-flow-download-modal__download-button\\\"\\n            variant=\\\"primary\\\"\\n            size=\\\"small\\\"\\n            :disabled=\\\"!acl.can('flow.viewer')\\\"\\n            @click=\\\"onDownload\\\"\\n        >\\n            {{ $tc('sw-flow-sharing.downloadModal.downloadButton') }}\\n        </sw-button>\\n    </template>\\n</sw-modal>\\n\";", "/**\n * Translates the list format produced by css-loader into something\n * easier to manipulate.\n */\nexport default function listToStyles (parentId, list) {\n  var styles = []\n  var newStyles = {}\n  for (var i = 0; i < list.length; i++) {\n    var item = list[i]\n    var id = item[0]\n    var css = item[1]\n    var media = item[2]\n    var sourceMap = item[3]\n    var part = {\n      id: parentId + ':' + i,\n      css: css,\n      media: media,\n      sourceMap: sourceMap\n    }\n    if (!newStyles[id]) {\n      styles.push(newStyles[id] = { id: id, parts: [part] })\n    } else {\n      newStyles[id].parts.push(part)\n    }\n  }\n  return styles\n}\n", "/*\n  MIT License http://www.opensource.org/licenses/mit-license.php\n  Author <PERSON> @sokra\n  Modified by <PERSON> @yyx990803\n*/\n\nimport listToStyles from './listToStyles'\n\nvar hasDocument = typeof document !== 'undefined'\n\nif (typeof DEBUG !== 'undefined' && DEBUG) {\n  if (!hasDocument) {\n    throw new Error(\n    'vue-style-loader cannot be used in a non-browser environment. ' +\n    \"Use { target: 'node' } in your Webpack config to indicate a server-rendering environment.\"\n  ) }\n}\n\n/*\ntype StyleObject = {\n  id: number;\n  parts: Array<StyleObjectPart>\n}\n\ntype StyleObjectPart = {\n  css: string;\n  media: string;\n  sourceMap: ?string\n}\n*/\n\nvar stylesInDom = {/*\n  [id: number]: {\n    id: number,\n    refs: number,\n    parts: Array<(obj?: StyleObjectPart) => void>\n  }\n*/}\n\nvar head = hasDocument && (document.head || document.getElementsByTagName('head')[0])\nvar singletonElement = null\nvar singletonCounter = 0\nvar isProduction = false\nvar noop = function () {}\nvar options = null\nvar ssrIdKey = 'data-vue-ssr-id'\n\n// Force single-tag solution on IE6-9, which has a hard limit on the # of <style>\n// tags it will allow on a page\nvar isOldIE = typeof navigator !== 'undefined' && /msie [6-9]\\b/.test(navigator.userAgent.toLowerCase())\n\nexport default function addStylesClient (parentId, list, _isProduction, _options) {\n  isProduction = _isProduction\n\n  options = _options || {}\n\n  var styles = listToStyles(parentId, list)\n  addStylesToDom(styles)\n\n  return function update (newList) {\n    var mayRemove = []\n    for (var i = 0; i < styles.length; i++) {\n      var item = styles[i]\n      var domStyle = stylesInDom[item.id]\n      domStyle.refs--\n      mayRemove.push(domStyle)\n    }\n    if (newList) {\n      styles = listToStyles(parentId, newList)\n      addStylesToDom(styles)\n    } else {\n      styles = []\n    }\n    for (var i = 0; i < mayRemove.length; i++) {\n      var domStyle = mayRemove[i]\n      if (domStyle.refs === 0) {\n        for (var j = 0; j < domStyle.parts.length; j++) {\n          domStyle.parts[j]()\n        }\n        delete stylesInDom[domStyle.id]\n      }\n    }\n  }\n}\n\nfunction addStylesToDom (styles /* Array<StyleObject> */) {\n  for (var i = 0; i < styles.length; i++) {\n    var item = styles[i]\n    var domStyle = stylesInDom[item.id]\n    if (domStyle) {\n      domStyle.refs++\n      for (var j = 0; j < domStyle.parts.length; j++) {\n        domStyle.parts[j](item.parts[j])\n      }\n      for (; j < item.parts.length; j++) {\n        domStyle.parts.push(addStyle(item.parts[j]))\n      }\n      if (domStyle.parts.length > item.parts.length) {\n        domStyle.parts.length = item.parts.length\n      }\n    } else {\n      var parts = []\n      for (var j = 0; j < item.parts.length; j++) {\n        parts.push(addStyle(item.parts[j]))\n      }\n      stylesInDom[item.id] = { id: item.id, refs: 1, parts: parts }\n    }\n  }\n}\n\nfunction createStyleElement () {\n  var styleElement = document.createElement('style')\n  styleElement.type = 'text/css'\n  head.appendChild(styleElement)\n  return styleElement\n}\n\nfunction addStyle (obj /* StyleObjectPart */) {\n  var update, remove\n  var styleElement = document.querySelector('style[' + ssrIdKey + '~=\"' + obj.id + '\"]')\n\n  if (styleElement) {\n    if (isProduction) {\n      // has SSR styles and in production mode.\n      // simply do nothing.\n      return noop\n    } else {\n      // has SSR styles but in dev mode.\n      // for some reason Chrome can't handle source map in server-rendered\n      // style tags - source maps in <style> only works if the style tag is\n      // created and inserted dynamically. So we remove the server rendered\n      // styles and inject new ones.\n      styleElement.parentNode.removeChild(styleElement)\n    }\n  }\n\n  if (isOldIE) {\n    // use singleton mode for IE9.\n    var styleIndex = singletonCounter++\n    styleElement = singletonElement || (singletonElement = createStyleElement())\n    update = applyToSingletonTag.bind(null, styleElement, styleIndex, false)\n    remove = applyToSingletonTag.bind(null, styleElement, styleIndex, true)\n  } else {\n    // use multi-style-tag mode in all other cases\n    styleElement = createStyleElement()\n    update = applyToTag.bind(null, styleElement)\n    remove = function () {\n      styleElement.parentNode.removeChild(styleElement)\n    }\n  }\n\n  update(obj)\n\n  return function updateStyle (newObj /* StyleObjectPart */) {\n    if (newObj) {\n      if (newObj.css === obj.css &&\n          newObj.media === obj.media &&\n          newObj.sourceMap === obj.sourceMap) {\n        return\n      }\n      update(obj = newObj)\n    } else {\n      remove()\n    }\n  }\n}\n\nvar replaceText = (function () {\n  var textStore = []\n\n  return function (index, replacement) {\n    textStore[index] = replacement\n    return textStore.filter(Boolean).join('\\n')\n  }\n})()\n\nfunction applyToSingletonTag (styleElement, index, remove, obj) {\n  var css = remove ? '' : obj.css\n\n  if (styleElement.styleSheet) {\n    styleElement.styleSheet.cssText = replaceText(index, css)\n  } else {\n    var cssNode = document.createTextNode(css)\n    var childNodes = styleElement.childNodes\n    if (childNodes[index]) styleElement.removeChild(childNodes[index])\n    if (childNodes.length) {\n      styleElement.insertBefore(cssNode, childNodes[index])\n    } else {\n      styleElement.appendChild(cssNode)\n    }\n  }\n}\n\nfunction applyToTag (styleElement, obj) {\n  var css = obj.css\n  var media = obj.media\n  var sourceMap = obj.sourceMap\n\n  if (media) {\n    styleElement.setAttribute('media', media)\n  }\n  if (options.ssrId) {\n    styleElement.setAttribute(ssrIdKey, obj.id)\n  }\n\n  if (sourceMap) {\n    // https://developer.chrome.com/devtools/docs/javascript-debugging\n    // this makes source maps inside style tags work properly in Chrome\n    css += '\\n/*# sourceURL=' + sourceMap.sources[0] + ' */'\n    // http://stackoverflow.com/a/26603875\n    css += '\\n/*# sourceMappingURL=data:application/json;base64,' + btoa(unescape(encodeURIComponent(JSON.stringify(sourceMap)))) + ' */'\n  }\n\n  if (styleElement.styleSheet) {\n    styleElement.styleSheet.cssText = css\n  } else {\n    while (styleElement.firstChild) {\n      styleElement.removeChild(styleElement.firstChild)\n    }\n    styleElement.appendChild(document.createTextNode(css))\n  }\n}\n"], "sourceRoot": ""}
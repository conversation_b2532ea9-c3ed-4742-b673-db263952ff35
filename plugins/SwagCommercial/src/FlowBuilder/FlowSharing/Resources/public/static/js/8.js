(this["webpackJsonpPluginflow-sharing"]=this["webpackJsonpPluginflow-sharing"]||[]).push([[8],{wUqA:function(e,t,n){"use strict";n.r(t);var r=Shopware.State;t.default={template:'{% block sw_flow_sequence_action_item_custom %}\n<sw-flow-sequence-error\n    v-if="item.error && Object.keys(item.error).length"\n    :sequence="item"\n/>\n{% endblock %}\n',methods:{editAction:function(e){if(!this.currentSequence.error||0===Object.keys(this.currentSequence.error).length)return this.$super("editAction",e);e.name&&r.commit("swFlowState/updateSequence",{id:this.currentSequence.id,actionName:e.name,config:e.config,error:{}})}}}}}]);
//# sourceMappingURL=8.js.map
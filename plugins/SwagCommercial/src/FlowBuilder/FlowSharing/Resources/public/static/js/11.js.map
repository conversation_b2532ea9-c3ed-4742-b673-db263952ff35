{"version": 3, "sources": ["webpack:////tmp/extension2400992207/SwagCommercial/src/FlowBuilder/FlowSharing/Resources/app/administration/src/module/sw-flow/page/sw-flow-index/index.ts", "webpack:////tmp/extension2400992207/SwagCommercial/src/FlowBuilder/FlowSharing/Resources/app/administration/src/module/sw-flow/page/sw-flow-index/sw-flow-index.html.twig"], "names": ["template", "inject", "data", "showUploadModal", "methods", "getLicense", "toggle", "Shopware", "License", "get", "openUploadModal", "this", "onCloseUploadModal", "onUploadFlowTemplateFile", "feature", "isActive", "$router", "push", "name", "query", "isUploading", "params"], "mappings": "2IAKe,WACXA,SCNW,ogCDQXC,OAAQ,CAAC,WAETC,KAAI,WAGA,MAAO,CACHC,iBAAiB,IAIzBC,QAAS,CACLC,WAAU,SAACC,GACP,OAAOC,SAASC,QAAQC,IAAIH,IAGhCI,gBAAe,WACXC,KAAKR,iBAAkB,GAG3BS,mBAAkB,WACdD,KAAKR,iBAAkB,GAG3BU,yBAAwB,WAChBF,KAAKG,QAAQC,SAAS,QACtBJ,KAAKK,QAAQC,KAAK,CACdC,KAAM,yBACNC,MAAO,CAACC,aAAa,KAM7BT,KAAKK,QAAQC,KAAK,CACdC,KAAM,yBACNG,OAAQ,CAAED,aAAa", "file": "static/js/11.js", "sourcesContent": ["import template from './sw-flow-index.html.twig';\n\n/**\n * @package services-settings\n */\nexport default {\n    template,\n\n    inject: ['feature'],\n\n    data(): {\n        showUploadModal: boolean\n    } {\n        return {\n            showUploadModal: false\n        };\n    },\n\n    methods: {\n        getLicense(toggle: string): boolean {\n            return Shopware.License.get(toggle);\n        },\n\n        openUploadModal(): void {\n            this.showUploadModal = true;\n        },\n\n        onCloseUploadModal(): void {\n            this.showUploadModal = false;\n        },\n\n        onUploadFlowTemplateFile(): void {\n            if (this.feature.isActive('VUE3')) {\n                this.$router.push({\n                    name: 'sw.flow.create.general',\n                    query: {isUploading: true}\n                });\n\n                return;\n            }\n\n            this.$router.push({\n                name: 'sw.flow.create.general',\n                params: { isUploading: true }\n            });\n        },\n    },\n};\n", "export default \"{% block sw_flow_index_smart_bar_actions_extension %}\\n    {% block sw_flow_index_smart_bar_actions_import %}\\n    <sw-button\\n        v-if=\\\"getLicense('FLOW_BUILDER-4142679')\\\"\\n        v-tooltip=\\\"{\\n            message: $tc('sw-privileges.tooltip.warning'),\\n            disabled: acl.can('flow.creator'),\\n            position: 'bottom',\\n            showOnDisabledElements: true\\n        }\\\"\\n        class=\\\"sw-flow-list__import\\\"\\n        @click=\\\"openUploadModal\\\"\\n    >\\n        <sw-icon\\n            name=\\\"regular-upload\\\"\\n            color=\\\"#758CA3\\\"\\n            size=\\\"16\\\"\\n        />\\n        {{ $tc('sw-flow-sharing.uploadButton') }}\\n    </sw-button>\\n    {% endblock %}\\n{% endblock %}\\n\\n{% block sw_flow_index_modal_content_custom %}\\n    {% block sw_flow_index_upload_modal %}\\n        <sw-flow-upload-modal\\n            v-if=\\\"showUploadModal\\\"\\n            @modal-upload-finished=\\\"onUploadFlowTemplateFile\\\"\\n            @modal-close=\\\"onCloseUploadModal\\\"\\n        />\\n    {% endblock %}\\n{% endblock %}\\n\";"], "sourceRoot": ""}
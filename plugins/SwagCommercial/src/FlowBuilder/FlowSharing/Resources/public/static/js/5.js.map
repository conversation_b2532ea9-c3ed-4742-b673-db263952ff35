{"version": 3, "sources": ["webpack:////tmp/extension2400992207/SwagCommercial/src/FlowBuilder/FlowSharing/Resources/app/administration/src/module/sw-flow/component/modals/sw-flow-mail-send-modal/index.ts", "webpack:////tmp/extension2400992207/SwagCommercial/src/FlowBuilder/FlowSharing/Resources/app/administration/src/module/sw-flow/component/modals/sw-flow-mail-send-modal/sw-flow-mail-send-modal.html.twig"], "names": ["template"], "mappings": "0IAKe,WACXA,SCNW", "file": "static/js/5.js", "sourcesContent": ["import template from './sw-flow-mail-send-modal.html.twig';\n\n/**\n * @package services-settings\n */\nexport default {\n    template,\n};\n", "export default \"{% block sw_flow_mail_send_modal_custom %}\\n<sw-flow-sequence-modal-error\\n    v-if=\\\"sequence.error && Object.keys(sequence.error).length > 0\\\"\\n    :sequence=\\\"sequence\\\"\\n/>\\n{% endblock %}\\n\";"], "sourceRoot": ""}
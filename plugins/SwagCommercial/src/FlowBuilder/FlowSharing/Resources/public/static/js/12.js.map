{"version": 3, "sources": ["webpack:////tmp/extension2400992207/SwagCommercial/src/FlowBuilder/FlowSharing/Resources/app/administration/src/module/sw-flow/view/sw-flow-list/index.ts", "webpack:////tmp/extension2400992207/SwagCommercial/src/FlowBuilder/FlowSharing/Resources/app/administration/src/module/sw-flow/view/sw-flow-list/sw-flow-list.html.twig"], "names": ["template", "data", "currentFlow", "isDownloading", "methods", "getLicense", "toggle", "Shopware", "License", "get", "onOpenDownloadModal", "item", "this", "onCloseDownloadModal", "onDownloadFlowSuccess", "isSuccess", "createNotificationSuccess", "message", "$tc", "createNotificationError"], "mappings": "2IAMe,WACXA,SCPW,kuBDSXC,KAAI,WAIA,MAAO,CACHC,YAAa,GACbC,eAAe,IAIvBC,QAAS,CACLC,WAAU,SAACC,GACP,OAAOC,SAASC,QAAQC,IAAIH,IAGhCI,oBAAmB,SAACC,GAChBC,KAAKT,eAAgB,EACrBS,KAAKV,YAAcS,GAGvBE,qBAAoB,WAChBD,KAAKT,eAAgB,EACrBS,KAAKV,YAAc,IAGvBY,sBAAqB,SAACC,GAClBH,KAAKT,eAAgB,EACrBS,KAAKV,YAAc,GAEfa,EACAH,KAAKI,0BAA0B,CAC3BC,QAASL,KAAKM,IAAI,yDAM1BN,KAAKO,wBAAwB,CACzBF,QAASL,KAAKM,IAAI", "file": "static/js/12.js", "sourcesContent": ["import template from './sw-flow-list.html.twig';\nimport type {FlowEntity} from '../../flow.types';\n\n/**\n * @package services-settings\n */\nexport default {\n    template,\n\n    data(): {\n        currentFlow: FlowEntity,\n        isDownloading: boolean,\n    } {\n        return {\n            currentFlow: {} as FlowEntity,\n            isDownloading: false,\n        };\n    },\n\n    methods: {\n        getLicense(toggle: string): boolean {\n            return Shopware.License.get(toggle);\n        },\n\n        onOpenDownloadModal(item: FlowEntity): void {\n            this.isDownloading = true;\n            this.currentFlow = item;\n        },\n\n        onCloseDownloadModal(): void {\n            this.isDownloading = false;\n            this.currentFlow = {};\n        },\n\n        onDownloadFlowSuccess(isSuccess: boolean): void {\n            this.isDownloading = false;\n            this.currentFlow = {};\n\n            if (isSuccess) {\n                this.createNotificationSuccess({\n                    message: this.$tc('sw-flow-sharing.notification.messageDownloadSuccess'),\n                });\n\n                return;\n            }\n\n            this.createNotificationError({\n                message: this.$tc('sw-flow-sharing.notification.messageDownloadError'),\n            });\n        },\n    },\n};\n", "export default \"{% block sw_flow_list_grid_actions_custom %}\\n    <sw-context-menu-item\\n        v-if=\\\"getLicense('FLOW_BUILDER-4142679')\\\"\\n        class=\\\"sw-flow-list__item-download\\\"\\n        :disabled=\\\"!acl.can('flow.viewer')\\\"\\n        @click=\\\"onOpenDownloadModal(item)\\\"\\n    >\\n        {{ $tc('sw-flow-sharing.downloadButton') }}\\n    </sw-context-menu-item>\\n{% endblock %}\\n\\n{% block sw_flow_list_modal_content_custom %}\\n    {% block sw_flow_list_download_modal %}\\n        <sw-flow-download-modal\\n            v-if=\\\"currentFlow && isDownloading\\\"\\n            :flow-id=\\\"currentFlow.id\\\"\\n            @download-finish=\\\"onDownloadFlowSuccess\\\"\\n            @modal-close=\\\"onCloseDownloadModal\\\"\\n        />\\n    {% endblock %}\\n{% endblock %}\\n\";"], "sourceRoot": ""}
(this["webpackJsonpPluginflow-sharing"]=this["webpackJsonpPluginflow-sharing"]||[]).push([[12],{"9whO":function(o,n,s){"use strict";s.r(n);n.default={template:'{% block sw_flow_list_grid_actions_custom %}\n    <sw-context-menu-item\n        v-if="getLicense(\'FLOW_BUILDER-4142679\')"\n        class="sw-flow-list__item-download"\n        :disabled="!acl.can(\'flow.viewer\')"\n        @click="onOpenDownloadModal(item)"\n    >\n        {{ $tc(\'sw-flow-sharing.downloadButton\') }}\n    </sw-context-menu-item>\n{% endblock %}\n\n{% block sw_flow_list_modal_content_custom %}\n    {% block sw_flow_list_download_modal %}\n        <sw-flow-download-modal\n            v-if="currentFlow && isDownloading"\n            :flow-id="currentFlow.id"\n            @download-finish="onDownloadFlowSuccess"\n            @modal-close="onCloseDownloadModal"\n        />\n    {% endblock %}\n{% endblock %}\n',data:function(){return{currentFlow:{},isDownloading:!1}},methods:{getLicense:function(o){return Shopware.License.get(o)},onOpenDownloadModal:function(o){this.isDownloading=!0,this.currentFlow=o},onCloseDownloadModal:function(){this.isDownloading=!1,this.currentFlow={}},onDownloadFlowSuccess:function(o){this.isDownloading=!1,this.currentFlow={},o?this.createNotificationSuccess({message:this.$tc("sw-flow-sharing.notification.messageDownloadSuccess")}):this.createNotificationError({message:this.$tc("sw-flow-sharing.notification.messageDownloadError")})}}}}}]);
//# sourceMappingURL=12.js.map
/*! For license information please see 10.js.LICENSE.txt */
(this["webpackJsonpPluginflow-sharing"]=this["webpackJsonpPluginflow-sharing"]||[]).push([[10],{MDv0:function(t,e,r){"use strict";r.r(e);var n=r("9O0K");function o(t){return(o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function i(t,e,r){return(e=function(t){var e=function(t,e){if("object"!==o(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==o(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"===o(e)?e:String(e)}(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function a(t){return function(t){if(Array.isArray(t))return c(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(!t)return;if("string"==typeof t)return c(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);"Object"===r&&t.constructor&&(r=t.constructor.name);if("Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return c(t,e)}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function c(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function u(){u=function(){return t};var t={},e=Object.prototype,r=e.hasOwnProperty,n=Object.defineProperty||function(t,e,r){t[e]=r.value},i="function"==typeof Symbol?Symbol:{},a=i.iterator||"@@iterator",c=i.asyncIterator||"@@asyncIterator",s=i.toStringTag||"@@toStringTag";function l(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{l({},"")}catch(t){l=function(t,e,r){return t[e]=r}}function f(t,e,r,o){var i=e&&e.prototype instanceof p?e:p,a=Object.create(i.prototype),c=new k(o||[]);return n(a,"_invoke",{value:S(t,r,c)}),a}function d(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}t.wrap=f;var h={};function p(){}function y(){}function v(){}var g={};l(g,a,(function(){return this}));var m=Object.getPrototypeOf,w=m&&m(m(F([])));w&&w!==e&&r.call(w,a)&&(g=w);var b=v.prototype=p.prototype=Object.create(g);function E(t){["next","throw","return"].forEach((function(e){l(t,e,(function(t){return this._invoke(e,t)}))}))}function I(t,e){function i(n,a,c,u){var s=d(t[n],t,a);if("throw"!==s.type){var l=s.arg,f=l.value;return f&&"object"==o(f)&&r.call(f,"__await")?e.resolve(f.__await).then((function(t){i("next",t,c,u)}),(function(t){i("throw",t,c,u)})):e.resolve(f).then((function(t){l.value=t,c(l)}),(function(t){return i("throw",t,c,u)}))}u(s.arg)}var a;n(this,"_invoke",{value:function(t,r){function n(){return new e((function(e,n){i(t,r,e,n)}))}return a=a?a.then(n,n):n()}})}function S(t,e,r){var n="suspendedStart";return function(o,i){if("executing"===n)throw new Error("Generator is already running");if("completed"===n){if("throw"===o)throw i;return L()}for(r.method=o,r.arg=i;;){var a=r.delegate;if(a){var c=O(a,r);if(c){if(c===h)continue;return c}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if("suspendedStart"===n)throw n="completed",r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);n="executing";var u=d(t,e,r);if("normal"===u.type){if(n=r.done?"completed":"suspendedYield",u.arg===h)continue;return{value:u.arg,done:r.done}}"throw"===u.type&&(n="completed",r.method="throw",r.arg=u.arg)}}}function O(t,e){var r=e.method,n=t.iterator[r];if(void 0===n)return e.delegate=null,"throw"===r&&t.iterator.return&&(e.method="return",e.arg=void 0,O(t,e),"throw"===e.method)||"return"!==r&&(e.method="throw",e.arg=new TypeError("The iterator does not provide a '"+r+"' method")),h;var o=d(n,t.iterator,e.arg);if("throw"===o.type)return e.method="throw",e.arg=o.arg,e.delegate=null,h;var i=o.arg;return i?i.done?(e[t.resultName]=i.value,e.next=t.nextLoc,"return"!==e.method&&(e.method="next",e.arg=void 0),e.delegate=null,h):i:(e.method="throw",e.arg=new TypeError("iterator result is not an object"),e.delegate=null,h)}function j(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function x(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function k(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(j,this),this.reset(!0)}function F(t){if(t){var e=t[a];if(e)return e.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var n=-1,o=function e(){for(;++n<t.length;)if(r.call(t,n))return e.value=t[n],e.done=!1,e;return e.value=void 0,e.done=!0,e};return o.next=o}}return{next:L}}function L(){return{value:void 0,done:!0}}return y.prototype=v,n(b,"constructor",{value:v,configurable:!0}),n(v,"constructor",{value:y,configurable:!0}),y.displayName=l(v,s,"GeneratorFunction"),t.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===y||"GeneratorFunction"===(e.displayName||e.name))},t.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,v):(t.__proto__=v,l(t,s,"GeneratorFunction")),t.prototype=Object.create(b),t},t.awrap=function(t){return{__await:t}},E(I.prototype),l(I.prototype,c,(function(){return this})),t.AsyncIterator=I,t.async=function(e,r,n,o,i){void 0===i&&(i=Promise);var a=new I(f(e,r,n,o),i);return t.isGeneratorFunction(r)?a:a.next().then((function(t){return t.done?t.value:a.next()}))},E(b),l(b,s,"Generator"),l(b,a,(function(){return this})),l(b,"toString",(function(){return"[object Generator]"})),t.keys=function(t){var e=Object(t),r=[];for(var n in e)r.push(n);return r.reverse(),function t(){for(;r.length;){var n=r.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},t.values=F,k.prototype={constructor:k,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,this.tryEntries.forEach(x),!t)for(var e in this)"t"===e.charAt(0)&&r.call(this,e)&&!isNaN(+e.slice(1))&&(this[e]=void 0)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var e=this;function n(r,n){return a.type="throw",a.arg=t,e.next=r,n&&(e.method="next",e.arg=void 0),!!n}for(var o=this.tryEntries.length-1;o>=0;--o){var i=this.tryEntries[o],a=i.completion;if("root"===i.tryLoc)return n("end");if(i.tryLoc<=this.prev){var c=r.call(i,"catchLoc"),u=r.call(i,"finallyLoc");if(c&&u){if(this.prev<i.catchLoc)return n(i.catchLoc,!0);if(this.prev<i.finallyLoc)return n(i.finallyLoc)}else if(c){if(this.prev<i.catchLoc)return n(i.catchLoc,!0)}else{if(!u)throw new Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return n(i.finallyLoc)}}}},abrupt:function(t,e){for(var n=this.tryEntries.length-1;n>=0;--n){var o=this.tryEntries[n];if(o.tryLoc<=this.prev&&r.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var i=o;break}}i&&("break"===t||"continue"===t)&&i.tryLoc<=e&&e<=i.finallyLoc&&(i=null);var a=i?i.completion:{};return a.type=t,a.arg=e,i?(this.method="next",this.next=i.finallyLoc,h):this.complete(a)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),h},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.finallyLoc===t)return this.complete(r.completion,r.afterLoc),x(r),h}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.tryLoc===t){var n=r.completion;if("throw"===n.type){var o=n.arg;x(r)}return o}}throw new Error("illegal catch attempt")},delegateYield:function(t,e,r){return this.delegate={iterator:F(t),resultName:e,nextLoc:r},"next"===this.method&&(this.arg=void 0),h}},t}function s(t,e,r,n,o,i,a){try{var c=t[i](a),u=c.value}catch(t){return void r(t)}c.done?e(u):Promise.resolve(u).then(n,o)}function l(t){return function(){var e=this,r=arguments;return new Promise((function(n,o){var i=t.apply(e,r);function a(t){s(i,n,o,a,c,"next",t)}function c(t){s(i,n,o,a,c,"throw",t)}a(void 0)}))}}var f=Shopware,d=f.State,h=f.Utils,p=Shopware.Data.Criteria,y=h.object.cloneDeep;e.default={inject:["feature"],data:function(){return{dataIncluded:{},referenceIncluded:{},invalidEntityIds:[]}},computed:{flowRepository:function(){return this.repositoryFactory.create("flow")},flowSequenceRepository:function(){return this.repositoryFactory.create("flow_sequence")},flowSharingState:function(){return d.get("swFlowSharingState")},isUploading:function(){var t,e;return this.feature.isActive("VUE3")?!0==!(null===(e=this.$route.query)||void 0===e||!e.isUploading):!0===(null===(t=this.$route.params)||void 0===t?void 0:t.isUploading)}},methods:{createdComponent:function(){var t=this;return l(u().mark((function e(){return u().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!t.isUploading){e.next=5;break}return t.referenceIncluded=t.flowSharingState.referenceIncluded,t.dataIncluded=t.flowSharingState.dataIncluded,e.next=5,t.getInvalidEntityIds();case 5:t.$super("createdComponent");case 6:case"end":return e.stop()}}),e)})))()},routeDetailTab:function(t){return this.isUploading?this.feature.isActive("VUE3")?{name:"sw.flow.create.".concat(t),query:{isUploading:!0}}:{name:"sw.flow.create.".concat(t),params:{isUploading:!0}}:this.$super("routeDetailTab",t)},createNewFlow:function(){if(!this.isUploading)return this.$super("createNewFlow");var t=this.flowRepository.create();t.priority=0,t.eventName="",d.commit("swFlowState/setFlow",t);var e=this.flowSharingState.flow;e&&this.restoreImportedFlow(e)},restoreImportedFlow:function(t){var e=this;if(0!==Object.keys(t).length){this.flow.id=h.createId(),Object.keys(t).forEach((function(r){["id","sequences"].includes(r)||(e.flow[r]="active"===r?Boolean(t[r]):t[r])}));var r=null==t?void 0:t.sequences;r=r?this.buildSequencesFromConfig(this.validateSequences(r)):[],d.commit("swFlowState/setFlow",this.flow),d.commit("swFlowState/setEventName",null==t?void 0:t.eventName),d.commit("error/removeApiError",{expression:"flow.".concat(this.flow.id,".eventName")}),d.commit("swFlowState/setSequences",r),d.commit("swFlowState/setOriginFlow",y(this.flow)),this.getDataForActionDescription()}},validateSequences:function(t){return this.dataIncluded.rule&&(t=this.addRuleErrors(t)),t=this.addActionErrors(t)},addRuleErrors:function(t){var e=this;return Object.keys(t).forEach((function(r){null===t[r].actionName&&(t[r].rule=e.dataIncluded.rule[t[r].ruleId],t[r].rule.value=Object.assign({},t[r].rule.value),e.invalidEntityIds.includes(t[r].ruleId)?t[r].error=e.generateErrorObject("missing-rule","rule",[t[r].ruleId]):e.hasInvalidReference(t[r].rule)&&(t[r].error=e.generateErrorObject("rule","rule",[t[r].ruleId])))})),t},addActionErrors:function(t){var e=this;return Object.keys(t).forEach((function(r){if(null!==t[r].actionName&&void 0!==n.b[t[r].actionName]){var o=n.b[t[r].actionName],i=e.getEntityIds(o,t[r].config).filter((function(t){return e.invalidEntityIds.includes(t)}));0!==i.length&&(t[r].error=e.generateErrorObject("action",o,i))}})),t},getEntityIds:function(t,e){switch(t){case"tag":return Object.keys(e.tagIds);case"customer_group":return[e.customerGroupId];case"custom_field":return[e.customFieldId];case"mail_template":return[e.mailTemplateId];default:return[]}},getInvalidEntityIds:function(){var t=this;return l(u().mark((function e(){var r,n,o,i,c,s,l,f;return u().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:r=Object.assign({},t.referenceIncluded,t.dataIncluded),n=u().mark((function e(){var n,c,s,l,f,d;return u().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return c=i[o],s=t.repositoryFactory.create(c),(l=new p(1,null)).addFilter(p.equalsAny("id",Object.keys(r[c]))),e.next=6,s.searchIds(l);case 6:f=e.sent,d=Object.keys(r[c]).filter((function(t){return!f.data.includes(t)})),(n=t.invalidEntityIds).push.apply(n,a(d));case 9:case"end":return e.stop()}}),e)})),o=0,i=Object.keys(r);case 3:if(!(o<i.length)){e.next=8;break}return e.delegateYield(n(),"t0",5);case 5:o++,e.next=3;break;case 8:c=t.getReferenceIncludedFromRule(),s=u().mark((function e(){var r,n,o,i,s,d;return u().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return n=f[l],o=t.repositoryFactory.create(n),(i=new p(1,null)).addFilter(p.equalsAny("id",c[n])),e.next=6,o.searchIds(i);case 6:s=e.sent,d=c[n].filter((function(t){return!s.data.includes(t)})),(r=t.invalidEntityIds).push.apply(r,a(d));case 9:case"end":return e.stop()}}),e)})),l=0,f=Object.keys(c);case 11:if(!(l<f.length)){e.next=16;break}return e.delegateYield(s(),"t1",13);case 13:l++,e.next=11;break;case 16:case"end":return e.stop()}}),e)})))()},generateErrorObject:function(t,e,r){var n=Object.assign({},{type:t,errorDetail:{}}),o=Object.assign({},this.referenceIncluded,this.dataIncluded);return r.forEach((function(t){if(Array.isArray(o[e][t])){var r=o[e][t].filter((function(t){return t.locale===d.get("session").currentLocale}));r.length>0&&(n.errorDetail=i({},"".concat(e),i({},"".concat(t),r[0])))}else n.errorDetail=i({},"".concat(e),i({},"".concat(t),o[e][t]))})),n},hasInvalidReference:function(t){var e=this;return!t.conditions.every((function(t){var r=e.getEntityIdsOfCondition(t);return 0===r.length||0===r.filter((function(t){return e.invalidEntityIds.includes(t)})).length}))},getEntityIdsOfCondition:function(t){var e=d.getters["ruleConditionsConfig/getConfigForType"](t.type);return e&&e.fields.length&&Object.keys(e.fields[0].config).includes("entity")?"multi-entity-id-select"===e.fields[0].type?t.value[e.fields[0].name]:[t.value[e.fields[0].name]]:[]},getReferenceIncludedFromRule:function(){var t=this,e={};return this.dataIncluded.rule?(Object.keys(this.dataIncluded.rule).forEach((function(r){t.dataIncluded.rule[r].conditions.forEach((function(r){var n=d.getters["ruleConditionsConfig/getConfigForType"](r.type),o=t.getEntityIdsOfCondition(r);if(o.length>0){var i,c=n.fields[0].config.entity;if(e[c])(i=e[c]).push.apply(i,a(o));else e[c]=o}}))})),e):e},onSave:function(){if(this.isUploading&&this.sequences.filter((function(t){return t.error&&Object.keys(t.error).length})).length)return this.createNotificationError({message:this.$tc("sw-flow.flowNotification.messageSaveError")}),null;this.$super("onSave")}}}}}]);
//# sourceMappingURL=10.js.map
(this["webpackJsonpPluginflow-sharing"]=this["webpackJsonpPluginflow-sharing"]||[]).push([[3],{"0SKE":function(e,r,t){"use strict";t.r(r);t("rD/N");var n=t("9O0K");r.default={template:'<div\n    :class="\'sw-flow-sequence-modal-error sw-flow-sequence-modal-error--\' + errorType"\n>\n    <span class="sw-flow-sequence-modal-error__heading">\n        {{ getErrorTitle(sequence.actionName) }}\n    </span>\n\n    <div class="sw-flow-sequence-modal-error__list-wrap">\n        <ul class="sw-flow-sequence-modal-error__list">\n            <li\n                v-for="(value, key) in errorData"\n                :key="key"\n                class="sw-flow-sequence-modal-error__list-item"\n            >\n                {{ value.name || value.mailTemplateTypeName }}\n            </li>\n        </ul>\n    </div>\n</div>\n',props:{sequence:{type:Object,required:!1,default:null}},computed:{errorType:function(){var e,r;return(null===(e=this.sequence)||void 0===e||null===(r=e.error)||void 0===r?void 0:r.type)||"action"},errorData:function(){switch(this.sequence.actionName){case n.a.ADD_CUSTOMER_TAG:case n.a.ADD_ORDER_TAG:case n.a.REMOVE_CUSTOMER_TAG:case n.a.REMOVE_ORDER_TAG:return this.sequence.error.errorDetail.tag;case n.a.SET_CUSTOMER_CUSTOM_FIELD:case n.a.SET_ORDER_CUSTOM_FIELD:case n.a.SET_CUSTOMER_GROUP_CUSTOM_FIELD:return this.sequence.error.errorDetail.custom_field;case n.a.CHANGE_CUSTOMER_GROUP:return this.sequence.error.errorDetail.customer_group;case n.a.MAIL_SEND:return this.sequence.error.errorDetail.mail_template;default:return{}}}},methods:{getErrorTitle:function(e){return this.$tc("sw-flow-sharing.importError.textMissingObject",{data:n.c[e]})}}}},P8hj:function(e,r,t){"use strict";function n(e,r){for(var t=[],n={},s=0;s<r.length;s++){var a=r[s],o=a[0],i={id:e+":"+s,css:a[1],media:a[2],sourceMap:a[3]};n[o]?n[o].parts.push(i):t.push(n[o]={id:o,parts:[i]})}return t}t.r(r),t.d(r,"default",(function(){return h}));var s="undefined"!=typeof document;if("undefined"!=typeof DEBUG&&DEBUG&&!s)throw new Error("vue-style-loader cannot be used in a non-browser environment. Use { target: 'node' } in your Webpack config to indicate a server-rendering environment.");var a={},o=s&&(document.head||document.getElementsByTagName("head")[0]),i=null,l=0,u=!1,c=function(){},d=null,f="data-vue-ssr-id",p="undefined"!=typeof navigator&&/msie [6-9]\b/.test(navigator.userAgent.toLowerCase());function h(e,r,t,s){u=t,d=s||{};var o=n(e,r);return v(o),function(r){for(var t=[],s=0;s<o.length;s++){var i=o[s];(l=a[i.id]).refs--,t.push(l)}r?v(o=n(e,r)):o=[];for(s=0;s<t.length;s++){var l;if(0===(l=t[s]).refs){for(var u=0;u<l.parts.length;u++)l.parts[u]();delete a[l.id]}}}}function v(e){for(var r=0;r<e.length;r++){var t=e[r],n=a[t.id];if(n){n.refs++;for(var s=0;s<n.parts.length;s++)n.parts[s](t.parts[s]);for(;s<t.parts.length;s++)n.parts.push(g(t.parts[s]));n.parts.length>t.parts.length&&(n.parts.length=t.parts.length)}else{var o=[];for(s=0;s<t.parts.length;s++)o.push(g(t.parts[s]));a[t.id]={id:t.id,refs:1,parts:o}}}}function m(){var e=document.createElement("style");return e.type="text/css",o.appendChild(e),e}function g(e){var r,t,n=document.querySelector("style["+f+'~="'+e.id+'"]');if(n){if(u)return c;n.parentNode.removeChild(n)}if(p){var s=l++;n=i||(i=m()),r=T.bind(null,n,s,!1),t=T.bind(null,n,s,!0)}else n=m(),r=w.bind(null,n),t=function(){n.parentNode.removeChild(n)};return r(e),function(n){if(n){if(n.css===e.css&&n.media===e.media&&n.sourceMap===e.sourceMap)return;r(e=n)}else t()}}var _,E=(_=[],function(e,r){return _[e]=r,_.filter(Boolean).join("\n")});function T(e,r,t,n){var s=t?"":n.css;if(e.styleSheet)e.styleSheet.cssText=E(r,s);else{var a=document.createTextNode(s),o=e.childNodes;o[r]&&e.removeChild(o[r]),o.length?e.insertBefore(a,o[r]):e.appendChild(a)}}function w(e,r){var t=r.css,n=r.media,s=r.sourceMap;if(n&&e.setAttribute("media",n),d.ssrId&&e.setAttribute(f,r.id),s&&(t+="\n/*# sourceURL="+s.sources[0]+" */",t+="\n/*# sourceMappingURL=data:application/json;base64,"+btoa(unescape(encodeURIComponent(JSON.stringify(s))))+" */"),e.styleSheet)e.styleSheet.cssText=t;else{for(;e.firstChild;)e.removeChild(e.firstChild);e.appendChild(document.createTextNode(t))}}},"rD/N":function(e,r,t){var n=t("uxnE");n.__esModule&&(n=n.default),"string"==typeof n&&(n=[[e.i,n,""]]),n.locals&&(e.exports=n.locals);(0,t("P8hj").default)("22784188",n,!0,{})},uxnE:function(e,r,t){}}]);
//# sourceMappingURL=3.js.map
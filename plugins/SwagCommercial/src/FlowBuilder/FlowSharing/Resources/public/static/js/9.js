(this["webpackJsonpPluginflow-sharing"]=this["webpackJsonpPluginflow-sharing"]||[]).push([[9],{NwTi:function(e,n,t){"use strict";t.r(n);var s=Shopware.State;n.default={template:'{% block sw_flow_sequence_condition_content_custom %}\n<sw-flow-sequence-error\n    v-if="sequence.error && Object.keys(sequence.error).length"\n    :sequence="sequence"\n/>\n{% endblock %}\n\n{% block sw_flow_sequence_condition_rule_context_button_edit %}\n    <sw-context-menu-item\n        class="sw-flow-sequence-condition__rule-edit"\n        :disabled="hasMissingRule"\n        @click="onEditRule"\n    >\n        {{ $tc(\'sw-flow.rule.contextButton.editRule\') }}\n    </sw-context-menu-item>\n{% endblock %}\n',computed:{hasMissingRule:function(){return this.sequence.error&&"missing-rule"===this.sequence.error.type}},methods:{onRuleChange:function(e){var n=this;if(!this.sequence.error||0===Object.keys(this.sequence.error).length)return this.$super("onRuleChange",e);e&&(s.commit("swFlowState/updateSequence",{id:this.sequence.id,error:{},rule:e,ruleId:e.id}),this.selectedRuleId&&(this.sequences.forEach((function(t){t.ruleId===n.selectedRuleId&&t.id!==n.sequence.id&&s.commit("swFlowState/updateSequence",{id:t.id,error:{},rule:e,ruleId:e.id})})),this.selectedRuleId=null),this.removeFieldError(),this.showRuleSelection=!1)}}}}}]);
//# sourceMappingURL=9.js.map
{"version": 3, "sources": ["webpack:////tmp/extension2400992207/SwagCommercial/src/FlowBuilder/FlowSharing/Resources/app/administration/src/module/sw-flow/component/sw-flow-sequence-condition/sw-flow-sequence-condition.html.twig", "webpack:////tmp/extension2400992207/SwagCommercial/src/FlowBuilder/FlowSharing/Resources/app/administration/src/module/sw-flow/component/sw-flow-sequence-condition/index.ts"], "names": ["State", "Shopware", "template", "computed", "hasMissingRule", "this", "sequence", "error", "type", "methods", "onRuleChange", "rule", "_this", "Object", "keys", "length", "$super", "commit", "id", "ruleId", "selectedRuleId", "sequences", "for<PERSON>ach", "removeFieldError", "showRuleSelection"], "mappings": "wIAAe,ICGPA,EAAUC,SAAVD,MAKO,WACXE,SDTW,wgBCWXC,SAAU,CACNC,eAAc,WACV,OAAOC,KAAKC,SAASC,OAAsC,iBAA7BF,KAAKC,SAASC,MAAMC,OAI1DC,QAAS,CACLC,aAAY,SAACC,GAAyB,IAADC,EAAA,KACjC,IAAKP,KAAKC,SAASC,OAAqD,IAA5CM,OAAOC,KAAKT,KAAKC,SAASC,OAAOQ,OACzD,OAAOV,KAAKW,OAAO,eAAgBL,GAGlCA,IAILX,EAAMiB,OAAO,6BAA8B,CACvCC,GAAIb,KAAKC,SAASY,GAClBX,MAAO,GACPI,OACAQ,OAAQR,EAAKO,KAGbb,KAAKe,iBAELf,KAAKgB,UAAUC,SAAQ,SAAAhB,GACfA,EAASa,SAAWP,EAAKQ,gBACtBd,EAASY,KAAON,EAAKN,SAASY,IAIrClB,EAAMiB,OAAO,6BAA8B,CACvCC,GAAIZ,EAASY,GACbX,MAAO,GACPI,OACAQ,OAAQR,EAAKO,QAIrBb,KAAKe,eAAiB,MAG1Bf,KAAKkB,mBACLlB,KAAKmB,mBAAoB", "file": "static/js/9.js", "sourcesContent": ["export default \"{% block sw_flow_sequence_condition_content_custom %}\\n<sw-flow-sequence-error\\n    v-if=\\\"sequence.error && Object.keys(sequence.error).length\\\"\\n    :sequence=\\\"sequence\\\"\\n/>\\n{% endblock %}\\n\\n{% block sw_flow_sequence_condition_rule_context_button_edit %}\\n    <sw-context-menu-item\\n        class=\\\"sw-flow-sequence-condition__rule-edit\\\"\\n        :disabled=\\\"hasMissingRule\\\"\\n        @click=\\\"onEditRule\\\"\\n    >\\n        {{ $tc('sw-flow.rule.contextButton.editRule') }}\\n    </sw-context-menu-item>\\n{% endblock %}\\n\";", "import template from './sw-flow-sequence-condition.html.twig';\nimport type {RuleEntity} from '../../flow.types';\n\nconst { State } = Shopware;\n\n/**\n * @package services-settings\n */\nexport default {\n    template,\n\n    computed: {\n        hasMissingRule() {\n            return this.sequence.error && this.sequence.error.type === 'missing-rule';\n        },\n    },\n\n    methods: {\n        onRuleChange(rule: RuleEntity): void {\n            if (!this.sequence.error || Object.keys(this.sequence.error).length === 0) {\n                return this.$super('onRuleChange', rule);\n            }\n\n            if (!rule) {\n                return;\n            }\n\n            State.commit('swFlowState/updateSequence', {\n                id: this.sequence.id,\n                error: {},\n                rule,\n                ruleId: rule.id,\n            });\n\n            if (this.selectedRuleId) {\n                // Update other conditions which use the same rule\n                this.sequences.forEach(sequence => {\n                    if (sequence.ruleId !== this.selectedRuleId\n                        || sequence.id === this.sequence.id) {\n                        return;\n                    }\n\n                    State.commit('swFlowState/updateSequence', {\n                        id: sequence.id,\n                        error: {},\n                        rule,\n                        ruleId: rule.id,\n                    });\n                });\n\n                this.selectedRuleId = null;\n            }\n\n            this.removeFieldError();\n            this.showRuleSelection = false;\n        },\n    }\n};\n"], "sourceRoot": ""}
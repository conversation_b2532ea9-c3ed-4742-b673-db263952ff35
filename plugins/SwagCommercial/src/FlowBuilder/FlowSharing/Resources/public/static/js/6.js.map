{"version": 3, "sources": ["webpack:////tmp/extension2400992207/SwagCommercial/src/FlowBuilder/FlowSharing/Resources/app/administration/src/module/sw-flow/component/modals/sw-flow-set-entity-custom-field-modal/index.ts", "webpack:////tmp/extension2400992207/SwagCommercial/src/FlowBuilder/FlowSharing/Resources/app/administration/src/module/sw-flow/component/modals/sw-flow-set-entity-custom-field-modal/sw-flow-set-entity-custom-field-modal.html.twig"], "names": ["template", "methods", "createdComponent", "this", "getEntityOptions", "sequence", "config", "entity", "customFieldSetId", "customFieldSetLabel", "customFieldId", "customFieldLabel", "customFieldValue", "fieldOptionSelected", "option", "fieldOptions", "defaultFieldOptions"], "mappings": "0IAMe,WACXA,SCPW,+MDSXC,QAAS,CACLC,iBAAgB,WACZC,KAAKC,mBACAD,KAAKE,SAASC,SAInBH,KAAKI,OAASJ,KAAKE,SAASC,OAAOC,OACnCJ,KAAKK,iBAAmBL,KAAKE,SAASC,OAAOE,iBAC7CL,KAAKM,oBAAsBN,KAAKE,SAASC,OAAOG,oBAChDN,KAAKO,cAAgBP,KAAKE,SAASC,OAAOI,cAC1CP,KAAKQ,iBAAmBR,KAAKE,SAASC,OAAOK,iBAC7CR,KAAKS,iBAAmBT,KAAKE,SAASC,OAAOM,iBAC7CT,KAAKU,oBAAsBV,KAAKE,SAASC,OAAOQ,OAChDX,KAAKY,aAAeZ,KAAKa", "file": "static/js/6.js", "sourcesContent": ["import template from './sw-flow-set-entity-custom-field-modal.html.twig';\n\n/**\n * @private\n * @package services-settings\n */\nexport default {\n    template,\n\n    methods: {\n        createdComponent(): void {\n            this.getEntityOptions();\n            if (!this.sequence.config) {\n                return;\n            }\n\n            this.entity = this.sequence.config.entity;\n            this.customFieldSetId = this.sequence.config.customFieldSetId;\n            this.customFieldSetLabel = this.sequence.config.customFieldSetLabel;\n            this.customFieldId = this.sequence.config.customFieldId;\n            this.customFieldLabel = this.sequence.config.customFieldLabel;\n            this.customFieldValue = this.sequence.config.customFieldValue;\n            this.fieldOptionSelected = this.sequence.config.option;\n            this.fieldOptions = this.defaultFieldOptions;\n        },\n    }\n};\n", "export default \"{% block sw_flow_set_entity_custom_field_modal_custom %}\\n<sw-flow-sequence-modal-error\\n    v-if=\\\"sequence.error && Object.keys(sequence.error).length > 0\\\"\\n    :sequence=\\\"sequence\\\"\\n/>\\n{% endblock %}\\n\";"], "sourceRoot": ""}
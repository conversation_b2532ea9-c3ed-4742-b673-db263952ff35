(this["webpackJsonpPluginflow-sharing"]=this["webpackJsonpPluginflow-sharing"]||[]).push([[2],{HSQc:function(e,r,n){var i=n("RoYY");i.__esModule&&(i=i.default),"string"==typeof i&&(i=[[e.i,i,""]]),i.locals&&(e.exports=i.locals);(0,n("P8hj").default)("7b82565c",i,!0,{})},P8hj:function(e,r,n){"use strict";function i(e,r){for(var n=[],i={},s=0;s<r.length;s++){var o=r[s],t=o[0],l={id:e+":"+s,css:o[1],media:o[2],sourceMap:o[3]};i[t]?i[t].parts.push(l):n.push(i[t]={id:t,parts:[l]})}return n}n.r(r),n.d(r,"default",(function(){return v}));var s="undefined"!=typeof document;if("undefined"!=typeof DEBUG&&DEBUG&&!s)throw new Error("vue-style-loader cannot be used in a non-browser environment. Use { target: 'node' } in your Webpack config to indicate a server-rendering environment.");var o={},t=s&&(document.head||document.getElementsByTagName("head")[0]),l=null,a=0,u=!1,c=function(){},d=null,f="data-vue-ssr-id",p="undefined"!=typeof navigator&&/msie [6-9]\b/.test(navigator.userAgent.toLowerCase());function v(e,r,n,s){u=n,d=s||{};var t=i(e,r);return h(t),function(r){for(var n=[],s=0;s<t.length;s++){var l=t[s];(a=o[l.id]).refs--,n.push(a)}r?h(t=i(e,r)):t=[];for(s=0;s<n.length;s++){var a;if(0===(a=n[s]).refs){for(var u=0;u<a.parts.length;u++)a.parts[u]();delete o[a.id]}}}}function h(e){for(var r=0;r<e.length;r++){var n=e[r],i=o[n.id];if(i){i.refs++;for(var s=0;s<i.parts.length;s++)i.parts[s](n.parts[s]);for(;s<n.parts.length;s++)i.parts.push(g(n.parts[s]));i.parts.length>n.parts.length&&(i.parts.length=n.parts.length)}else{var t=[];for(s=0;s<n.parts.length;s++)t.push(g(n.parts[s]));o[n.id]={id:n.id,refs:1,parts:t}}}}function w(){var e=document.createElement("style");return e.type="text/css",t.appendChild(e),e}function g(e){var r,n,i=document.querySelector("style["+f+'~="'+e.id+'"]');if(i){if(u)return c;i.parentNode.removeChild(i)}if(p){var s=a++;i=l||(l=w()),r=q.bind(null,i,s,!1),n=q.bind(null,i,s,!0)}else i=w(),r=y.bind(null,i),n=function(){i.parentNode.removeChild(i)};return r(e),function(i){if(i){if(i.css===e.css&&i.media===e.media&&i.sourceMap===e.sourceMap)return;r(e=i)}else n()}}var m,_=(m=[],function(e,r){return m[e]=r,m.filter(Boolean).join("\n")});function q(e,r,n,i){var s=n?"":i.css;if(e.styleSheet)e.styleSheet.cssText=_(r,s);else{var o=document.createTextNode(s),t=e.childNodes;t[r]&&e.removeChild(t[r]),t.length?e.insertBefore(o,t[r]):e.appendChild(o)}}function y(e,r){var n=r.css,i=r.media,s=r.sourceMap;if(i&&e.setAttribute("media",i),d.ssrId&&e.setAttribute(f,r.id),s&&(n+="\n/*# sourceURL="+s.sources[0]+" */",n+="\n/*# sourceMappingURL=data:application/json;base64,"+btoa(unescape(encodeURIComponent(JSON.stringify(s))))+" */"),e.styleSheet)e.styleSheet.cssText=n;else{for(;e.firstChild;)e.removeChild(e.firstChild);e.appendChild(document.createTextNode(n))}}},RoYY:function(e,r,n){},lD6b:function(e,r,n){"use strict";n.r(r);n("HSQc"),r.default={template:'<div\n    class="sw-flow-sequence-error"\n    :class="`sw-flow-sequence-error--${errorType}`"\n>\n    <div\n        class="sw-flow-sequence-error__info"\n    >\n        <div class="sw-flow-sequence-error__header">\n            <div class="sw-flow-sequence-error__name">\n                <sw-icon\n                    size="12px"\n                    name="regular-exclamation-triangle"\n                />\n                <h3\n                    v-if="isActionError"\n                    class="sw-flow-sequence-error__heading sw-flow-sequence-error__heading-action"\n                >\n                    {{ $tc(\'sw-flow-sharing.importError.invalidActionHeading\') }}\n                </h3>\n\n                <h3\n                    v-if="isRuleError"\n                    class="sw-flow-sequence-error__heading sw-flow-sequence-error__heading-rule"\n                >\n                    {{ $tc(\'sw-flow-sharing.importError.invalidRuleHeading\') }}\n                </h3>\n\n                <h3\n                    v-if="isMissingRuleError"\n                    class="sw-flow-sequence-error__heading sw-flow-sequence-error__heading-rule"\n                >\n                    {{ $tc(\'sw-flow-sharing.importError.invalidRuleHeading\') }}\n                </h3>\n            </div>\n        </div>\n\n        <div\n            v-if="isActionError"\n            class="sw-flow-sequence-error__description sw-flow-sequence-error__description-action"\n        >\n            {{ $tc(\'sw-flow-sharing.importError.invalidActionText\') }}\n        </div>\n        <div\n            v-if="isRuleError"\n            class="sw-flow-sequence-error__description sw-flow-sequence-error__description-rule"\n        >\n            {{ $tc(\'sw-flow-sharing.importError.invalidRuleText\') }}\n        </div>\n        <div\n            v-if="isMissingRuleError"\n            class="sw-flow-sequence-error__description sw-flow-sequence-error__description-rule"\n        >\n            {{ $tc(\'sw-flow-sharing.importError.missingRuleText\') }}\n        </div>\n    </div>\n</div>\n',props:{sequence:{type:Object,required:!1,default:null}},computed:{errorType:function(){var e,r;return(null===(e=this.sequence)||void 0===e||null===(r=e.error)||void 0===r?void 0:r.type)||"action"},isActionError:function(){var e,r;return"action"===(null===(e=this.sequence)||void 0===e||null===(r=e.error)||void 0===r?void 0:r.type)},isRuleError:function(){var e,r;return"rule"===(null===(e=this.sequence)||void 0===e||null===(r=e.error)||void 0===r?void 0:r.type)},isMissingRuleError:function(){var e,r;return"missing-rule"===(null===(e=this.sequence)||void 0===e||null===(r=e.error)||void 0===r?void 0:r.type)}}}}}]);
//# sourceMappingURL=2.js.map
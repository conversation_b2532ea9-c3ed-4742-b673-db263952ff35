{"version": 3, "sources": ["webpack:////tmp/extension2400992207/SwagCommercial/src/FlowBuilder/FlowSharing/Resources/app/administration/src/module/sw-flow/component/sw-flow-sequence-action/sw-flow-sequence-action.html.twig", "webpack:////tmp/extension2400992207/SwagCommercial/src/FlowBuilder/FlowSharing/Resources/app/administration/src/module/sw-flow/component/sw-flow-sequence-action/index.ts"], "names": ["State", "Shopware", "template", "methods", "editAction", "action", "this", "currentSequence", "error", "Object", "keys", "length", "$super", "name", "commit", "id", "actionName", "config"], "mappings": "wIAAe,ICGPA,EAAUC,SAAVD,MAKO,WACXE,SDTW,gLCWXC,QAAS,CACLC,WAAU,SAACC,GACP,IAAKC,KAAKC,gBAAgBC,OAA4D,IAAnDC,OAAOC,KAAKJ,KAAKC,gBAAgBC,OAAOG,OACvE,OAAOL,KAAKM,OAAO,aAAcP,GAGhCA,EAAOQ,MAIZb,EAAMc,OAAO,6BAA8B,CACvCC,GAAIT,KAAKC,gBAAgBQ,GACzBC,WAAYX,EAAOQ,KACnBI,OAAQZ,EAAOY,OACfT,MAAO", "file": "static/js/8.js", "sourcesContent": ["export default \"{% block sw_flow_sequence_action_item_custom %}\\n<sw-flow-sequence-error\\n    v-if=\\\"item.error && Object.keys(item.error).length\\\"\\n    :sequence=\\\"item\\\"\\n/>\\n{% endblock %}\\n\";", "import template from './sw-flow-sequence-action.html.twig';\nimport type {Action} from '../../flow.types';\n\nconst { State } = Shopware;\n\n/**\n * @package services-settings\n */\nexport default {\n    template,\n\n    methods: {\n        editAction(action: Action): void {\n            if (!this.currentSequence.error || Object.keys(this.currentSequence.error).length === 0) {\n                return this.$super('editAction', action);\n            }\n\n            if (!action.name) {\n                return;\n            }\n\n            State.commit('swFlowState/updateSequence', {\n                id: this.currentSequence.id,\n                actionName: action.name,\n                config: action.config,\n                error: {}\n            });\n        },\n    }\n};\n"], "sourceRoot": ""}
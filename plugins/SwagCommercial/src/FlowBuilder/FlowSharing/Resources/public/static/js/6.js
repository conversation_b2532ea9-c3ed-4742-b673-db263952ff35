(this["webpackJsonpPluginflow-sharing"]=this["webpackJsonpPluginflow-sharing"]||[]).push([[6],{"3Jlm":function(e,t,s){"use strict";s.r(t);t.default={template:'{% block sw_flow_set_entity_custom_field_modal_custom %}\n<sw-flow-sequence-modal-error\n    v-if="sequence.error && Object.keys(sequence.error).length > 0"\n    :sequence="sequence"\n/>\n{% endblock %}\n',methods:{createdComponent:function(){this.getEntityOptions(),this.sequence.config&&(this.entity=this.sequence.config.entity,this.customFieldSetId=this.sequence.config.customFieldSetId,this.customFieldSetLabel=this.sequence.config.customFieldSetLabel,this.customFieldId=this.sequence.config.customFieldId,this.customFieldLabel=this.sequence.config.customFieldLabel,this.customFieldValue=this.sequence.config.customFieldValue,this.fieldOptionSelected=this.sequence.config.option,this.fieldOptions=this.defaultFieldOptions)}}}}}]);
//# sourceMappingURL=6.js.map
(this["webpackJsonpPluginflow-sharing"]=this["webpackJsonpPluginflow-sharing"]||[]).push([[0],{"1BP9":function(e,t,n){},"8q9Y":function(e,t,n){var a=n("1BP9");a.__esModule&&(a=a.default),"string"==typeof a&&(a=[[e.i,a,""]]),a.locals&&(e.exports=a.locals);(0,n("P8hj").default)("49ce0bde",a,!0,{})},HiZ0:function(e,t,n){"use strict";n.r(t);n("8q9Y");function a(e){return function(e){if(Array.isArray(e))return l(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(!e)return;if("string"==typeof e)return l(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return l(e,t)}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function l(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,a=new Array(t);n<t;n++)a[n]=e[n];return a}var o=Shopware.State;t.default={template:'<sw-modal\n    class="sw-flow-download-modal"\n    :title="$tc(\'sw-flow-sharing.downloadModal.title\')"\n    @modal-close="onCancel"\n>\n    <div class="sw-flow-download-modal__description">\n        <p v-html="$tc(\'sw-flow-sharing.downloadModal.description\')"></p>\n    </div>\n\n    <div class="sw-flow-download-modal__included">\n        <div class="sw-flow-download-modal__data-included">\n            <ul>\n                <li\n                    v-for="(rule, index) in rules"\n                    :key="index"\n                >\n                    <sw-checkbox-field\n                        class="sw-flow-download-modal__rule-item"\n                        :value="!!rule.id"\n                        :label="$tc(\'sw-flow-sharing.downloadModal.ruleLabel\',0, { ruleName: rule.name })"\n                        @change="(checked) => handleSelectRule(rule, checked)"\n                    />\n                </li>\n            </ul>\n\n            <ul>\n                <li\n                    v-for="(mail, index) in mailTemplates"\n                    :key="index"\n                >\n                    <sw-checkbox-field\n                        class="sw-flow-download-modal__mail-template-item"\n                        :value="!!mail.id"\n                        :label="$tc(\'sw-flow-sharing.downloadModal.mailTemplateLabel\',0, { mail: mail.mailTemplateTypeName })"\n                        @change="(checked) => handleSelectMail(mail, checked)"\n                    />\n                </li>\n            </ul>\n        </div>\n    </div>\n\n    <template #modal-footer>\n        <sw-button\n            class="sw-flow-download-modal__cancel-button"\n            size="small"\n            @click="onCancel"\n        >\n            {{ $tc(\'global.default.cancel\') }}\n        </sw-button>\n\n        <sw-button\n            class="sw-flow-download-modal__download-button"\n            variant="primary"\n            size="small"\n            :disabled="!acl.can(\'flow.viewer\')"\n            @click="onDownload"\n        >\n            {{ $tc(\'sw-flow-sharing.downloadModal.downloadButton\') }}\n        </sw-button>\n    </template>\n</sw-modal>\n',inject:["acl","repositoryFactory","flowSharingService"],props:{flowId:{type:String,required:!0}},data:function(){return{flowData:{},rules:[],mailTemplates:[],notSelectedRuleIds:[],notSelectedMailIds:[],hasError:!1}},computed:{downloadData:function(){var e=this.flowData.dataIncluded,t=e.rule,n=e.mail_template;t&&this.notSelectedRuleIds.length>0&&this.notSelectedRuleIds.forEach((function(e){delete t["".concat(e)]})),n&&this.notSelectedMailIds.length>0&&this.notSelectedMailIds.forEach((function(e){delete n["".concat(e)]}));var a=Object.assign({},this.flowData);return t&&Object.keys(t).length>0?a.dataIncluded.rule=t:delete a.dataIncluded.rule,n&&Object.keys(n).length>0?a.dataIncluded.mail_template=n:delete a.dataIncluded.mail_template,a}},created:function(){this.createdComponent()},methods:{createdComponent:function(){this.getDataIncluded()},onCancel:function(){this.$emit("modal-close")},onDownload:function(){if(this.hasError||this.flowData.length<=0)this.$emit("download-finish",!1);else{var e="".concat(this.flowData.flow.name,".json"),t=document.createElement("a");t.href="data:application/json;charset=utf-8,".concat(encodeURIComponent(JSON.stringify(this.downloadData))),t.download=e,t.dispatchEvent(new MouseEvent("click")),t.remove(),this.$emit("download-finish",!0)}},getDataIncluded:function(){var e=this;this.flowSharingService.downloadFlow(this.flowId).then((function(t){(e.flowData=t,t.dataIncluded&&t.dataIncluded.rule&&(e.rules=Object.values(t.dataIncluded.rule)),t.dataIncluded&&t.dataIncluded.mail_template)&&Object.values(t.dataIncluded.mail_template).forEach((function(t){var n=t.find((function(e){return e.locale===o.get("session").currentLocale}));n&&e.mailTemplates.push(n)}))})).catch((function(){e.hasError=!0}))},handleSelectRule:function(e,t){this.notSelectedRuleIds=t?this.notSelectedRuleIds.filter((function(t){return t!==e.id})):[].concat(a(this.notSelectedRuleIds),[e.id])},handleSelectMail:function(e,t){this.notSelectedMailIds=t?this.notSelectedMailIds.filter((function(t){return t!==e.id})):[].concat(a(this.notSelectedMailIds),[e.id])}}}},P8hj:function(e,t,n){"use strict";function a(e,t){for(var n=[],a={},l=0;l<t.length;l++){var o=t[l],r=o[0],i={id:e+":"+l,css:o[1],media:o[2],sourceMap:o[3]};a[r]?a[r].parts.push(i):n.push(a[r]={id:r,parts:[i]})}return n}n.r(t),n.d(t,"default",(function(){return m}));var l="undefined"!=typeof document;if("undefined"!=typeof DEBUG&&DEBUG&&!l)throw new Error("vue-style-loader cannot be used in a non-browser environment. Use { target: 'node' } in your Webpack config to indicate a server-rendering environment.");var o={},r=l&&(document.head||document.getElementsByTagName("head")[0]),i=null,d=0,s=!1,c=function(){},u=null,f="data-vue-ssr-id",h="undefined"!=typeof navigator&&/msie [6-9]\b/.test(navigator.userAgent.toLowerCase());function m(e,t,n,l){s=n,u=l||{};var r=a(e,t);return p(r),function(t){for(var n=[],l=0;l<r.length;l++){var i=r[l];(d=o[i.id]).refs--,n.push(d)}t?p(r=a(e,t)):r=[];for(l=0;l<n.length;l++){var d;if(0===(d=n[l]).refs){for(var s=0;s<d.parts.length;s++)d.parts[s]();delete o[d.id]}}}}function p(e){for(var t=0;t<e.length;t++){var n=e[t],a=o[n.id];if(a){a.refs++;for(var l=0;l<a.parts.length;l++)a.parts[l](n.parts[l]);for(;l<n.parts.length;l++)a.parts.push(v(n.parts[l]));a.parts.length>n.parts.length&&(a.parts.length=n.parts.length)}else{var r=[];for(l=0;l<n.parts.length;l++)r.push(v(n.parts[l]));o[n.id]={id:n.id,refs:1,parts:r}}}}function w(){var e=document.createElement("style");return e.type="text/css",r.appendChild(e),e}function v(e){var t,n,a=document.querySelector("style["+f+'~="'+e.id+'"]');if(a){if(s)return c;a.parentNode.removeChild(a)}if(h){var l=d++;a=i||(i=w()),t=y.bind(null,a,l,!1),n=y.bind(null,a,l,!0)}else a=w(),t=S.bind(null,a),n=function(){a.parentNode.removeChild(a)};return t(e),function(a){if(a){if(a.css===e.css&&a.media===e.media&&a.sourceMap===e.sourceMap)return;t(e=a)}else n()}}var g,b=(g=[],function(e,t){return g[e]=t,g.filter(Boolean).join("\n")});function y(e,t,n,a){var l=n?"":a.css;if(e.styleSheet)e.styleSheet.cssText=b(t,l);else{var o=document.createTextNode(l),r=e.childNodes;r[t]&&e.removeChild(r[t]),r.length?e.insertBefore(o,r[t]):e.appendChild(o)}}function S(e,t){var n=t.css,a=t.media,l=t.sourceMap;if(a&&e.setAttribute("media",a),u.ssrId&&e.setAttribute(f,t.id),l&&(n+="\n/*# sourceURL="+l.sources[0]+" */",n+="\n/*# sourceMappingURL=data:application/json;base64,"+btoa(unescape(encodeURIComponent(JSON.stringify(l))))+" */"),e.styleSheet)e.styleSheet.cssText=n;else{for(;e.firstChild;)e.removeChild(e.firstChild);e.appendChild(document.createTextNode(n))}}}}]);
//# sourceMappingURL=0.js.map
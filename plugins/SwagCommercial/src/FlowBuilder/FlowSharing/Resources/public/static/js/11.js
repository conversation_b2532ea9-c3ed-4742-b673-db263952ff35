(this["webpackJsonpPluginflow-sharing"]=this["webpackJsonpPluginflow-sharing"]||[]).push([[11],{"0SWc":function(o,n,l){"use strict";l.r(n);n.default={template:'{% block sw_flow_index_smart_bar_actions_extension %}\n    {% block sw_flow_index_smart_bar_actions_import %}\n    <sw-button\n        v-if="getLicense(\'FLOW_BUILDER-4142679\')"\n        v-tooltip="{\n            message: $tc(\'sw-privileges.tooltip.warning\'),\n            disabled: acl.can(\'flow.creator\'),\n            position: \'bottom\',\n            showOnDisabledElements: true\n        }"\n        class="sw-flow-list__import"\n        @click="openUploadModal"\n    >\n        <sw-icon\n            name="regular-upload"\n            color="#758CA3"\n            size="16"\n        />\n        {{ $tc(\'sw-flow-sharing.uploadButton\') }}\n    </sw-button>\n    {% endblock %}\n{% endblock %}\n\n{% block sw_flow_index_modal_content_custom %}\n    {% block sw_flow_index_upload_modal %}\n        <sw-flow-upload-modal\n            v-if="showUploadModal"\n            @modal-upload-finished="onUploadFlowTemplateFile"\n            @modal-close="onCloseUploadModal"\n        />\n    {% endblock %}\n{% endblock %}\n',inject:["feature"],data:function(){return{showUploadModal:!1}},methods:{getLicense:function(o){return Shopware.License.get(o)},openUploadModal:function(){this.showUploadModal=!0},onCloseUploadModal:function(){this.showUploadModal=!1},onUploadFlowTemplateFile:function(){this.feature.isActive("VUE3")?this.$router.push({name:"sw.flow.create.general",query:{isUploading:!0}}):this.$router.push({name:"sw.flow.create.general",params:{isUploading:!0}})}}}}}]);
//# sourceMappingURL=11.js.map
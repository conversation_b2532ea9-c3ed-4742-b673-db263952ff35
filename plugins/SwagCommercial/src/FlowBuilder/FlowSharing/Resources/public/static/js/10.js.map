{"version": 3, "sources": ["webpack:////tmp/extension2400992207/SwagCommercial/src/FlowBuilder/FlowSharing/Resources/app/administration/src/module/sw-flow/page/sw-flow-detail/index.ts"], "names": ["_regeneratorRuntime", "exports", "Op", "Object", "prototype", "hasOwn", "hasOwnProperty", "defineProperty", "obj", "key", "desc", "value", "$Symbol", "Symbol", "iteratorSymbol", "iterator", "asyncIteratorSymbol", "asyncIterator", "toStringTagSymbol", "toStringTag", "define", "enumerable", "configurable", "writable", "err", "wrap", "innerFn", "outerFn", "self", "tryLocsList", "protoGenerator", "Generator", "generator", "create", "context", "Context", "makeInvokeMethod", "tryCatch", "fn", "arg", "type", "call", "ContinueSentinel", "GeneratorFunction", "GeneratorFunctionPrototype", "IteratorPrototype", "getProto", "getPrototypeOf", "NativeIteratorPrototype", "values", "Gp", "defineIteratorMethods", "for<PERSON>ach", "method", "_invoke", "AsyncIterator", "PromiseImpl", "invoke", "resolve", "reject", "record", "result", "_typeof", "__await", "then", "unwrapped", "error", "previousPromise", "callInvokeWithMethodAndArg", "state", "Error", "doneResult", "delegate", "delegate<PERSON><PERSON><PERSON>", "maybeInvokeDelegate", "sent", "_sent", "dispatchException", "abrupt", "done", "methodName", "undefined", "return", "TypeError", "info", "resultName", "next", "nextLoc", "pushTryEntry", "locs", "entry", "tryLoc", "catchLoc", "finallyLoc", "afterLoc", "tryEntries", "push", "resetTryEntry", "completion", "reset", "iterable", "iteratorMethod", "isNaN", "length", "i", "displayName", "isGeneratorFunction", "gen<PERSON>un", "ctor", "constructor", "name", "mark", "setPrototypeOf", "__proto__", "awrap", "async", "Promise", "iter", "keys", "val", "object", "reverse", "pop", "skip<PERSON>emp<PERSON><PERSON><PERSON>", "prev", "char<PERSON>t", "slice", "stop", "rootRecord", "rval", "exception", "handle", "loc", "caught", "hasCatch", "hasFinally", "finallyEntry", "complete", "finish", "catch", "thrown", "<PERSON><PERSON><PERSON>", "asyncGeneratorStep", "gen", "_next", "_throw", "_asyncToGenerator", "args", "arguments", "apply", "_Shopware", "Shopware", "State", "Utils", "Criteria", "Data", "cloneDeep", "inject", "data", "dataIncluded", "referenceIncluded", "invalidEntityIds", "computed", "flowRepository", "this", "repositoryFactory", "flowSequenceRepository", "flowSharingState", "get", "isUploading", "_this$$route$params", "_this$$route$query", "feature", "isActive", "$route", "query", "params", "methods", "createdComponent", "_this", "_callee", "_context", "getInvalidEntityIds", "$super", "routeDetailTab", "tabName", "concat", "createNewFlow", "flow", "priority", "eventName", "commit", "flowObject", "restoreImportedFlow", "_this2", "id", "createId", "includes", "Boolean", "sequences", "buildSequencesFromConfig", "validateSequences", "expression", "getDataForActionDescription", "addRuleErrors", "addActionErrors", "_this3", "actionName", "rule", "ruleId", "assign", "generateErrorObject", "hasInvalidReference", "_this4", "ACTION_ENTITY", "entity", "invalidIds", "getEntityIds", "config", "filter", "tagIds", "customerGroupId", "customFieldId", "mailTemplateId", "_this5", "_callee2", "entities", "_loop", "_i", "_Object$keys", "ruleReferences", "_loop2", "_i2", "_Object$keys2", "_context4", "_this5$invalidEntityI", "repository", "criteria", "existingIds", "_context2", "addFilter", "equalsAny", "searchIds", "_toConsumableArray", "getReferenceIncludedFromRule", "_this5$invalidEntityI2", "_context3", "errorDetail", "Array", "isArray", "entityReference", "item", "locale", "currentLocale", "_defineProperty", "_this6", "conditions", "every", "condition", "entityIds", "getEntityIdsOfCondition", "getters", "fields", "_this7", "_ruleReferences$entit", "entityName", "onSave", "sequence", "createNotificationError", "message", "$tc"], "mappings": ";ukDACAA,EAAA,kBAAAC,GAAA,IAAAA,EAAA,GAAAC,EAAAC,OAAAC,UAAAC,EAAAH,EAAAI,eAAAC,EAAAJ,OAAAI,gBAAA,SAAAC,EAAAC,EAAAC,GAAAF,EAAAC,GAAAC,EAAAC,OAAAC,EAAA,mBAAAC,cAAA,GAAAC,EAAAF,EAAAG,UAAA,aAAAC,EAAAJ,EAAAK,eAAA,kBAAAC,EAAAN,EAAAO,aAAA,yBAAAC,EAAAZ,EAAAC,EAAAE,GAAA,OAAAR,OAAAI,eAAAC,EAAAC,EAAA,CAAAE,QAAAU,YAAA,EAAAC,cAAA,EAAAC,UAAA,IAAAf,EAAAC,GAAA,IAAAW,EAAA,aAAAI,GAAAJ,EAAA,SAAAZ,EAAAC,EAAAE,GAAA,OAAAH,EAAAC,GAAAE,GAAA,SAAAc,EAAAC,EAAAC,EAAAC,EAAAC,GAAA,IAAAC,EAAAH,KAAAvB,qBAAA2B,EAAAJ,EAAAI,EAAAC,EAAA7B,OAAA8B,OAAAH,EAAA1B,WAAA8B,EAAA,IAAAC,EAAAN,GAAA,WAAAtB,EAAAyB,EAAA,WAAArB,MAAAyB,EAAAV,EAAAE,EAAAM,KAAAF,EAAA,SAAAK,EAAAC,EAAA9B,EAAA+B,GAAA,WAAAC,KAAA,SAAAD,IAAAD,EAAAG,KAAAjC,EAAA+B,IAAA,MAAAf,GAAA,OAAAgB,KAAA,QAAAD,IAAAf,IAAAvB,EAAAwB,OAAA,IAAAiB,EAAA,YAAAX,KAAA,SAAAY,KAAA,SAAAC,KAAA,IAAAC,EAAA,GAAAzB,EAAAyB,EAAA/B,GAAA,8BAAAgC,EAAA3C,OAAA4C,eAAAC,EAAAF,OAAAG,EAAA,MAAAD,OAAA9C,GAAAG,EAAAoC,KAAAO,EAAAlC,KAAA+B,EAAAG,GAAA,IAAAE,EAAAN,EAAAxC,UAAA2B,EAAA3B,UAAAD,OAAA8B,OAAAY,GAAA,SAAAM,EAAA/C,GAAA,0BAAAgD,SAAA,SAAAC,GAAAjC,EAAAhB,EAAAiD,GAAA,SAAAd,GAAA,YAAAe,QAAAD,EAAAd,SAAA,SAAAgB,EAAAvB,EAAAwB,GAAA,SAAAC,EAAAJ,EAAAd,EAAAmB,EAAAC,GAAA,IAAAC,EAAAvB,EAAAL,EAAAqB,GAAArB,EAAAO,GAAA,aAAAqB,EAAApB,KAAA,KAAAqB,EAAAD,EAAArB,IAAA5B,EAAAkD,EAAAlD,MAAA,OAAAA,GAAA,UAAAmD,EAAAnD,IAAAN,EAAAoC,KAAA9B,EAAA,WAAA6C,EAAAE,QAAA/C,EAAAoD,SAAAC,MAAA,SAAArD,GAAA8C,EAAA,OAAA9C,EAAA+C,EAAAC,MAAA,SAAAnC,GAAAiC,EAAA,QAAAjC,EAAAkC,EAAAC,MAAAH,EAAAE,QAAA/C,GAAAqD,MAAA,SAAAC,GAAAJ,EAAAlD,MAAAsD,EAAAP,EAAAG,MAAA,SAAAK,GAAA,OAAAT,EAAA,QAAAS,EAAAR,EAAAC,QAAAC,EAAArB,KAAA,IAAA4B,EAAA5D,EAAA,gBAAAI,MAAA,SAAA0C,EAAAd,GAAA,SAAA6B,IAAA,WAAAZ,GAAA,SAAAE,EAAAC,GAAAF,EAAAJ,EAAAd,EAAAmB,EAAAC,MAAA,OAAAQ,MAAAH,KAAAI,YAAA,SAAAhC,EAAAV,EAAAE,EAAAM,GAAA,IAAAmC,EAAA,iCAAAhB,EAAAd,GAAA,iBAAA8B,EAAA,UAAAC,MAAA,iDAAAD,EAAA,cAAAhB,EAAA,MAAAd,EAAA,OAAAgC,IAAA,IAAArC,EAAAmB,SAAAnB,EAAAK,QAAA,KAAAiC,EAAAtC,EAAAsC,SAAA,GAAAA,EAAA,KAAAC,EAAAC,EAAAF,EAAAtC,GAAA,GAAAuC,EAAA,IAAAA,IAAA/B,EAAA,gBAAA+B,GAAA,YAAAvC,EAAAmB,OAAAnB,EAAAyC,KAAAzC,EAAA0C,MAAA1C,EAAAK,SAAA,aAAAL,EAAAmB,OAAA,uBAAAgB,EAAA,MAAAA,EAAA,YAAAnC,EAAAK,IAAAL,EAAA2C,kBAAA3C,EAAAK,SAAA,WAAAL,EAAAmB,QAAAnB,EAAA4C,OAAA,SAAA5C,EAAAK,KAAA8B,EAAA,gBAAAT,EAAAvB,EAAAX,EAAAE,EAAAM,GAAA,cAAA0B,EAAApB,KAAA,IAAA6B,EAAAnC,EAAA6C,KAAA,6BAAAnB,EAAArB,MAAAG,EAAA,gBAAA/B,MAAAiD,EAAArB,IAAAwC,KAAA7C,EAAA6C,MAAA,UAAAnB,EAAApB,OAAA6B,EAAA,YAAAnC,EAAAmB,OAAA,QAAAnB,EAAAK,IAAAqB,EAAArB,OAAA,SAAAmC,EAAAF,EAAAtC,GAAA,IAAA8C,EAAA9C,EAAAmB,SAAAmB,EAAAzD,SAAAiE,GAAA,QAAAC,IAAA5B,EAAA,OAAAnB,EAAAsC,SAAA,eAAAQ,GAAAR,EAAAzD,SAAAmE,SAAAhD,EAAAmB,OAAA,SAAAnB,EAAAK,SAAA0C,EAAAP,EAAAF,EAAAtC,GAAA,UAAAA,EAAAmB,SAAA,WAAA2B,IAAA9C,EAAAmB,OAAA,QAAAnB,EAAAK,IAAA,IAAA4C,UAAA,oCAAAH,EAAA,aAAAtC,EAAA,IAAAkB,EAAAvB,EAAAgB,EAAAmB,EAAAzD,SAAAmB,EAAAK,KAAA,aAAAqB,EAAApB,KAAA,OAAAN,EAAAmB,OAAA,QAAAnB,EAAAK,IAAAqB,EAAArB,IAAAL,EAAAsC,SAAA,KAAA9B,EAAA,IAAA0C,EAAAxB,EAAArB,IAAA,OAAA6C,IAAAL,MAAA7C,EAAAsC,EAAAa,YAAAD,EAAAzE,MAAAuB,EAAAoD,KAAAd,EAAAe,QAAA,WAAArD,EAAAmB,SAAAnB,EAAAmB,OAAA,OAAAnB,EAAAK,SAAA0C,GAAA/C,EAAAsC,SAAA,KAAA9B,GAAA0C,GAAAlD,EAAAmB,OAAA,QAAAnB,EAAAK,IAAA,IAAA4C,UAAA,oCAAAjD,EAAAsC,SAAA,KAAA9B,GAAA,SAAA8C,EAAAC,GAAA,IAAAC,EAAA,CAAAC,OAAAF,EAAA,SAAAA,IAAAC,EAAAE,SAAAH,EAAA,SAAAA,IAAAC,EAAAG,WAAAJ,EAAA,GAAAC,EAAAI,SAAAL,EAAA,SAAAM,WAAAC,KAAAN,GAAA,SAAAO,EAAAP,GAAA,IAAA9B,EAAA8B,EAAAQ,YAAA,GAAAtC,EAAApB,KAAA,gBAAAoB,EAAArB,IAAAmD,EAAAQ,WAAAtC,EAAA,SAAAzB,EAAAN,GAAA,KAAAkE,WAAA,EAAAJ,OAAA,SAAA9D,EAAAuB,QAAAoC,EAAA,WAAAW,OAAA,YAAAlD,EAAAmD,GAAA,GAAAA,EAAA,KAAAC,EAAAD,EAAAtF,GAAA,GAAAuF,EAAA,OAAAA,EAAA5D,KAAA2D,GAAA,sBAAAA,EAAAd,KAAA,OAAAc,EAAA,IAAAE,MAAAF,EAAAG,QAAA,KAAAC,GAAA,EAAAlB,EAAA,SAAAA,IAAA,OAAAkB,EAAAJ,EAAAG,QAAA,GAAAlG,EAAAoC,KAAA2D,EAAAI,GAAA,OAAAlB,EAAA3E,MAAAyF,EAAAI,GAAAlB,EAAAP,MAAA,EAAAO,EAAA,OAAAA,EAAA3E,WAAAsE,EAAAK,EAAAP,MAAA,EAAAO,GAAA,OAAAA,UAAA,OAAAA,KAAAf,GAAA,SAAAA,IAAA,OAAA5D,WAAAsE,EAAAF,MAAA,UAAApC,EAAAvC,UAAAwC,EAAArC,EAAA2C,EAAA,eAAAvC,MAAAiC,EAAAtB,cAAA,IAAAf,EAAAqC,EAAA,eAAAjC,MAAAgC,EAAArB,cAAA,IAAAqB,EAAA8D,YAAArF,EAAAwB,EAAA1B,EAAA,qBAAAjB,EAAAyG,oBAAA,SAAAC,GAAA,IAAAC,EAAA,mBAAAD,KAAAE,YAAA,QAAAD,QAAAjE,GAAA,uBAAAiE,EAAAH,aAAAG,EAAAE,QAAA7G,EAAA8G,KAAA,SAAAJ,GAAA,OAAAxG,OAAA6G,eAAA7G,OAAA6G,eAAAL,EAAA/D,IAAA+D,EAAAM,UAAArE,EAAAxB,EAAAuF,EAAAzF,EAAA,sBAAAyF,EAAAvG,UAAAD,OAAA8B,OAAAiB,GAAAyD,GAAA1G,EAAAiH,MAAA,SAAA3E,GAAA,OAAAwB,QAAAxB,IAAAY,EAAAI,EAAAnD,WAAAgB,EAAAmC,EAAAnD,UAAAY,GAAA,0BAAAf,EAAAsD,gBAAAtD,EAAAkH,MAAA,SAAAzF,EAAAC,EAAAC,EAAAC,EAAA2B,QAAA,IAAAA,MAAA4D,SAAA,IAAAC,EAAA,IAAA9D,EAAA9B,EAAAC,EAAAC,EAAAC,EAAAC,GAAA2B,GAAA,OAAAvD,EAAAyG,oBAAA/E,GAAA0F,IAAA/B,OAAAtB,MAAA,SAAAH,GAAA,OAAAA,EAAAkB,KAAAlB,EAAAlD,MAAA0G,EAAA/B,WAAAnC,EAAAD,GAAA9B,EAAA8B,EAAAhC,EAAA,aAAAE,EAAA8B,EAAApC,GAAA,0BAAAM,EAAA8B,EAAA,qDAAAjD,EAAAqH,KAAA,SAAAC,GAAA,IAAAC,EAAArH,OAAAoH,GAAAD,EAAA,WAAA7G,KAAA+G,EAAAF,EAAAtB,KAAAvF,GAAA,OAAA6G,EAAAG,UAAA,SAAAnC,IAAA,KAAAgC,EAAAf,QAAA,KAAA9F,EAAA6G,EAAAI,MAAA,GAAAjH,KAAA+G,EAAA,OAAAlC,EAAA3E,MAAAF,EAAA6E,EAAAP,MAAA,EAAAO,EAAA,OAAAA,EAAAP,MAAA,EAAAO,IAAArF,EAAAgD,SAAAd,EAAA/B,UAAA,CAAAyG,YAAA1E,EAAAgE,MAAA,SAAAwB,GAAA,QAAAC,KAAA,OAAAtC,KAAA,OAAAX,KAAA,KAAAC,WAAAK,EAAA,KAAAF,MAAA,OAAAP,SAAA,UAAAnB,OAAA,YAAAd,SAAA0C,EAAA,KAAAc,WAAA3C,QAAA6C,IAAA0B,EAAA,QAAAb,KAAA,WAAAA,EAAAe,OAAA,IAAAxH,EAAAoC,KAAA,KAAAqE,KAAAR,OAAAQ,EAAAgB,MAAA,WAAAhB,QAAA7B,IAAA8C,KAAA,gBAAAhD,MAAA,MAAAiD,EAAA,KAAAjC,WAAA,GAAAG,WAAA,aAAA8B,EAAAxF,KAAA,MAAAwF,EAAAzF,IAAA,YAAA0F,MAAApD,kBAAA,SAAAqD,GAAA,QAAAnD,KAAA,MAAAmD,EAAA,IAAAhG,EAAA,cAAAiG,EAAAC,EAAAC,GAAA,OAAAzE,EAAApB,KAAA,QAAAoB,EAAArB,IAAA2F,EAAAhG,EAAAoD,KAAA8C,EAAAC,IAAAnG,EAAAmB,OAAA,OAAAnB,EAAAK,SAAA0C,KAAAoD,EAAA,QAAA7B,EAAA,KAAAT,WAAAQ,OAAA,EAAAC,GAAA,IAAAA,EAAA,KAAAd,EAAA,KAAAK,WAAAS,GAAA5C,EAAA8B,EAAAQ,WAAA,YAAAR,EAAAC,OAAA,OAAAwC,EAAA,UAAAzC,EAAAC,QAAA,KAAAiC,KAAA,KAAAU,EAAAjI,EAAAoC,KAAAiD,EAAA,YAAA6C,EAAAlI,EAAAoC,KAAAiD,EAAA,iBAAA4C,GAAAC,EAAA,SAAAX,KAAAlC,EAAAE,SAAA,OAAAuC,EAAAzC,EAAAE,UAAA,WAAAgC,KAAAlC,EAAAG,WAAA,OAAAsC,EAAAzC,EAAAG,iBAAA,GAAAyC,GAAA,QAAAV,KAAAlC,EAAAE,SAAA,OAAAuC,EAAAzC,EAAAE,UAAA,YAAA2C,EAAA,UAAAjE,MAAA,kDAAAsD,KAAAlC,EAAAG,WAAA,OAAAsC,EAAAzC,EAAAG,gBAAAf,OAAA,SAAAtC,EAAAD,GAAA,QAAAiE,EAAA,KAAAT,WAAAQ,OAAA,EAAAC,GAAA,IAAAA,EAAA,KAAAd,EAAA,KAAAK,WAAAS,GAAA,GAAAd,EAAAC,QAAA,KAAAiC,MAAAvH,EAAAoC,KAAAiD,EAAA,oBAAAkC,KAAAlC,EAAAG,WAAA,KAAA2C,EAAA9C,EAAA,OAAA8C,IAAA,UAAAhG,GAAA,aAAAA,IAAAgG,EAAA7C,QAAApD,MAAAiG,EAAA3C,aAAA2C,EAAA,UAAA5E,EAAA4E,IAAAtC,WAAA,UAAAtC,EAAApB,OAAAoB,EAAArB,MAAAiG,GAAA,KAAAnF,OAAA,YAAAiC,KAAAkD,EAAA3C,WAAAnD,GAAA,KAAA+F,SAAA7E,IAAA6E,SAAA,SAAA7E,EAAAkC,GAAA,aAAAlC,EAAApB,KAAA,MAAAoB,EAAArB,IAAA,gBAAAqB,EAAApB,MAAA,aAAAoB,EAAApB,KAAA,KAAA8C,KAAA1B,EAAArB,IAAA,WAAAqB,EAAApB,MAAA,KAAAyF,KAAA,KAAA1F,IAAAqB,EAAArB,IAAA,KAAAc,OAAA,cAAAiC,KAAA,kBAAA1B,EAAApB,MAAAsD,IAAA,KAAAR,KAAAQ,GAAApD,GAAAgG,OAAA,SAAA7C,GAAA,QAAAW,EAAA,KAAAT,WAAAQ,OAAA,EAAAC,GAAA,IAAAA,EAAA,KAAAd,EAAA,KAAAK,WAAAS,GAAA,GAAAd,EAAAG,eAAA,YAAA4C,SAAA/C,EAAAQ,WAAAR,EAAAI,UAAAG,EAAAP,GAAAhD,IAAAiG,MAAA,SAAAhD,GAAA,QAAAa,EAAA,KAAAT,WAAAQ,OAAA,EAAAC,GAAA,IAAAA,EAAA,KAAAd,EAAA,KAAAK,WAAAS,GAAA,GAAAd,EAAAC,WAAA,KAAA/B,EAAA8B,EAAAQ,WAAA,aAAAtC,EAAApB,KAAA,KAAAoG,EAAAhF,EAAArB,IAAA0D,EAAAP,GAAA,OAAAkD,GAAA,UAAAtE,MAAA,0BAAAuE,cAAA,SAAAzC,EAAAf,EAAAE,GAAA,YAAAf,SAAA,CAAAzD,SAAAkC,EAAAmD,GAAAf,aAAAE,WAAA,cAAAlC,SAAA,KAAAd,SAAA0C,GAAAvC,IAAAzC,EAAA,SAAA6I,EAAAC,EAAArF,EAAAC,EAAAqF,EAAAC,EAAAxI,EAAA8B,GAAA,QAAA6C,EAAA2D,EAAAtI,GAAA8B,GAAA5B,EAAAyE,EAAAzE,MAAA,MAAAuD,GAAA,YAAAP,EAAAO,GAAAkB,EAAAL,KAAArB,EAAA/C,GAAAyG,QAAA1D,QAAA/C,GAAAqD,KAAAgF,EAAAC,GAAA,SAAAC,EAAA5G,GAAA,sBAAAV,EAAA,KAAAuH,EAAAC,UAAA,WAAAhC,SAAA,SAAA1D,EAAAC,GAAA,IAAAoF,EAAAzG,EAAA+G,MAAAzH,EAAAuH,GAAA,SAAAH,EAAArI,GAAAmI,EAAAC,EAAArF,EAAAC,EAAAqF,EAAAC,EAAA,OAAAtI,GAAA,SAAAsI,EAAAzH,GAAAsH,EAAAC,EAAArF,EAAAC,EAAAqF,EAAAC,EAAA,QAAAzH,GAAAwH,OAAA/D,OAgBA,IAAAqE,EAAyBC,SAAjBC,EAAKF,EAALE,MAAOC,EAAKH,EAALG,MACPC,EAAaH,SAASI,KAAtBD,SACAE,EAAcH,EAAMjC,OAApBoC,UAKO,WACXC,OAAQ,CAAC,WAETC,KAAI,WAKA,MAAO,CACHC,aAAc,GACdC,kBAAmB,GACnBC,iBAAkB,KAI1BC,SAAU,CACNC,eAAc,WACV,OAAOC,KAAKC,kBAAkBpI,OAAO,SAGzCqI,uBAAsB,WAClB,OAAOF,KAAKC,kBAAkBpI,OAAO,kBAGzCsI,iBAAgB,WACZ,OAAOf,EAAMgB,IAAI,uBAGrBC,YAAW,WAAI,IAADC,EACyBC,EAAnC,OAAIP,KAAKQ,QAAQC,SAAS,SACsB,KAAlB,QAAlBF,EAACP,KAAKU,OAAOC,aAAK,IAAAJ,IAAjBA,EAAmBF,cAGW,KAAlB,QAAlBC,EAAAN,KAAKU,OAAOE,cAAM,IAAAN,OAAA,EAAlBA,EAAoBD,eAInCQ,QAAS,CACCC,iBAAgB,WAAmB,IAADC,EAAA,YAAAjC,EAAAlJ,IAAA+G,MAAA,SAAAqE,IAAA,OAAApL,IAAAyB,MAAA,SAAA4J,GAAA,cAAAA,EAAAzD,KAAAyD,EAAA/F,MAAA,WAChC6F,EAAKV,YAAY,CAADY,EAAA/F,KAAA,QAEuC,OADvD6F,EAAKnB,kBAAoBmB,EAAKZ,iBAAiBP,kBAC/CmB,EAAKpB,aAAeoB,EAAKZ,iBAAiBR,aAAasB,EAAA/F,KAAA,EAEjD6F,EAAKG,sBAAsB,KAAD,EAGpCH,EAAKI,OAAO,oBAAoB,wBAAAF,EAAAtD,UAAAqD,MARIlC,IAWxCsC,eAAc,SAACC,GAMX,OAAKrB,KAAKK,YAINL,KAAKQ,QAAQC,SAAS,QACf,CAAE/D,KAAK,kBAAD4E,OAAoBD,GAAWV,MAAO,CAAEN,aAAa,IAG/D,CAAE3D,KAAK,kBAAD4E,OAAoBD,GAAWT,OAAQ,CAAEP,aAAa,IAPxDL,KAAKmB,OAAO,iBAAkBE,IAU7CE,cAAa,WACT,IAAKvB,KAAKK,YACN,OAAOL,KAAKmB,OAAO,iBAGvB,IAAMK,EAAOxB,KAAKD,eAAelI,SACjC2J,EAAKC,SAAW,EAChBD,EAAKE,UAAY,GAEjBtC,EAAMuC,OAAO,sBAAuBH,GAEpC,IAAMI,EAAa5B,KAAKG,iBAAiBqB,KAErCI,GACA5B,KAAK6B,oBAAoBD,IAIjCC,oBAAmB,SAACD,GAAyB,IAADE,EAAA,KACxC,GAAuC,IAAnC/L,OAAOmH,KAAK0E,GAAYzF,OAA5B,CAGA6D,KAAKwB,KAAKO,GAAK1C,EAAM2C,WAErBjM,OAAOmH,KAAK0E,GAAY5I,SAAQ,SAAC3C,GACzB,CAAC,KAAM,aAAa4L,SAAS5L,KAIjCyL,EAAKN,KAAKnL,GAAe,WAARA,EACX6L,QAAQN,EAAWvL,IACnBuL,EAAWvL,OAGrB,IAAI8L,EAAYP,aAAU,EAAVA,EAAYO,UAC5BA,EAAYA,EAAYnC,KAAKoC,yBAAyBpC,KAAKqC,kBAAkBF,IAAc,GAE3F/C,EAAMuC,OAAO,sBAAuB3B,KAAKwB,MACzCpC,EAAMuC,OAAO,2BAA4BC,aAAU,EAAVA,EAAYF,WAErDtC,EAAMuC,OAAO,uBAAwB,CACjCW,WAAW,QAADhB,OAAUtB,KAAKwB,KAAKO,GAAE,gBAGpC3C,EAAMuC,OAAO,2BAA4BQ,GACzC/C,EAAMuC,OAAO,4BAA6BnC,EAAUQ,KAAKwB,OACzDxB,KAAKuC,gCAGTF,kBAAiB,SAACF,GAOd,OANInC,KAAKL,aAAmB,OACxBwC,EAAYnC,KAAKwC,cAAcL,IAGnCA,EAAYnC,KAAKyC,gBAAgBN,IAKrCK,cAAa,SAACL,GAA8C,IAADO,EAAA,KA4BvD,OA3BA3M,OAAOmH,KAAKiF,GAAWnJ,SAAQ,SAAC3C,GACM,OAA9B8L,EAAU9L,GAAKsM,aAInBR,EAAU9L,GAAKuM,KAAOF,EAAK/C,aAAaiD,KAAKT,EAAU9L,GAAKwM,QAC5DV,EAAU9L,GAAKuM,KAAKrM,MAAQR,OAAO+M,OAAO,GAAIX,EAAU9L,GAAKuM,KAAKrM,OAE9DmM,EAAK7C,iBAAiBoC,SAASE,EAAU9L,GAAKwM,QAC9CV,EAAU9L,GAAKyD,MAAQ4I,EAAKK,oBACxB,eACA,OACA,CAACZ,EAAU9L,GAAKwM,SAMpBH,EAAKM,oBAAoBb,EAAU9L,GAAKuM,QACxCT,EAAU9L,GAAKyD,MAAQ4I,EAAKK,oBACxB,OACA,OACA,CAACZ,EAAU9L,GAAKwM,cAKrBV,GAGXM,gBAAe,SAACN,GAA8C,IAADc,EAAA,KAqBzD,OApBAlN,OAAOmH,KAAKiF,GAAWnJ,SAAQ,SAAC3C,GAC5B,GAAkC,OAA9B8L,EAAU9L,GAAKsM,iBAI8B9H,IAA7CqI,IAAcf,EAAU9L,GAAKsM,YAAjC,CAIA,IAAMQ,EAASD,IAAcf,EAAU9L,GAAKsM,YACtCS,EAAaH,EAAKI,aAAaF,EAAQhB,EAAU9L,GAAKiN,QAAQC,QAAO,SAACxB,GACxE,OAAOkB,EAAKpD,iBAAiBoC,SAASF,MAGhB,IAAtBqB,EAAWjH,SAGfgG,EAAU9L,GAAKyD,MAAQmJ,EAAKF,oBAAoB,SAAUI,EAAQC,QAG/DjB,GAGXkB,aAAY,SAACF,EAAgBG,GACzB,OAAQH,GACJ,IAAK,MACD,OAAOpN,OAAOmH,KAAKoG,EAAOE,QAC9B,IAAK,iBACD,MAAO,CAACF,EAAOG,iBACnB,IAAK,eACD,MAAO,CAACH,EAAOI,eACnB,IAAK,gBACD,MAAO,CAACJ,EAAOK,gBACnB,QACI,MAAO,KAIbzC,oBAAmB,WAAmB,IAAD0C,EAAA,YAAA9E,EAAAlJ,IAAA+G,MAAA,SAAAkH,IAAA,IAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAA,OAAAzO,IAAAyB,MAAA,SAAAiN,GAAA,cAAAA,EAAA9G,KAAA8G,EAAApJ,MAAA,OACjC4I,EAAW/N,OAAO+M,OAAO,GAAIc,EAAKhE,kBAAmBgE,EAAKjE,cAAaoE,EAAAnO,IAAA+G,MAAA,SAAAoH,IAAA,IAAAQ,EAAApB,EAAAqB,EAAAC,EAAAC,EAAAtB,EAAC,OAADxN,IAAAyB,MAAA,SAAAsN,GAAA,cAAAA,EAAAnH,KAAAmH,EAAAzJ,MAAA,OAKG,OAJrEiI,EAAMc,EAAAD,GACPQ,EAAaZ,EAAK3D,kBAAkBpI,OAAOsL,IAE3CsB,EAAW,IAAInF,EAAS,EAAG,OACxBsF,UAAUtF,EAASuF,UAAU,KAAM9O,OAAOmH,KAAK4G,EAASX,MAAWwB,EAAAzJ,KAAA,EAElDsJ,EAAWM,UAAUL,GAAU,KAAD,EAAlDC,EAAWC,EAAApK,KAEX6I,EAAarN,OAAOmH,KAAK4G,EAASX,IAASI,QAAO,SAACxB,GACrD,OAAQ2C,EAAYhF,KAAKuC,SAASF,OAGtCwC,EAAAX,EAAK/D,kBAAiBjE,KAAIqD,MAAAsF,EAAAQ,EAAI3B,IAAY,wBAAAuB,EAAAhH,UAAAoG,MAAAC,EAAA,EAAAC,EAZzBlO,OAAOmH,KAAK4G,GAAS,YAAAE,EAAAC,EAAA9H,QAAA,CAAAmI,EAAApJ,KAAA,eAAAoJ,EAAA7F,cAAAsF,IAAA,eAAAC,IAAAM,EAAApJ,KAAA,eAepCgJ,EAAiBN,EAAKoB,+BAA8Bb,EAAAvO,IAAA+G,MAAA,SAAAwH,IAAA,IAAAc,EAAA9B,EAAAqB,EAAAC,EAAAC,EAAAtB,EAAC,OAADxN,IAAAyB,MAAA,SAAA6N,GAAA,cAAAA,EAAA1H,KAAA0H,EAAAhK,MAAA,OAKe,OAJ9DiI,EAAMkB,EAAAD,GACPI,EAAaZ,EAAK3D,kBAAkBpI,OAAOsL,IAE3CsB,EAAW,IAAInF,EAAS,EAAG,OACxBsF,UAAUtF,EAASuF,UAAU,KAAMX,EAAef,KAAU+B,EAAAhK,KAAA,EAE3CsJ,EAAWM,UAAUL,GAAU,KAAD,EAAlDC,EAAWQ,EAAA3K,KAEX6I,EAAac,EAAef,GAAQI,QAAO,SAACxB,GAC9C,OAAQ2C,EAAYhF,KAAKuC,SAASF,OAGtCkD,EAAArB,EAAK/D,kBAAiBjE,KAAIqD,MAAAgG,EAAAF,EAAI3B,IAAY,wBAAA8B,EAAAvH,UAAAwG,MAAAC,EAAA,EAAAC,EAZzBtO,OAAOmH,KAAKgH,GAAe,aAAAE,EAAAC,EAAAlI,QAAA,CAAAmI,EAAApJ,KAAA,gBAAAoJ,EAAA7F,cAAA0F,IAAA,iBAAAC,IAAAE,EAAApJ,KAAA,kCAAAoJ,EAAA3G,UAAAkG,MAlBT/E,IAkC3CiE,oBAAmB,SACf3K,EACA+K,EACAC,GAEA,IAAItJ,EAAQ/D,OAAO+M,OAAO,GAAI,CAC1B1K,KAAMA,EACN+M,YAAa,KAGXrB,EAAW/N,OAAO+M,OAAO,GAAI9C,KAAKJ,kBAAmBI,KAAKL,cA0BhE,OAxBAyD,EAAWpK,SAAQ,SAAC+I,GAChB,GAAKqD,MAAMC,QAAQvB,EAASX,GAAQpB,IAApC,CAUA,IAAMuD,EAAmBxB,EAASX,GAAQpB,GAA0BwB,QAAO,SAACgC,GACxE,OAAOA,EAAKC,SAAWpG,EAAMgB,IAAI,WAAWqF,iBAG5CH,EAAgBnJ,OAAS,IACzBrC,EAAMqL,YAAWO,EAAA,MAAApE,OACT6B,GAAMuC,EAAA,MAAApE,OACFS,GAAOuD,EAAgB,WAhBnCxL,EAAMqL,YAAWO,EAAA,MAAApE,OACT6B,GAAMuC,EAAA,MAAApE,OACFS,GAAO+B,EAASX,GAAQpB,QAoBrCjI,GAGXkJ,oBAAmB,SAACJ,GAAsB,IAAD+C,EAAA,KAerC,OAdgB/C,EAAKgD,WAAWC,OAAM,SAACC,GACnC,IAAMC,EAAYJ,EAAKK,wBAAwBF,GAE/C,OAAyB,IAArBC,EAAU5J,QAQe,IAJV4J,EAAUxC,QAAO,SAACxB,GACjC,OAAO4D,EAAK9F,iBAAiBoC,SAASF,MAGxB5F,WAM1B6J,wBAAuB,SAACF,GACpB,IAAMxC,EAASlE,EAAM6G,QAAQ,yCAAyCH,EAAU1N,MAEhF,OAAKkL,GAAWA,EAAO4C,OAAO/J,QAIzBpG,OAAOmH,KAAKoG,EAAO4C,OAAO,GAAG5C,QAAQrB,SAAS,UAIlB,2BAA1BqB,EAAO4C,OAAO,GAAG9N,KAClB0N,EAAUvP,MAAM+M,EAAO4C,OAAO,GAAGxJ,MACjC,CAACoJ,EAAUvP,MAAM+M,EAAO4C,OAAO,GAAGxJ,OAT7B,IAYfsI,6BAA4B,WAAmB,IAADmB,EAAA,KACpCjC,EAAiB,GAEvB,OAAKlE,KAAKL,aAAaiD,MAIvB7M,OAAOmH,KAAK8C,KAAKL,aAAaiD,MAAM5J,SAAQ,SAAC6J,GACzCsD,EAAKxG,aAAaiD,KAAKC,GAAQ+C,WAAW5M,SAAQ,SAAC8M,GAC/C,IAAMxC,EAASlE,EAAM6G,QAAQ,yCAAyCH,EAAU1N,MAE1E2N,EAAYI,EAAKH,wBAAwBF,GAE/C,GAAIC,EAAU5J,OAAS,EAAG,CACtB,IAGOiK,EAHDC,EAAa/C,EAAO4C,OAAO,GAAG5C,OAAOH,OAC3C,GAAKe,EAAemC,IAGhBD,EAAAlC,EAAemC,IAAYzK,KAAIqD,MAAAmH,EAAArB,EAAIgB,SAFnC7B,EAAemC,GAAcN,SAQtC7B,GApBIA,GAuBfoC,OAAM,WACF,GAAItG,KAAKK,aACkBL,KAAKmC,UAAUoB,QAAO,SAACgD,GAAQ,OAAKA,EAASzM,OAAS/D,OAAOmH,KAAKqJ,EAASzM,OAAOqC,UAEtFA,OAKf,OAJA6D,KAAKwG,wBAAwB,CACzBC,QAASzG,KAAK0G,IAAI,+CAGf,KAIf1G,KAAKmB,OAAO", "file": "static/js/10.js", "sourcesContent": ["import type Repository from 'src/core/data/repository.data';\nimport type {\n    DataIncluded,\n    EntityData,\n    Error,\n    Flow,\n    ReferenceIncluded,\n    Rule,\n    Condition,\n    Sequence,\n} from '../../flow.types';\nimport {ACTION_ENTITY} from '../../../../constant/flow-sharing.constant';\n\ninterface RuleReference {\n    [key: string]: Array<string>\n}\n\nconst { State, Utils } = Shopware;\nconst { Criteria } = Shopware.Data;\nconst { cloneDeep } = Utils.object;\n\n/**\n * @package services-settings\n */\nexport default {\n    inject: ['feature'],\n\n    data(): {\n        dataIncluded: DataIncluded,\n        referenceIncluded: ReferenceIncluded,\n        invalidEntityIds: Array<string>\n    } {\n        return {\n            dataIncluded: {},\n            referenceIncluded: {},\n            invalidEntityIds: []\n        };\n    },\n\n    computed: {\n        flowRepository(): Repository {\n            return this.repositoryFactory.create('flow');\n        },\n\n        flowSequenceRepository(): Repository {\n            return this.repositoryFactory.create('flow_sequence');\n        },\n\n        flowSharingState() {\n            return State.get('swFlowSharingState');\n        },\n\n        isUploading() {\n            if (this.feature.isActive('VUE3')) {\n                return !!this.$route.query?.isUploading === true;\n            }\n\n            return this.$route.params?.isUploading === true;\n        }\n    },\n\n    methods: {\n        async createdComponent(): Promise<void> {\n            if (this.isUploading) {\n                this.referenceIncluded = this.flowSharingState.referenceIncluded;\n                this.dataIncluded = this.flowSharingState.dataIncluded;\n\n                await this.getInvalidEntityIds();\n            }\n\n            this.$super('createdComponent');\n        },\n\n        routeDetailTab(tabName: string): {\n            name: string,\n            params: {\n                [key: string]: boolean | string\n            }\n        } {\n            if (!this.isUploading) {\n                return this.$super('routeDetailTab', tabName);\n            }\n\n            if (this.feature.isActive('VUE3')) {\n                return { name: `sw.flow.create.${tabName}`, query: { isUploading: true } };\n            }\n\n            return { name: `sw.flow.create.${tabName}`, params: { isUploading: true } };\n        },\n\n        createNewFlow(): void {\n            if (!this.isUploading) {\n                return this.$super('createNewFlow');\n            }\n\n            const flow = this.flowRepository.create();\n            flow.priority = 0;\n            flow.eventName = '';\n\n            State.commit('swFlowState/setFlow', flow);\n\n            const flowObject = this.flowSharingState.flow;\n\n            if (flowObject) {\n                this.restoreImportedFlow(flowObject);\n            }\n        },\n\n        restoreImportedFlow(flowObject: Flow): void {\n            if (Object.keys(flowObject).length === 0) {\n                return;\n            }\n            this.flow.id = Utils.createId();\n\n            Object.keys(flowObject).forEach((key) => {\n                if (['id', 'sequences'].includes(key)) {\n                    return;\n                }\n\n                this.flow[key] = key === 'active'\n                    ? Boolean(flowObject[key])\n                    : flowObject[key];\n            });\n\n            let sequences = flowObject?.sequences;\n            sequences = sequences ? this.buildSequencesFromConfig(this.validateSequences(sequences)) : [];\n\n            State.commit('swFlowState/setFlow', this.flow);\n            State.commit('swFlowState/setEventName', flowObject?.eventName);\n\n            State.commit('error/removeApiError', {\n                expression: `flow.${this.flow.id}.eventName`,\n            });\n\n            State.commit('swFlowState/setSequences', sequences);\n            State.commit('swFlowState/setOriginFlow', cloneDeep(this.flow));\n            this.getDataForActionDescription();\n        },\n\n        validateSequences(sequences: Array<Sequence>): Array<Sequence> {\n            if (this.dataIncluded['rule']) {\n                sequences = this.addRuleErrors(sequences);\n            }\n\n            sequences = this.addActionErrors(sequences);\n\n            return sequences;\n        },\n\n        addRuleErrors(sequences: Array<Sequence>): Array<Sequence> {\n            Object.keys(sequences).forEach((key) => {\n                if (sequences[key].actionName !== null) {\n                    return;\n                }\n\n                sequences[key].rule = this.dataIncluded.rule[sequences[key].ruleId];\n                sequences[key].rule.value = Object.assign({}, sequences[key].rule.value);\n\n                if (this.invalidEntityIds.includes(sequences[key].ruleId)) {\n                    sequences[key].error = this.generateErrorObject(\n                        'missing-rule',\n                        'rule',\n                        [sequences[key].ruleId]\n                    );\n\n                    return;\n                }\n\n                if (this.hasInvalidReference(sequences[key].rule)) {\n                    sequences[key].error = this.generateErrorObject(\n                        'rule',\n                        'rule',\n                        [sequences[key].ruleId]\n                    );\n                }\n            });\n\n            return sequences;\n        },\n\n        addActionErrors(sequences: Array<Sequence>): Array<Sequence> {\n            Object.keys(sequences).forEach((key) => {\n                if (sequences[key].actionName === null) {\n                    return;\n                }\n\n                if (ACTION_ENTITY[sequences[key].actionName] === undefined) {\n                    return;\n                }\n\n                const entity = ACTION_ENTITY[sequences[key].actionName];\n                const invalidIds = this.getEntityIds(entity, sequences[key].config).filter((id) => {\n                    return this.invalidEntityIds.includes(id);\n                });\n\n                if (invalidIds.length === 0) {\n                    return;\n                }\n                sequences[key].error = this.generateErrorObject('action', entity, invalidIds);\n            });\n\n            return sequences;\n        },\n\n        getEntityIds(entity: string, config): Array<string> {\n            switch (entity) {\n                case 'tag':\n                    return Object.keys(config.tagIds);\n                case 'customer_group':\n                    return [config.customerGroupId];\n                case 'custom_field':\n                    return [config.customFieldId];\n                case 'mail_template':\n                    return [config.mailTemplateId];\n                default:\n                    return [];\n            }\n        },\n\n        async getInvalidEntityIds(): Promise<void> {\n            const entities = Object.assign({}, this.referenceIncluded, this.dataIncluded);\n            for (const entity of Object.keys(entities)) {\n                const repository = this.repositoryFactory.create(entity);\n\n                const criteria = new Criteria(1, null);\n                criteria.addFilter(Criteria.equalsAny('id', Object.keys(entities[entity])));\n\n                const existingIds = await repository.searchIds(criteria);\n\n                const invalidIds = Object.keys(entities[entity]).filter((id) => {\n                    return !existingIds.data.includes(id);\n                });\n\n                this.invalidEntityIds.push(...invalidIds);\n            }\n\n            const ruleReferences = this.getReferenceIncludedFromRule();\n            for (const entity of Object.keys(ruleReferences)) {\n                const repository = this.repositoryFactory.create(entity);\n\n                const criteria = new Criteria(1, null);\n                criteria.addFilter(Criteria.equalsAny('id', ruleReferences[entity]));\n\n                const existingIds = await repository.searchIds(criteria);\n\n                const invalidIds = ruleReferences[entity].filter((id) => {\n                    return !existingIds.data.includes(id);\n                });\n\n                this.invalidEntityIds.push(...invalidIds);\n            }\n        },\n\n        generateErrorObject(\n            type: string,\n            entity: string,\n            invalidIds: Array<string>\n        ): Error {\n            let error = Object.assign({}, {\n                type: type,\n                errorDetail: {}\n            });\n\n            const entities = Object.assign({}, this.referenceIncluded, this.dataIncluded);\n\n            invalidIds.forEach((id) => {\n                if (!Array.isArray(entities[entity][id])) {\n                    error.errorDetail = {\n                        [`${entity}`]: {\n                            [`${id}`]: entities[entity][id]\n                        }\n                    };\n\n                    return;\n                }\n\n                const entityReference = (entities[entity][id] as Array<EntityData>).filter((item) => {\n                    return item.locale === State.get('session').currentLocale;\n                });\n\n                if (entityReference.length > 0) {\n                    error.errorDetail = {\n                        [`${entity}`]: {\n                            [`${id}`]: entityReference[0]\n                        }\n                    };\n                }\n            });\n\n            return error;\n        },\n\n        hasInvalidReference(rule: Rule): boolean {\n            const isValid = rule.conditions.every((condition) => {\n                const entityIds = this.getEntityIdsOfCondition(condition);\n\n                if (entityIds.length === 0) {\n                    return true;\n                }\n\n                const invalidIds = entityIds.filter((id) => {\n                    return this.invalidEntityIds.includes(id);\n                });\n\n                return invalidIds.length === 0;\n            });\n\n            return !isValid;\n        },\n\n        getEntityIdsOfCondition(condition: Condition): Array<string> {\n            const config = State.getters['ruleConditionsConfig/getConfigForType'](condition.type);\n\n            if (!config || !config.fields.length) {\n                return [];\n            }\n\n            if (!Object.keys(config.fields[0].config).includes('entity')) {\n                return [];\n            }\n\n            return config.fields[0].type === 'multi-entity-id-select'\n                ? condition.value[config.fields[0].name]\n                : [condition.value[config.fields[0].name]];\n        },\n\n        getReferenceIncludedFromRule(): RuleReference {\n            const ruleReferences = {};\n\n            if (!this.dataIncluded.rule) {\n                return ruleReferences;\n            }\n\n            Object.keys(this.dataIncluded.rule).forEach((ruleId) => {\n                this.dataIncluded.rule[ruleId].conditions.forEach((condition) => {\n                    const config = State.getters['ruleConditionsConfig/getConfigForType'](condition.type);\n\n                    const entityIds = this.getEntityIdsOfCondition(condition);\n\n                    if (entityIds.length > 0) {\n                        const entityName = config.fields[0].config.entity;\n                        if (!ruleReferences[entityName]) {\n                            ruleReferences[entityName] = entityIds;\n                        } else {\n                            ruleReferences[entityName].push(...entityIds);\n                        }\n                    }\n                });\n            });\n\n            return ruleReferences;\n        },\n\n        onSave(): void {\n            if (this.isUploading) {\n                const errorSequences = this.sequences.filter((sequence) => sequence.error && Object.keys(sequence.error).length);\n\n                if (errorSequences.length) {\n                    this.createNotificationError({\n                        message: this.$tc('sw-flow.flowNotification.messageSaveError'),\n                    });\n\n                    return null;\n                }\n            }\n\n            this.$super('onSave');\n        },\n    },\n};\n"], "sourceRoot": ""}
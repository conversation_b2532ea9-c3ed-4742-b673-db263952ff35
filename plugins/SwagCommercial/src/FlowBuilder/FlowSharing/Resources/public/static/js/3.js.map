{"version": 3, "sources": ["webpack:////tmp/extension2400992207/SwagCommercial/src/FlowBuilder/FlowSharing/Resources/app/administration/src/module/sw-flow/component/sw-flow-sequence-modal-error/sw-flow-sequence-modal-error.html.twig", "webpack:////tmp/extension2400992207/SwagCommercial/src/FlowBuilder/FlowSharing/Resources/app/administration/src/module/sw-flow/component/sw-flow-sequence-modal-error/index.ts", "webpack:///./node_modules/vue-style-loader/lib/listToStyles.js", "webpack:///./node_modules/vue-style-loader/lib/addStylesClient.js", "webpack:////tmp/extension2400992207/SwagCommercial/src/FlowBuilder/FlowSharing/Resources/app/administration/src/module/sw-flow/component/sw-flow-sequence-modal-error/sw-flow-sequence-modal-error.scss"], "names": ["template", "props", "sequence", "type", "Object", "required", "default", "computed", "errorType", "_this$sequence", "_this$sequence$error", "this", "error", "errorData", "actionName", "ACTION", "ADD_CUSTOMER_TAG", "ADD_ORDER_TAG", "REMOVE_CUSTOMER_TAG", "REMOVE_ORDER_TAG", "errorDetail", "tag", "SET_CUSTOMER_CUSTOM_FIELD", "SET_ORDER_CUSTOM_FIELD", "SET_CUSTOMER_GROUP_CUSTOM_FIELD", "custom_field", "CHANGE_CUSTOMER_GROUP", "customer_group", "MAIL_SEND", "mail_template", "methods", "getErrorTitle", "$tc", "data", "ACTION_GROUP_MISSING_ERROR", "listToStyles", "parentId", "list", "styles", "newStyles", "i", "length", "item", "id", "part", "css", "media", "sourceMap", "parts", "push", "hasDocument", "document", "DEBUG", "Error", "stylesInDom", "head", "getElementsByTagName", "singletonElement", "singletonCounter", "isProduction", "noop", "options", "ssrIdKey", "isOldIE", "navigator", "test", "userAgent", "toLowerCase", "addStylesClient", "_isProduction", "_options", "addStylesToDom", "newList", "<PERSON><PERSON><PERSON><PERSON>", "domStyle", "refs", "j", "addStyle", "createStyleElement", "styleElement", "createElement", "append<PERSON><PERSON><PERSON>", "obj", "update", "remove", "querySelector", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "styleIndex", "applyToSingletonTag", "bind", "applyToTag", "newObj", "textStore", "replaceText", "index", "replacement", "filter", "Boolean", "join", "styleSheet", "cssText", "cssNode", "createTextNode", "childNodes", "insertBefore", "setAttribute", "ssrId", "sources", "btoa", "unescape", "encodeURIComponent", "JSON", "stringify", "<PERSON><PERSON><PERSON><PERSON>", "content", "__esModule", "module", "locals", "exports", "add"], "mappings": "oJAAe,I,YCQA,WACXA,SDTW,2nBCUXC,MAAO,CACHC,SAAU,CACNC,KAAMC,OACNC,UAAU,EACVC,QAAS,OAIjBC,SAAU,CACNC,UAAS,WAAY,IAADC,EAAAC,EAChB,OAAoB,QAAbD,EAAAE,KAAKT,gBAAQ,IAAAO,GAAO,QAAPC,EAAbD,EAAeG,aAAK,IAAAF,OAAP,EAAbA,EAAsBP,OAAQ,UAGzCU,UAAS,WACL,OAAQF,KAAKT,SAASY,YAClB,KAAKC,IAAOC,iBACZ,KAAKD,IAAOE,cACZ,KAAKF,IAAOG,oBACZ,KAAKH,IAAOI,iBACR,OAAOR,KAAKT,SAASU,MAAMQ,YAAYC,IAC3C,KAAKN,IAAOO,0BACZ,KAAKP,IAAOQ,uBACZ,KAAKR,IAAOS,gCACR,OAAOb,KAAKT,SAASU,MAAMQ,YAAYK,aAC3C,KAAKV,IAAOW,sBACR,OAAOf,KAAKT,SAASU,MAAMQ,YAAYO,eAC3C,KAAKZ,IAAOa,UACR,OAAOjB,KAAKT,SAASU,MAAMQ,YAAYS,cAC3C,QACI,MAAO,MAKvBC,QAAS,CACLC,cAAa,SAACjB,GACV,OAAOH,KAAKqB,IAAI,gDAAiD,CAAEC,KAAMC,IAA2BpB,S,kCC1CjG,SAASqB,EAAcC,EAAUC,GAG9C,IAFA,IAAIC,EAAS,GACTC,EAAY,GACPC,EAAI,EAAGA,EAAIH,EAAKI,OAAQD,IAAK,CACpC,IAAIE,EAAOL,EAAKG,GACZG,EAAKD,EAAK,GAIVE,EAAO,CACTD,GAAIP,EAAW,IAAMI,EACrBK,IALQH,EAAK,GAMbI,MALUJ,EAAK,GAMfK,UALcL,EAAK,IAOhBH,EAAUI,GAGbJ,EAAUI,GAAIK,MAAMC,KAAKL,GAFzBN,EAAOW,KAAKV,EAAUI,GAAM,CAAEA,GAAIA,EAAIK,MAAO,CAACJ,KAKlD,OAAON,E,+CCjBT,IAAIY,EAAkC,oBAAbC,SAEzB,GAAqB,oBAAVC,OAAyBA,QAC7BF,EACH,MAAM,IAAIG,MACV,2JAkBJ,IAAIC,EAAc,GAQdC,EAAOL,IAAgBC,SAASI,MAAQJ,SAASK,qBAAqB,QAAQ,IAC9EC,EAAmB,KACnBC,EAAmB,EACnBC,GAAe,EACfC,EAAO,aACPC,EAAU,KACVC,EAAW,kBAIXC,EAA+B,oBAAdC,WAA6B,eAAeC,KAAKD,UAAUE,UAAUC,eAE3E,SAASC,EAAiBhC,EAAUC,EAAMgC,EAAeC,GACtEX,EAAeU,EAEfR,EAAUS,GAAY,GAEtB,IAAIhC,EAASH,EAAaC,EAAUC,GAGpC,OAFAkC,EAAejC,GAER,SAAiBkC,GAEtB,IADA,IAAIC,EAAY,GACPjC,EAAI,EAAGA,EAAIF,EAAOG,OAAQD,IAAK,CACtC,IAAIE,EAAOJ,EAAOE,IACdkC,EAAWpB,EAAYZ,EAAKC,KACvBgC,OACTF,EAAUxB,KAAKyB,GAEbF,EAEFD,EADAjC,EAASH,EAAaC,EAAUoC,IAGhClC,EAAS,GAEX,IAASE,EAAI,EAAGA,EAAIiC,EAAUhC,OAAQD,IAAK,CACzC,IAAIkC,EACJ,GAAsB,KADlBA,EAAWD,EAAUjC,IACZmC,KAAY,CACvB,IAAK,IAAIC,EAAI,EAAGA,EAAIF,EAAS1B,MAAMP,OAAQmC,IACzCF,EAAS1B,MAAM4B,YAEVtB,EAAYoB,EAAS/B,OAMpC,SAAS4B,EAAgBjC,GACvB,IAAK,IAAIE,EAAI,EAAGA,EAAIF,EAAOG,OAAQD,IAAK,CACtC,IAAIE,EAAOJ,EAAOE,GACdkC,EAAWpB,EAAYZ,EAAKC,IAChC,GAAI+B,EAAU,CACZA,EAASC,OACT,IAAK,IAAIC,EAAI,EAAGA,EAAIF,EAAS1B,MAAMP,OAAQmC,IACzCF,EAAS1B,MAAM4B,GAAGlC,EAAKM,MAAM4B,IAE/B,KAAOA,EAAIlC,EAAKM,MAAMP,OAAQmC,IAC5BF,EAAS1B,MAAMC,KAAK4B,EAASnC,EAAKM,MAAM4B,KAEtCF,EAAS1B,MAAMP,OAASC,EAAKM,MAAMP,SACrCiC,EAAS1B,MAAMP,OAASC,EAAKM,MAAMP,YAEhC,CACL,IAAIO,EAAQ,GACZ,IAAS4B,EAAI,EAAGA,EAAIlC,EAAKM,MAAMP,OAAQmC,IACrC5B,EAAMC,KAAK4B,EAASnC,EAAKM,MAAM4B,KAEjCtB,EAAYZ,EAAKC,IAAM,CAAEA,GAAID,EAAKC,GAAIgC,KAAM,EAAG3B,MAAOA,KAK5D,SAAS8B,IACP,IAAIC,EAAe5B,SAAS6B,cAAc,SAG1C,OAFAD,EAAa5E,KAAO,WACpBoD,EAAK0B,YAAYF,GACVA,EAGT,SAASF,EAAUK,GACjB,IAAIC,EAAQC,EACRL,EAAe5B,SAASkC,cAAc,SAAWvB,EAAW,MAAQoB,EAAIvC,GAAK,MAEjF,GAAIoC,EAAc,CAChB,GAAIpB,EAGF,OAAOC,EAOPmB,EAAaO,WAAWC,YAAYR,GAIxC,GAAIhB,EAAS,CAEX,IAAIyB,EAAa9B,IACjBqB,EAAetB,IAAqBA,EAAmBqB,KACvDK,EAASM,EAAoBC,KAAK,KAAMX,EAAcS,GAAY,GAClEJ,EAASK,EAAoBC,KAAK,KAAMX,EAAcS,GAAY,QAGlET,EAAeD,IACfK,EAASQ,EAAWD,KAAK,KAAMX,GAC/BK,EAAS,WACPL,EAAaO,WAAWC,YAAYR,IAMxC,OAFAI,EAAOD,GAEA,SAAsBU,GAC3B,GAAIA,EAAQ,CACV,GAAIA,EAAO/C,MAAQqC,EAAIrC,KACnB+C,EAAO9C,QAAUoC,EAAIpC,OACrB8C,EAAO7C,YAAcmC,EAAInC,UAC3B,OAEFoC,EAAOD,EAAMU,QAEbR,KAKN,IACMS,EADFC,GACED,EAAY,GAET,SAAUE,EAAOC,GAEtB,OADAH,EAAUE,GAASC,EACZH,EAAUI,OAAOC,SAASC,KAAK,QAI1C,SAASV,EAAqBV,EAAcgB,EAAOX,EAAQF,GACzD,IAAIrC,EAAMuC,EAAS,GAAKF,EAAIrC,IAE5B,GAAIkC,EAAaqB,WACfrB,EAAaqB,WAAWC,QAAUP,EAAYC,EAAOlD,OAChD,CACL,IAAIyD,EAAUnD,SAASoD,eAAe1D,GAClC2D,EAAazB,EAAayB,WAC1BA,EAAWT,IAAQhB,EAAaQ,YAAYiB,EAAWT,IACvDS,EAAW/D,OACbsC,EAAa0B,aAAaH,EAASE,EAAWT,IAE9ChB,EAAaE,YAAYqB,IAK/B,SAASX,EAAYZ,EAAcG,GACjC,IAAIrC,EAAMqC,EAAIrC,IACVC,EAAQoC,EAAIpC,MACZC,EAAYmC,EAAInC,UAiBpB,GAfID,GACFiC,EAAa2B,aAAa,QAAS5D,GAEjCe,EAAQ8C,OACV5B,EAAa2B,aAAa5C,EAAUoB,EAAIvC,IAGtCI,IAGFF,GAAO,mBAAqBE,EAAU6D,QAAQ,GAAK,MAEnD/D,GAAO,uDAAyDgE,KAAKC,SAASC,mBAAmBC,KAAKC,UAAUlE,MAAgB,OAG9HgC,EAAaqB,WACfrB,EAAaqB,WAAWC,QAAUxD,MAC7B,CACL,KAAOkC,EAAamC,YAClBnC,EAAaQ,YAAYR,EAAamC,YAExCnC,EAAaE,YAAY9B,SAASoD,eAAe1D,O,uBCxNrD,IAAIsE,EAAU,EAAQ,QACnBA,EAAQC,aAAYD,EAAUA,EAAQ7G,SACnB,iBAAZ6G,IAAsBA,EAAU,CAAC,CAACE,EAAO7E,EAAI2E,EAAS,MAC7DA,EAAQG,SAAQD,EAAOE,QAAUJ,EAAQG,SAG/BE,EADH,EAAQ,QAAwLlH,SACzL,WAAY6G,GAAS,EAAM,K", "file": "static/js/3.js", "sourcesContent": ["export default \"<div\\n    :class=\\\"'sw-flow-sequence-modal-error sw-flow-sequence-modal-error--' + errorType\\\"\\n>\\n    <span class=\\\"sw-flow-sequence-modal-error__heading\\\">\\n        {{ getErrorTitle(sequence.actionName) }}\\n    </span>\\n\\n    <div class=\\\"sw-flow-sequence-modal-error__list-wrap\\\">\\n        <ul class=\\\"sw-flow-sequence-modal-error__list\\\">\\n            <li\\n                v-for=\\\"(value, key) in errorData\\\"\\n                :key=\\\"key\\\"\\n                class=\\\"sw-flow-sequence-modal-error__list-item\\\"\\n            >\\n                {{ value.name || value.mailTemplateTypeName }}\\n            </li>\\n        </ul>\\n    </div>\\n</div>\\n\";", "import template from './sw-flow-sequence-modal-error.html.twig';\nimport './sw-flow-sequence-modal-error.scss';\nimport type {ErrorData} from '../../flow.types';\nimport {ACTION_GROUP_MISSING_ERROR, ACTION} from '../../../../constant/flow-sharing.constant';\n\n/**\n * @package services-settings\n */\nexport default {\n    template,\n    props: {\n        sequence: {\n            type: Object,\n            required: false,\n            default: null,\n        },\n    },\n\n    computed: {\n        errorType(): string {\n            return this.sequence?.error?.type || 'action';\n        },\n\n        errorData(): ErrorData {\n            switch (this.sequence.actionName) {\n                case ACTION.ADD_CUSTOMER_TAG:\n                case ACTION.ADD_ORDER_TAG:\n                case ACTION.REMOVE_CUSTOMER_TAG:\n                case ACTION.REMOVE_ORDER_TAG:\n                    return this.sequence.error.errorDetail.tag;\n                case ACTION.SET_CUSTOMER_CUSTOM_FIELD:\n                case ACTION.SET_ORDER_CUSTOM_FIELD:\n                case ACTION.SET_CUSTOMER_GROUP_CUSTOM_FIELD:\n                    return this.sequence.error.errorDetail.custom_field;\n                case ACTION.CHANGE_CUSTOMER_GROUP:\n                    return this.sequence.error.errorDetail.customer_group;\n                case ACTION.MAIL_SEND:\n                    return this.sequence.error.errorDetail.mail_template;\n                default:\n                    return {};\n            }\n        },\n    },\n\n    methods: {\n        getErrorTitle(actionName: string): string {\n            return this.$tc('sw-flow-sharing.importError.textMissingObject', { data: ACTION_GROUP_MISSING_ERROR[actionName] });\n        },\n    },\n};\n", "/**\n * Translates the list format produced by css-loader into something\n * easier to manipulate.\n */\nexport default function listToStyles (parentId, list) {\n  var styles = []\n  var newStyles = {}\n  for (var i = 0; i < list.length; i++) {\n    var item = list[i]\n    var id = item[0]\n    var css = item[1]\n    var media = item[2]\n    var sourceMap = item[3]\n    var part = {\n      id: parentId + ':' + i,\n      css: css,\n      media: media,\n      sourceMap: sourceMap\n    }\n    if (!newStyles[id]) {\n      styles.push(newStyles[id] = { id: id, parts: [part] })\n    } else {\n      newStyles[id].parts.push(part)\n    }\n  }\n  return styles\n}\n", "/*\n  MIT License http://www.opensource.org/licenses/mit-license.php\n  Author <PERSON> @sokra\n  Modified by <PERSON> @yyx990803\n*/\n\nimport listToStyles from './listToStyles'\n\nvar hasDocument = typeof document !== 'undefined'\n\nif (typeof DEBUG !== 'undefined' && DEBUG) {\n  if (!hasDocument) {\n    throw new Error(\n    'vue-style-loader cannot be used in a non-browser environment. ' +\n    \"Use { target: 'node' } in your Webpack config to indicate a server-rendering environment.\"\n  ) }\n}\n\n/*\ntype StyleObject = {\n  id: number;\n  parts: Array<StyleObjectPart>\n}\n\ntype StyleObjectPart = {\n  css: string;\n  media: string;\n  sourceMap: ?string\n}\n*/\n\nvar stylesInDom = {/*\n  [id: number]: {\n    id: number,\n    refs: number,\n    parts: Array<(obj?: StyleObjectPart) => void>\n  }\n*/}\n\nvar head = hasDocument && (document.head || document.getElementsByTagName('head')[0])\nvar singletonElement = null\nvar singletonCounter = 0\nvar isProduction = false\nvar noop = function () {}\nvar options = null\nvar ssrIdKey = 'data-vue-ssr-id'\n\n// Force single-tag solution on IE6-9, which has a hard limit on the # of <style>\n// tags it will allow on a page\nvar isOldIE = typeof navigator !== 'undefined' && /msie [6-9]\\b/.test(navigator.userAgent.toLowerCase())\n\nexport default function addStylesClient (parentId, list, _isProduction, _options) {\n  isProduction = _isProduction\n\n  options = _options || {}\n\n  var styles = listToStyles(parentId, list)\n  addStylesToDom(styles)\n\n  return function update (newList) {\n    var mayRemove = []\n    for (var i = 0; i < styles.length; i++) {\n      var item = styles[i]\n      var domStyle = stylesInDom[item.id]\n      domStyle.refs--\n      mayRemove.push(domStyle)\n    }\n    if (newList) {\n      styles = listToStyles(parentId, newList)\n      addStylesToDom(styles)\n    } else {\n      styles = []\n    }\n    for (var i = 0; i < mayRemove.length; i++) {\n      var domStyle = mayRemove[i]\n      if (domStyle.refs === 0) {\n        for (var j = 0; j < domStyle.parts.length; j++) {\n          domStyle.parts[j]()\n        }\n        delete stylesInDom[domStyle.id]\n      }\n    }\n  }\n}\n\nfunction addStylesToDom (styles /* Array<StyleObject> */) {\n  for (var i = 0; i < styles.length; i++) {\n    var item = styles[i]\n    var domStyle = stylesInDom[item.id]\n    if (domStyle) {\n      domStyle.refs++\n      for (var j = 0; j < domStyle.parts.length; j++) {\n        domStyle.parts[j](item.parts[j])\n      }\n      for (; j < item.parts.length; j++) {\n        domStyle.parts.push(addStyle(item.parts[j]))\n      }\n      if (domStyle.parts.length > item.parts.length) {\n        domStyle.parts.length = item.parts.length\n      }\n    } else {\n      var parts = []\n      for (var j = 0; j < item.parts.length; j++) {\n        parts.push(addStyle(item.parts[j]))\n      }\n      stylesInDom[item.id] = { id: item.id, refs: 1, parts: parts }\n    }\n  }\n}\n\nfunction createStyleElement () {\n  var styleElement = document.createElement('style')\n  styleElement.type = 'text/css'\n  head.appendChild(styleElement)\n  return styleElement\n}\n\nfunction addStyle (obj /* StyleObjectPart */) {\n  var update, remove\n  var styleElement = document.querySelector('style[' + ssrIdKey + '~=\"' + obj.id + '\"]')\n\n  if (styleElement) {\n    if (isProduction) {\n      // has SSR styles and in production mode.\n      // simply do nothing.\n      return noop\n    } else {\n      // has SSR styles but in dev mode.\n      // for some reason Chrome can't handle source map in server-rendered\n      // style tags - source maps in <style> only works if the style tag is\n      // created and inserted dynamically. So we remove the server rendered\n      // styles and inject new ones.\n      styleElement.parentNode.removeChild(styleElement)\n    }\n  }\n\n  if (isOldIE) {\n    // use singleton mode for IE9.\n    var styleIndex = singletonCounter++\n    styleElement = singletonElement || (singletonElement = createStyleElement())\n    update = applyToSingletonTag.bind(null, styleElement, styleIndex, false)\n    remove = applyToSingletonTag.bind(null, styleElement, styleIndex, true)\n  } else {\n    // use multi-style-tag mode in all other cases\n    styleElement = createStyleElement()\n    update = applyToTag.bind(null, styleElement)\n    remove = function () {\n      styleElement.parentNode.removeChild(styleElement)\n    }\n  }\n\n  update(obj)\n\n  return function updateStyle (newObj /* StyleObjectPart */) {\n    if (newObj) {\n      if (newObj.css === obj.css &&\n          newObj.media === obj.media &&\n          newObj.sourceMap === obj.sourceMap) {\n        return\n      }\n      update(obj = newObj)\n    } else {\n      remove()\n    }\n  }\n}\n\nvar replaceText = (function () {\n  var textStore = []\n\n  return function (index, replacement) {\n    textStore[index] = replacement\n    return textStore.filter(Boolean).join('\\n')\n  }\n})()\n\nfunction applyToSingletonTag (styleElement, index, remove, obj) {\n  var css = remove ? '' : obj.css\n\n  if (styleElement.styleSheet) {\n    styleElement.styleSheet.cssText = replaceText(index, css)\n  } else {\n    var cssNode = document.createTextNode(css)\n    var childNodes = styleElement.childNodes\n    if (childNodes[index]) styleElement.removeChild(childNodes[index])\n    if (childNodes.length) {\n      styleElement.insertBefore(cssNode, childNodes[index])\n    } else {\n      styleElement.appendChild(cssNode)\n    }\n  }\n}\n\nfunction applyToTag (styleElement, obj) {\n  var css = obj.css\n  var media = obj.media\n  var sourceMap = obj.sourceMap\n\n  if (media) {\n    styleElement.setAttribute('media', media)\n  }\n  if (options.ssrId) {\n    styleElement.setAttribute(ssrIdKey, obj.id)\n  }\n\n  if (sourceMap) {\n    // https://developer.chrome.com/devtools/docs/javascript-debugging\n    // this makes source maps inside style tags work properly in Chrome\n    css += '\\n/*# sourceURL=' + sourceMap.sources[0] + ' */'\n    // http://stackoverflow.com/a/26603875\n    css += '\\n/*# sourceMappingURL=data:application/json;base64,' + btoa(unescape(encodeURIComponent(JSON.stringify(sourceMap)))) + ' */'\n  }\n\n  if (styleElement.styleSheet) {\n    styleElement.styleSheet.cssText = css\n  } else {\n    while (styleElement.firstChild) {\n      styleElement.removeChild(styleElement.firstChild)\n    }\n    styleElement.appendChild(document.createTextNode(css))\n  }\n}\n", "// style-loader: Adds some css to the DOM by adding a <style> tag\n\n// load the styles\nvar content = require(\"!!../../../../../../../../../../../../../../builds/shopware/6/product/commercial/src/Administration/Resources/app/administration/node_modules/mini-css-extract-plugin/dist/loader.js??ref--15-1!../../../../../../../../../../../../../../builds/shopware/6/product/commercial/src/Administration/Resources/app/administration/node_modules/css-loader/dist/cjs.js??ref--15-2!../../../../../../../../../../../../../../builds/shopware/6/product/commercial/src/Administration/Resources/app/administration/node_modules/sass-loader/dist/cjs.js??ref--15-3!./sw-flow-sequence-modal-error.scss\");\nif(content.__esModule) content = content.default;\nif(typeof content === 'string') content = [[module.id, content, '']];\nif(content.locals) module.exports = content.locals;\n// add the styles to the DOM\nvar add = require(\"!../../../../../../../../../../../../../../builds/shopware/6/product/commercial/src/Administration/Resources/app/administration/node_modules/vue-style-loader/lib/addStylesClient.js\").default\nvar update = add(\"22784188\", content, true, {});"], "sourceRoot": ""}
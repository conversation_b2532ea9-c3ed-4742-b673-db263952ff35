<?xml version="1.0" ?>

<container xmlns="http://symfony.com/schema/dic/services"
           xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
           xsi:schemaLocation="http://symfony.com/schema/dic/services http://symfony.com/schema/dic/services/services-1.0.xsd">

    <services>
        <service id="Shopware\Commercial\FlowBuilder\FlowSharing\Domain\Sharing\FlowSharingService">
            <argument type="tagged" tag="flow.sharing.requirement"/>
            <argument type="service" id="Doctrine\DBAL\Connection"/>
        </service>

        <service id="Shopware\Commercial\FlowBuilder\FlowSharing\Api\FlowSharingController" public="true">
            <argument type="service" id="Shopware\Commercial\FlowBuilder\FlowSharing\Domain\Sharing\FlowSharingService"/>
        </service>

        <service id="Shopware\Commercial\FlowBuilder\FlowSharing\Domain\Sharing\Requirement\EmailTemplateRequirementChecker">
            <argument type="service" id="Doctrine\DBAL\Connection"/>

            <tag name="flow.sharing.requirement" priority="500" />
        </service>

        <service id="Shopware\Commercial\FlowBuilder\FlowSharing\Domain\Sharing\Requirement\RuleRequirementChecker">
            <argument type="service" id="Doctrine\DBAL\Connection"/>

            <tag name="flow.sharing.requirement" priority="500" />
        </service>

        <service id="Shopware\Commercial\FlowBuilder\FlowSharing\Domain\Sharing\Requirement\TagRequirementChecker">
            <argument type="service" id="Doctrine\DBAL\Connection"/>

            <tag name="flow.sharing.requirement" priority="500" />
        </service>

        <service id="Shopware\Commercial\FlowBuilder\FlowSharing\Domain\Sharing\Requirement\CustomerGroupRequirementChecker">
            <argument type="service" id="Doctrine\DBAL\Connection"/>

            <tag name="flow.sharing.requirement" priority="500" />
        </service>

        <service id="Shopware\Commercial\FlowBuilder\FlowSharing\Domain\Sharing\Requirement\OrderStateRequirementChecker">
            <argument type="service" id="Doctrine\DBAL\Connection"/>

            <tag name="flow.sharing.requirement" priority="500" />
        </service>

        <service id="Shopware\Commercial\FlowBuilder\FlowSharing\Domain\Sharing\Requirement\CustomFieldRequirementChecker">
            <argument type="service" id="Doctrine\DBAL\Connection"/>

            <tag name="flow.sharing.requirement" priority="500" />
        </service>

        <service id="Shopware\Commercial\FlowBuilder\FlowSharing\Domain\Sharing\Requirement\ShopwareVersionRequirementChecker">
            <argument>%kernel.shopware_version%</argument>

            <tag name="flow.sharing.requirement" priority="500" />
        </service>

        <service id="Shopware\Commercial\FlowBuilder\FlowSharing\Domain\Sharing\Requirement\AppInstalledRequirementChecker">
            <argument type="service" id="Doctrine\DBAL\Connection" />

            <tag name="flow.sharing.requirement" priority="500" />
        </service>

        <service id="Shopware\Commercial\FlowBuilder\FlowSharing\Domain\Sharing\Requirement\PluginInstalledRequirementChecker">
            <argument type="service" id="kernel" />
            <argument type="tagged" tag="flow.action" />
            <argument type="service" id="Doctrine\DBAL\Connection" />

            <tag name="flow.sharing.requirement" priority="1000" />
        </service>
    </services>
</container>

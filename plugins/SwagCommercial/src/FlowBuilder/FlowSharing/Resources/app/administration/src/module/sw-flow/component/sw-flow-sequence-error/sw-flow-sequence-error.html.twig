<div
    class="sw-flow-sequence-error"
    :class="`sw-flow-sequence-error--${errorType}`"
>
    <div
        class="sw-flow-sequence-error__info"
    >
        <div class="sw-flow-sequence-error__header">
            <div class="sw-flow-sequence-error__name">
                <sw-icon
                    size="12px"
                    name="regular-exclamation-triangle"
                />
                <h3
                    v-if="isActionError"
                    class="sw-flow-sequence-error__heading sw-flow-sequence-error__heading-action"
                >
                    {{ $tc('sw-flow-sharing.importError.invalidActionHeading') }}
                </h3>

                <h3
                    v-if="isRuleError"
                    class="sw-flow-sequence-error__heading sw-flow-sequence-error__heading-rule"
                >
                    {{ $tc('sw-flow-sharing.importError.invalidRuleHeading') }}
                </h3>

                <h3
                    v-if="isMissingRuleError"
                    class="sw-flow-sequence-error__heading sw-flow-sequence-error__heading-rule"
                >
                    {{ $tc('sw-flow-sharing.importError.invalidRuleHeading') }}
                </h3>
            </div>
        </div>

        <div
            v-if="isActionError"
            class="sw-flow-sequence-error__description sw-flow-sequence-error__description-action"
        >
            {{ $tc('sw-flow-sharing.importError.invalidActionText') }}
        </div>
        <div
            v-if="isRuleError"
            class="sw-flow-sequence-error__description sw-flow-sequence-error__description-rule"
        >
            {{ $tc('sw-flow-sharing.importError.invalidRuleText') }}
        </div>
        <div
            v-if="isMissingRuleError"
            class="sw-flow-sequence-error__description sw-flow-sequence-error__description-rule"
        >
            {{ $tc('sw-flow-sharing.importError.missingRuleText') }}
        </div>
    </div>
</div>

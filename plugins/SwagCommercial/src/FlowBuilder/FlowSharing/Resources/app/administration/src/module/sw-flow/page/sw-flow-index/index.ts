import template from './sw-flow-index.html.twig';

/**
 * @package services-settings
 */
export default {
    template,

    inject: ['feature'],

    data(): {
        showUploadModal: boolean
    } {
        return {
            showUploadModal: false
        };
    },

    methods: {
        getLicense(toggle: string): boolean {
            return Shopware.License.get(toggle);
        },

        openUploadModal(): void {
            this.showUploadModal = true;
        },

        onCloseUploadModal(): void {
            this.showUploadModal = false;
        },

        onUploadFlowTemplateFile(): void {
            if (this.feature.isActive('VUE3')) {
                this.$router.push({
                    name: 'sw.flow.create.general',
                    query: {isUploading: true}
                });

                return;
            }

            this.$router.push({
                name: 'sw.flow.create.general',
                params: { isUploading: true }
            });
        },
    },
};

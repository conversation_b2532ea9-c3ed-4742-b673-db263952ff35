@import "~scss/variables";

.sw-flow-upload-modal {
    &.sw-modal .sw-modal__body {
        padding: 24px;
        letter-spacing: 0.08px;
    }

    &__description {
        margin-bottom: 24px;
    }

    &__included-list {
        margin: 24px;
    }

    &__included-list-item {
        margin-left: 20px;
    }

    &__data-included {
        ul > li {
            list-style: none;
        }

        &:not(:last-child) {
            margin-bottom: 24px;
        }
    }

    &__file-upload {
        margin-bottom: 24px;
    }

    &__content {
        margin-top: 24px;
    }

    &__warning {
        ul {
            margin-left: 12px;
        }
    }

    &__affected-rules {
        line-height: 25px;

        ul {
            padding-left: 24px;
            padding-top: 8px;
        }
    }

    &__resolve-rule-conflict-option {
        margin-top: 24px;
       
    }

    .sw-field--radio {
        &.sw-field--radio-block .sw-field__radio-option {
            padding: 24px;

            label {
                font-weight: $font-weight-bold;
                line-height: 25px;
            }
        }

        .sw-field__radio-option-description {
            font-size: $font-size-s;
            line-height: 25px;
        }
    }
}

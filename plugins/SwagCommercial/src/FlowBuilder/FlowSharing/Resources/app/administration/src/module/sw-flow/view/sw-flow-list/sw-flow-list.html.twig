{% block sw_flow_list_grid_actions_custom %}
    <sw-context-menu-item
        v-if="getLicense('FLOW_BUILDER-4142679')"
        class="sw-flow-list__item-download"
        :disabled="!acl.can('flow.viewer')"
        @click="onOpenDownloadModal(item)"
    >
        {{ $tc('sw-flow-sharing.downloadButton') }}
    </sw-context-menu-item>
{% endblock %}

{% block sw_flow_list_modal_content_custom %}
    {% block sw_flow_list_download_modal %}
        <sw-flow-download-modal
            v-if="currentFlow && isDownloading"
            :flow-id="currentFlow.id"
            @download-finish="onDownloadFlowSuccess"
            @modal-close="onCloseDownloadModal"
        />
    {% endblock %}
{% endblock %}

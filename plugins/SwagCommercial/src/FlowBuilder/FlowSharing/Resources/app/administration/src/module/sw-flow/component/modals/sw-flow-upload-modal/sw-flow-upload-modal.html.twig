<sw-modal
    class="sw-flow-upload-modal"
    :closable="false"
    :title="$tc('sw-flow-sharing.uploadModal.title')"
    @modal-close="onCancel"
>
    <div v-if="!ruleConflict">
        <sw-file-input
            {% if VUE3 %}
            v-model:value="jsonFile"
            {% else %}
            v-model="jsonFile"
            {% endif %}
            class="sw-flow-upload-modal__file-upload"
            :allowed-mime-types="['application/json']"
            :key="isLoading"
            :label="$tc('sw-flow-sharing.uploadModal.uploadFileLabel')"
            {% if VUE3 %}
            @update:value="onFileChange"
            {% else %}
            @change="onFileChange"
            {% endif %}
        >
            <temlpate #caption-label>
                {{ $tc('sw-flow-sharing.uploadModal.labelUpload') }}
            </temlpate>
        </sw-file-input>

        <div
            v-if="(rules.length || mailTemplates.length) && !showWarning"
            class="sw-flow-upload-modal__content"
        >
            <p
                class="sw-flow-upload-modal__description"
                v-html="$tc('sw-flow-sharing.uploadModal.description')"
            ></p>
            <div class="sw-flow-upload-modal__included">
                <div class="sw-flow-upload-modal__data-included">
                    <ul>
                        <li
                            v-for="(rule, index) in rules"
                            :key="index"
                        >
                            <sw-checkbox-field
                                class="sw-flow-upload-modal__rule-item"
                                :value="!!rule.id"
                                :label="$tc('sw-flow-sharing.uploadModal.ruleLabel',0, { ruleName: rule.name })"
                                @change="(checked) => handleSelectRule(rule, checked)"
                            />
                        </li>
                    </ul>

                    <ul>
                        <li
                            v-for="(mail, index) in mailTemplates"
                            :key="index"
                        >
                            <sw-checkbox-field
                                class="sw-flow-upload-modal__mail-template-item"
                                :value="!!mail.id"
                                :label="$tc('sw-flow-sharing.uploadModal.mailTemplateLabel',0, { mail: mail.mailTemplateTypeName })"
                                @change="(checked) => handleSelectMail(mail, checked)"
                            />
                        </li>
                    </ul>
                </div>
            </div>
        </div>

        <sw-alert
            v-if="showWarning"
            class="sw-flow-upload-modal__warning"
            variant="warning"
        >
            <p>
                {{ $tc('sw-flow-sharing.uploadModal.warningAlert.description') }}
            </p>

            <div v-if="requiredSWVersion">
                <p>{{ $tc('sw-flow-sharing.uploadModal.warningAlert.shopwareVersionLabel') }}</p>
                <ul>
                    <li>
                        {{ requiredSWVersion }}
                    </li>
                </ul>
            </div>

            <div v-if="requiredPlugins || requiredApps">
                <p>{{ $tc('sw-flow-sharing.uploadModal.warningAlert.extensionsLabel') }}</p>
                <ul v-if="requiredPlugins.length">
                    <li
                        v-for="(item, index) in requiredPlugins"
                        :key="index"
                    >
                        {{ item }}
                    </li>
                </ul>

                <ul v-if="requiredApps.length">
                    <li
                        v-for="(item, index) in requiredApps"
                        :key="index"
                    >
                        {{ item }}
                    </li>
                </ul>
            </div>
        </sw-alert>
    </div>
    <div v-else>
        <sw-alert
            class="sw-flow-upload-modal__warning"
            variant="warning"
        >
            <p>
                <strong>{{ $tc('sw-flow-sharing.uploadModal.warningAlert.ruleConflictLabel') }}</strong> <br>
                {{ $tc('sw-flow-sharing.uploadModal.warningAlert.ruleConflictDescription', 'flowFile', { flowFile: jsonFile.name }) }}
            </p>
        </sw-alert>

        <div
            v-if="affectedRules.length"
            class="sw-flow-upload-modal__affected-rules"
        >
            <p>
                <strong>{{$tc('sw-flow-sharing.uploadModal.affectedRules')}}:</strong>
            </p>
            <ul>
                <li
                    v-for="(rule, index) in affectedRules"
                    :key="index"
                >
                    {{ rule.name }}
                </li>
            </ul>
        </div>
        <div class="sw-flow-upload-modal__resolve-rule-conflict">
            <sw-radio-field
                {% if VUE3 %}
                v-model:value="keepLocalRules"
                {% else %}
                v-model="keepLocalRules"
                {% endif %}
                block
                class="sw-flow-upload-modal__resolve-rule-conflict-option"
                identification=""
                :options="resolveRulesConflictOptions"
                :disabled="!acl.can('sales_channel.editor')"
            />
        </div>
    </div>
    <template #modal-footer>
        <sw-button
            class="sw-flow-upload-modal__cancel-button"
            size="small"
            @click="onCancel"
        >
            {{ $tc('global.default.cancel') }}
        </sw-button>

        <sw-button
            class="sw-flow-upload-modal__upload-button"
            variant="primary"
            size="small"
            :disabled="!acl.can('flow.creator') || disableUpload"
            @click="onUpload"
        >
            {{ $tc('sw-flow-sharing.uploadButton') }}
        </sw-button>
    </template>
</sw-modal>

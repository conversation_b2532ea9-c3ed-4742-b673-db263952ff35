<sw-modal
    class="sw-flow-download-modal"
    :title="$tc('sw-flow-sharing.downloadModal.title')"
    @modal-close="onCancel"
>
    <div class="sw-flow-download-modal__description">
        <p v-html="$tc('sw-flow-sharing.downloadModal.description')"></p>
    </div>

    <div class="sw-flow-download-modal__included">
        <div class="sw-flow-download-modal__data-included">
            <ul>
                <li
                    v-for="(rule, index) in rules"
                    :key="index"
                >
                    <sw-checkbox-field
                        class="sw-flow-download-modal__rule-item"
                        :value="!!rule.id"
                        :label="$tc('sw-flow-sharing.downloadModal.ruleLabel',0, { ruleName: rule.name })"
                        @change="(checked) => handleSelectRule(rule, checked)"
                    />
                </li>
            </ul>

            <ul>
                <li
                    v-for="(mail, index) in mailTemplates"
                    :key="index"
                >
                    <sw-checkbox-field
                        class="sw-flow-download-modal__mail-template-item"
                        :value="!!mail.id"
                        :label="$tc('sw-flow-sharing.downloadModal.mailTemplateLabel',0, { mail: mail.mailTemplateTypeName })"
                        @change="(checked) => handleSelectMail(mail, checked)"
                    />
                </li>
            </ul>
        </div>
    </div>

    <template #modal-footer>
        <sw-button
            class="sw-flow-download-modal__cancel-button"
            size="small"
            @click="onCancel"
        >
            {{ $tc('global.default.cancel') }}
        </sw-button>

        <sw-button
            class="sw-flow-download-modal__download-button"
            variant="primary"
            size="small"
            :disabled="!acl.can('flow.viewer')"
            @click="onDownload"
        >
            {{ $tc('sw-flow-sharing.downloadModal.downloadButton') }}
        </sw-button>
    </template>
</sw-modal>

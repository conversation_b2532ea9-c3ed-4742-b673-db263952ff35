{% set currencyIsoCode = order.currency.isoCode %}
{% set return = order.extensions.returns|first %}
Hallo {% if order.orderCustomer.salutation %}{{ order.orderCustomer.salutation.translated.letterName ~ ' ' }}{% endif %}{{ order.orderCustomer.firstName }} {{ order.orderCustomer.lastName }},

Ihr Retourenantrag ist am {{ return.createdAt|format_datetime('medium', 'short', locale='de-DE') }} bei uns eingegangen.

Bestellnummer: {{ order.orderNumber }}
Retourennummer: {{ return.returnNumber }}

Informationen zu Ihrer Retoure:

Pos.   Mengen.			Name des Produkts 			Preis pro Einheit			Steuer			Insgesamt

{% for returnItem in return.lineItems %}
    {{ loop.index }}      {{ returnItem.quantity }}        {{ returnItem.lineItem.label|u.wordwrap(80) }}        {{ returnItem.price.unitPrice|currency(currencyIsoCode) }}
    {%if returnItem.price.taxRules|length > 0 %} {{ (returnItem.price.taxRules|first).taxRate }}% {% endif %}			{{ returnItem.price.totalPrice|currency(currencyIsoCode) }}
{% endfor %}

Den aktuellen Status Ihrer Retoure können Sie auch jederzeit auf unserer Webseite im  Bereich "Mein Konto" - "Meine Bestellungen" abrufen: {{ rawUrl('frontend.account.order.single.page', { 'deepLinkCode': order.deepLinkCode }, salesChannel.domains|first.url) }}
Für Rückfragen stehen wir Ihnen jederzeit gerne zur Verfügung.

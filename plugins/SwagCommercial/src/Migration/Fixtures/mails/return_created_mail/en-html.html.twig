{# Currently, it only support for the first return, will be changed in the future#}
<style>
    th, td {
        padding: 0 15px;
    }
</style>
<div style="font-family:arial; font-size:12px;">
    {% set currencyIsoCode = order.currency.isoCode %}
    {% set return = order.extensions.returns|first %}
    {% if order.orderCustomer.salutation %}{{ order.orderCustomer.salutation.translated.letterName ~ ' ' }}{% endif %}{{ order.orderCustomer.firstName }} {{ order.orderCustomer.lastName }},<br>
    <br>
    We have received your return request from {{ return.createdAt|format_datetime('medium', 'short', locale='en-GB') }}.<br>
    <br>
    Order number: {{ order.orderNumber }}<br>
    Return number: {{ return.returnNumber }}<br>
    <br>
    <strong>Information on your return:</strong><br>
    <br>

    <table border="0" style="font-family:Arial, Helvetica, sans-serif; font-size:12px;">
        <tr>
            <td bgcolor="#F7F7F2" style="border-bottom:1px solid #cccccc;"><strong>Quantities</strong></td>
            <td bgcolor="#F7F7F2" style="border-bottom:1px solid #cccccc;"><strong>Product name</strong></td>
            <td bgcolor="#F7F7F2" style="border-bottom:1px solid #cccccc;"><strong>Unit price</strong></td>
            <td bgcolor="#F7F7F2" style="border-bottom:1px solid #cccccc;"><strong>Tax</strong></td>
            <td bgcolor="#F7F7F2" style="border-bottom:1px solid #cccccc;"><strong>Total</strong></td>
        </tr>
        {% for returnItem in return.lineItems %}
            <tr>
                <td style="text-align: center">{{ returnItem.quantity }}</td>
                <td>{{ returnItem.lineItem.label|u.wordwrap(80) }}</td>
                <td>{{ returnItem.price.unitPrice|currency(currencyIsoCode) }}</td>
                <td>{%if returnItem.price.taxRules|length > 0 %} {{ (returnItem.price.taxRules|first).taxRate }}% {% endif %}</td>
                <td>{{ returnItem.price.totalPrice|currency(currencyIsoCode) }}</td>
            </tr>
        {% endfor %}
    </table>
    <p>
        <br>
        You can check the current status of your return on our website under "My account" - "My orders" anytime: {{ rawUrl('frontend.account.order.single.page', { 'deepLinkCode': order.deepLinkCode }, salesChannel.domains|first.url) }}
        </br>
        If you have any questions, do not hesitate to contact us.
    </p>
    <br>
</div>

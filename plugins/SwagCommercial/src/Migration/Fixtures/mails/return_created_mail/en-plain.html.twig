{% set currencyIsoCode = order.currency.isoCode %}
{% set return = order.extensions.returns|first %}
{% if order.orderCustomer.salutation %}{{ order.orderCustomer.salutation.translated.letterName ~ ' ' }}{% endif %}{{ order.orderCustomer.firstName }} {{ order.orderCustomer.lastName }},

We have received your return request from {{ return.createdAt|format_datetime('medium', 'short', locale='en-GB') }}.

Order number: {{ order.orderNumber }}
Return number: {{ return.returnNumber }}

Information on your return:

Pos.   Quantities			Product name			Unit price			Tax			Total

{% for returnItem in return.lineItems %}
    {{ loop.index }}      {{ returnItem.quantity }}        {{ returnItem.lineItem.label|u.wordwrap(80) }}        {{ returnItem.price.unitPrice|currency(currencyIsoCode) }}
    {%if returnItem.price.taxRules|length > 0 %} {{ (returnItem.price.taxRules|first).taxRate }}% {% endif %}			{{ returnItem.price.totalPrice|currency(currencyIsoCode) }}
{% endfor %}


You can check the current status of your return on our website under "My account" - "My orders" anytime: {{ rawUrl('frontend.account.order.single.page', { 'deepLinkCode': order.deepLinkCode }, salesChannel.domains|first.url) }}
If you have any questions, do not hesitate to contact us.

However, in case you have purchased without a registration or a customer account, you do not have this option.

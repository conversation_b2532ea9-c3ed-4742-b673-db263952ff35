{% set order = subscription.convertedOrder %}

<div style="font-family:arial; font-size:12px;">
    {% if subscription.subscriptionCustomer.salutation %}{{ subscription.subscriptionCustomer.salutation.translated.letterName ~ ' ' }}{% endif %}{{ subscription.subscriptionCustomer.firstName }} {{ subscription.subscriptionCustomer.lastName }},<br>
    <br>
    <PERSON><PERSON>, <PERSON><PERSON><PERSON> mitte<PERSON> zu müssen, dass Ihr {{ subscription.subscriptionPlanName }} storniert wurde.<br>
    <br>
    Kündigungsdatum: {{ subscription.updatedAt|format_datetime('medium', 'short', locale='de-DE') }}<br>
    <br>
    Gekündigtes Abonnement:
    <br>
    Abonnement-Plan: {{ subscription.subscriptionPlanName }}
    Startdatum: {{ subscription.createdAt|format_datetime('medium', 'short', locale='de-DE') }}
    <br>
    <<table border="0" style="font-family:Arial, Helvetica, sans-serif; font-size:12px;">
        <tr>
            <td bgcolor="#F7F7F2" style="border-bottom:1px solid #cccccc;"><strong>Pos.</strong></td>
            <td bgcolor="#F7F7F2" style="border-bottom:1px solid #cccccc;"><strong>Artikel-Nr.</strong></td>
            <td bgcolor="#F7F7F2" style="border-bottom:1px solid #cccccc;"><strong>Produktbild(Alt-Text)</strong></td>
            <td bgcolor="#F7F7F2" style="border-bottom:1px solid #cccccc;"><strong>Beschreibung</strong></td>
            <td bgcolor="#F7F7F2" style="border-bottom:1px solid #cccccc;"><strong>Menge</strong></td>
            <td bgcolor="#F7F7F2" style="border-bottom:1px solid #cccccc;"><strong>Preis</strong></td>
            <td bgcolor="#F7F7F2" style="border-bottom:1px solid #cccccc;"><strong>Summe</strong></td>
        </tr>

        {% for lineItem in order.lineItems %}
            {% if lineItem.coverId is defined and lineItem.coverId is not null %}
                {% set cover = searchMedia([lineItem.coverId], context).first %}
            {% endif %}

            <tr>
                <td>{{ loop.index }}</td>
                <td>{% if lineItem.payload.productNumber is defined %}{{ lineItem.payload.productNumber|u.wordwrap(80) }}{% endif %}</td>
                <td>{% if cover is defined and cover is not null %}<img src="{{ cover.url }}" width="75" height="auto"/>{% endif %}</td>
                <td>
                    <div>{{ lineItem.label|u.wordwrap(80) }}</div>

                    {% if lineItem.payload.options is defined and lineItem.payload.options|length >= 1 %}
                        <div>
                            {% for option in lineItem.payload.options %}
                                {{ option.group }}: {{ option.option }}
                                {% if lineItem.payload.options|last != option %}
                                    {{ " | " }}
                                {% endif %}
                            {% endfor %}
                        </div>
                    {% endif %}

                    {% if lineItem.payload.features is defined and lineItem.payload.features|length >= 1 %}
                        {% set referencePriceFeatures = lineItem.payload.features|filter(feature => feature.type == 'referencePrice') %}
                        {% if referencePriceFeatures|length >= 1 %}
                            {% set referencePriceFeature = referencePriceFeatures|first %}
                            <div>
                                {{ referencePriceFeature.value.purchaseUnit }} {{ referencePriceFeature.value.unitName }}
                                ({{ referencePriceFeature.value.price|currency(subscription.currency.isoCode) }}* / {{ referencePriceFeature.value.referenceUnit }} {{ referencePriceFeature.value.unitName }})
                            </div>
                        {% endif %}
                    {% endif %}
                </td>
                <td style="text-align: center">{{ lineItem.quantity }}</td>
                <td>{{ lineItem.price.unitPrice|currency(subscription.currency.isoCode) }}</td>
                <td>{{ lineItem.price.totalPrice|currency(subscription.currency.isoCode) }}</td>
            </tr>
        {% endfor %}
    </table>
    <br>
    Abonnement Kosten: {{ order.price.totalPrice|currency(subscription.currency.isoCode,decimals=order.totalRounding.decimals) }}
    <br>
    Abrechnungszeitraum: {{ subscription.subscriptionInterval.translated.name }}
    <br>
    Sie können Ihr Abonnement auf unserer Website unter "Mein Konto" - "Meine Abonnements" jederzeit aktivieren:<br>
    <br>
    Für Rückfragen stehen wir Ihnen jederzeit gerne zur Verfügung.<br>
    <br>
</div>

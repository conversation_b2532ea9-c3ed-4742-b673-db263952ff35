<style>
    th, td {
        padding: 0 15px;
    }
</style>
<div style="font-family:arial; font-size:12px;">
    {% set currencyIsoCode = quote.currency.isoCode %}
    {% if quote.customer.salutation %}{{ quote.customer.salutation.translated.letterName ~ ' ' }}{% endif %}{{ quote.customer.firstName }} {{ quote.customer.lastName }},<br>
    <br>
    Wir haben Ihre Quote-anfrage am {{ quote.createdAt|format_datetime('medium', 'short', locale='en-GB') }} erhalten.<br>
    <br>
    Quote Nummer: {{ quote.quoteNumber }}<br>
    <br>
    <strong>Informationen zu Ihrem Quote:</strong><br>
    <br>

    <table border="0" style="font-family:Arial, Helvetica, sans-serif; font-size:12px;">
        <tr>
            <td bgcolor="#F7F7F2" style="border-bottom:1px solid #cccccc;"><strong>Quantities</strong></td>
            <td bgcolor="#F7F7F2" style="border-bottom:1px solid #cccccc;"><strong>Produkt name</strong></td>
            <td bgcolor="#F7F7F2" style="border-bottom:1px solid #cccccc;"><strong>Einzelpreis</strong></td>
            <td bgcolor="#F7F7F2" style="border-bottom:1px solid #cccccc;"><strong>Gesamt</strong></td>
        </tr>
        {% for item in quote.lineItems %}
            <tr>
                <td style="text-align: center">{{ item.quantity }}</td>
                <td>
                    {{ item.label|u.wordwrap(80) }}

                    {% if item.payload.options is defined and item.payload.options|length >= 1 %}
                        <div>(
                            {% for option in item.payload.options %}
                                {{ option.group }}: {{ option.option }}
                                {% if item.payload.options|last != option %}
                                    {{ " | " }}
                                {% endif %}
                            {% endfor %}
                            )</div>
                    {% endif %}
                </td>
                <td>{{ item.price.unitPrice|currency(currencyIsoCode) }}</td>
                <td>{{ item.price.totalPrice|currency(currencyIsoCode) }}</td>
            </tr>
        {% endfor %}
    </table>
    <br>
    <strong>Gesamtbetrag:</strong> {{ quote.amountTotal }}
    <br>
    <p>
        <br>
        Sie können den aktuellen Status Ihres Quotes jederzeit auf unserer Website unter "Mein Konto" - "Quote" überprüfen: {{ rawUrl('frontend.account.quote.detail', { 'id': quote.id }, salesChannel.domains|first.url) }}
        </br>
        Wenn Sie Fragen haben, zögern Sie nicht, uns zu kontaktieren.
    </p>
    <br>
</div>

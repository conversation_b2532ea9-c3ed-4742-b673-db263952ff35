{% set currencyIsoCode = quote.currency.isoCode %}
{% if quote.customer.salutation %}{{ quote.customer.salutation.translated.letterName ~ ' ' }}{% endif %}{{ quote.customer.firstName }} {{ quote.customer.lastName }},

We have received your quote request from {{ quote.createdAt|format_datetime('medium', 'short', locale='en-GB') }}.

Quote number: {{ quote.quoteNumber }}

Information on your quote:

Pos.   Quantities			Product name			Unit price			Total

{% for item in quote.lineItems %}
    {{ loop.index }}      {{ item.quantity }}        {{ item.label|u.wordwrap(80) }}{% if item.payload.options is defined and item.payload.options|length >= 1 %}, {% for option in item.payload.options %}{{ option.group }}: {{ option.option }}{% if item.payload.options|last != option %}{{ " | " }}{% endif %}{% endfor %}{% endif %}        {{ item.price.unitPrice|currency(currencyIsoCode) }}
    {{ item.price.totalPrice|currency(currencyIsoCode) }}
{% endfor %}

Total price: {{ quote.amountTotal }}


You can check the current status of your quote on our website under "My account" - "Quotes" anytime: {{ rawUrl('frontend.account.quote.detail', { 'id': quote.id }, salesChannel.domains|first.url) }}
If you have any questions, do not hesitate to contact us.

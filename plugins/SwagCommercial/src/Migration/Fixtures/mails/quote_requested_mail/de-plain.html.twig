{% set currencyIsoCode = quote.currency.isoCode %}
{% if quote.customer.salutation %}{{ quote.customer.salutation.translated.letterName ~ ' ' }}{% endif %}{{ quote.customer.firstName }} {{ quote.customer.lastName }},

Wir haben Ihre Quote-anfrage am {{ quote.createdAt|format_datetime('medium', 'short', locale='en-GB') }} erhalten.

Quote Nummer: {{ quote.quoteNumber }}

Informationen zu Ihrem Quote:

Pos.   Mengen			Produktname			Einzelpreis			Gesamt

{% for item in quote.lineItems %}
    {{ loop.index }}      {{ item.quantity }}        {{ item.label|u.wordwrap(80) }}{% if item.payload.options is defined and item.payload.options|length >= 1 %}, {% for option in item.payload.options %}{{ option.group }}: {{ option.option }}{% if item.payload.options|last != option %}{{ " | " }}{% endif %}{% endfor %}{% endif %}        {{ item.price.unitPrice|currency(currencyIsoCode) }}
    {{ item.price.totalPrice|currency(currencyIsoCode) }}
{% endfor %}

Gesamtbetrag: {{ quote.amountTotal }}


Sie können den aktuellen Status Ihres Quotes jederzeit auf unserer Website unter "Mein Konto" - "Quote" überprüfen: {{ rawUrl('frontend.account.quote.detail', { 'id': quote.id }, salesChannel.domains|first.url) }}
Wenn Sie Fragen haben, zögern Sie nicht, uns zu kontaktieren.

{# Currently, it only support for the first return, will be changed in the future#}
<div style="font-family:arial; font-size:12px;">
    <br/>
    <p>
        {% set return = order.extensions.returns|first %}
        {% if order.orderCustomer.salutation %}{{ order.orderCustomer.salutation.translated.letterName ~ ' ' }}{% endif %}{{ order.orderCustomer.firstName }} {{ order.orderCustomer.lastName }},<br/>
        <br/>
        der Status Ihrer Rücksendung bei {{ salesChannel.translated.name }} (Number: {{ return.returnNumber }}) vom {{ return.createdAt|format_datetime('medium', 'short', locale='de-DE') }} hat sich geändert.<br/>
        <strong>Die Rücksendung hat jetzt den Status: {{ return.stateMachineState.translated.name }}.</strong><br/>
        <br/>
        Den aktuellen Status der Rücksendung können Sie auch jederzeit auf unserer Webseite im  Bereich "Mein Konto" - "<PERSON>ne <PERSON>ellungen" abrufen: {{ rawUrl('frontend.account.order.single.page', { 'deepLinkCode': order.deepLinkCode }, salesChannel.domains|first.url) }}
        </br>
        Sollten Sie allerdings den Kauf ohne Registrierung, also ohne Anlage eines Kundenkontos, gewählt haben, steht Ihnen diese Möglichkeit nicht zur Verfügung.
    </p>
</div>

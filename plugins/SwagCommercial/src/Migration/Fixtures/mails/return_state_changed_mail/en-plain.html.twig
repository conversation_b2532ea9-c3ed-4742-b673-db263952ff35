{# Currently, it only support for the first return, will be changed in the future#}
{% set return = order.extensions.returns|first %}
{% if order.orderCustomer.salutation %}{{ order.orderCustomer.salutation.translated.letterName ~ ' ' }}{% endif %}{{ order.orderCustomer.firstName }} {{ order.orderCustomer.lastName }},

the status of your return at {{ salesChannel.translated.name }} (Number: {{ return.returnNumber }}) on {{ return.createdAt|format_datetime('medium', 'short', locale='en-GB') }}  has changed.
The new status is as follows: {{ return.state.translated.name }}.

You can check the current status of your returned items on our website under "My account" - "My orders" anytime: {{ rawUrl('frontend.account.order.single.page', { 'deepLinkCode': order.deepLinkCode }, salesChannel.domains|first.url) }}
However, in case you have purchased without a registration or a customer account, you do not have this option.

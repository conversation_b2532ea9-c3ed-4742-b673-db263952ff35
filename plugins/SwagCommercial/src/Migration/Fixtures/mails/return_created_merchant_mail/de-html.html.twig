{# Currently, it only support for the first return, will be changed in the future#}
<style>
    th, td {
        padding: 0 15px;
    }
</style>
<div style="font-family:arial; font-size:12px;">

    {% set currencyIsoCode = order.currency.isoCode %}
    {% set return = order.extensions.returns|first %}
    Folgende Retouren Anfrage wurde gestellt
    <br>
    Bestellnummer: {{ order.orderNumber }}<br>
    Retourennummer: {{ return.returnNumber }}<br>
    <br>
    <strong>Artikel:</strong><br>
    <br>

    <table border="0" style="font-family:Arial, Helvetica, sans-serif; font-size:12px;">
        <tr>
            <td bgcolor="#F7F7F2" style="border-bottom:1px solid #cccccc;"><strong>Mengen.</strong></td>
            <td bgcolor="#F7F7F2" style="border-bottom:1px solid #cccccc;"><strong>Name des Produkts</strong></td>
            <td bgcolor="#F7F7F2" style="border-bottom:1px solid #cccccc;"><strong>Preis pro Einheit</strong></td>
            <td bgcolor="#F7F7F2" style="border-bottom:1px solid #cccccc;"><strong>Steuer</strong></td>
            <td bgcolor="#F7F7F2" style="border-bottom:1px solid #cccccc;"><strong>Insgesamt</strong></td>
            <td bgcolor="#F7F7F2" style="border-bottom:1px solid #cccccc;"><strong>Erstattung</strong></td>
        </tr>

        {% for returnItem in return.lineItems %}
            <tr>
                <td style="text-align: center">{{ returnItem.quantity }}</td>
                <td>{{ returnItem.lineItem.label|u.wordwrap(80) }}</td>
                <td>{{ returnItem.price.unitPrice|currency(currencyIsoCode) }}</td>
                <td>{%if returnItem.price.taxRules|length > 0 %} {{ (returnItem.price.taxRules|first).taxRate }}% {% endif %}</td>
                <td>{{ returnItem.price.totalPrice|currency(currencyIsoCode) }}</td>
                <td>{{ returnItem.refundAmount|currency(currencyIsoCode) }}</td>
            </tr>
        {% endfor %}
    </table>
</div>

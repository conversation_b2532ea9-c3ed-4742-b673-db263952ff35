{% set currencyIsoCode = order.currency.isoCode %}
{% set return = order.extensions.returns|first %}
Following Return Request was created

Order number: {{ order.orderNumber }}
Return number: {{ return.returnNumber }}

Items:

Pos.   Quantities			Product name			Unit price			Tax			Total			Refund

{% for returnItem in return.lineItems %}
    {{ loop.index }}      {{ returnItem.quantity }}        {{ returnItem.lineItem.label|u.wordwrap(80) }}        {{ returnItem.price.unitPrice|currency(currencyIsoCode) }}
    {%if returnItem.price.taxRules|length > 0 %} {{ (returnItem.price.taxRules|first).taxRate }}% {% endif %}			{{ returnItem.price.totalPrice|currency(currencyIsoCode) }}			{{ returnItem.refundAmount|currency(currencyIsoCode) }}
{% endfor %}

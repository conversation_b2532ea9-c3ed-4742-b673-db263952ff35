{% set currencyIsoCode = order.currency.isoCode %}
{% set return = order.extensions.returns|first %}
Folgende Retouren Anfrage wurde gestellt

Bestellnummer: {{ order.orderNumber }}
Retourennummer: {{ return.returnNumber }}

Artikel:

Pos.   Mengen.			Name des Produkts 			Preis pro Einheit			Steuer			Insgesamt			Erstattung

{% for returnItem in return.lineItems %}
    {{ loop.index }}      {{ returnItem.quantity }}        {{ returnItem.lineItem.label|u.wordwrap(80) }}        {{ returnItem.price.unitPrice|currency(currencyIsoCode) }}
    {%if returnItem.price.taxRules|length > 0 %} {{ (returnItem.price.taxRules|first).taxRate }}% {% endif %}			{{ returnItem.price.totalPrice|currency(currencyIsoCode) }}			{{ returnItem.refundAmount|currency(currencyIsoCode) }}
{% endfor %}

{% set order = subscription.convertedOrder %}

{% if subscription.subscriptionCustomer.salutation %}{{ subscription.subscriptionCustomer.salutation.translated.letterName ~ ' ' }}{% endif %}{{ subscription.subscriptionCustomer.firstName }} {{ subscription.subscriptionCustomer.lastName }},

We wanted to let you know that your {{ subscription.subscriptionPlanName }} has been successfully reactivated as requested.

The subscription will continue on the following date: {{ subscription.nextSchedule|format_datetime('medium', 'short', locale='en-GB') }}

Information on your subscription:

Subscription plan: {{ subscription.subscriptionPlanName }}
Start date: {{ subscription.createdAt|format_datetime('medium', 'short', locale='de-DE') }}

Pos.   Prod. No.			Product image(Alt text)			Description			Quantities			Price			Total

{% for lineItem in subscription.convertedOrder.lineItems %}
{% if lineItem.coverId is defined and lineItem.coverId is not null %}
    {% set cover = searchMedia([lineItem.coverId], context).first %}
{% endif %}
{{ loop.index }}      {% if lineItem.payload.productNumber is defined %}{{ lineItem.payload.productNumber|u.wordwrap(80) }}{% endif %}        {% if cover is defined and cover is not null %}{{ cover.alt }}{% endif %}        {{ lineItem.label|u.wordwrap(80) }}{% if lineItem.payload.options is defined and lineItem.payload.options|length >= 1 %}, {% for option in lineItem.payload.options %}{{ option.group }}: {{ option.option }}{% if lineItem.payload.options|last != option %}{{ " | " }}{% endif %}{% endfor %}{% endif %}{% if lineItem.payload.features is defined and lineItem.payload.features|length >= 1 %}{% set referencePriceFeatures = lineItem.payload.features|filter(feature => feature.type == 'referencePrice') %}{% if referencePriceFeatures|length >= 1 %}{% set referencePriceFeature = referencePriceFeatures|first %}, {{ referencePriceFeature.value.purchaseUnit }} {{ referencePriceFeature.value.unitName }}({{ referencePriceFeature.value.price|currency(subscription.currency.isoCode) }}* / {{ referencePriceFeature.value.referenceUnit }} {{ referencePriceFeature.value.unitName }}){% endif %}{% endif %}
{{ lineItem.quantity }}			{{ lineItem.price.unitPrice|currency(subscription.currency.isoCode) }}			{{ lineItem.price.totalPrice|currency(subscription.currency.isoCode) }}
{% endfor %}

Subscription costs: {{ order.price.totalPrice|currency(subscription.currency.isoCode,decimals=order.totalRounding.decimals) }}
Billing cycle: {{ subscription.subscriptionInterval.translated.name }}

You can activate your subscription on our website under "My account" - "My subscriptions" anytime:

If you have any questions, do not hesitate to contact us.

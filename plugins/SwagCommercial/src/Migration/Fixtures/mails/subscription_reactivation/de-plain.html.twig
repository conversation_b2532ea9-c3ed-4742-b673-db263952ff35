{% set order = subscription.convertedOrder %}

{% if subscription.subscriptionCustomer.salutation %}{{ subscription.subscriptionCustomer.salutation.translated.letterName ~ ' ' }}{% endif %}{{ subscription.subscriptionCustomer.firstName }} {{ subscription.subscriptionCustomer.lastName }},

Wir möchten Sie darüber informieren, dass Ihr {{ subscription.subscriptionPlanName }} wie gewünscht erfolgreich reaktiviert worden ist.

Das Abonnement wird am folgenden Datum fortgesetzt: {{ subscription.nextSchedule|format_datetime('medium', 'short', locale='de-DE') }}

Informationen zu Ihrem Abonnement:

Abonnement-Plan: {{ subscription.subscriptionPlanName }}
Startdatum: {{ subscription.createdAt|format_datetime('medium', 'short', locale='de-DE') }}

Pos.   Artikel-Nr.			Produktbild(Alt-Text) 			Beschreibung			Menge			Preis			Summe

{% for lineItem in subscription.convertedOrder.lineItems %}
{% if lineItem.coverId is defined and lineItem.coverId is not null %}
    {% set cover = searchMedia([lineItem.coverId], context).first %}
{% endif %}
{{ loop.index }}      {% if lineItem.payload.productNumber is defined %}{{ lineItem.payload.productNumber|u.wordwrap(80) }}{% endif %}        {% if cover is defined and cover is not null %}{{ cover.alt }}{% endif %}        {{ lineItem.label|u.wordwrap(80) }}{% if lineItem.payload.options is defined and lineItem.payload.options|length >= 1 %}, {% for option in lineItem.payload.options %}{{ option.group }}: {{ option.option }}{% if lineItem.payload.options|last != option %}{{ " | " }}{% endif %}{% endfor %}{% endif %}{% if lineItem.payload.features is defined and lineItem.payload.features|length >= 1 %}{% set referencePriceFeatures = lineItem.payload.features|filter(feature => feature.type == 'referencePrice') %}{% if referencePriceFeatures|length >= 1 %}{% set referencePriceFeature = referencePriceFeatures|first %}, {{ referencePriceFeature.value.purchaseUnit }} {{ referencePriceFeature.value.unitName }}({{ referencePriceFeature.value.price|currency(subscription.currency.isoCode) }}* / {{ referencePriceFeature.value.referenceUnit }} {{ referencePriceFeature.value.unitName }}){% endif %}{% endif %}
{{ lineItem.quantity }}			{{ lineItem.price.unitPrice|currency(subscription.currency.isoCode) }}			{{ lineItem.price.totalPrice|currency(subscription.currency.isoCode) }}
{% endfor %}

Abonnement Kosten: {{ order.price.totalPrice|currency(subscription.currency.isoCode,decimals=order.totalRounding.decimals) }}
Abrechnungszeitraum: {{ subscription.subscriptionInterval.translated.name }}

Sie können Ihr Abonnement auf unserer Website unter "Mein Konto" - "Meine Abonnements" jederzeit aktivieren:

Für Rückfragen stehen wir Ihnen jederzeit gerne zur Verfügung.

<style>
    th, td {
        padding: 0 15px;
    }
</style>
<div style="font-family:arial; font-size:12px;">
        {% set currencyIsoCode = quote.currency.isoCode %}
        Following quote request was created
        <br>
        Quote number: {{ quote.quoteNumber }}<br>
        <br>
        <strong>Items:</strong><br>
        <br>
    <table border="0" style="font-family:Arial, Helvetica, sans-serif; font-size:12px;">
        <tr>
            <td bgcolor="#F7F7F2" style="border-bottom:1px solid #cccccc;"><strong>Quantities</strong></td>
            <td bgcolor="#F7F7F2" style="border-bottom:1px solid #cccccc;"><strong>Product name</strong></td>
            <td bgcolor="#F7F7F2" style="border-bottom:1px solid #cccccc;"><strong>Unit price</strong></td>
            <td bgcolor="#F7F7F2" style="border-bottom:1px solid #cccccc;"><strong>Total</strong></td>
        </tr>
        {% for item in quote.lineItems %}
            <tr>
                <td style="text-align: center">{{ item.quantity }}</td>
                <td>
                    {{ item.label|u.wordwrap(80) }}

                    {% if item.payload.options is defined and item.payload.options|length >= 1 %}
                        <div>(
                            {% for option in item.payload.options %}
                                {{ option.group }}: {{ option.option }}
                                {% if item.payload.options|last != option %}
                                    {{ " | " }}
                                {% endif %}
                            {% endfor %}
                            )</div>
                    {% endif %}
                </td>
                <td>{{ item.price.unitPrice|currency(currencyIsoCode) }}</td>
                <td>{{ item.price.totalPrice|currency(currencyIsoCode) }}</td>
            </tr>
        {% endfor %}
    </table>
    <br>
    <strong>Total price:</strong> {{ quote.amountTotal }}
    <br>
</div>

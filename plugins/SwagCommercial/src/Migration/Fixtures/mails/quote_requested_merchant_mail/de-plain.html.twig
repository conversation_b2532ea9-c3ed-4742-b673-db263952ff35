{% set currencyIsoCode = quote.currency.isoCode %}
Folgende Quote-anfrage wurde erstellt

Quote Nummer: {{ quote.quoteNumber }}

Artikel:

Pos.   Mengen			Produktname			Einzelpreis			Gesamt

{% for item in quote.lineItems %}
    {{ loop.index }}      {{ item.quantity }}        {{ item.label|u.wordwrap(80) }}{% if item.payload.options is defined and item.payload.options|length >= 1 %}, {% for option in item.payload.options %}{{ option.group }}: {{ option.option }}{% if item.payload.options|last != option %}{{ " | " }}{% endif %}{% endfor %}{% endif %}        {{ item.price.unitPrice|currency(currencyIsoCode) }}
    {{ item.price.totalPrice|currency(currencyIsoCode) }}
{% endfor %}

Gesamtbetrag: {{ quote.amountTotal }}

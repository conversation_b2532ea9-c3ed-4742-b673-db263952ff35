{% set order = subscription.convertedOrder %}
{% set currencyIsoCode = subscription.currency.isoCode %}
{% set subscriptionPlanName = subscription.subscriptionPlan.activeStorefrontLabel ? subscription.subscriptionPlan.translated.label : subscription.subscriptionPlan.translated.name %}
{% if subscription.subscriptionCustomer.salutation %}{{ subscription.subscriptionCustomer.salutation.translated.letterName ~ ' ' }}{% endif %}{{ subscription.subscriptionCustomer.firstName }} {{ subscription.subscriptionCustomer.lastName }},

We have received your subscription from {{ subscription.createdAt|format_datetime('medium', 'short', locale='en-GB') }}.

Subscription number: {{ subscription.subscriptionNumber }}

As soon as your payment has been made, you will receive a separate notification and your subscription will be processed.

You may check the current status of your subscription with this link:
You can pause or cancel the automatic renewal of your subscription at any time in your personal account settings.

Information on your subscription:

Subscription plan: {{ subscriptionPlanName }}
Start date: {{ subscription.createdAt|format_datetime('medium', 'short', locale='de-DE') }}

Pos.   Prod. No.			Product image(Alt text)			Description			Quantities			Price			Total

{% for lineItem in order.lineItems %}
{% if lineItem.coverId is defined and lineItem.coverId is not null %}
    {% set cover = searchMedia([lineItem.coverId], context).first %}
{% endif %}
{{ loop.index }}      {% if lineItem.payload.productNumber is defined %}{{ lineItem.payload.productNumber|u.wordwrap(80) }}{% endif %}        {% if cover is defined and cover is not null %}{{ cover.alt }}{% endif %}        {{ lineItem.label|u.wordwrap(80) }}{% if lineItem.payload.options is defined and lineItem.payload.options|length >= 1 %}, {% for option in lineItem.payload.options %}{{ option.group }}: {{ option.option }}{% if lineItem.payload.options|last != option %}{{ " | " }}{% endif %}{% endfor %}{% endif %}{% if lineItem.payload.features is defined and lineItem.payload.features|length >= 1 %}{% set referencePriceFeatures = lineItem.payload.features|filter(feature => feature.type == 'referencePrice') %}{% if referencePriceFeatures|length >= 1 %}{% set referencePriceFeature = referencePriceFeatures|first %}, {{ referencePriceFeature.value.purchaseUnit }} {{ referencePriceFeature.value.unitName }}({{ referencePriceFeature.value.price|currency(currencyIsoCode) }}* / {{ referencePriceFeature.value.referenceUnit }} {{ referencePriceFeature.value.unitName }}){% endif %}{% endif %}
    {{ lineItem.quantity }}			{{ lineItem.price.unitPrice|currency(currencyIsoCode) }}			{{ lineItem.price.totalPrice|currency(currencyIsoCode) }}
{% endfor %}

Subscription costs: {{ order.price.totalPrice|currency(currencyIsoCode,decimals=order.totalRounding.decimals) }}
Billing cycle: {{ subscription.subscriptionInterval.translated.name }}
{% set delivery = order.deliveries[0] | default(null) %}

{% set displayRounded = order.totalRounding.interval != 0.01 or order.totalRounding.decimals != order.itemRounding.decimals %}
{% set decimals = order.totalRounding.decimals %}
{% set total = order.price.totalPrice %}
{% if displayRounded %}
    {% set total = order.price.rawTotal %}
    {% set decimals = order.itemRounding.decimals %}
{% endif %}

{% for shippingCost in order.deliveries %}
Shipping costs: {{ shippingCost.shippingCosts.totalPrice|currency(currencyIsoCode) }}
{% endfor %}
Net total: {{ order.price.netPrice|currency(currencyIsoCode) }}
{% for calculatedTax in order.price.calculatedTaxes %}
{% if order.price.taxStatus is same as('net') %}plus{% else %}including{% endif %} {{ calculatedTax.taxRate }}% VAT. {{ calculatedTax.tax|currency(currencyIsoCode) }}
{% endfor %}
Total gross: {{ total|currency(currencyIsoCode,decimals=decimals) }}
{% if displayRounded %}
Rounded total gross: {{ order.price.totalPrice|currency(currencyIsoCode,decimals=order.totalRounding.decimals) }}
{% endif %}

{% if delivery %}
Selected shipping type: {{ subscription.shippingMethod.translated.name }}
{{ subscription.shippingMethod.translated.description }}
{% endif %}

{% set billingAddress = subscription.billingAddress %}
Billing address:
{{ billingAddress.company }}
{{ billingAddress.firstName }} {{ billingAddress.lastName }}
{{ billingAddress.street }}
{{ billingAddress.zipcode }} {{ billingAddress.city }}
{{ billingAddress.country.translated.name }}

{% set shippingAddress = subscription.shippingAddress %}
Shipping address:
{{ shippingAddress.company }}
{{ shippingAddress.firstName }} {{ shippingAddress.lastName }}
{{ shippingAddress.street }}
{{ shippingAddress.zipcode}} {{ shippingAddress.city }}
{{ shippingAddress.country.translated.name }}


{% if subscription.subscriptionCustomer.vatIds %}
Your VAT-ID: {{ subscription.subscriptionCustomer.vatIds|first }}
In case of a successful order and if you are based in one of the EU countries, you will receive your goods exempt from turnover tax.
{% endif %}

You can check the current status of your subscription on our website under "My account" - "My subscriptions" anytime:
If you have any questions, do not hesitate to contact us.


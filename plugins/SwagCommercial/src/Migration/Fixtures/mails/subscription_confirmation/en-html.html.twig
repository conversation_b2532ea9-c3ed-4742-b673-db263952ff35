<div style="font-family:arial; font-size:12px;">

{% set order = subscription.convertedOrder %}
{% set currencyIsoCode = subscription.currency.isoCode %}
{% set subscriptionPlanName = subscription.subscriptionPlan.activeStorefrontLabel ? subscription.subscriptionPlan.translated.label : subscription.subscriptionPlan.translated.name %}
{% if subscription.subscriptionCustomer.salutation %}{{ subscription.subscriptionCustomer.salutation.translated.letterName ~ ' ' }}{% endif %}{{ subscription.subscriptionCustomer.firstName }} {{ subscription.subscriptionCustomer.lastName }},<br>
<br>
We have received your subscription from {{ subscription.createdAt|format_datetime('medium', 'short', locale='en-GB') }}.<br>
<br>
Subscription number: {{ subscription.subscriptionNumber }}<br>
<br>
As soon as your payment has been made, you will receive a separate notification and your subscription will be processed.<br>
<br>
You may check the current status of your subscription with this link:<br>
You can pause or cancel the automatic renewal of your subscription at any time in your personal account settings.<br>
<br>
<strong>Information on your subscription:</strong><br>
<br>
Subscription plan: {{ subscriptionPlanName }}
Start date: {{ subscription.createdAt|format_datetime('medium', 'short', locale='de-DE') }}
<br>

<table border="0" style="font-family:Arial, Helvetica, sans-serif; font-size:12px;">
    <tr>
        <td bgcolor="#F7F7F2" style="border-bottom:1px solid #cccccc;"><strong>Pos.</strong></td>
        <td bgcolor="#F7F7F2" style="border-bottom:1px solid #cccccc;"><strong>Prod. No.</strong></td>
        <td bgcolor="#F7F7F2" style="border-bottom:1px solid #cccccc;"><strong>Product image(Alt text)</strong></td>
        <td bgcolor="#F7F7F2" style="border-bottom:1px solid #cccccc;"><strong>Description</strong></td>
        <td bgcolor="#F7F7F2" style="border-bottom:1px solid #cccccc;"><strong>Quantities</strong></td>
        <td bgcolor="#F7F7F2" style="border-bottom:1px solid #cccccc;"><strong>Price</strong></td>
        <td bgcolor="#F7F7F2" style="border-bottom:1px solid #cccccc;"><strong>Total</strong></td>
    </tr>

    {% for lineItem in order.lineItems %}
        {% if lineItem.coverId is defined and lineItem.coverId is not null %}
            {% set cover = searchMedia([lineItem.coverId], context).first %}
        {% endif %}

        <tr>
            <td>{{ loop.index }}</td>
            <td>{% if lineItem.payload.productNumber is defined %}{{ lineItem.payload.productNumber|u.wordwrap(80) }}{% endif %}</td>
            <td>{% if cover is defined and cover is not null %}<img src="{{ cover.url }}" width="75" height="auto"/>{% endif %}</td>
            <td>
                <div>{{ lineItem.label|u.wordwrap(80) }}</div>

                {% if lineItem.payload.options is defined and lineItem.payload.options|length >= 1 %}
                    <div>
                        {% for option in lineItem.payload.options %}
                            {{ option.group }}: {{ option.option }}
                            {% if lineItem.payload.options|last != option %}
                                {{ " | " }}
                            {% endif %}
                        {% endfor %}
                    </div>
                {% endif %}

                {% if lineItem.payload.features is defined and lineItem.payload.features|length >= 1 %}
                    {% set referencePriceFeatures = lineItem.payload.features|filter(feature => feature.type == 'referencePrice') %}
                    {% if referencePriceFeatures|length >= 1 %}
                        {% set referencePriceFeature = referencePriceFeatures|first %}
                        <div>
                            {{ referencePriceFeature.value.purchaseUnit }} {{ referencePriceFeature.value.unitName }}
                            ({{ referencePriceFeature.value.price|currency(currencyIsoCode) }}* / {{ referencePriceFeature.value.referenceUnit }} {{ referencePriceFeature.value.unitName }})
                        </div>
                    {% endif %}
                {% endif %}
            </td>
            <td style="text-align: center">{{ lineItem.quantity }}</td>
            <td>{{ lineItem.price.unitPrice|currency(currencyIsoCode) }}</td>
            <td>{{ lineItem.price.totalPrice|currency(currencyIsoCode) }}</td>
        </tr>
    {% endfor %}
</table>

<br>
Subscription costs: {{ order.price.totalPrice|currency(currencyIsoCode,decimals=order.totalRounding.decimals) }}
<br>
Billing cycle: {{ subscription.subscriptionInterval.translated.name }}

{% set delivery = order.deliveries[0] | default(null) %}

{% set displayRounded = order.totalRounding.interval != 0.01 or order.totalRounding.decimals != order.itemRounding.decimals %}
{% set decimals = order.totalRounding.decimals %}
{% set total = order.price.totalPrice %}
{% if displayRounded %}
    {% set total = order.price.rawTotal %}
    {% set decimals = order.itemRounding.decimals %}
{% endif %}
<p>
    <br>
    <br>
    {% for shippingCost in order.deliveries %}
        Shipping costs: {{ shippingCost.shippingCosts.totalPrice|currency(currencyIsoCode) }}<br>
    {% endfor %}
    Net total:{{ order.price.netPrice|currency(currencyIsoCode) }}<br>
    {% for calculatedTax in order.price.calculatedTaxes %}
        {% if order.price.taxStatus is same as('net') %}plus{% else %}including{% endif %} {{ calculatedTax.taxRate }}% VAT. {{ calculatedTax.tax|currency(currencyIsoCode) }}<br>
    {% endfor %}
    {% if not displayRounded %}<strong>{% endif %}Total gross: {{ total|currency(currencyIsoCode,decimals=decimals) }}{% if not displayRounded %}</strong>{% endif %}<br>
    {% if displayRounded %}
        <strong>Rounded total gross: {{ order.price.totalPrice|currency(currencyIsoCode,decimals=order.totalRounding.decimals) }}</strong><br>
    {% endif %}
    <br>

    {% if delivery %}
        <strong>Selected shipping type:</strong> {{ subscription.shippingMethod.translated.name }}<br>
        {{ subscription.shippingMethod.translated.description }}<br>
        <br>
    {% endif %}

    {% set billingAddress = subscription.billingAddress %}
    <strong>Billing address:</strong><br>
    {{ billingAddress.company }}<br>
    {{ billingAddress.firstName }} {{ billingAddress.lastName }}<br>
    {{ billingAddress.street }}<br>
    {{ billingAddress.zipcode }} {{ billingAddress.city }}<br>
    {{ billingAddress.country.translated.name }}<br>
    <br>

    {% set shippingAddress = subscription.shippingAddress %}
    <strong>Shipping address:</strong><br>
    {{ shippingAddress.company }}<br>
    {{ shippingAddress.firstName }} {{ shippingAddress.lastName }}<br>
    {{ shippingAddress.street }}<br>
    {{ shippingAddress.zipcode}} {{ shippingAddress.city }}<br>
    {{ shippingAddress.country.translated.name }}<br>
    <br>

    {% if subscription.subscriptionCustomer.vatIds %}
        Your VAT-ID: {{ subscription.subscriptionCustomer.vatIds|first }}
        In case of a successful order and if you are based in one of the EU countries, you will receive your goods exempt from turnover tax. <br>
    {% endif %}
    <br>
    You can check the current status of your subscription on our website under "My account" - "My subscriptions" anytime:
    <br>
    If you have any questions, do not hesitate to contact us.
</p>
<br>
</div>

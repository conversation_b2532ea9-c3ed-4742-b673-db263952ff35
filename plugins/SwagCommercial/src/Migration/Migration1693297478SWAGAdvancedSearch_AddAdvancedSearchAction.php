<?php declare(strict_types=1);

namespace Shopware\Commercial\Migration;

use Doctrine\DBAL\Connection;
use Shopware\Core\Framework\Log\Package;
use Shopware\Core\Framework\Migration\MigrationStep;

/**
 * @internal
 */
#[Package('buyers-experience')]
class Migration1693297478SWAGAdvancedSearch_AddAdvancedSearchAction extends MigrationStep
{
    public function getCreationTimestamp(): int
    {
        return 1693297478;
    }

    public function update(Connection $connection): void
    {
        $connection->executeStatement('
            CREATE TABLE IF NOT EXISTS `advanced_search_action` (
              `id` BINARY(16) NOT NULL,
              `name` VARCHAR(255) NOT NULL,
              `type` VARCHAR(255) NOT NULL,
              `config_id` BINARY(16) NOT NULL,
              `active` TINYINT(1) NOT NULL DEFAULT 1,
              `valid_from` DATETIME(3) NULL,
              `valid_to` DATETIME(3) NULL,
              `type_config` JSON NOT NULL,
              `created_at` DATETIME(3) NOT NULL,
              `updated_at` DATETIME(3) NULL,
              CONSTRAINT `fk.advanced_search_action.config_id` FOREIGN KEY (`config_id`)
                REFERENCES `advanced_search_config` (`id`) ON DELETE CASCADE,
              PRIMARY KEY (`id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
        ');

        $connection->executeStatement('
            CREATE TABLE IF NOT EXISTS `advanced_search_action_search_term` (
              `id` BINARY(16) NOT NULL,
              `sales_channel_id` BINARY(16) NOT NULL,
              `action_id` BINARY(16) NOT NULL,
              `term` VARCHAR(255) NOT NULL,
              `created_at` DATETIME(3) NOT NULL,
              `updated_at` DATETIME(3) NULL,
              CONSTRAINT `fk.advanced_search_action_search_term.sales_channel_id` FOREIGN KEY (`sales_channel_id`)
                REFERENCES `sales_channel` (`id`) ON DELETE CASCADE,
              CONSTRAINT `fk.advanced_search_action_search_term.action_id` FOREIGN KEY (`action_id`)
                REFERENCES `advanced_search_action` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
              PRIMARY KEY (`id`),
              UNIQUE KEY `uk.advanced_search_action_search_term.sales_channel_id.term` (`sales_channel_id`, `term`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
        ');
    }

    public function updateDestructive(Connection $connection): void
    {
    }
}

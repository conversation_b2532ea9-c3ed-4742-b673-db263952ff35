<?php declare(strict_types=1);

namespace Shopware\Commercial\Migration;

use Doctrine\DBAL\Connection;
use Shopware\Commercial\B2B\QuoteManagement\Entity\Quote\QuoteStates;
use Shopware\Core\Framework\Log\Package;
use Shopware\Core\Framework\Migration\MigrationStep;
use Shopware\Core\Migration\Traits\StateMachineMigration;
use Shopware\Core\Migration\Traits\StateMachineMigrationTrait;
use Shopware\Core\System\StateMachine\StateMachineDefinition;

/**
 * @internal
 */
#[Package('customer-order')]
class Migration1692345852QuoteManagement_AddStateDraftToQuote extends MigrationStep
{
    use StateMachineMigrationTrait;

    public function getCreationTimestamp(): int
    {
        return 1692345852;
    }

    public function update(Connection $connection): void
    {
        $connection->update(
            StateMachineDefinition::ENTITY_NAME,
            ['initial_state_id' => null],
            ['technical_name' => QuoteStates::STATE_MACHINE]
        );

        $stateMachine = new StateMachineMigration(
            QuoteStates::STATE_MACHINE,
            'Quote state',
            'Quote state',
            [
                StateMachineMigration::state(QuoteStates::STATE_DRAFT, 'Entwurf', 'Draft'),
            ],
            [
                StateMachineMigration::transition(
                    QuoteStates::ACTION_ADMIN_SEND,
                    QuoteStates::STATE_DRAFT,
                    QuoteStates::STATE_REPLIED
                ),
            ],
            null
        );

        $this->import($stateMachine, $connection);
    }

    public function updateDestructive(Connection $connection): void
    {
        // implement update destructive
    }
}

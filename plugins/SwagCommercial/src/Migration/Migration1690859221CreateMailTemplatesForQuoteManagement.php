<?php declare(strict_types=1);

namespace Shopware\Commercial\Migration;

use Doctrine\DBAL\Connection;
use Shopware\Commercial\B2B\QuoteManagement\Domain\Mail\MailTemplateTypes;
use Shopware\Core\Defaults;
use Shopware\Core\Framework\Log\Package;
use Shopware\Core\Framework\Migration\MigrationStep;
use Shopware\Core\Framework\Uuid\Uuid;
use Shopware\Core\Migration\Traits\ImportTranslationsTrait;
use Shopware\Core\Migration\Traits\Translations;

/**
 *  @phpstan-type TemplateTypeItem array{id: string, type: string, name: string, nameDe: string, availableEntities: array<string, string|null>}
 *  @phpstan-type TemplateItem array{folder:string, sender: string, senderDe: string, subject: string, subjectDe: string, description: string, descriptionDe: string}
 *
 * @internal
 */
#[Package('customer-order')]
class Migration1690859221CreateMailTemplatesForQuoteManagement extends MigrationStep
{
    use ImportTranslationsTrait;

    public function getCreationTimestamp(): int
    {
        return 1690859221;
    }

    public function update(Connection $connection): void
    {
        $templateMapping = $this->getTemplatesMapping();
        foreach ($this->getTemplateTypesMapping() as $index => $type) {
            $templateId = $this->insertMailTemplateType($type, $connection);
            if ($templateId === null) {
                continue;
            }

            $this->insertMailTemplate($templateId, $templateMapping[$index], $connection);
        }
    }

    public function updateDestructive(Connection $connection): void
    {
        // implement update destructive
    }

    /**
     * @return TemplateTypeItem[]
     */
    private function getTemplateTypesMapping(): array
    {
        return [
            [
                'id' => Uuid::randomBytes(),
                'type' => MailTemplateTypes::MAILTYPE_QUOTE_REQUEST,
                'name' => 'Quote requested',
                'nameDe' => 'Angebot angefordert',
                'availableEntities' => ['quote' => 'quote', 'salesChannel' => 'sales_channel'],
            ],
            [
                'id' => Uuid::randomBytes(),
                'type' => MailTemplateTypes::MAILTYPE_QUOTE_REQUEST_MERCHANT,
                'name' => 'Quote requested (Administration)',
                'nameDe' => 'Angebot angefordert (Administration)',
                'availableEntities' => ['quote' => 'quote', 'salesChannel' => 'sales_channel'],
            ],
            [
                'id' => Uuid::randomBytes(),
                'type' => MailTemplateTypes::MAILTYPE_STATE_ENTER_QUOTE_STATE_IN_REVIEW,
                'name' => 'Enter quote state: In review',
                'nameDe' => 'Enter quote state: In review',
                'availableEntities' => ['quote' => 'quote', 'salesChannel' => 'sales_channel'],
            ],
            [
                'id' => Uuid::randomBytes(),
                'type' => MailTemplateTypes::MAILTYPE_STATE_ENTER_QUOTE_STATE_REPLIED,
                'name' => 'Enter quote state: Replied',
                'nameDe' => 'Enter quote state: Replied',
                'availableEntities' => ['quote' => 'quote', 'salesChannel' => 'sales_channel'],
            ],
            [
                'id' => Uuid::randomBytes(),
                'type' => MailTemplateTypes::MAILTYPE_STATE_ENTER_QUOTE_STATE_ACCEPTED,
                'name' => 'Enter quote state: Accepted (Administration)',
                'nameDe' => 'Enter quote state: Accepted (Administration)',
                'availableEntities' => ['quote' => 'quote', 'salesChannel' => 'sales_channel'],
            ],
            [
                'id' => Uuid::randomBytes(),
                'type' => MailTemplateTypes::MAILTYPE_STATE_ENTER_QUOTE_STATE_DECLINED,
                'name' => 'Enter quote state: Declined (Administration)',
                'nameDe' => 'Enter quote state: Declined (Administration)',
                'availableEntities' => ['quote' => 'quote', 'salesChannel' => 'sales_channel'],
            ],
        ];
    }

    /**
     * @return TemplateItem[]
     */
    private function getTemplatesMapping(): array
    {
        return [
            [
                'folder' => 'quote_requested_mail',
                'sender' => '{{ salesChannel.name }}',
                'senderDe' => '{{ salesChannel.name }}',
                'subject' => 'Your quote with {{ salesChannel.name }} was requested',
                'subjectDe' => 'Ihre Angebot bei {{ salesChannel.name }} wurde angefordert',
                'description' => 'Quote management Basis Template',
                'descriptionDe' => 'Angebotsmanagement Basis Template',
            ],
            [
                'folder' => 'quote_requested_merchant_mail',
                'sender' => '{{ salesChannel.name }}',
                'senderDe' => '{{ salesChannel.name }}',
                'subject' => 'Customer in {{ salesChannel.name }} requested a quote {{ quote.quoteNumber }}',
                'subjectDe' => 'Kunde in {{ salesChannel.name }} hat eine Angebot {{ quote.quoteNumber }} angefordert',
                'description' => 'Quote management Basis Template',
                'descriptionDe' => 'Angebotsmanagement Basis Template',
            ],
            [
                'folder' => 'quote_state_changed_mail',
                'sender' => '{{ salesChannel.name }}',
                'senderDe' => '{{ salesChannel.name }}',
                'subject' => 'Your quote with {{ salesChannel.name }} is in review',
                'subjectDe' => 'Ihre Angebot bei {{ salesChannel.name }} wurde in Überprüfung',
                'description' => 'Quote management Basis Template',
                'descriptionDe' => 'Angebotsmanagement Basis Template',
            ],
            [
                'folder' => 'quote_state_changed_mail',
                'sender' => '{{ salesChannel.name }}',
                'senderDe' => '{{ salesChannel.name }}',
                'subject' => 'Your quote with {{ salesChannel.name }} is replied',
                'subjectDe' => 'Ihre Angebot bei {{ salesChannel.name }} wurde antwortet',
                'description' => 'Quote management Basis Template',
                'descriptionDe' => 'Angebotsmanagement Basis Template',
            ],
            [
                'folder' => 'quote_state_changed_mail',
                'sender' => '{{ salesChannel.name }}',
                'senderDe' => '{{ salesChannel.name }}',
                'subject' => 'Customer in {{ salesChannel.name }} accepted a quote {{ quote.quoteNumber }}',
                'subjectDe' => 'Kunde in {{ salesChannel.name }} hat eine Angebot {{ quote.quoteNumber }} akzeptiert',
                'description' => 'Quote management Basis Template',
                'descriptionDe' => 'Angebotsmanagement Basis Template',
            ],
            [
                'folder' => 'quote_state_changed_mail',
                'sender' => '{{ salesChannel.name }}',
                'senderDe' => '{{ salesChannel.name }}',
                'subject' => 'Customer in {{ salesChannel.name }} declined a quote {{ quote.quoteNumber }}',
                'subjectDe' => 'Kunde in {{ salesChannel.name }} hat eine Angebot {{ quote.quoteNumber }} abgelehnt',
                'description' => 'Quote management Basis Template',
                'descriptionDe' => 'Angebotsmanagement Basis Template',
            ],
        ];
    }

    /**
     * @param TemplateTypeItem $templateTypeItem
     */
    private function insertMailTemplateType($templateTypeItem, Connection $connection): ?string
    {
        $technicalName = $templateTypeItem['type'];
        $isExistingMailTemplate = $this->mailTemplatesExist($technicalName, $connection);
        if ($isExistingMailTemplate) {
            return null;
        }

        $nameEn = $templateTypeItem['name'];
        $nameDe = $templateTypeItem['nameDe'];

        $existingTypeId = $this->getExistingMailTemplateTypeId($technicalName, $connection);
        $typeId = $existingTypeId ?: $templateTypeItem['id'];

        if (!$existingTypeId) {
            $connection->insert(
                'mail_template_type',
                [
                    'id' => $typeId,
                    'technical_name' => $technicalName,
                    'available_entities' => json_encode($templateTypeItem['availableEntities']),
                    'created_at' => (new \DateTime())->format(Defaults::STORAGE_DATE_TIME_FORMAT),
                ]
            );

            $translations = new Translations(
                [
                    'mail_template_type_id' => $typeId,
                    'name' => $nameDe,
                ],
                [
                    'mail_template_type_id' => $typeId,
                    'name' => $nameEn,
                ]
            );

            $this->importTranslation('mail_template_type_translation', $translations, $connection);
        }

        $templateId = Uuid::randomBytes();

        $connection->insert(
            'mail_template',
            [
                'id' => $templateId,
                'mail_template_type_id' => $typeId,
                'system_default' => 1,
                'created_at' => (new \DateTime())->format(Defaults::STORAGE_DATE_TIME_FORMAT),
            ]
        );

        return $templateId;
    }

    /**
     * @param TemplateItem $templateItem
     */
    private function insertMailTemplate(string $templateId, $templateItem, Connection $connection): void
    {
        $mailContentHtmlEn = \file_get_contents(__DIR__ . '/Fixtures/mails/' . $templateItem['folder'] . '/en-html.html.twig');
        $mailContentPlainEn = \file_get_contents(__DIR__ . '/Fixtures/mails/' . $templateItem['folder'] . '/en-plain.html.twig');

        $mailContentHtmlDe = \file_get_contents(__DIR__ . '/Fixtures/mails/' . $templateItem['folder'] . '/de-html.html.twig');
        $mailContentPlainDe = \file_get_contents(__DIR__ . '/Fixtures/mails/' . $templateItem['folder'] . '/de-plain.html.twig');

        $translations = new Translations(
            [
                'mail_template_id' => $templateId,
                'sender_name' => $templateItem['senderDe'],
                'subject' => $templateItem['subjectDe'],
                'description' => $templateItem['descriptionDe'],
                'content_html' => $mailContentHtmlDe,
                'content_plain' => $mailContentPlainDe,
            ],
            [
                'mail_template_id' => $templateId,
                'sender_name' => $templateItem['sender'],
                'subject' => $templateItem['subject'],
                'description' => $templateItem['description'],
                'content_html' => $mailContentHtmlEn,
                'content_plain' => $mailContentPlainEn,
            ],
        );

        $this->importTranslation('mail_template_translation', $translations, $connection);
    }

    private function getExistingMailTemplateTypeId(string $technicalName, Connection $connection): ?string
    {
        $result = $connection->createQueryBuilder()
            ->select('id')
            ->from('mail_template_type')
            ->where('technical_name = :technicalName')
            ->setParameter('technicalName', $technicalName)
            ->executeQuery()
            ->fetchOne();

        return \is_string($result) ? $result : null;
    }

    private function mailTemplatesExist(string $technicalName, Connection $connection): bool
    {
        return (bool) $connection->fetchOne(
            'SELECT count(`id`) FROM `mail_template`
             WHERE `mail_template_type_id`
             IN (SELECT `id` FROM `mail_template_type` WHERE `technical_name` = :technical_name)',
            [
                'technical_name' => $technicalName,
            ]
        );
    }
}

<?php declare(strict_types=1);

namespace Shopware\Commercial\Migration;

use Doctrine\DBAL\Connection;
use Shopware\Commercial\B2B\QuoteManagement\Entity\Quote\QuoteDefinition;
use Shopware\Commercial\B2B\QuoteManagement\Entity\QuoteLineItem\QuoteLineItemDefinition;
use Shopware\Core\Framework\DataAbstractionLayer\Dbal\EntityDefinitionQueryHelper;
use Shopware\Core\Framework\Log\Package;
use Shopware\Core\Framework\Migration\MigrationStep;

/**
 * @internal
 */
#[Package('checkout')]
class Migration1697524804QuoteManagement_AddCustomFieldToQuoteAndQuoteLineItem extends MigrationStep
{
    public function getCreationTimestamp(): int
    {
        return 1697524804;
    }

    public function update(Connection $connection): void
    {
        $isExisted = EntityDefinitionQueryHelper::columnExists($connection, QuoteDefinition::ENTITY_NAME, 'custom_fields');
        if (!$isExisted) {
            $connection->executeStatement(
                'ALTER TABLE `quote`
                ADD COLUMN `custom_fields` JSON NULL,
                ADD CONSTRAINT `json.quote.custom_fields` CHECK (JSON_VALID(`custom_fields`));'
            );
        }

        $isExisted = EntityDefinitionQueryHelper::columnExists($connection, QuoteLineItemDefinition::ENTITY_NAME, 'custom_fields');

        if (!$isExisted) {
            $connection->executeStatement(
                'ALTER TABLE `quote_line_item`
                ADD COLUMN `custom_fields` JSON NULL,
                ADD CONSTRAINT `json.quote_line_item.custom_fields` CHECK (JSON_VALID(`custom_fields`));'
            );
        }
    }

    public function updateDestructive(Connection $connection): void
    {
        // implement update destructive
    }
}

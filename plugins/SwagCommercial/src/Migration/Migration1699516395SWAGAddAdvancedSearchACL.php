<?php

declare(strict_types=1);

namespace Shopware\Commercial\Migration;

use Doctrine\DBAL\Connection;
use Shopware\Core\Framework\Log\Package;
use Shopware\Core\Framework\Migration\MigrationStep;

/**
 * @internal
 */
#[Package('buyers-experience')]
class Migration1699516395SWAGAddAdvancedSearchACL extends MigrationStep
{
    final public const NEW_PRIVILEGES = [
        'product_search_config.viewer' => [
            'advanced_search_config:read',
            'advanced_search_config_field:read',
            'advanced_search_boosting:read',
            'advanced_search_entity_stream:read',
            'advanced_search_entity_stream_filter:read',
            'advanced_search_action:read',
            'advanced_search_action_search_term:read',
            'advanced_search_synonym:read',
            'product_stream:read',
            'product_stream_filter:read',
            'custom_field_set:read',
            'custom_field:read',
            'custom_field_set_relation:read',
            'sales_channel:read',
            'sales_channel_type:read',
            'product:read',
            'product_manufacturer:read',
            'property_group_option:read',
            'property_group:read',
            'currency:read',
            'user_config:read',
            'user_config:create',
            'user_config:update',
            'media:read', 'media_folder:read',
            'media_default_folder:read',
            'media_thumbnail_size:read',
            'media_folder_configuration:read',
            'tag:read', 'product_media:read',
            'category:read',
            'mail_template_media:read',
            'mail_template:read',
            'document_base_config:read',
            'user:read', 'payment_method:read',
            'shipping_method:read',
            'cms_page:read',
            'cms_section:read',
            'cms_block:read',
            'seo_url:read',
            'category_translation:read',
            'cms_slot:read',
            'landing_page:read',
            'delivery_time:read',
            'product_sorting:read',
            'product_cross_selling:read',
            'product_cross_selling_assigned_products:read',
        ],
        'product_search_config.editor' => [
            'advanced_search_entity_stream:create',
            'advanced_search_entity_stream_filter:create',
            'advanced_search_config_field:create',
            'advanced_search_action_search_term:create',
            'advanced_search_config:update',
            'advanced_search_config_field:update',
            'advanced_search_boosting:update',
            'advanced_search_entity_stream:update',
            'advanced_search_entity_stream_filter:update',
            'advanced_search_action:update',
            'advanced_search_action_search_term:update',
            'advanced_search_synonym:update',
        ],
        'product_search_config.creator' => [
            'advanced_search_config:create',
            'advanced_search_config_field:create',
            'advanced_search_boosting:create',
            'advanced_search_entity_stream:create',
            'advanced_search_entity_stream_filter:create',
            'advanced_search_action:create',
            'advanced_search_action_search_term:create',
            'advanced_search_synonym:create',
        ],
        'product_search_config.deleter' => [
            'advanced_search_config:delete',
            'advanced_search_config_field:delete',
            'advanced_search_boosting:delete',
            'advanced_search_entity_stream:delete',
            'advanced_search_entity_stream_filter:delete',
            'advanced_search_action:delete',
            'advanced_search_action_search_term:delete',
            'advanced_search_synonym:delete',
        ],
    ];

    public function getCreationTimestamp(): int
    {
        return 1699516395;
    }

    public function update(Connection $connection): void
    {
        $this->addAdditionalPrivileges($connection, self::NEW_PRIVILEGES);
    }

    /**
     * @codeCoverageIgnore
     */
    public function updateDestructive(Connection $connection): void
    {
    }
}

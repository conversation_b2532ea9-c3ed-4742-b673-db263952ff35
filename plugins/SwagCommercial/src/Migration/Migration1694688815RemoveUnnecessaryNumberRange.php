<?php declare(strict_types=1);

namespace Shopware\Commercial\Migration;

use Doctrine\DBAL\Connection;
use Shopware\Core\Framework\Log\Package;
use Shopware\Core\Framework\Migration\MigrationStep;

/**
 * @internal
 */
#[Package('customer-order')]
class Migration1694688815RemoveUnnecessaryNumberRange extends MigrationStep
{
    public function getCreationTimestamp(): int
    {
        return 1694688815;
    }

    public function update(Connection $connection): void
    {
        $connection->executeStatement('
            DELETE `number_range` FROM `number_range`
                LEFT JOIN `number_range_type`
                   ON `number_range`.`type_id` = `number_range_type`.`id`
            WHERE `number_range_type`.`id` IS NULL;
       ');
    }

    public function updateDestructive(Connection $connection): void
    {
        // implement update destructive
    }
}

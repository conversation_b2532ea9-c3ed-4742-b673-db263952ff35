<?php declare(strict_types=1);

namespace Shopware\Commercial\Migration;

use Doctrine\DBAL\Connection;
use Shopware\Commercial\B2B\QuoteManagement\Entity\Quote\QuoteStates;
use Shopware\Core\Defaults;
use Shopware\Core\Framework\Log\Package;
use Shopware\Core\Framework\Migration\MigrationStep;
use Shopware\Core\Framework\Uuid\Uuid;
use Shopware\Core\Migration\Traits\ImportTranslationsTrait;
use Shopware\Core\Migration\Traits\StateMachineMigration;
use Shopware\Core\Migration\Traits\StateMachineMigrationTrait;
use Shopware\Core\Migration\Traits\Translations;

/**
 * @internal
 */
#[Package('checkout')]
class Migration1686110293CreateTablesForQuoteManagement extends MigrationStep
{
    use ImportTranslationsTrait;
    use StateMachineMigrationTrait;

    final public const NUMBER_RANGE_NAME = 'quote';

    final public const DOCUMENT_TYPE_NAME_EN = 'Quote';

    final public const DOCUMENT_TYPE_NAME_DE = 'Quote';

    public function getCreationTimestamp(): int
    {
        return 1686110293;
    }

    public function update(Connection $connection): void
    {
        $this->createTableQuote($connection);
        $this->createTableQuoteLineItem($connection);
        $this->createTableQuoteDelivery($connection);
        $this->createTableQuoteDeliveryPosition($connection);
        $this->createTableQuoteTransaction($connection);
        $this->createNumberRanges($connection);
        $this->createQuoteStates($connection);
    }

    public function updateDestructive(Connection $connection): void
    {
        // implement update destructive
    }

    private function createTableQuote(Connection $connection): void
    {
        $connection->executeStatement('
            CREATE TABLE IF NOT EXISTS `quote` (
                `id`                            BINARY(16) NOT NULL,
                `version_id`                    BINARY(16) NOT NULL,
                `auto_increment`                BIGINT unsigned NOT NULL AUTO_INCREMENT,
                `state_id`                      BINARY(16) NOT NULL,
                `user_id`                       BINARY(16) NULL,
                `currency_id`                   BINARY(16) NOT NULL,
                `language_id`                   BINARY(16) NOT NULL,
                `sales_channel_id`              BINARY(16) NOT NULL,
                `created_by_id`                 BINARY(16) NULL,
                `updated_by_id`                 BINARY(16) NULL,
                `customer_id`                   BINARY(16) NOT NULL,
                `order_id`                      BINARY(16) NULL,
                `order_version_id`              BINARY(16) NULL,
                `quote_number`                  VARCHAR(64),
                `expiration_date`               DATETIME(3) NULL,
                `price`                         JSON NOT NULL,
                `shipping_costs`                JSON NOT NULL,
                `discount`                      JSON NULL,
                `item_rounding`                 JSON NULL,
                `total_rounding`                JSON NULL,
                `tax_status`                    VARCHAR(255) GENERATED ALWAYS AS (JSON_UNQUOTE(JSON_EXTRACT(`price`, "$.taxStatus"))) VIRTUAL,
                `amount_total`                  DOUBLE GENERATED ALWAYS AS (JSON_UNQUOTE(JSON_EXTRACT(`price`, "$.totalPrice"))) VIRTUAL,
                `amount_net`                    DOUBLE GENERATED ALWAYS AS (JSON_UNQUOTE(JSON_EXTRACT(`price`, "$.netPrice"))) VIRTUAL,
                `subtotal_net`                  DOUBLE NULL,
                `total_discount`                DOUBLE NULL,
                `original_price`                DOUBLE NULL,
                `created_at`                    DATETIME(3) NOT NULL,
                `updated_at`                    DATETIME(3) NULL,
                PRIMARY KEY (`id`, `version_id`),
                INDEX `idx.state_index` (`state_id`),
                UNIQUE `uniq.auto_increment` (`auto_increment`),
                CONSTRAINT `json.quote.price` CHECK  (JSON_VALID(`price`)),
                CONSTRAINT `json.quote.discount` CHECK  (JSON_VALID(`discount`)),
                CONSTRAINT `fk.quote.state_id` FOREIGN KEY (`state_id`)
                    REFERENCES `state_machine_state` (`id`) ON DELETE RESTRICT ON UPDATE CASCADE,
                CONSTRAINT `fk.quote.currency_id` FOREIGN KEY (`currency_id`)
                    REFERENCES `currency` (`id`) ON DELETE RESTRICT ON UPDATE CASCADE,
                CONSTRAINT `fk.quote.language_id` FOREIGN KEY (`language_id`)
                    REFERENCES `language` (`id`) ON DELETE RESTRICT ON UPDATE CASCADE,
                CONSTRAINT `fk.quote.sales_channel_id` FOREIGN KEY (`sales_channel_id`)
                    REFERENCES `sales_channel` (`id`) ON DELETE RESTRICT ON UPDATE CASCADE,
                CONSTRAINT `fk.quote.created_by_id` FOREIGN KEY (`created_by_id`)
                    REFERENCES `user` (`id`) ON DELETE RESTRICT ON UPDATE CASCADE,
                CONSTRAINT `fk.quote.updated_by_id` FOREIGN KEY (`updated_by_id`)
                    REFERENCES `user` (`id`) ON DELETE RESTRICT ON UPDATE CASCADE,
                CONSTRAINT `fk.quote.user_id` FOREIGN KEY (`user_id`)
                    REFERENCES `user` (`id`) ON DELETE RESTRICT ON UPDATE CASCADE,
                CONSTRAINT `fk.quote.customer_id` FOREIGN KEY (`customer_id`)
                    REFERENCES `customer` (`id`) ON DELETE RESTRICT ON UPDATE CASCADE,
                CONSTRAINT `fk.quote.order_id` FOREIGN KEY (`order_id`, `order_version_id`)
                    REFERENCES `order` (`id`, `version_id`) ON DELETE CASCADE ON UPDATE CASCADE
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
        ');
    }

    private function createTableQuoteLineItem(Connection $connection): void
    {
        $connection->executeStatement('
            CREATE TABLE IF NOT EXISTS `quote_line_item` (
                `id`                    BINARY(16) NOT NULL,
                `version_id`            BINARY(16) NOT NULL,
                `quote_id`              BINARY(16) NOT NULL,
                `quote_version_id`      BINARY(16) NOT NULL,
                `parent_id`             BINARY(16) NULL,
                `parent_version_id`     BINARY(16) NULL,
                `product_id`            BINARY(16) NULL,
                `product_version_id`    BINARY(16) NOT NULL,
                `promotion_id`          BINARY(16) NULL,
                `cover_id`              BINARY(16) NULL,
                `states`                JSON NULL,
                `label`                 VARCHAR(255) COLLATE utf8mb4_unicode_ci NOT NULL,
                `referenced_id`         VARCHAR(255) COLLATE utf8mb4_unicode_ci NULL,
                `identifier`            VARCHAR(255) COLLATE utf8mb4_unicode_ci NOT NULL,
                `description`           MEDIUMTEXT COLLATE utf8mb4_unicode_ci NULL,
                `quantity`              INT(11) NOT NULL,
                `type`                  VARCHAR(255) COLLATE utf8mb4_unicode_ci NULL,
                `payload`               JSON NULL,
                `unit_price`            DOUBLE GENERATED ALWAYS AS (JSON_UNQUOTE(JSON_EXTRACT(`price`, "$.unitPrice"))) VIRTUAL,
                `total_price`           DOUBLE GENERATED ALWAYS AS (JSON_UNQUOTE(JSON_EXTRACT(`price`, "$.totalPrice"))) VIRTUAL,
                `price_definition`      JSON NULL,
                `price`                 JSON NOT NULL,
                `discount`              JSON NULL,
                `stackable`             TINYINT(1) NOT NULL DEFAULT 1,
                `removable`             TINYINT(1) NOT NULL DEFAULT 1,
                `good`                  TINYINT(1) NOT NULL DEFAULT 1,
                `position`              INT(11) NOT NULL DEFAULT 1,
                `created_at`            DATETIME(3) NOT NULL,
                `updated_at`            DATETIME(3) NULL,
                PRIMARY KEY (`id`, `version_id`),
                CONSTRAINT `json.quote_line_item.payload` CHECK(JSON_VALID(`payload`)),
                CONSTRAINT `json.quote_line_item.price` CHECK(JSON_VALID(`price`)),
                CONSTRAINT `json.quote_line_item.price_definition` CHECK(JSON_VALID(`price_definition`)),
                CONSTRAINT `json.quote_line_item.discount` CHECK  (JSON_VALID(`discount`)),
                CONSTRAINT `json.quote_line_item.states` CHECK  (JSON_VALID(`states`)),
                CONSTRAINT `fk.quote_line_item.quote_id` FOREIGN KEY (`quote_id`, `quote_version_id`)
                    REFERENCES `quote` (`id`, `version_id`) ON DELETE CASCADE ON UPDATE CASCADE,
                CONSTRAINT `fk.quote_line_item.parent_id` FOREIGN KEY (`parent_id`, `parent_version_id`)
                    REFERENCES `quote_line_item` (`id`, `version_id`) ON DELETE CASCADE ON UPDATE CASCADE,
                CONSTRAINT `fk.quote_line_item.promotion_id` FOREIGN KEY (`promotion_id`)
                    REFERENCES `promotion` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
                CONSTRAINT `fk.quote_line_item.cover_id` FOREIGN KEY (`cover_id`)
                    REFERENCES `media` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
        ');
    }

    private function createTableQuoteDelivery(Connection $connection): void
    {
        $connection->executeStatement('
            CREATE TABLE IF NOT EXISTS `quote_delivery` (
                `id`                                BINARY(16) NOT NULL,
                `version_id`                        BINARY(16) NOT NULL,
                `quote_id`                          BINARY(16) NOT NULL,
                `quote_version_id`                  BINARY(16) NOT NULL,
                `shipping_method_id`                BINARY(16) NULL,
                `shipping_date_earliest`            DATE NOT NULL,
                `shipping_date_latest`              DATE NOT NULL,
                `shipping_costs`                    JSON NOT NULL,
                `custom_fields`                     JSON NULL,
                `created_at`                        DATETIME(3) NOT NULL,
                `updated_at`                        DATETIME(3) NULL,
                PRIMARY KEY (`id`, `version_id`),
                CONSTRAINT `json.quote_delivery.shipping_costs` CHECK (JSON_VALID(`shipping_costs`)),
                CONSTRAINT `json.quote_delivery.custom_fields` CHECK (JSON_VALID(`custom_fields`)),
                CONSTRAINT `fk.quote_delivery.quote_id` FOREIGN KEY (`quote_id`, `quote_version_id`)
                    REFERENCES `quote` (`id`, `version_id`) ON DELETE CASCADE ON UPDATE CASCADE,
                CONSTRAINT `fk.quote_delivery.shipping_method_id` FOREIGN KEY (`shipping_method_id`)
                    REFERENCES `shipping_method` (`id`) ON DELETE RESTRICT ON UPDATE CASCADE
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
        ');
    }

    private function createTableQuoteDeliveryPosition(Connection $connection): void
    {
        $connection->executeStatement('
            CREATE TABLE IF NOT EXISTS `quote_delivery_position` (
              `id`                          BINARY(16) NOT NULL,
              `version_id`                  BINARY(16) NOT NULL,
              `quote_delivery_id`           BINARY(16) NOT NULL,
              `quote_delivery_version_id`   BINARY(16) NOT NULL,
              `quote_line_item_id`          BINARY(16) NOT NULL,
              `quote_line_item_version_id`  BINARY(16) NOT NULL,
              `price`                       JSON NOT NULL,
              `total_price`                 INT(11) GENERATED ALWAYS AS (JSON_UNQUOTE(JSON_EXTRACT(`price`, "$.totalPrice"))) VIRTUAL,
              `unit_price`                  INT(11) GENERATED ALWAYS AS (JSON_UNQUOTE(JSON_EXTRACT(`price`, "$.unitPrice"))) VIRTUAL,
              `quantity`                    INT(11) GENERATED ALWAYS AS (JSON_UNQUOTE(JSON_EXTRACT(`price`, "$.quantity"))) VIRTUAL,
              `custom_fields`               JSON NULL,
              `created_at`                  DATETIME(3) NOT NULL,
              `updated_at`                  DATETIME(3) NULL,
              PRIMARY KEY (`id`, `version_id`),
              CONSTRAINT `json.quote_delivery_position.price` CHECK (JSON_VALID(`price`)),
              CONSTRAINT `json.quote_delivery_position.custom_fields` CHECK (JSON_VALID(`custom_fields`)),
              CONSTRAINT `fk.quote_delivery_position.quote_delivery_id` FOREIGN KEY (`quote_delivery_id`, `quote_delivery_version_id`)
                REFERENCES `quote_delivery` (`id`, `version_id`) ON DELETE CASCADE ON UPDATE CASCADE,
              CONSTRAINT `fk.quote_delivery_position.quote_line_item_id` FOREIGN KEY (`quote_line_item_id`, `quote_line_item_version_id`)
                REFERENCES `quote_line_item` (`id`, `version_id`) ON DELETE CASCADE ON UPDATE CASCADE
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
        ');
    }

    private function createTableQuoteTransaction(Connection $connection): void
    {
        $connection->executeStatement('
            CREATE TABLE IF NOT EXISTS `quote_transaction` (
              `id`                          BINARY(16) NOT NULL,
              `version_id`                  BINARY(16) NOT NULL,
              `quote_id`                    BINARY(16) NOT NULL,
              `quote_version_id`            BINARY(16) NOT NULL,
              `payment_method_id`           BINARY(16) NOT NULL,
              `amount`                      JSON NOT NULL,
              `custom_fields`               JSON NULL,
              `created_at`                  DATETIME(3) NOT NULL,
              `updated_at`                  DATETIME(3) NULL,
              PRIMARY KEY (`id`, `version_id`),
              CONSTRAINT `json.quote_transaction.amount` CHECK (JSON_VALID(`amount`)),
              CONSTRAINT `json.quote_transaction.custom_fields` CHECK (JSON_VALID(`custom_fields`)),
              CONSTRAINT `fk.quote_transaction.quote_id` FOREIGN KEY (`quote_id`, `quote_version_id`)
                REFERENCES `quote` (`id`, `version_id`) ON DELETE CASCADE ON UPDATE CASCADE,
              CONSTRAINT `fk.quote_transaction.payment_method_id` FOREIGN KEY (`payment_method_id`)
                REFERENCES `payment_method` (`id`) ON DELETE RESTRICT ON UPDATE CASCADE
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
        ');
    }

    private function createNumberRanges(Connection $connection): void
    {
        $isNumberRangeTypeExist = $connection->fetchOne(
            'SELECT `id` FROM `number_range_type` WHERE `technical_name` = :technicalName LIMIT 1',
            [
                'technicalName' => self::NUMBER_RANGE_NAME,
            ]
        );

        if ($isNumberRangeTypeExist) {
            return;
        }

        $definitionNumberRangeTypes = [
            self::NUMBER_RANGE_NAME => [
                'id' => Uuid::randomHex(),
                'global' => 0,
                'nameDe' => self::DOCUMENT_TYPE_NAME_DE,
                'nameEn' => self::DOCUMENT_TYPE_NAME_EN,
            ],
        ];

        foreach ($definitionNumberRangeTypes as $typeName => $numberRangeType) {
            $connection->insert(
                'number_range_type',
                [
                    'id' => Uuid::fromHexToBytes($numberRangeType['id']),
                    'global' => $numberRangeType['global'],
                    'technical_name' => $typeName,
                    'created_at' => (new \DateTime())->format(Defaults::STORAGE_DATE_TIME_FORMAT),
                ]
            );

            $translation = new Translations(
                [
                    'number_range_type_id' => Uuid::fromHexToBytes($numberRangeType['id']),
                    'type_name' => $numberRangeType['nameDe'],
                ],
                [
                    'number_range_type_id' => Uuid::fromHexToBytes($numberRangeType['id']),
                    'type_name' => $numberRangeType['nameEn'],
                ]
            );

            $this->importTranslation('number_range_type_translation', $translation, $connection);
        }

        $definitionNumberRanges = [
            self::NUMBER_RANGE_NAME => [
                'id' => Uuid::randomHex(),
                'nameEn' => self::DOCUMENT_TYPE_NAME_EN,
                'nameDe' => self::DOCUMENT_TYPE_NAME_DE,
                'global' => 1,
                'typeId' => $definitionNumberRangeTypes[self::NUMBER_RANGE_NAME]['id'],
                'pattern' => '{n}',
                'start' => 1000,
            ],
        ];

        foreach ($definitionNumberRanges as $numberRange) {
            $connection->insert(
                'number_range',
                [
                    'id' => Uuid::fromHexToBytes($numberRange['id']),
                    'global' => $numberRange['global'],
                    'type_id' => Uuid::fromHexToBytes($numberRange['typeId']),
                    'pattern' => $numberRange['pattern'],
                    'start' => $numberRange['start'],
                    'created_at' => (new \DateTime())->format(Defaults::STORAGE_DATE_TIME_FORMAT),
                ]
            );

            $translation = new Translations(
                [
                    'number_range_id' => Uuid::fromHexToBytes($numberRange['id']),
                    'name' => $numberRange['nameDe'],
                ],
                [
                    'number_range_id' => Uuid::fromHexToBytes($numberRange['id']),
                    'name' => $numberRange['nameEn'],
                ],
            );

            $this->importTranslation('number_range_translation', $translation, $connection);
        }
    }

    private function createQuoteStates(Connection $connection): void
    {
        $stateMachine = new StateMachineMigration(
            QuoteStates::STATE_MACHINE,
            'Quote state',
            'Quote state',
            [
                StateMachineMigration::state(QuoteStates::STATE_OPEN, 'Offen', 'Open'),
                StateMachineMigration::state(QuoteStates::STATE_REPLIED, 'Beantwortet', 'Replied'),
                StateMachineMigration::state(QuoteStates::STATE_IN_REVIEW, 'In Bearbeitung', 'In Review'),
                StateMachineMigration::state(QuoteStates::STATE_ACCEPTED, 'Akzeptiert', 'Accepted'),
                StateMachineMigration::state(QuoteStates::STATE_DECLINED, 'Abgelehnt', 'Declined'),
                StateMachineMigration::state(QuoteStates::STATE_EXPIRED, 'Expired', 'Expired'),
            ],
            [
                // open -> in-review
                StateMachineMigration::transition(
                    QuoteStates::ACTION_PROCESS,
                    QuoteStates::STATE_OPEN,
                    QuoteStates::STATE_IN_REVIEW
                ),
                // in-review -> replied
                StateMachineMigration::transition(
                    QuoteStates::ACTION_SENT,
                    QuoteStates::STATE_IN_REVIEW,
                    QuoteStates::STATE_REPLIED
                ),
                // replied -> accepted
                StateMachineMigration::transition(
                    QuoteStates::ACTION_ACCEPT,
                    QuoteStates::STATE_REPLIED,
                    QuoteStates::STATE_ACCEPTED
                ),
                // replied -> expired
                StateMachineMigration::transition(
                    QuoteStates::ACTION_EXPIRE,
                    QuoteStates::STATE_REPLIED,
                    QuoteStates::STATE_EXPIRED
                ),
                // replied -> declined
                StateMachineMigration::transition(
                    QuoteStates::ACTION_DECLINE,
                    QuoteStates::STATE_REPLIED,
                    QuoteStates::STATE_DECLINED
                ),
            ],
            QuoteStates::STATE_OPEN
        );

        $this->import($stateMachine, $connection);
    }
}

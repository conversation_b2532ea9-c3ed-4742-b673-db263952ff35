<?php

declare(strict_types=1);

namespace Shopware\Commercial\Migration;

use Doctrine\DBAL\Connection;
use Shopware\Core\Framework\Log\Package;
use Shopware\Core\Framework\Migration\MigrationStep;

/**
 * @internal
 */
#[Package('buyers-experience')]
class Migration1697025395SWAGCreateSynonymsTable extends MigrationStep
{
    public function getCreationTimestamp(): int
    {
        return 1697025395;
    }

    public function update(Connection $connection): void
    {
        $query = <<<SQL
CREATE TABLE IF NOT EXISTS `advanced_search_synonym`
(
    `id` BINARY(16)   NOT NULL,
    `left` JSON NOT NULL,
    `right` <PERSON><PERSON><PERSON> NULL,
    `language_id` BINARY(16) NULL,
    `created_at` DATETIME(3) NOT NULL,
    `updated_at` DATETIME(3) NULL,
    CONSTRAINT `fk.advanced_search_synonym.language_id` FOREIGN KEY (`language_id`)
        REFERENCES `language` (`id`) ON DELETE RESTRICT,
    PRIMARY KEY (`id`)
)
    ENGINE = InnoDB
    DEFAULT CHARSET = utf8mb4
    COLLATE = utf8mb4_unicode_ci;
SQL;

        $connection->executeStatement($query);
    }

    public function updateDestructive(Connection $connection): void
    {
    }
}

const { Component } = Shopware;
const { Criteria } = Shopware.Data;
const { get } = Shopware.Utils;

Component.extend('order-status-multiselect', 'sw-entity-multi-id-select', {
    inheritAttrs: false, // So we can detect "componentName" in $attrs

    inject: ['repositoryFactory'],

    props: {
        entity: {
            type: String,
            required: false,
            default: 'state_machine',
        },
        criteria: {
            type: Object,
            required: false,
            default() {
                const criteria = new Criteria();
                criteria.addAssociation('stateMachine');
                return criteria;
            },
        },
        repository: {
            type: Object,
            required: false,
            default() {
                return this.repositoryFactory.create('state_machine_state');
            },
        },
    },
});

Component.override('sw-entity-multi-select', {
    methods: {
        displayLabelProperty(item) {
            // 1. If the parent is "order-status-multiselect", apply custom label logic
            if (this.$parent?.$attrs?.componentName === 'order-status-multiselect') {
                return this.getOrderStatusLabel(item);
            }

            // 2. Otherwise, we call our local copy of the default label property
            return this.defaultDisplayLabelProperty(item);
        },

        /**
         * Local copy of the original `sw-entity-multi-select` logic for
         * `displayLabelProperty`. This ensures we don't rely on `this.$super`
         * (which may be undefined if there's no parent or a conflicting override).
         */
        defaultDisplayLabelProperty(item) {
            if (!item) {
                return '';
            }

            // The original code checks if `this.labelProperty` is an array or string.
            const labelProperties = Array.isArray(this.labelProperty)
                ? [...this.labelProperty]
                : [this.labelProperty];

            // Join property values (e.g., "name" or "translated.name")
            return labelProperties
            .map((propertyKey) => {
                return this.getKey(item, propertyKey)
                    || this.getKey(item, `translated.${propertyKey}`);
            })
            .join(' ');
        },

        /**
         * Custom label logic for "order-status-multiselect".
         * Example: "Order delivery state - Shipped".
         */
        getOrderStatusLabel(item) {
            if (!item) {
                return '';
            }

            // The state name
            const state =
                this.getKey(item, 'name')
                || this.getKey(item, 'translated.name')
                || '';

            // The stateMachine name
            let stateMachine =
                this.getKey(item.stateMachine, 'name')
                || this.getKey(item.stateMachine, 'translated.name')
                || '';

            // Fix: "order_delivery.state" mislabeled as "Order state"
            if (
                item.stateMachine?.technicalName === 'order_delivery.state'
                && item.stateMachine?.name === 'Order state'
            ) {
                stateMachine = 'Order delivery state';
            }

            return `${stateMachine} - ${state}`.trim();
        },

        getKey(object, path, defaultValue = '') {
            return get(object, path, defaultValue);
        },
    },
});

/******/ (() => { // webpackBootstrap
/******/ 	"use strict";
/******/ 	var __webpack_modules__ = ({

/***/ 857:
/***/ ((module) => {



var isMergeableObject = function isMergeableObject(value) {
	return isNonNullObject(value)
		&& !isSpecial(value)
};

function isNonNullObject(value) {
	return !!value && typeof value === 'object'
}

function isSpecial(value) {
	var stringValue = Object.prototype.toString.call(value);

	return stringValue === '[object RegExp]'
		|| stringValue === '[object Date]'
		|| isReactElement(value)
}

// see https://github.com/facebook/react/blob/b5ac963fb791d1298e7f396236383bc955f916c1/src/isomorphic/classic/element/ReactElement.js#L21-L25
var canUseSymbol = typeof Symbol === 'function' && Symbol.for;
var REACT_ELEMENT_TYPE = canUseSymbol ? Symbol.for('react.element') : 0xeac7;

function isReactElement(value) {
	return value.$$typeof === REACT_ELEMENT_TYPE
}

function emptyTarget(val) {
	return Array.isArray(val) ? [] : {}
}

function cloneUnlessOtherwiseSpecified(value, options) {
	return (options.clone !== false && options.isMergeableObject(value))
		? deepmerge(emptyTarget(value), value, options)
		: value
}

function defaultArrayMerge(target, source, options) {
	return target.concat(source).map(function(element) {
		return cloneUnlessOtherwiseSpecified(element, options)
	})
}

function getMergeFunction(key, options) {
	if (!options.customMerge) {
		return deepmerge
	}
	var customMerge = options.customMerge(key);
	return typeof customMerge === 'function' ? customMerge : deepmerge
}

function getEnumerableOwnPropertySymbols(target) {
	return Object.getOwnPropertySymbols
		? Object.getOwnPropertySymbols(target).filter(function(symbol) {
			return Object.propertyIsEnumerable.call(target, symbol)
		})
		: []
}

function getKeys(target) {
	return Object.keys(target).concat(getEnumerableOwnPropertySymbols(target))
}

function propertyIsOnObject(object, property) {
	try {
		return property in object
	} catch(_) {
		return false
	}
}

// Protects from prototype poisoning and unexpected merging up the prototype chain.
function propertyIsUnsafe(target, key) {
	return propertyIsOnObject(target, key) // Properties are safe to merge if they don't exist in the target yet,
		&& !(Object.hasOwnProperty.call(target, key) // unsafe if they exist up the prototype chain,
			&& Object.propertyIsEnumerable.call(target, key)) // and also unsafe if they're nonenumerable.
}

function mergeObject(target, source, options) {
	var destination = {};
	if (options.isMergeableObject(target)) {
		getKeys(target).forEach(function(key) {
			destination[key] = cloneUnlessOtherwiseSpecified(target[key], options);
		});
	}
	getKeys(source).forEach(function(key) {
		if (propertyIsUnsafe(target, key)) {
			return
		}

		if (propertyIsOnObject(target, key) && options.isMergeableObject(source[key])) {
			destination[key] = getMergeFunction(key, options)(target[key], source[key], options);
		} else {
			destination[key] = cloneUnlessOtherwiseSpecified(source[key], options);
		}
	});
	return destination
}

function deepmerge(target, source, options) {
	options = options || {};
	options.arrayMerge = options.arrayMerge || defaultArrayMerge;
	options.isMergeableObject = options.isMergeableObject || isMergeableObject;
	// cloneUnlessOtherwiseSpecified is added to `options` so that custom arrayMerge()
	// implementations can use it. The caller may not replace it.
	options.cloneUnlessOtherwiseSpecified = cloneUnlessOtherwiseSpecified;

	var sourceIsArray = Array.isArray(source);
	var targetIsArray = Array.isArray(target);
	var sourceAndTargetTypesMatch = sourceIsArray === targetIsArray;

	if (!sourceAndTargetTypesMatch) {
		return cloneUnlessOtherwiseSpecified(source, options)
	} else if (sourceIsArray) {
		return options.arrayMerge(target, source, options)
	} else {
		return mergeObject(target, source, options)
	}
}

deepmerge.all = function deepmergeAll(array, options) {
	if (!Array.isArray(array)) {
		throw new Error('first argument should be an array')
	}

	return array.reduce(function(prev, next) {
		return deepmerge(prev, next, options)
	}, {})
};

var deepmerge_1 = deepmerge;

module.exports = deepmerge_1;


/***/ })

/******/ 	});
/************************************************************************/
/******/ 	// The module cache
/******/ 	var __webpack_module_cache__ = {};
/******/ 	
/******/ 	// The require function
/******/ 	function __webpack_require__(moduleId) {
/******/ 		// Check if module is in cache
/******/ 		var cachedModule = __webpack_module_cache__[moduleId];
/******/ 		if (cachedModule !== undefined) {
/******/ 			return cachedModule.exports;
/******/ 		}
/******/ 		// Create a new module (and put it into the cache)
/******/ 		var module = __webpack_module_cache__[moduleId] = {
/******/ 			// no module.id needed
/******/ 			// no module.loaded needed
/******/ 			exports: {}
/******/ 		};
/******/ 	
/******/ 		// Execute the module function
/******/ 		__webpack_modules__[moduleId](module, module.exports, __webpack_require__);
/******/ 	
/******/ 		// Return the exports of the module
/******/ 		return module.exports;
/******/ 	}
/******/ 	
/************************************************************************/
/******/ 	/* webpack/runtime/compat get default export */
/******/ 	(() => {
/******/ 		// getDefaultExport function for compatibility with non-harmony modules
/******/ 		__webpack_require__.n = (module) => {
/******/ 			var getter = module && module.__esModule ?
/******/ 				() => (module['default']) :
/******/ 				() => (module);
/******/ 			__webpack_require__.d(getter, { a: getter });
/******/ 			return getter;
/******/ 		};
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/define property getters */
/******/ 	(() => {
/******/ 		// define getter functions for harmony exports
/******/ 		__webpack_require__.d = (exports, definition) => {
/******/ 			for(var key in definition) {
/******/ 				if(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {
/******/ 					Object.defineProperty(exports, key, { enumerable: true, get: definition[key] });
/******/ 				}
/******/ 			}
/******/ 		};
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/hasOwnProperty shorthand */
/******/ 	(() => {
/******/ 		__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))
/******/ 	})();
/******/ 	
/************************************************************************/
var __webpack_exports__ = {};
// This entry need to be wrapped in an IIFE because it need to be isolated against other modules in the chunk.
(() => {

;// CONCATENATED MODULE: ./helper/storage/cookie-storage.helper.js
/**
 * @package storefront
 */ class CookieStorageHelper {
    /**
     * returns if cookies are supported
     *
     * @returns {boolean}
     */ static isSupported() {
        return document.cookie !== "undefined";
    }
    /**
     * Sets cookie with name, value and expiration date
     *
     * @param {string} key
     * @param {string} value
     *
     * @param {number} expirationDays
     */ static setItem(key, value, expirationDays) {
        if (typeof key === "undefined" || key === null) {
            throw new Error("You must specify a key to set a cookie");
        }
        const date = new Date();
        date.setTime(date.getTime() + expirationDays * 24 * 60 * 60 * 1000);
        let secure = "";
        if (location.protocol === "https:") {
            secure = "secure";
        }
        document.cookie = "".concat(key, "=").concat(value, ";expires=").concat(date.toUTCString(), ";path=/;sameSite=lax;").concat(secure);
    }
    /**
     * Gets cookie value through the cookie name
     *
     * @param {string} key
     *
     * @returns {string} cookieValue
     */ static getItem(key) {
        if (!key) {
            return false;
        }
        const name = key + "=";
        const allCookies = document.cookie.split(";");
        for(let i = 0; i < allCookies.length; i++){
            let singleCookie = allCookies[i];
            while(singleCookie.charAt(0) === " "){
                singleCookie = singleCookie.substring(1);
            }
            if (singleCookie.indexOf(name) === 0) {
                return singleCookie.substring(name.length, singleCookie.length);
            }
        }
        return false;
    }
    /**
     * removes a cookie
     *
     * @param key
     */ static removeItem(key) {
        document.cookie = "".concat(key, "= ; expires = Thu, 01 Jan 1970 00:00:00 GMT;path=/");
    }
    /**
     * cookies don't support this options
     *
     * @returns {string}
     */ static key() {
        return "";
    }
    /**
     * cookies don't support this options
     *
     * @returns {string}
     */ static clear() {}
}


// EXTERNAL MODULE: ../node_modules/deepmerge/dist/cjs.js
var cjs = __webpack_require__(857);
var cjs_default = /*#__PURE__*/__webpack_require__.n(cjs);
;// CONCATENATED MODULE: ./helper/string.helper.js
/**
 * @package storefront
 */ class StringHelper {
    /**
     * turns first character of word to uppercase
     *
     * @param {string} string
     * @returns {string}
     * @private
     */ static ucFirst(string) {
        return string.charAt(0).toUpperCase() + string.slice(1);
    }
    /**
     * turns first character of string to uppercase
     *
     * @param {string} string
     * @returns {string}
     * @private
     */ static lcFirst(string) {
        return string.charAt(0).toLowerCase() + string.slice(1);
    }
    /**
     * converts a camel case string
     * into a dash case string
     *
     * @param string
     * @returns {string}
     */ static toDashCase(string) {
        return string.replace(/([A-Z])/g, "-$1").replace(/^-/, "").toLowerCase();
    }
    /**
     *
     * @param {string} string
     * @param {string} separator
     *
     * @returns {string}
     */ static toLowerCamelCase(string, separator) {
        const upperCamelCase = StringHelper.toUpperCamelCase(string, separator);
        return StringHelper.lcFirst(upperCamelCase);
    }
    /**
     *
     * @param {string} string
     * @param {string} separator
     *
     * @returns {string}
     */ static toUpperCamelCase(string, separator) {
        if (!separator) {
            return StringHelper.ucFirst(string.toLowerCase());
        }
        const stringParts = string.split(separator);
        return stringParts.map((string)=>StringHelper.ucFirst(string.toLowerCase())).join("");
    }
    /**
     * returns primitive value of a string
     *
     * @param value
     * @returns {*}
     * @private
     */ static parsePrimitive(value) {
        try {
            // replace comma with dot
            // if value only contains numbers and commas
            if (/^\d+(.|,)\d+$/.test(value)) {
                value = value.replace(",", ".");
            }
            return JSON.parse(value);
        } catch (e) {
            return value.toString();
        }
    }
}


;// CONCATENATED MODULE: ./helper/dom-access.helper.js

class DomAccess {
    /**
     * Returns if the element is an HTML node
     *
     * @param {HTMLElement} element
     * @returns {boolean}
     */ static isNode(element) {
        if (typeof element !== "object" || element === null) {
            return false;
        }
        if (element === document || element === window) {
            return true;
        }
        return element instanceof Node;
    }
    /**
     * Returns if the given element has the requested attribute/property
     * @param {HTMLElement} element
     * @param {string} attribute
     */ static hasAttribute(element, attribute) {
        if (!DomAccess.isNode(element)) {
            throw new Error("The element must be a valid HTML Node!");
        }
        if (typeof element.hasAttribute !== "function") return false;
        return element.hasAttribute(attribute);
    }
    /**
     * Returns the value of a given element's attribute/property
     * @param {HTMLElement|EventTarget} element
     * @param {string} attribute
     * @param {boolean} strict
     * @returns {*|this|string}
     */ static getAttribute(element, attribute) {
        let strict = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : true;
        if (strict && DomAccess.hasAttribute(element, attribute) === false) {
            throw new Error('The required property "'.concat(attribute, '" does not exist!'));
        }
        if (typeof element.getAttribute !== "function") {
            if (strict) {
                throw new Error("This node doesn't support the getAttribute function!");
            }
            return undefined;
        }
        return element.getAttribute(attribute);
    }
    /**
     * Returns the value of a given elements dataset entry
     *
     * @param {HTMLElement|EventTarget} element
     * @param {string} key
     * @param {boolean} strict
     * @returns {*|this|string}
     */ static getDataAttribute(element, key) {
        let strict = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : true;
        const keyWithoutData = key.replace(/^data(|-)/, "");
        const parsedKey = StringHelper.toLowerCamelCase(keyWithoutData, "-");
        if (!DomAccess.isNode(element)) {
            if (strict) {
                throw new Error("The passed node is not a valid HTML Node!");
            }
            return undefined;
        }
        if (typeof element.dataset === "undefined") {
            if (strict) {
                throw new Error("This node doesn't support the dataset attribute!");
            }
            return undefined;
        }
        const attribute = element.dataset[parsedKey];
        if (typeof attribute === "undefined") {
            if (strict) {
                throw new Error('The required data attribute "'.concat(key, '" does not exist on ').concat(element, "!"));
            }
            return attribute;
        }
        return StringHelper.parsePrimitive(attribute);
    }
    /**
     * Returns the selected element of a defined parent node
     * @param {HTMLElement|EventTarget} parentNode
     * @param {string} selector
     * @param {boolean} strict
     * @returns {HTMLElement}
     */ static querySelector(parentNode, selector) {
        let strict = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : true;
        if (strict && !DomAccess.isNode(parentNode)) {
            throw new Error("The parent node is not a valid HTML Node!");
        }
        const element = parentNode.querySelector(selector) || false;
        if (strict && element === false) {
            throw new Error('The required element "'.concat(selector, '" does not exist in parent node!'));
        }
        return element;
    }
    /**
     * Returns the selected elements of a defined parent node
     *
     * @param {HTMLElement|EventTarget} parentNode
     * @param {string} selector
     * @param {boolean} strict
     * @returns {NodeList|false}
     */ static querySelectorAll(parentNode, selector) {
        let strict = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : true;
        if (strict && !DomAccess.isNode(parentNode)) {
            throw new Error("The parent node is not a valid HTML Node!");
        }
        let elements = parentNode.querySelectorAll(selector);
        if (elements.length === 0) {
            elements = false;
        }
        if (strict && elements === false) {
            throw new Error('At least one item of "'.concat(selector, '" must exist in parent node!'));
        }
        return elements;
    }
    /**
     * Returns all focusable elements in the given parent node.
     *
     * @param {HTMLElement|document} parentNode
     * @returns {NodeListOf<Element>}
     */ static getFocusableElements() {
        let parentNode = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : document.body;
        const focusAbleElements = '\n            input:not([tabindex^="-"]):not([disabled]):not([type="hidden"]),\n            select:not([tabindex^="-"]):not([disabled]),\n            textarea:not([tabindex^="-"]):not([disabled]),\n            button:not([tabindex^="-"]):not([disabled]),\n            a[href]:not([tabindex^="-"]):not([disabled]),\n            [tabindex]:not([tabindex^="-"]):not([disabled])\n        ';
        return parentNode.querySelectorAll(focusAbleElements);
    }
    /**
     * Returns the first focusable element in the given parent node.
     *
     * @param parentNode
     * @returns {HTMLElement}
     */ static getFirstFocusableElement() {
        let parentNode = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : document.body;
        return this.getFocusableElements(parentNode)[0];
    }
    /**
     * Returns the last focusable element in the given parent node.
     *
     * @param parentNode
     * @returns {HTMLElement}
     */ static getLastFocusableElement() {
        let parentNode = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : document;
        const result = this.getFocusableElements(parentNode);
        return result[result.length - 1];
    }
}
/**
 * @package storefront
 */ 

;// CONCATENATED MODULE: ./helper/emitter.helper.js
/**
 * @package storefront
 */ class NativeEventEmitter {
    /**
     * Publishes an event on the element. Additional information can be added using the `data` parameter.
     * The data are accessible in the event handler in `event.detail` which represents the standard
     * implementation.
     *
     * @param {Boolean} cancelable
     * @return {CustomEvent}
     */ publish(eventName) {
        let detail = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {}, cancelable = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : false;
        const event = new CustomEvent(eventName, {
            detail,
            cancelable
        });
        this.el.dispatchEvent(event);
        return event;
    }
    /**
     * Subscribes to an event and adds a listener.
     *
     * @param {String} eventName
     * @param {Function} callback
     * @param {Object} [opts = {}]
     */ subscribe(eventName, callback) {
        let opts = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : {};
        const emitter = this;
        const splitEventName = eventName.split(".");
        let cb = opts.scope ? callback.bind(opts.scope) : callback;
        // Support for listeners which are fired once
        if (opts.once && opts.once === true) {
            const onceCallback = cb;
            cb = function onceListener(event) {
                emitter.unsubscribe(eventName);
                onceCallback(event);
            };
        }
        this.el.addEventListener(splitEventName[0], cb);
        this.listeners.push({
            splitEventName,
            opts,
            cb
        });
        return true;
    }
    /**
     * Removes an event listener.
     *
     * @param {String} eventName
     */ unsubscribe(eventName) {
        const splitEventName = eventName.split(".");
        this.listeners = this.listeners.reduce((accumulator, listener)=>{
            const foundEvent = [
                ...listener.splitEventName
            ].sort().toString() === splitEventName.sort().toString();
            if (foundEvent) {
                this.el.removeEventListener(listener.splitEventName[0], listener.cb);
                return accumulator;
            }
            accumulator.push(listener);
            return accumulator;
        }, []);
        return true;
    }
    /**
     * Resets the listeners
     *
     * @return {boolean}
     */ reset() {
        // Loop through the event listener and remove them from the element
        this.listeners.forEach((listener)=>{
            this.el.removeEventListener(listener.splitEventName[0], listener.cb);
        });
        // Reset registry
        this.listeners = [];
        return true;
    }
    get el() {
        return this._el;
    }
    set el(value) {
        this._el = value;
    }
    get listeners() {
        return this._listeners;
    }
    set listeners(value) {
        this._listeners = value;
    }
    /**
     * Event Emitter which works with the provided DOM element. The class isn't meant to be
     * extended. It should rather being used as a mixin component to provide the ability to
     * publish events.
     *
     * @example
     * const emitter = new NativeEventEmitter();
     * emitter.publish('my-event-name');
     *
     * @example using custom data
     * const emitter = new NativeEventEmitter();
     * emitter.subscribe('my-event-name', (event) => {
     *     console.log(event.detail);
     * });
     * emitter.publish('my-event-name', { custom: 'data' });
     *
     * @example using a custom scope
     * const emitter = new NativeEventEmitter();
     * emitter.subscribe('my-event-name', (event) => {
     *     console.log(event.detail);
     * }, { scope: myScope });
     * emitter.publish('my-event-name', { custom: 'data' });
     *
     * @example once listeners
     * const emitter = new NativeEventEmitter();
     * emitter.subscribe('my-event-name', (event) => {
     *     console.log(event.detail);
     * }, { once: true });
     * emitter.publish('my-event-name', { custom: 'data' });
     *
     * @constructor
     * @param {Document|HTMLElement} [el = document]
     */ constructor(el = document){
        this._el = el;
        el.$emitter = this;
        this._listeners = [];
    }
}


;// CONCATENATED MODULE: ./plugin-system/plugin.class.js




class Plugin {
    /**
     * this function gets executed when the plugin is initialized
     */ init() {
        throw new Error('The "init" method for the plugin "'.concat(this._pluginName, '" is not defined.'));
    }
    /**
     * this function gets executed when the plugin is being updated
     */ update() {}
    /**
     * internal init method which checks
     * if the plugin is already initialized
     * before executing the public init
     *
     * @private
     */ _init() {
        if (this._initialized) return;
        this.init();
        this._initialized = true;
    }
    /**
     * internal update method which checks
     * if the plugin is already initialized
     * before executing the public update
     *
     * @private
     */ _update() {
        if (!this._initialized) return;
        this.update();
    }
    /**
     * deep merge the passed options and the static defaults
     *
     * @param {Object} options
     *
     * @private
     */ _mergeOptions(options) {
        const dashedPluginName = StringHelper.toDashCase(this._pluginName);
        const dataAttributeConfig = DomAccess.getDataAttribute(this.el, "data-".concat(dashedPluginName, "-config"), false);
        const dataAttributeOptions = DomAccess.getAttribute(this.el, "data-".concat(dashedPluginName, "-options"), false);
        // static plugin options
        // previously merged options
        // explicit options when creating a plugin instance with 'new'
        const merge = [
            this.constructor.options,
            this.options,
            options
        ];
        // options which are set via data-plugin-name-config="config name"
        if (dataAttributeConfig) merge.push(window.PluginConfigManager.get(this._pluginName, dataAttributeConfig));
        // options which are set via data-plugin-name-options="{json..}"
        try {
            if (dataAttributeOptions) merge.push(JSON.parse(dataAttributeOptions));
        } catch (e) {
            console.error(this.el);
            throw new Error('The data attribute "data-'.concat(dashedPluginName, '-options" could not be parsed to json: ').concat(e.message));
        }
        return cjs_default().all(merge.filter((config)=>{
            return config instanceof Object && !(config instanceof Array);
        }).map((config)=>config || {}));
    }
    /**
     * registers the plugin Instance to the element
     *
     * @private
     */ _registerInstance() {
        const elementPluginInstances = window.PluginManager.getPluginInstancesFromElement(this.el);
        elementPluginInstances.set(this._pluginName, this);
        const plugin = window.PluginManager.getPlugin(this._pluginName, false);
        plugin.get("instances").push(this);
    }
    /**
     * returns the plugin name
     *
     * @param {string} pluginName
     *
     * @returns {string}
     * @private
     */ _getPluginName(pluginName) {
        if (!pluginName) pluginName = this.constructor.name;
        return pluginName;
    }
    /**
     * plugin constructor
     *
     * @param {HTMLElement} el
     * @param {Object} options
     * @param {string} pluginName
     */ constructor(el, options = {}, pluginName = false){
        if (!DomAccess.isNode(el)) {
            throw new Error("There is no valid element given.");
        }
        this.el = el;
        this.$emitter = new NativeEventEmitter(this.el);
        this._pluginName = this._getPluginName(pluginName);
        this.options = this._mergeOptions(options);
        this._initialized = false;
        this._registerInstance();
        this._init();
    }
}
/**
 * Plugin Base class
 * @package storefront
 */ 

;// CONCATENATED MODULE: ./helper/device-detection.helper.js
/**
 * @package storefront
 */ class DeviceDetection {
    /**
     * Returns whether the current device is a touch device
     * @returns {boolean}
     */ static isTouchDevice() {
        return "ontouchstart" in document.documentElement;
    }
    /**
     * Returns whether the current userAgent is an IOS device
     * @returns {boolean}
     */ static isIOSDevice() {
        return DeviceDetection.isIPhoneDevice() || DeviceDetection.isIPadDevice();
    }
    /**
     * Returns if we're dealing with a native Windows browser
     * @returns {boolean}
     */ static isNativeWindowsBrowser() {
        return DeviceDetection.isIEBrowser() || DeviceDetection.isEdgeBrowser();
    }
    /**
     * Returns whether the current userAgent is an iPhone device
     * @returns {boolean}
     */ static isIPhoneDevice() {
        const userAgent = navigator.userAgent;
        return !!userAgent.match(/iPhone/i);
    }
    /**
     * Returns whether the current userAgent is an iPad device
     * @returns {boolean}
     */ static isIPadDevice() {
        const userAgent = navigator.userAgent;
        return !!userAgent.match(/iPad/i);
    }
    /**
     * Returns if we're dealing with the Internet Explorer.
     * @returns {boolean}
     */ static isIEBrowser() {
        const userAgent = navigator.userAgent.toLowerCase();
        return userAgent.indexOf("msie") !== -1 || !!navigator.userAgent.match(/Trident.*rv:\d+\./);
    }
    /**
     * Returns if we're dealing with the Windows Edge browser.
     * @returns {boolean}
     */ static isEdgeBrowser() {
        const userAgent = navigator.userAgent;
        return !!userAgent.match(/Edge\/\d+/i);
    }
    /**
     * Returns a list of css classes with the boolean result of all device detection functions.
     * @returns {object}
     */ static getList() {
        return {
            "is-touch": DeviceDetection.isTouchDevice(),
            "is-ios": DeviceDetection.isIOSDevice(),
            "is-native-windows": DeviceDetection.isNativeWindowsBrowser(),
            "is-iphone": DeviceDetection.isIPhoneDevice(),
            "is-ipad": DeviceDetection.isIPadDevice(),
            "is-ie": DeviceDetection.isIEBrowser(),
            "is-edge": DeviceDetection.isEdgeBrowser()
        };
    }
}


;// CONCATENATED MODULE: ./helper/iterator.helper.js
/**
 * @package storefront
 */ class Iterator {
    /**
     * This callback is displayed as a global member.
     * @callback ObjectIterateCallback
     * @param {value} value
     * @param {key} key
     */ /**
     * Iterates over an object
     *
     * @param {Array|Object} source
     * @param {ObjectIterateCallback} callback
     *
     * @returns {*}
     */ static iterate(source, callback) {
        if (source instanceof Map) {
            return source.forEach(callback);
        }
        if (Array.isArray(source)) {
            return source.forEach(callback);
        }
        if (source instanceof FormData) {
            for (var entry of source.entries()){
                callback(entry[1], entry[0]);
            }
            return;
        }
        if (source instanceof NodeList) {
            return source.forEach(callback);
        }
        if (source instanceof HTMLCollection) {
            return Array.from(source).forEach(callback);
        }
        if (source instanceof Object) {
            return Object.keys(source).forEach((key)=>{
                callback(source[key], key);
            });
        }
        throw new Error("The element type ".concat(typeof source, " is not iterable!"));
    }
}


;// CONCATENATED MODULE: ./plugin/offcanvas/offcanvas.plugin.js



const OFF_CANVAS_CLASS = "offcanvas";
const OFF_CANVAS_FULLWIDTH_CLASS = "is-fullwidth";
const OFF_CANVAS_CLOSE_TRIGGER_CLASS = "js-offcanvas-close";
const REMOVE_OFF_CANVAS_DELAY = 350;
/**
 * OffCanvas uses Bootstraps OffCanvas JavaScript implementation
 * @see https://getbootstrap.com/docs/5.2/components/offcanvas
 * @package storefront
 */ class OffCanvasSingleton {
    /**
     * Open the offcanvas and its backdrop
     * @param {string} content
     * @param {function|null} callback
     * @param {'left'|'right'|'bottom'} position
     * @param {boolean} closable
     * @param {number} delay
     * @param {boolean} fullwidth
     * @param {array|string} cssClass
     */ open(content, callback, position, closable, delay, fullwidth, cssClass) {
        // avoid multiple backdrops
        this._removeExistingOffCanvas();
        const offCanvas = this._createOffCanvas(position, fullwidth, cssClass, closable);
        this.setContent(content, closable, delay);
        this._openOffcanvas(offCanvas, callback);
    }
    /**
     * Method to change the content of the already visible OffCanvas
     * @param {string} content
     * @param {number} delay
     */ setContent(content, delay) {
        const offCanvas = this.getOffCanvas();
        if (!offCanvas[0]) {
            return;
        }
        offCanvas[0].innerHTML = content;
        // register events again
        this._registerEvents(delay);
    }
    /**
     * adds an additional class to the offcanvas
     *
     * @param {string} className
     */ setAdditionalClassName(className) {
        const offCanvas = this.getOffCanvas();
        offCanvas[0].classList.add(className);
    }
    /**
     * Determine list of existing offcanvas
     * @returns {NodeListOf<Element>}
     * @private
     */ getOffCanvas() {
        return document.querySelectorAll(".".concat(OFF_CANVAS_CLASS));
    }
    /**
     * Close the offcanvas and its backdrop when the browser goes back in history
     * @param {number} delay
     */ close(delay) {
        const OffCanvasElements = this.getOffCanvas();
        Iterator.iterate(OffCanvasElements, (offCanvas)=>{
            const offCanvasInstance = bootstrap.Offcanvas.getInstance(offCanvas);
            offCanvasInstance.hide();
        });
        setTimeout(()=>{
            this.$emitter.publish("onCloseOffcanvas", {
                offCanvasContent: OffCanvasElements
            });
        }, delay);
    }
    /**
     * Callback for close button, goes back in browser history to trigger close
     * @returns {void}
     */ goBackInHistory() {
        window.history.back();
    }
    /**
     * Returns whether any OffCanvas exists or not
     * @returns {boolean}
     */ exists() {
        return this.getOffCanvas().length > 0;
    }
    /**
     * Opens the offcanvas and its backdrop
     *
     * @param {HTMLElement} offCanvas
     * @param {function} callback
     *
     * @private
     */ _openOffcanvas(offCanvas, callback) {
        window.focusHandler.saveFocusState("offcanvas");
        OffCanvasSingleton.bsOffcanvas.show();
        window.history.pushState("offcanvas-open", "");
        // if a callback function is being injected execute it after opening the OffCanvas
        if (typeof callback === "function") {
            callback();
        }
    }
    /**
     * Register events
     * @param {number} delay
     * @private
     */ _registerEvents(delay) {
        const event = DeviceDetection.isTouchDevice() ? "touchend" : "click";
        const offCanvasElements = this.getOffCanvas();
        // Ensure OffCanvas is removed from the DOM and events are published.
        Iterator.iterate(offCanvasElements, (offCanvas)=>{
            const onBsClose = ()=>{
                setTimeout(()=>{
                    offCanvas.remove();
                    window.focusHandler.resumeFocusState("offcanvas");
                    this.$emitter.publish("onCloseOffcanvas", {
                        offCanvasContent: offCanvasElements
                    });
                }, delay);
                offCanvas.removeEventListener("hide.bs.offcanvas", onBsClose);
            };
            offCanvas.addEventListener("hide.bs.offcanvas", onBsClose);
        });
        window.addEventListener("popstate", this.close.bind(this, delay), {
            once: true
        });
        const closeTriggers = document.querySelectorAll(".".concat(OFF_CANVAS_CLOSE_TRIGGER_CLASS));
        Iterator.iterate(closeTriggers, (trigger)=>trigger.addEventListener(event, this.close.bind(this, delay)));
    }
    /**
     * Remove all existing offcanvas from DOM
     * @private
     */ _removeExistingOffCanvas() {
        OffCanvasSingleton.bsOffcanvas = null;
        const offCanvasElements = this.getOffCanvas();
        return Iterator.iterate(offCanvasElements, (offCanvas)=>offCanvas.remove());
    }
    /**
     * Defines the position of the offcanvas by setting css class
     * @param {'left'|'right'|'bottom'} position
     * @returns {string}
     * @private
     */ _getPositionClass(position) {
        if (position === "left") {
            return "offcanvas-start";
        }
        if (position === "right") {
            return "offcanvas-end";
        }
        return "offcanvas-".concat(position);
    }
    /**
     * Creates the offcanvas element prototype including all relevant settings,
     * appends it to the DOM and returns the HTMLElement for further processing
     * @param {'left'|'right'|'bottom'} position
     * @param {boolean} fullwidth
     * @param {array|string} cssClass
     * @param {boolean} closable
     * @returns {HTMLElement}
     * @private
     */ _createOffCanvas(position, fullwidth, cssClass, closable) {
        const offCanvas = document.createElement("div");
        offCanvas.classList.add(OFF_CANVAS_CLASS);
        offCanvas.classList.add(this._getPositionClass(position));
        offCanvas.setAttribute("tabindex", "-1");
        if (fullwidth === true) {
            offCanvas.classList.add(OFF_CANVAS_FULLWIDTH_CLASS);
        }
        if (cssClass) {
            const type = typeof cssClass;
            if (type === "string") {
                offCanvas.classList.add(cssClass);
            } else if (Array.isArray(cssClass)) {
                cssClass.forEach((value)=>{
                    offCanvas.classList.add(value);
                });
            } else {
                throw new Error('The type "'.concat(type, '" is not supported. Please pass an array or a string.'));
            }
        }
        document.body.appendChild(offCanvas);
        OffCanvasSingleton.bsOffcanvas = new bootstrap.Offcanvas(offCanvas, {
            // Only use "static" mode (no close via click on backdrop) when "closable" option is explicitly set to "false".
            backdrop: closable === false ? "static" : true
        });
        return offCanvas;
    }
    constructor(){
        this.$emitter = new NativeEventEmitter();
    }
}
/**
 * Create the OffCanvas instance.
 * @type {Readonly<OffCanvasSingleton>}
 */ const OffCanvasInstance = Object.freeze(new OffCanvasSingleton());
class OffCanvas {
    /**
     * Open the OffCanvas
     * @param {string} content
     * @param {function|null} callback
     * @param {'left'|'right'|'bottom'} position
     * @param {boolean} closable
     * @param {number} delay
     * @param {boolean} fullwidth
     * @param {array|string} cssClass
     */ static open(content) {
        let callback = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : null, position = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : "left", closable = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : true, delay = arguments.length > 4 && arguments[4] !== void 0 ? arguments[4] : REMOVE_OFF_CANVAS_DELAY, fullwidth = arguments.length > 5 && arguments[5] !== void 0 ? arguments[5] : false, cssClass = arguments.length > 6 && arguments[6] !== void 0 ? arguments[6] : "";
        OffCanvasInstance.open(content, callback, position, closable, delay, fullwidth, cssClass);
    }
    /**
     * Change content of visible OffCanvas
     * @param {string} content
     * @param {boolean} closable
     * @param {number} delay
     */ static setContent(content) {
        let closable = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : true, delay = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : REMOVE_OFF_CANVAS_DELAY;
        OffCanvasInstance.setContent(content, closable, delay);
    }
    /**
     * adds an additional class to the offcanvas
     *
     * @param {string} className
     */ static setAdditionalClassName(className) {
        OffCanvasInstance.setAdditionalClassName(className);
    }
    /**
     * Close the OffCanvas
     * @param {number} delay
     */ static close() {
        let delay = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : REMOVE_OFF_CANVAS_DELAY;
        OffCanvasInstance.close(delay);
    }
    /**
     * Returns whether any OffCanvas exists or not
     * @returns {boolean}
     */ static exists() {
        return OffCanvasInstance.exists();
    }
    /**
     * returns all existing offcanvas elements
     *
     * @returns {NodeListOf<Element>}
     */ static getOffCanvas() {
        return OffCanvasInstance.getOffCanvas();
    }
    /**
     * Expose constant
     * @returns {number}
     */ static REMOVE_OFF_CANVAS_DELAY() {
        return REMOVE_OFF_CANVAS_DELAY;
    }
}


;// CONCATENATED MODULE: ./service/http-client.service.js
/**
 * @package storefront
 */ class HttpClient {
    /**
     * Request GET
     *
     * @param {string} url
     * @param {function} callback
     * @param {string} contentType
     *
     * @returns {XMLHttpRequest}
     */ get(url, callback) {
        let contentType = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : "application/json";
        const request = this._createPreparedRequest("GET", url, contentType);
        return this._sendRequest(request, null, callback);
    }
    /**
     * Request POST
     *
     * @param {string} url
     * @param {object|null} data
     * @param {function} callback
     * @param {string} contentType
     *
     * @returns {XMLHttpRequest}
     */ post(url, data, callback) {
        let contentType = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : "application/json";
        contentType = this._getContentType(data, contentType);
        const request = this._createPreparedRequest("POST", url, contentType);
        return this._sendRequest(request, data, callback);
    }
    /**
     * Request DELETE
     *
     * @param {string} url
     * @param {object|null} data
     * @param {function} callback
     * @param {string} contentType
     *
     * @returns {XMLHttpRequest}
     */ delete(url, data, callback) {
        let contentType = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : "application/json";
        contentType = this._getContentType(data, contentType);
        const request = this._createPreparedRequest("DELETE", url, contentType);
        return this._sendRequest(request, data, callback);
    }
    /**
     * Request PATCH
     *
     * @param {string} url
     * @param {object|null} data
     * @param {function} callback
     * @param {string} contentType
     *
     * @returns {XMLHttpRequest}
     */ patch(url, data, callback) {
        let contentType = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : "application/json";
        contentType = this._getContentType(data, contentType);
        const request = this._createPreparedRequest("PATCH", url, contentType);
        return this._sendRequest(request, data, callback);
    }
    /**
     * Abort running Request
     *
     * @returns {*}
     */ abort() {
        if (this._request) {
            return this._request.abort();
        }
    }
    /**
     * Set the error handling
     *
     * @param {boolean} errorHandlingInternal
     */ setErrorHandlingInternal(errorHandlingInternal) {
        this._errorHandlingInternal = errorHandlingInternal;
    }
    /**
     * @private
     * Register event listener, which executes the given callback, when the request has finished
     *
     * @param {XMLHttpRequest} request
     * @param {function} callback
     */ _registerOnLoaded(request, callback) {
        if (!callback) {
            return;
        }
        if (this._errorHandlingInternal === true) {
            request.addEventListener("load", ()=>{
                callback(request.responseText, request);
            });
            request.addEventListener("abort", ()=>{
                console.warn("the request to ".concat(request.responseURL, " was aborted"));
            });
            request.addEventListener("error", ()=>{
                console.warn("the request to ".concat(request.responseURL, " failed with status ").concat(request.status));
            });
            request.addEventListener("timeout", ()=>{
                console.warn("the request to ".concat(request.responseURL, " timed out"));
            });
        } else {
            request.addEventListener("loadend", ()=>{
                callback(request.responseText, request);
            });
        }
    }
    _sendRequest(request, data, callback) {
        this._registerOnLoaded(request, callback);
        request.send(data);
        return request;
    }
    /**
     * @private
     * Returns the appropriate content type for the request
     *
     * @param {*} data
     * @param {string} contentType
     *
     * @returns {string|boolean}
     */ _getContentType(data, contentType) {
        // when sending form data,
        // the content-type has to be automatically set,
        // to use the correct content-disposition
        // https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Content-Disposition
        if (data instanceof FormData) {
            contentType = false;
        }
        return contentType;
    }
    /**
     * @private
     * Returns a new and configured XMLHttpRequest object
     *
     * @param {'GET'|'POST'|'DELETE'|'PATCH'} type
     * @param {string} url
     * @param {string} contentType
     *
     * @returns {XMLHttpRequest}
     */ _createPreparedRequest(type, url, contentType) {
        this._request = new XMLHttpRequest();
        this._request.open(type, url);
        this._request.setRequestHeader("X-Requested-With", "XMLHttpRequest");
        if (contentType) {
            this._request.setRequestHeader("Content-type", contentType);
        }
        return this._request;
    }
    constructor(){
        this._request = null;
        this._errorHandlingInternal = false;
    }
}


;// CONCATENATED MODULE: ./utility/loading-indicator/loading-indicator.util.js

const SELECTOR_CLASS = "loader";
const VISUALLY_HIDDEN_CLASS = "visually-hidden";
const INDICATOR_POSITION = {
    BEFORE: "before",
    AFTER: "after",
    INNER: "inner"
};
class LoadingIndicatorUtil {
    /**
     * Inserts a loading indicator inside the parent element's html
     */ create() {
        if (this.exists()) return;
        if (this.position === INDICATOR_POSITION.INNER) {
            this.parent.innerHTML = LoadingIndicatorUtil.getTemplate();
            return;
        }
        this.parent.insertAdjacentHTML(this._getPosition(), LoadingIndicatorUtil.getTemplate());
    }
    /**
     * Removes all existing loading indicators inside the parent
     */ remove() {
        const indicators = this.parent.querySelectorAll(".".concat(SELECTOR_CLASS));
        Iterator.iterate(indicators, (indicator)=>indicator.remove());
    }
    /**
     * Checks if a loading indicator already exists
     * @returns {boolean}
     * @protected
     */ exists() {
        return this.parent.querySelectorAll(".".concat(SELECTOR_CLASS)).length > 0;
    }
    /**
     * Defines the position to which the loading indicator shall be inserted.
     * Depends on the usage of the "insertAdjacentHTML" method.
     * @returns {"afterbegin"|"beforeend"}
     * @private
     */ _getPosition() {
        return this.position === INDICATOR_POSITION.BEFORE ? "afterbegin" : "beforeend";
    }
    /**
     * The loading indicators HTML template definition
     * @returns {string}
     */ static getTemplate() {
        return '<div class="'.concat(SELECTOR_CLASS, '" role="status">\n                    <span class="').concat(VISUALLY_HIDDEN_CLASS, '">Loading...</span>\n                </div>');
    }
    /**
     * Return the constant
     * @returns {string}
     * @constructor
     */ static SELECTOR_CLASS() {
        return SELECTOR_CLASS;
    }
    /**
     * Constructor
     * @param {Element|string} parent
     * @param position
     */ constructor(parent, position = INDICATOR_POSITION.BEFORE){
        this.parent = parent instanceof Element ? parent : document.body.querySelector(parent);
        this.position = position;
    }
}
/**
 * @package storefront
 */ 

;// CONCATENATED MODULE: ./plugin/offcanvas/ajax-offcanvas.plugin.js



// xhr call storage
let xhr = null;
class AjaxOffCanvas extends OffCanvas {
    /**
     * Fire AJAX request to get the offcanvas content
     *
     * @param {string} url
     * @param {*|boolean} data
     * @param {function|null} callback
     * @param {'left'|'right'} position
     * @param {boolean} closable
     * @param {number} delay
     * @param {boolean} fullwidth
     * @param {array|string} cssClass
     */ static open() {
        let url = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : false, data = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false, callback = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : null, position = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : "left", closable = arguments.length > 4 && arguments[4] !== void 0 ? arguments[4] : true, delay = arguments.length > 5 && arguments[5] !== void 0 ? arguments[5] : OffCanvas.REMOVE_OFF_CANVAS_DELAY(), fullwidth = arguments.length > 6 && arguments[6] !== void 0 ? arguments[6] : false, cssClass = arguments.length > 7 && arguments[7] !== void 0 ? arguments[7] : "";
        if (!url) {
            throw new Error("A url must be given!");
        }
        // avoid multiple backdrops
        OffCanvasInstance._removeExistingOffCanvas();
        const offCanvas = OffCanvasInstance._createOffCanvas(position, fullwidth, cssClass, closable);
        this.setContent(url, data, callback, closable, delay);
        OffCanvasInstance._openOffcanvas(offCanvas);
    }
    /**
     * Method to change the content of the already visible OffCanvas via xhr
     *
     * @param {string} url
     * @param {*} data
     * @param {function} callback
     * @param {boolean} closable
     * @param {number} delay
     */ static setContent(url, data, callback, closable, delay) {
        const client = new HttpClient();
        super.setContent('<div class="offcanvas-body">'.concat(LoadingIndicatorUtil.getTemplate(), "</div>"), closable, delay);
        // interrupt already running ajax calls
        if (xhr) xhr.abort();
        const cb = (response)=>{
            super.setContent(response, closable, delay);
            // if a callback function is being injected execute it after opening the OffCanvas
            if (typeof callback === "function") {
                callback(response);
            }
        };
        if (data) {
            xhr = client.post(url, data, AjaxOffCanvas.executeCallback.bind(this, cb));
        } else {
            xhr = client.get(url, AjaxOffCanvas.executeCallback.bind(this, cb));
        }
    }
    /**
     * Executes the given callback
     * and initializes all plugins
     *
     * @param {function} cb
     * @param {string} response
     */ static executeCallback(cb, response) {
        if (typeof cb === "function") {
            cb(response);
        }
        window.PluginManager.initializePlugins();
    }
}
/**
 * @package storefront
 */ 

;// CONCATENATED MODULE: ./utility/loading-indicator/element-loading-indicator.util.js

const ELEMENT_LOADER_CLASS = "element-loader-backdrop";
const element_loading_indicator_util_VISUALLY_HIDDEN_CLASS = "visually-hidden";
class ElementLoadingIndicatorUtil extends LoadingIndicatorUtil {
    /**
     * adds the loader from the element
     *
     * @param {HTMLElement} el
     */ static create(el) {
        el.classList.add("has-element-loader");
        if (ElementLoadingIndicatorUtil.exists(el)) return;
        ElementLoadingIndicatorUtil.appendLoader(el);
        setTimeout(()=>{
            const loader = el.querySelector(".".concat(ELEMENT_LOADER_CLASS));
            if (!loader) {
                return;
            }
            loader.classList.add("element-loader-backdrop-open");
        }, 1);
    }
    /**
     * removes the loader from the element
     *
     * @param {HTMLElement} el
     */ static remove(el) {
        el.classList.remove("has-element-loader");
        const loader = el.querySelector(".".concat(ELEMENT_LOADER_CLASS));
        if (!loader) {
            return;
        }
        loader.remove();
    }
    /**
     * checks if a loader is already present
     *
     * @param {HTMLElement} el
     *
     * @returns {boolean}
     */ static exists(el) {
        return el.querySelectorAll(".".concat(ELEMENT_LOADER_CLASS)).length > 0;
    }
    /**
     * returns the loader template
     *
     * @returns {string}
     */ static getTemplate() {
        return '\n        <div class="'.concat(ELEMENT_LOADER_CLASS, '">\n            <div class="loader" role="status">\n                <span class="').concat(element_loading_indicator_util_VISUALLY_HIDDEN_CLASS, '">Loading...</span>\n            </div>\n        </div>\n        ');
    }
    /**
     * inserts the loader into the passed element
     *
     * @param {HTMLElement} el
     */ static appendLoader(el) {
        el.insertAdjacentHTML("beforeend", ElementLoadingIndicatorUtil.getTemplate());
    }
}
/**
 * @package storefront
 */ 

;// CONCATENATED MODULE: ./plugin/cookie/cookie-configuration.plugin.js
/**
 *
 * CookieConfiguration plugin
 * --------------------------
 * Renders the configuration template inside an offCanvas
 *
 * Applies its "openOffCanvas"-eventHandler to the following selector:
 * 1) '.js-cookie-configuration-button button'
 * 2) `[href="${window.router['frontend.cookie.offcanvas']}"]`
 *
 * Can be opened manually using the public method "openOffCanvas"
 *
 * The cookie form is defined via CookieController.php
 * Cookies marked as "required" (see CookieController.php) are ignored, since they are assumed to be set manually
 *
 * Configuration changes are pushed to the global (document) event "CookieConfiguration_Update"
 *
 */ /* global PluginManager */ 





// These events will be published via a global (document) EventEmitter
const COOKIE_CONFIGURATION_UPDATE = "CookieConfiguration_Update";
const COOKIE_CONFIGURATION_CLOSE_OFF_CANVAS = "CookieConfiguration_CloseOffCanvas";
class CookieConfiguration extends Plugin {
    init() {
        this.lastState = {
            active: [],
            inactive: []
        };
        this._httpClient = new HttpClient();
        this._registerEvents();
    }
    /**
     * Registers the events for displaying the offCanvas
     * Applies the event to all elements using the "buttonOpenSelector" or "customLinkSelector"
     *
     * @private
     */ _registerEvents() {
        const { submitEvent, buttonOpenSelector, customLinkSelector, globalButtonAcceptAllSelector } = this.options;
        Array.from(document.querySelectorAll(buttonOpenSelector)).forEach((button)=>{
            button.addEventListener(submitEvent, this.openOffCanvas.bind(this));
        });
        Array.from(document.querySelectorAll(customLinkSelector)).forEach((customLink)=>{
            customLink.addEventListener(submitEvent, this._handleCustomLink.bind(this));
        });
        Array.from(document.querySelectorAll(globalButtonAcceptAllSelector)).forEach((customLink)=>{
            customLink.addEventListener(submitEvent, this._acceptAllCookiesFromCookieBar.bind(this));
        });
    }
    /**
     * Registers events required by the offCanvas template
     *
     * @private
     */ _registerOffCanvasEvents() {
        const { submitEvent, buttonSubmitSelector, buttonAcceptAllSelector, wrapperToggleSelector } = this.options;
        const offCanvas = this._getOffCanvas();
        if (offCanvas) {
            const button = offCanvas.querySelector(buttonSubmitSelector);
            const buttonAcceptAll = offCanvas.querySelector(buttonAcceptAllSelector);
            const checkboxes = Array.from(offCanvas.querySelectorAll('input[type="checkbox"]'));
            const wrapperTrigger = Array.from(offCanvas.querySelectorAll(wrapperToggleSelector));
            if (button) {
                button.addEventListener(submitEvent, this._handleSubmit.bind(this, CookieStorageHelper));
            }
            if (buttonAcceptAll) {
                buttonAcceptAll.addEventListener(submitEvent, this._acceptAllCookiesFromOffCanvas.bind(this, CookieStorageHelper));
            }
            checkboxes.forEach((checkbox)=>{
                checkbox.addEventListener(submitEvent, this._handleCheckbox.bind(this));
            });
            wrapperTrigger.forEach((trigger)=>{
                trigger.addEventListener(submitEvent, this._handleWrapperTrigger.bind(this));
            });
        }
    }
    /**
     * Prevent the event default e.g. for anchor elements using the href-selector
     *
     * @param event
     * @private
     */ _handleCustomLink(event) {
        event.preventDefault();
        this.openOffCanvas();
    }
    _handleUpdateListener(active, inactive) {
        const updatedCookies = this._getUpdatedCookies(active, inactive);
        document.$emitter.publish(COOKIE_CONFIGURATION_UPDATE, updatedCookies);
    }
    /**
     * Compare the current in-/active cookies to the initialState and return updated cookies only
     *
     * @param active
     * @param inactive
     * @private
     */ _getUpdatedCookies(active, inactive) {
        const { lastState } = this;
        const updated = {};
        active.forEach((currentCheckbox)=>{
            if (lastState.inactive.includes(currentCheckbox)) {
                updated[currentCheckbox] = true;
            }
        });
        inactive.forEach((currentCheckbox)=>{
            if (lastState.active.includes(currentCheckbox)) {
                updated[currentCheckbox] = false;
            }
        });
        return updated;
    }
    /**
     * Public method to open the offCanvas
     *
     * @param {function|null} callback
     */ openOffCanvas(callback) {
        const { offCanvasPosition } = this.options;
        const url = window.router["frontend.cookie.offcanvas"];
        this._hideCookieBar();
        AjaxOffCanvas.open(url, false, this._onOffCanvasOpened.bind(this, callback), offCanvasPosition);
    }
    /**
     * Public method to close the offCanvas
     *
     * @param callback
     */ closeOffCanvas(callback) {
        AjaxOffCanvas.close();
        if (typeof callback === "function") {
            callback();
        }
    }
    /**
     * Private method to apply events to the cookie-configuration template
     * Also sets the initial checkbox state based on currently set cookies
     *
     * @private
     */ _onOffCanvasOpened(callback) {
        this._registerOffCanvasEvents();
        this._setInitialState();
        this._setInitialOffcanvasState();
        PluginManager.initializePlugins();
        if (typeof callback === "function") {
            callback();
        }
    }
    _hideCookieBar() {
        const cookiePermissionPlugin = PluginManager.getPluginInstances("CookiePermission");
        if (cookiePermissionPlugin && cookiePermissionPlugin[0]) {
            cookiePermissionPlugin[0]._hideCookieBar();
            cookiePermissionPlugin[0]._removeBodyPadding();
        }
    }
    /**
     * Sets the `lastState` of the current cookie configuration, either passed as
     * parameter `cookies`, otherwise it is loaded by parsing the DOM of the off
     * canvas sidebar
     *
     * @param {?Array} cookies
     * @private
     */ _setInitialState() {
        let cookies = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : null;
        const availableCookies = cookies || this._getCookies("all");
        const activeCookies = [];
        const inactiveCookies = [];
        availableCookies.forEach((param)=>{
            let { cookie, required } = param;
            const isActive = CookieStorageHelper.getItem(cookie);
            if (isActive || required) {
                activeCookies.push(cookie);
            } else {
                inactiveCookies.push(cookie);
            }
        });
        this.lastState = {
            active: activeCookies,
            inactive: inactiveCookies
        };
    }
    /**
     * Preselect coherent checkboxes in the off canvas sidebar
     *
     * @private
     */ _setInitialOffcanvasState() {
        const activeCookies = this.lastState.active;
        const offCanvas = this._getOffCanvas();
        activeCookies.forEach((activeCookie)=>{
            const target = offCanvas.querySelector('[data-cookie="'.concat(activeCookie, '"]'));
            target.checked = true;
            this._childCheckboxEvent(target);
        });
    }
    /**
     * From click target, try to find the cookie group container and toggle the open state
     *
     * @param event
     * @private
     */ _handleWrapperTrigger(event) {
        event.preventDefault();
        const { entriesActiveClass, entriesClass, groupClass } = this.options;
        const { target } = event;
        const cookieEntryContainer = this._findParentEl(target, entriesClass, groupClass);
        if (cookieEntryContainer) {
            const active = cookieEntryContainer.classList.contains(entriesActiveClass);
            if (active) {
                cookieEntryContainer.classList.remove(entriesActiveClass);
            } else {
                cookieEntryContainer.classList.add(entriesActiveClass);
            }
        }
    }
    /**
     * Determine whether the target checkbox is a parent or a child checkbox
     *
     * @param event
     * @private
     */ _handleCheckbox(event) {
        const { parentInputClass } = this.options;
        const { target } = event;
        const callback = target.classList.contains(parentInputClass) ? this._parentCheckboxEvent : this._childCheckboxEvent;
        callback.call(this, target);
    }
    /**
     * Recursively checks the provided elements parent for the first class parameter
     * Stops the recursion, if the parentElement contains the second class parameter
     *
     * @param el
     * @param findClass
     * @param abortClass
     * @returns {*|HTMLElement|*}
     * @private
     */ _findParentEl(el, findClass) {
        let abortClass = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : null;
        while(!!el && !el.classList.contains(abortClass)){
            if (el.classList.contains(findClass)) {
                return el;
            }
            el = el.parentElement;
        }
        return null;
    }
    _isChecked(target) {
        return !!target.checked;
    }
    /**
     * De-/select all checkboxes of the current group
     *
     * @param target
     * @private
     */ _parentCheckboxEvent(target) {
        const { groupClass } = this.options;
        const newState = this._isChecked(target);
        const group = this._findParentEl(target, groupClass);
        this._toggleWholeGroup(newState, group);
    }
    /**
     *
     * Trigger a change event for the "select-all" checkbox of the childs group
     *
     * @param target
     * @private
     */ _childCheckboxEvent(target) {
        const { groupClass } = this.options;
        const newState = this._isChecked(target);
        const group = this._findParentEl(target, groupClass);
        this._toggleParentCheckbox(newState, group);
    }
    /**
     * Toogle each checkbox inside the given group
     *
     * @param state
     * @param group
     * @private
     */ _toggleWholeGroup(state, group) {
        Array.from(group.querySelectorAll("input")).forEach((checkbox)=>{
            checkbox.checked = state;
        });
    }
    /**
     * Toggle a groups "select-all" checkbox according to changes to its child checkboxes
     * "Check, if any child checkbox is checked" / "Uncheck, if no child checkboxes are checked"
     *
     * @param state
     * @param group
     * @private
     */ _toggleParentCheckbox(state, group) {
        const { parentInputSelector } = this.options;
        const checkboxes = Array.from(group.querySelectorAll("input:not(".concat(parentInputSelector, ")")));
        const activeCheckboxes = Array.from(group.querySelectorAll("input:not(".concat(parentInputSelector, "):checked")));
        if (checkboxes.length > 0) {
            const parentCheckbox = group.querySelector(parentInputSelector);
            if (parentCheckbox) {
                const checked = activeCheckboxes.length > 0;
                const indeterminate = checked && activeCheckboxes.length !== checkboxes.length;
                parentCheckbox.checked = checked;
                parentCheckbox.indeterminate = indeterminate;
            }
        }
    }
    /**
     * Event handler for the 'Save' button inside the offCanvas
     *
     * Removes unselected cookies, if already set
     * Sets or refreshes selected cookies
     *
     * @private
     */ _handleSubmit() {
        const activeCookies = this._getCookies("active");
        const inactiveCookies = this._getCookies("inactive");
        const { cookiePreference } = this.options;
        const activeCookieNames = [];
        const inactiveCookieNames = [];
        inactiveCookies.forEach((param)=>{
            let { cookie } = param;
            inactiveCookieNames.push(cookie);
            if (CookieStorageHelper.getItem(cookie)) {
                CookieStorageHelper.removeItem(cookie);
            }
        });
        /**
         * Cookies without value are passed to the updateListener
         * ( see "_handleUpdateListener" method )
         */ activeCookies.forEach((param)=>{
            let { cookie, value, expiration } = param;
            activeCookieNames.push(cookie);
            if (cookie && value) {
                CookieStorageHelper.setItem(cookie, value, expiration);
            }
        });
        CookieStorageHelper.setItem(cookiePreference, "1", "30");
        this._handleUpdateListener(activeCookieNames, inactiveCookieNames);
        this.closeOffCanvas(document.$emitter.publish(COOKIE_CONFIGURATION_CLOSE_OFF_CANVAS));
    }
    /**
     * Accepts all cookies. Pass `true` to the loadIntoMemory parameter to load the DOM into memory instead of
     * opening the OffCanvas menu.
     *
     * @param loadIntoMemory
     */ acceptAllCookies() {
        let loadIntoMemory = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : false;
        if (!loadIntoMemory) {
            this._handleAcceptAll();
            this.closeOffCanvas();
            return;
        }
        ElementLoadingIndicatorUtil.create(this.el);
        const url = window.router["frontend.cookie.offcanvas"];
        this._httpClient.get(url, (response)=>{
            const dom = new DOMParser().parseFromString(response, "text/html");
            this._handleAcceptAll(dom);
            ElementLoadingIndicatorUtil.remove(this.el);
            this._hideCookieBar();
        });
    }
    /**
     * Event handler for the 'Allow all'-button in the cookie bar.
     * It loads the DOM into memory before searching for, and accepting the cookies.
     *
     * @private
     */ _acceptAllCookiesFromCookieBar() {
        return this.acceptAllCookies(true);
    }
    /**
     * Event handler for the 'Allow all'-button in the off canvas view.
     * It uses the DOM from the Off Canvas container to search for, and accept the cookies.
     * After accepting, it closes the OffCanvas sidebar.
     *
     * @private
     */ _acceptAllCookiesFromOffCanvas() {
        return this.acceptAllCookies();
    }
    /**
     * This will set and refresh all registered cookies.
     *
     * @param {?(Document|HTMLElement)} offCanvas
     * @private
     */ _handleAcceptAll() {
        let offCanvas = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : null;
        const allCookies = this._getCookies("all", offCanvas);
        this._setInitialState(allCookies);
        const { cookiePreference } = this.options;
        allCookies.forEach((param)=>{
            let { cookie, value, expiration } = param;
            if (cookie && value) {
                CookieStorageHelper.setItem(cookie, value, expiration);
            }
        });
        CookieStorageHelper.setItem(cookiePreference, "1", "30");
        this._handleUpdateListener(allCookies.map((param)=>{
            let { cookie } = param;
            return cookie;
        }), []);
    }
    /**
     * Get cookies passed to the configuration template
     * Can be filtered by "all", "active" or "inactive"
     *
     * Always excludes "required" cookies, since they are assumed to be set separately.
     *
     * @param type
     * @param {?(Document|HTMLElement)} offCanvas
     * @returns {Array}
     * @private
     */ _getCookies() {
        let type = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : "all", offCanvas = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : null;
        const { cookieSelector } = this.options;
        if (!offCanvas) {
            offCanvas = this._getOffCanvas();
        }
        return Array.from(offCanvas.querySelectorAll(cookieSelector)).filter((cookieInput)=>{
            switch(type){
                case "all":
                    return true;
                case "active":
                    return this._isChecked(cookieInput);
                case "inactive":
                    return !this._isChecked(cookieInput);
                default:
                    return false;
            }
        }).map((filteredInput)=>{
            const { cookie, cookieValue, cookieExpiration, cookieRequired } = filteredInput.dataset;
            return {
                cookie,
                value: cookieValue,
                expiration: cookieExpiration,
                required: cookieRequired
            };
        });
    }
    /**
     * Returns the current offcanvas element if available
     *
     * @returns {*}
     * @private
     */ _getOffCanvas() {
        const elements = OffCanvas ? OffCanvas.getOffCanvas() : [];
        return elements && elements.length > 0 ? elements[0] : false;
    }
}
CookieConfiguration.options = {
    offCanvasPosition: "left",
    submitEvent: "click",
    cookiePreference: "cookie-preference",
    cookieSelector: "[data-cookie]",
    buttonOpenSelector: ".js-cookie-configuration-button button",
    buttonSubmitSelector: ".js-offcanvas-cookie-submit",
    buttonAcceptAllSelector: ".js-offcanvas-cookie-accept-all",
    globalButtonAcceptAllSelector: ".js-cookie-accept-all-button",
    wrapperToggleSelector: ".offcanvas-cookie-entries span",
    parentInputSelector: ".offcanvas-cookie-parent-input",
    customLinkSelector: '[href="'.concat(window.router["frontend.cookie.offcanvas"], '"]'),
    entriesActiveClass: "offcanvas-cookie-entries--active",
    entriesClass: "offcanvas-cookie-entries",
    groupClass: "offcanvas-cookie-group",
    parentInputClass: "offcanvas-cookie-parent-input"
};


;// CONCATENATED MODULE: ../../../../../../../custom/plugins/profitmetrics/src/Resources/app/storefront/src/main.js


function _pm_geturlparm(parmname) {
    var regex = new RegExp("[\\?&]" + parmname + "=([^&#]*)");
    var result = regex.exec(location.search);
    return result === null ? "" : decodeURIComponent(result[1].replace(/\+/g, " "));
}
function _pm_getGclid() {
    let gclid = _pm_geturlparm("gclid");
    if (gclid !== "") {
        return gclid;
    }
    let gclidfromGclAw = _pm_getcookie("_gcl_aw");
    if (gclidfromGclAw != null) {
        let gclAwSplitAll = gclidfromGclAw.split(".");
        if (gclAwSplitAll.length >= 3) {
            return gclidfromGclAw.substring(gclAwSplitAll[0].length + gclAwSplitAll[1].length + 1 + 1); // each +1 corresponds to '.'s
        }
    }
    let gclidfromFPGCLAW = _pm_getcookie("FPGCLAW");
    if (gclidfromFPGCLAW != null) {
        const fpgSplitAll = gclidfromFPGCLAW.split(".");
        if (fpgSplitAll.length >= 3) {
            return gclidfromFPGCLAW.substring(fpgSplitAll[0].length + fpgSplitAll[1].length + 1 + 1); // each +1 corresponds to '.'s
        }
    }
}
function _pm_getcookie(cookiename) {
    cookiename += "=";
    if (document.cookie.indexOf(cookiename) !== -1) {
        var idxofSource = document.cookie.indexOf(cookiename) + cookiename.length;
        var idxofEnd = document.cookie.indexOf(";", idxofSource);
        var cookval = "";
        if (idxofEnd === -1) {
            cookval = document.cookie.substr(idxofSource);
        } else {
            cookval = document.cookie.substr(idxofSource, idxofEnd - idxofSource);
        }
        if (cookval.length !== 0) {
            return cookval;
        } else {
            return null;
        }
    }
}
function _pm_getGa4SessionId() {
    const retImploded = document.cookie.split(";").filter((c)=>c.indexOf("_ga_") !== -1).map((c)=>c.trim().split(".")).map((c)=>c[0].substring(4, c[0].indexOf("=")) + ":" + c[2]) // index 2 for session id
    .join(",");
    return null != retImploded && retImploded.length > 0 ? retImploded : null;
}
function _pm_getGa4SessionCount() {
    const retImploded = document.cookie.split(";").filter((c)=>c.indexOf("_ga_") !== -1).map((c)=>c.trim().split(".")).map((c)=>c[0].substring(4, c[0].indexOf("=")) + ":" + c[3]) // index 3 for session count
    .join(",");
    return null != retImploded && retImploded.length > 0 ? retImploded : null;
}
function _pm_getStoredTPTrack() {
    var ret = _pm_getcookie("pmTPTrack");
    if (null != ret && ret.length > 0) {
        ret = JSON.parse(decodeURIComponent(ret));
    } else {
        ret = {
            gclid: null,
            gacid: null,
            gacid_source: null,
            fbp: null,
            fbc: null,
            gbraid: null,
            wbraid: null,
            sccid: null,
            ttclid: null,
            msclkid: null,
            twclid: null,
            ga4SessionId: null,
            ga4SessionCount: null,
            timestamp: (new Date / 1E3 | 0) - 100
        };
    }
    return ret;
}
function _pm_storeTPTrack(tptrack) {
    var _pm_old_tpTrackCookVal = _pm_getcookie("pmTPTrack");
    if (!headlessMode) {
        var _pm_tpTrackCookVal = encodeURIComponent(JSON.stringify(tptrack));
        document.cookie = "pmTPTrack=" + _pm_tpTrackCookVal + "; path=/";
    }
}
function _pm_GetGacidFromTracker() {
    if (typeof ga == "function") {
        try {
            ga(function(tracker) {
                var gacid = tracker.get("clientId");
                if (null != gacid) {
                    var _pm_curPMTPTrack = _pm_getStoredTPTrack();
                    if (_pm_curPMTPTrack.gacid !== gacid) {
                        _pm_curPMTPTrack.gacid = gacid;
                        _pm_curPMTPTrack.gacid_source = "gatracker";
                        _pm_curPMTPTrack.timestamp = new Date / 1E3 | 0;
                        _pm_storeTPTrack(_pm_curPMTPTrack);
                    }
                }
            });
        } catch (eee) {}
    } else {
        setTimeout(_pm_GetGacidFromTracker, 100);
    }
}
function load_pmTPTrack() {
    var _pm_curPMTPTrack = _pm_getStoredTPTrack();
    var _pm_newFBC = _pm_getcookie("_fbc");
    if (null != _pm_newFBC && _pm_curPMTPTrack.fbc !== _pm_newFBC) {
        _pm_curPMTPTrack.fbc = _pm_newFBC;
        _pm_curPMTPTrack.timestamp = new Date / 1E3 | 0;
    }
    var _pm_newFBP = _pm_getcookie("_fbp");
    if (null != _pm_newFBP && _pm_curPMTPTrack.fbp !== _pm_newFBP) {
        _pm_curPMTPTrack.fbp = _pm_newFBP;
        _pm_curPMTPTrack.timestamp = new Date / 1E3 | 0;
    }
    var _pm_newGacid = _pm_getcookie("_ga");
    if (null != _pm_newGacid && _pm_curPMTPTrack.gacid_source !== "gatracker" && _pm_curPMTPTrack.gacid !== _pm_newGacid) {
        _pm_curPMTPTrack.gacid = _pm_newGacid;
        _pm_curPMTPTrack.gacid_source = "gacookie";
        _pm_curPMTPTrack.timestamp = new Date / 1E3 | 0;
    }
    var _pm_newGclid = _pm_getGclid();
    if (_pm_newGclid !== "") {
        _pm_curPMTPTrack.gclid = _pm_newGclid;
        _pm_curPMTPTrack.timestamp = new Date / 1E3 | 0;
    }
    var _pm_gbraid = _pm_geturlparm("gbraid");
    if (_pm_gbraid !== "") {
        _pm_curPMTPTrack.gbraid = _pm_gbraid;
        _pm_curPMTPTrack.timestamp = new Date / 1E3 | 0;
    }
    var _pm_wbraid = _pm_geturlparm("wbraid");
    if (_pm_wbraid !== "") {
        _pm_curPMTPTrack.wbraid = _pm_wbraid;
        _pm_curPMTPTrack.timestamp = new Date / 1E3 | 0;
    }
    var _pm_sccid = _pm_geturlparm("sccid");
    if (_pm_sccid !== "") {
        _pm_curPMTPTrack.sccid = _pm_sccid;
        _pm_curPMTPTrack.timestamp = new Date / 1E3 | 0;
    }
    var _pm_ttclid = _pm_geturlparm("ttclid");
    if (_pm_ttclid !== "") {
        _pm_curPMTPTrack.ttclid = _pm_ttclid;
        _pm_curPMTPTrack.timestamp = new Date / 1E3 | 0;
    }
    var _pm_msclkid = _pm_geturlparm("msclkid");
    if (_pm_msclkid !== "") {
        _pm_curPMTPTrack.msclkid = _pm_msclkid;
        _pm_curPMTPTrack.timestamp = new Date / 1E3 | 0;
    }
    var _pm_twclid = _pm_geturlparm("twclid");
    if (_pm_twclid !== "") {
        _pm_curPMTPTrack.twclid = _pm_twclid;
        _pm_curPMTPTrack.timestamp = new Date / 1E3 | 0;
    }
    var _pm_ga4SessionId = _pm_getGa4SessionId();
    if (_pm_ga4SessionId != null && _pm_ga4SessionId !== "") {
        _pm_curPMTPTrack.ga4SessionId = _pm_ga4SessionId;
        _pm_curPMTPTrack.timestamp = new Date / 1E3 | 0;
    }
    var _pm_ga4SessionCount = _pm_getGa4SessionCount();
    if (_pm_ga4SessionCount != null && _pm_ga4SessionCount !== "") {
        _pm_curPMTPTrack.ga4SessionCount = _pm_ga4SessionCount;
        _pm_curPMTPTrack.timestamp = new Date / 1E3 | 0;
    }
    // Temp solution
    _pm_curPMTPTrack.cc_marketing = window.cc_marketing;
    _pm_curPMTPTrack.cc_statistics = window.cc_statistics;
    _pm_storeTPTrack(_pm_curPMTPTrack);
    _pm_GetGacidFromTracker();
    // Set previousDecision
    localStorage.setItem("pfm-consent-granted", true);
}
// Set default consent state
window.cc_statistics = false;
window.cc_marketing = false;
window.addEventListener("load", function(_event) {
    var _Cookiebot_consent, _Cookiebot, _Cookiebot_consent1, _Cookiebot1, _CookieInformation, _CookieInformation1, _OnetrustActiveGroups, _OnetrustActiveGroups1, _getCkyConsent_categories, _getCkyConsent, _getCkyConsent_categories1, _getCkyConsent1, _CookieFirst_consent, _CookieFirst, _CookieFirst_consent1, _CookieFirst1, _CookieScript_instance_currentState_categories, _CookieScript_instance_currentState, _CookieScript_instance, _CookieScript, _CookieScript_instance_currentState_categories1, _CookieScript_instance_currentState1, _CookieScript_instance1, _CookieScript1, _window_google_tag_data, _window_google_tag_data_ics_entries_ad_storage, _window_google_tag_data_ics_entries, _window_google_tag_data_ics, _window_google_tag_data1, _window_google_tag_data_ics_entries_analytics_storage, _window_google_tag_data_ics_entries1, _window_google_tag_data_ics1, _window_google_tag_data2;
    let previousDecision = localStorage.getItem("pfm-consent-granted");
    function checkConsent() {
        if (CookieStorageHelper.getItem("pmTPTrack") === "1") {
            CookieStorageHelper.removeItem("pmTPTrack");
            window.cc_statistics = true;
            window.cc_marketing = true;
            load_pmTPTrack();
        }
    }
    checkConsent();
    if (!window.blockScriptBeforeConsent || previousDecision) {
        // Set default consent to true
        window.cc_statistics = true;
        window.cc_marketing = true;
        load_pmTPTrack();
    } else {
        document.$emitter.subscribe(COOKIE_CONFIGURATION_UPDATE, checkConsent);
    }
    // CookieBot
    // Get consent
    if (typeof Cookiebot !== "undefined" && ((_Cookiebot = Cookiebot) === null || _Cookiebot === void 0 ? void 0 : (_Cookiebot_consent = _Cookiebot.consent) === null || _Cookiebot_consent === void 0 ? void 0 : _Cookiebot_consent.statistics) && ((_Cookiebot1 = Cookiebot) === null || _Cookiebot1 === void 0 ? void 0 : (_Cookiebot_consent1 = _Cookiebot1.consent) === null || _Cookiebot_consent1 === void 0 ? void 0 : _Cookiebot_consent1.marketing)) {
        window.cc_statistics = Cookiebot.consent.statistics;
        window.cc_marketing = Cookiebot.consent.marketing;
        load_pmTPTrack();
    } else {
        // Add event listener
        window.addEventListener("CookiebotOnConsentReady", function() {
            var _Cookiebot_consent, _Cookiebot, _Cookiebot_consent1, _Cookiebot1;
            if (((_Cookiebot = Cookiebot) === null || _Cookiebot === void 0 ? void 0 : (_Cookiebot_consent = _Cookiebot.consent) === null || _Cookiebot_consent === void 0 ? void 0 : _Cookiebot_consent.statistics) && ((_Cookiebot1 = Cookiebot) === null || _Cookiebot1 === void 0 ? void 0 : (_Cookiebot_consent1 = _Cookiebot1.consent) === null || _Cookiebot_consent1 === void 0 ? void 0 : _Cookiebot_consent1.marketing)) {
                window.cc_statistics = Cookiebot.consent.statistics;
                window.cc_marketing = Cookiebot.consent.marketing;
                load_pmTPTrack();
            }
        });
    }
    // CookieInformation
    // Get consent
    if (typeof CookieInformation !== "undefined" && ((_CookieInformation = CookieInformation) === null || _CookieInformation === void 0 ? void 0 : _CookieInformation.getConsentGivenFor("cookie_cat_statistic")) && ((_CookieInformation1 = CookieInformation) === null || _CookieInformation1 === void 0 ? void 0 : _CookieInformation1.getConsentGivenFor("cookie_cat_marketing"))) {
        window.cc_statistics = CookieInformation.getConsentGivenFor("cookie_cat_statistic");
        window.cc_marketing = CookieInformation.getConsentGivenFor("cookie_cat_marketing");
        load_pmTPTrack();
    } else {
        // Add event listener
        window.addEventListener("CookieInformationConsentGiven", function() {
            var _CookieInformation, _CookieInformation1;
            if (((_CookieInformation = CookieInformation) === null || _CookieInformation === void 0 ? void 0 : _CookieInformation.getConsentGivenFor("cookie_cat_statistic")) && ((_CookieInformation1 = CookieInformation) === null || _CookieInformation1 === void 0 ? void 0 : _CookieInformation1.getConsentGivenFor("cookie_cat_marketing"))) {
                window.cc_statistics = CookieInformation.getConsentGivenFor("cookie_cat_statistic");
                window.cc_marketing = CookieInformation.getConsentGivenFor("cookie_cat_marketing");
                load_pmTPTrack();
            }
        });
    }
    // OneTrust
    // Get consent
    if (typeof OneTrust !== "undefined" && ((_OnetrustActiveGroups = OnetrustActiveGroups) === null || _OnetrustActiveGroups === void 0 ? void 0 : _OnetrustActiveGroups.includes("2")) && ((_OnetrustActiveGroups1 = OnetrustActiveGroups) === null || _OnetrustActiveGroups1 === void 0 ? void 0 : _OnetrustActiveGroups1.includes("4"))) {
        window.cc_statistics = OnetrustActiveGroups.includes("2");
        window.cc_marketing = OnetrustActiveGroups.includes("4");
        load_pmTPTrack();
    } else {
        // Add event listener
        window.addEventListener("OneTrustGroupsUpdated", (event)=>{
            var _event_detail, _event_detail1;
            if ((event === null || event === void 0 ? void 0 : (_event_detail = event.detail) === null || _event_detail === void 0 ? void 0 : _event_detail.some((group)=>group.includes("4"))) && (event === null || event === void 0 ? void 0 : (_event_detail1 = event.detail) === null || _event_detail1 === void 0 ? void 0 : _event_detail1.some((group)=>group.includes("2")))) {
                window.cc_statistics = event.detail.some((group)=>group.includes("4"));
                window.cc_marketing = event.detail.some((group)=>group.includes("2"));
                load_pmTPTrack();
            }
        });
    }
    // CookieYes
    // Get consent
    if (typeof getCkyConsent !== "undefined" && ((_getCkyConsent = getCkyConsent()) === null || _getCkyConsent === void 0 ? void 0 : (_getCkyConsent_categories = _getCkyConsent.categories) === null || _getCkyConsent_categories === void 0 ? void 0 : _getCkyConsent_categories.analytics) && ((_getCkyConsent1 = getCkyConsent()) === null || _getCkyConsent1 === void 0 ? void 0 : (_getCkyConsent_categories1 = _getCkyConsent1.categories) === null || _getCkyConsent_categories1 === void 0 ? void 0 : _getCkyConsent_categories1.advertisement)) {
        window.cc_statistics = getCkyConsent().categories.analytics;
        window.cc_marketing = getCkyConsent().categories.advertisement;
        load_pmTPTrack();
    } else {
        // Add event listener
        document.addEventListener("cookieyes_consent_update", function(eventData) {
            var _eventData_detail_accepted, _eventData_detail, _eventData_detail_accepted1, _eventData_detail1;
            if ((eventData === null || eventData === void 0 ? void 0 : (_eventData_detail = eventData.detail) === null || _eventData_detail === void 0 ? void 0 : (_eventData_detail_accepted = _eventData_detail.accepted) === null || _eventData_detail_accepted === void 0 ? void 0 : _eventData_detail_accepted.includes("analytics")) && (eventData === null || eventData === void 0 ? void 0 : (_eventData_detail1 = eventData.detail) === null || _eventData_detail1 === void 0 ? void 0 : (_eventData_detail_accepted1 = _eventData_detail1.accepted) === null || _eventData_detail_accepted1 === void 0 ? void 0 : _eventData_detail_accepted1.includes("advertisement"))) {
                window.cc_statistics = eventData.detail.accepted.includes("analytics");
                window.cc_marketing = eventData.detail.accepted.includes("advertisement");
                load_pmTPTrack();
            }
        });
    }
    // CookieFirst
    // Get consent
    if (typeof CookieFirst !== "undefined" && ((_CookieFirst = CookieFirst) === null || _CookieFirst === void 0 ? void 0 : (_CookieFirst_consent = _CookieFirst.consent) === null || _CookieFirst_consent === void 0 ? void 0 : _CookieFirst_consent.performance) && ((_CookieFirst1 = CookieFirst) === null || _CookieFirst1 === void 0 ? void 0 : (_CookieFirst_consent1 = _CookieFirst1.consent) === null || _CookieFirst_consent1 === void 0 ? void 0 : _CookieFirst_consent1.advertising)) {
        window.cc_statistics = CookieFirst.consent.performance;
        window.cc_marketing = CookieFirst.consent.advertising;
        load_pmTPTrack();
    } else {
        // Add event listener
        window.addEventListener("cf_consent", function(event) {
            var _event_detail, _event_detail1;
            if ((event === null || event === void 0 ? void 0 : (_event_detail = event.detail) === null || _event_detail === void 0 ? void 0 : _event_detail.performance) && (event === null || event === void 0 ? void 0 : (_event_detail1 = event.detail) === null || _event_detail1 === void 0 ? void 0 : _event_detail1.advertising)) {
                window.cc_statistics = event.detail.performance;
                window.cc_marketing = event.detail.advertising;
                load_pmTPTrack();
            }
        });
        // Add event listener
        window.addEventListener("cf_consent_loaded", function(event) {
            var _event_detail, _event_detail1;
            if ((event === null || event === void 0 ? void 0 : (_event_detail = event.detail) === null || _event_detail === void 0 ? void 0 : _event_detail.performance) && (event === null || event === void 0 ? void 0 : (_event_detail1 = event.detail) === null || _event_detail1 === void 0 ? void 0 : _event_detail1.advertising)) {
                window.cc_statistics = event.detail.performance;
                window.cc_marketing = event.detail.advertising;
                load_pmTPTrack();
            }
        });
    }
    // CookieScript
    // Get consent
    if (typeof CookieScript !== "undefined" && ((_CookieScript = CookieScript) === null || _CookieScript === void 0 ? void 0 : (_CookieScript_instance = _CookieScript.instance) === null || _CookieScript_instance === void 0 ? void 0 : (_CookieScript_instance_currentState = _CookieScript_instance.currentState()) === null || _CookieScript_instance_currentState === void 0 ? void 0 : (_CookieScript_instance_currentState_categories = _CookieScript_instance_currentState.categories) === null || _CookieScript_instance_currentState_categories === void 0 ? void 0 : _CookieScript_instance_currentState_categories.includes("performance")) && ((_CookieScript1 = CookieScript) === null || _CookieScript1 === void 0 ? void 0 : (_CookieScript_instance1 = _CookieScript1.instance) === null || _CookieScript_instance1 === void 0 ? void 0 : (_CookieScript_instance_currentState1 = _CookieScript_instance1.currentState()) === null || _CookieScript_instance_currentState1 === void 0 ? void 0 : (_CookieScript_instance_currentState_categories1 = _CookieScript_instance_currentState1.categories) === null || _CookieScript_instance_currentState_categories1 === void 0 ? void 0 : _CookieScript_instance_currentState_categories1.includes("targeting"))) {
        window.cc_statistics = CookieScript.instance.currentState().categories.includes("performance");
        window.cc_marketing = CookieScript.instance.currentState().categories.includes("targeting");
        load_pmTPTrack();
    } else {
        // Add event listener
        window.addEventListener("CookieScriptCategory-strict", function() {
            var _CookieScript_instance_currentState_categories, _CookieScript_instance_currentState, _CookieScript_instance, _CookieScript, _CookieScript_instance_currentState_categories1, _CookieScript_instance_currentState1, _CookieScript_instance1, _CookieScript1;
            if (((_CookieScript = CookieScript) === null || _CookieScript === void 0 ? void 0 : (_CookieScript_instance = _CookieScript.instance) === null || _CookieScript_instance === void 0 ? void 0 : (_CookieScript_instance_currentState = _CookieScript_instance.currentState()) === null || _CookieScript_instance_currentState === void 0 ? void 0 : (_CookieScript_instance_currentState_categories = _CookieScript_instance_currentState.categories) === null || _CookieScript_instance_currentState_categories === void 0 ? void 0 : _CookieScript_instance_currentState_categories.includes("performance")) && ((_CookieScript1 = CookieScript) === null || _CookieScript1 === void 0 ? void 0 : (_CookieScript_instance1 = _CookieScript1.instance) === null || _CookieScript_instance1 === void 0 ? void 0 : (_CookieScript_instance_currentState1 = _CookieScript_instance1.currentState()) === null || _CookieScript_instance_currentState1 === void 0 ? void 0 : (_CookieScript_instance_currentState_categories1 = _CookieScript_instance_currentState1.categories) === null || _CookieScript_instance_currentState_categories1 === void 0 ? void 0 : _CookieScript_instance_currentState_categories1.includes("targeting"))) {
                window.cc_statistics = CookieScript.instance.currentState().categories.includes("performance");
                window.cc_marketing = CookieScript.instance.currentState().categories.includes("targeting");
                load_pmTPTrack();
            }
        });
    }
    // Google Consent Mode
    // Get consent
    if (typeof window.google_tag_data !== "undefined" && ((_window_google_tag_data = window.google_tag_data) === null || _window_google_tag_data === void 0 ? void 0 : _window_google_tag_data.ics) && ((_window_google_tag_data1 = window.google_tag_data) === null || _window_google_tag_data1 === void 0 ? void 0 : (_window_google_tag_data_ics = _window_google_tag_data1.ics) === null || _window_google_tag_data_ics === void 0 ? void 0 : (_window_google_tag_data_ics_entries = _window_google_tag_data_ics.entries) === null || _window_google_tag_data_ics_entries === void 0 ? void 0 : (_window_google_tag_data_ics_entries_ad_storage = _window_google_tag_data_ics_entries.ad_storage) === null || _window_google_tag_data_ics_entries_ad_storage === void 0 ? void 0 : _window_google_tag_data_ics_entries_ad_storage.update) && ((_window_google_tag_data2 = window.google_tag_data) === null || _window_google_tag_data2 === void 0 ? void 0 : (_window_google_tag_data_ics1 = _window_google_tag_data2.ics) === null || _window_google_tag_data_ics1 === void 0 ? void 0 : (_window_google_tag_data_ics_entries1 = _window_google_tag_data_ics1.entries) === null || _window_google_tag_data_ics_entries1 === void 0 ? void 0 : (_window_google_tag_data_ics_entries_analytics_storage = _window_google_tag_data_ics_entries1.analytics_storage) === null || _window_google_tag_data_ics_entries_analytics_storage === void 0 ? void 0 : _window_google_tag_data_ics_entries_analytics_storage.update)) {
        window.cc_marketing = window.google_tag_data.ics.entries.ad_storage.update;
        window.cc_statistics = window.google_tag_data.ics.entries.analytics_storage.update;
        load_pmTPTrack();
    } else {
        var // Add event listener
        _window_google_tag_data_ics2, _window_google_tag_data3;
        (_window_google_tag_data3 = window.google_tag_data) === null || _window_google_tag_data3 === void 0 ? void 0 : (_window_google_tag_data_ics2 = _window_google_tag_data3.ics) === null || _window_google_tag_data_ics2 === void 0 ? void 0 : _window_google_tag_data_ics2.addListener([
            "ad_storage",
            "analytics_storage"
        ], function(event) {
            var _window_google_tag_data_ics_entries_ad_storage, _window_google_tag_data_ics_entries, _window_google_tag_data_ics, _window_google_tag_data, _window_google_tag_data_ics_entries_analytics_storage, _window_google_tag_data_ics_entries1, _window_google_tag_data_ics1, _window_google_tag_data1;
            if (((_window_google_tag_data = window.google_tag_data) === null || _window_google_tag_data === void 0 ? void 0 : (_window_google_tag_data_ics = _window_google_tag_data.ics) === null || _window_google_tag_data_ics === void 0 ? void 0 : (_window_google_tag_data_ics_entries = _window_google_tag_data_ics.entries) === null || _window_google_tag_data_ics_entries === void 0 ? void 0 : (_window_google_tag_data_ics_entries_ad_storage = _window_google_tag_data_ics_entries.ad_storage) === null || _window_google_tag_data_ics_entries_ad_storage === void 0 ? void 0 : _window_google_tag_data_ics_entries_ad_storage.update) && ((_window_google_tag_data1 = window.google_tag_data) === null || _window_google_tag_data1 === void 0 ? void 0 : (_window_google_tag_data_ics1 = _window_google_tag_data1.ics) === null || _window_google_tag_data_ics1 === void 0 ? void 0 : (_window_google_tag_data_ics_entries1 = _window_google_tag_data_ics1.entries) === null || _window_google_tag_data_ics_entries1 === void 0 ? void 0 : (_window_google_tag_data_ics_entries_analytics_storage = _window_google_tag_data_ics_entries1.analytics_storage) === null || _window_google_tag_data_ics_entries_analytics_storage === void 0 ? void 0 : _window_google_tag_data_ics_entries_analytics_storage.update)) {
                window.cc_marketing = window.google_tag_data.ics.entries.ad_storage.update;
                window.cc_statistics = window.google_tag_data.ics.entries.analytics_storage.update;
                load_pmTPTrack();
            }
        });
    }
});
// Necessary for the webpack hot module reloading server
if (false) {}

})();

/******/ })()
;
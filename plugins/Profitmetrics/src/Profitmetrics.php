<?php declare(strict_types=1);
/*
 * (c) ProfitMetrics.io <<EMAIL>>
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace Profitmetrics\Profitmetrics;

use Profitmetrics\Profitmetrics\Service\PluginInstallationService;
use Shopware\Core\Framework\Plugin;
use Shopware\Core\Framework\Plugin\Context\InstallContext;
use Shopware\Core\Framework\Plugin\Context\UninstallContext;
use Shopware\Core\Framework\Plugin\Context\UpdateContext;
use Symfony\Component\Config\FileLocator;
use Symfony\Component\DependencyInjection\ContainerBuilder;
use Symfony\Component\DependencyInjection\Loader\YamlFileLoader;

class Profitmetrics extends Plugin
{
    /**
     * Handle plugin installation.
     *
     * @throws \Exception
     */
    public function install(InstallContext $installContext): void
    {
        parent::install($installContext);

        $installService = $this->getPluginInstaller();
        $installService->installCustomFields($installContext->getContext());
        $installService->installScheduledTasks($installContext->getContext());
        $installService->updateConfig($installContext->getContext());
    }

    /**
     * Handle plugin updates.
     *
     * @throws \Exception
     */
    public function update(UpdateContext $updateContext): void
    {
        parent::update($updateContext);

        $installService = $this->getPluginInstaller();
        $installService->installCustomFields($updateContext->getContext());
        $installService->installScheduledTasks($updateContext->getContext());
        $installService->updateConfig($updateContext->getContext());
    }

    /**
     * Handle plugin uninstallation.
     *
     * @throws \Exception
     */
    public function uninstall(UninstallContext $uninstallContext): void
    {
        parent::uninstall($uninstallContext);

        if (!$uninstallContext->keepUserData()) {
            $installService = $this->getPluginInstaller();
            $installService->uninstallCustomFields($uninstallContext->getContext());
            $installService->uninstallScheduledTasks($uninstallContext->getContext());
        }
    }

    /**
     * Override the build method to load custom service configurations.
     *
     * @throws \Exception
     */
    public function build(ContainerBuilder $container): void
    {
        parent::build($container);
        $loader = new YamlFileLoader($container, new FileLocator(__DIR__ . '/Resources/config'));
        $loader->load('services.yaml');
    }

    /**
     * Retrieve the PluginInstallationService from the container.
     */
    private function getPluginInstaller(): PluginInstallationService
    {
        return new PluginInstallationService($this->container);
    }
}

<?php declare(strict_types=1);
/*
 * (c) ProfitMetrics.io <<EMAIL>>
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace Profitmetrics\Profitmetrics\Storefront\Subscriber;

use Doctrine\DBAL\Connection;
use Doctrine\DBAL\Exception;
use Monolog\Level;
use Profitmetrics\Profitmetrics\Core\Profitmetrics\VisitorDefinition;
use Profitmetrics\Profitmetrics\Defaults\ConfigDefaults;
use Profitmetrics\Profitmetrics\Defaults\CustomFieldDefaults as CF;
use Profitmetrics\Profitmetrics\Service\ErrorHandler;
use Shopware\Core\Checkout\Cart\Event\CheckoutOrderPlacedEvent;
use Shopware\Core\Checkout\Cart\Order\CartConvertedEvent;
use Shopware\Core\Checkout\Order\OrderCollection;
use Shopware\Core\Checkout\Order\OrderEntity;
use Shopware\Core\Framework\Context;
use Shopware\Core\Framework\DataAbstractionLayer\EntityRepository;
use Shopware\Core\Framework\Uuid\Uuid;
use Shopware\Core\System\SystemConfig\SystemConfigService;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Symfony\Component\Messenger\Exception\ExceptionInterface;

class OrderConversionSubscriber implements EventSubscriberInterface
{
    private const ORDER_TOTAL_VALUE = 'ORDER_TOTAL_VALUE';
    private const ORDER_CURRENCY = 'ORDER_CURRENCY';
    private const ORDER_ID = 'ORDER_ID';

    /**
     * @param EntityRepository<OrderCollection> $orderRepository
     */
    public function __construct(
        private readonly SystemConfigService $systemConfigService,
        private readonly Connection $connection,
        private readonly EntityRepository $orderRepository,
        private readonly ErrorHandler $errorHandler,
    ) {
    }

    /**
     * Returns subscribed events for order processing
     */
    public static function getSubscribedEvents(): array
    {
        return [
            CartConvertedEvent::class => 'cartConverted',
            CheckoutOrderPlacedEvent::class => 'onOrderPlacedProfitmetrics',
        ];
    }

    /**
     * Handles cart conversion by linking visitor data to the order
     */
    public function cartConverted(CartConvertedEvent $event): void
    {
        $context = $event->getSalesChannelContext();
        $salesChannelId = $context->getSalesChannelId();

        $active = $this->systemConfigService->get(ConfigDefaults::CONFIG_ACTIVE, $salesChannelId);

        if (!$active) {
            return;
        }

        try {
            $visitorId = $this->connection->createQueryBuilder()
                ->from(VisitorDefinition::ENTITY_NAME)
                ->select('id')
                ->where('token = :token')
                ->andWhere('order_id IS NULL')
                ->setParameter('token', $context->getToken())
                ->executeQuery()->fetchOne();
        } catch (Exception $e) {
            $this->errorHandler->log('profitmetrics.order-converted.visitor-select.error', [
                'msg' => $e->getMessage(),
                'salesChannelId' => $salesChannelId,
                'token' => $context->getToken(),
            ], Level::Error->value)->addToLogEntry($event->getContext());

            return;
        }

        if (!$visitorId) {
            return;
        }

        $visitorId = Uuid::fromBytesToHex($visitorId);
        $converted = $event->getConvertedCart();

        if (!isset($converted['customFields'])) {
            $converted['customFields'] = [];
        }
        $converted['customFields'][CF::PM_ORDER_VISITOR_ID] = $visitorId;

        $event->setConvertedCart($converted);
    }

    /**
     * Processes newly placed orders for Profitmetrics integration
     *
     * @throws Exception
     */
    public function onOrderPlacedProfitmetrics(CheckoutOrderPlacedEvent $event): void
    {
        $order = $event->getOrder();
        $salesChannelId = $event->getSalesChannelId();

        $this->updateGoogleAdsScript($order, $salesChannelId, $event->getContext());

        if (!$this->isPluginActive($salesChannelId)) {
            return;
        }

        $this->updateVisitorOrderData($order, $event);
    }

    /**
     * Checks if Profitmetrics plugin is active for given sales channel
     */
    private function isPluginActive(string $salesChannelId): bool
    {
        return (bool) $this->systemConfigService->get(ConfigDefaults::CONFIG_ACTIVE, $salesChannelId);
    }

    /**
     * Updates order with processed Google Ads conversion tracking script
     */
    private function updateGoogleAdsScript(
        OrderEntity $order,
        string $salesChannelId,
        Context $context
    ): void {
        $rawScript = $this->systemConfigService->get(ConfigDefaults::CONFIG_CONVERSION_BOOSTER_GOOGLE_ADS, $salesChannelId);
        $googleAdsScript = \is_string($rawScript) ? $rawScript : '';

        $placeholders = [
            self::ORDER_TOTAL_VALUE => $order->getAmountTotal(),
            self::ORDER_CURRENCY => $order->getCurrency()->getIsoCode(),
            self::ORDER_ID => $order->getOrderNumber(),
        ];

        foreach ($placeholders as $placeholder => $value) {
            $encoded = json_encode($value);
            if ($encoded === false) {
                continue;
            }
            if (\in_array($placeholder, [self::ORDER_TOTAL_VALUE, self::ORDER_CURRENCY, self::ORDER_ID], true)) {
                $googleAdsScript = str_replace($placeholder, $encoded, $googleAdsScript);
            }
        }

        $customFields = $order->getCustomFields() ?? [];
        $customFields[CF::PM_ORDER_GOOGLE_ADS_SCRIPT] = $googleAdsScript;

        $this->orderRepository->update(
            [['id' => $order->getId(), 'customFields' => $customFields]],
            $context
        );
    }

    /**
     * Links visitor tracking data with the placed order
     *
     * @throws Exception
     */
    private function updateVisitorOrderData(
        OrderEntity $order,
        CheckoutOrderPlacedEvent $event
    ): void {
        $visitorId = $order->getCustomFields()[CF::PM_ORDER_VISITOR_ID] ?? null;

        if (!$visitorId || !\is_string($visitorId) || !Uuid::isValid($visitorId)) {
            return;
        }

        $this->connection->createQueryBuilder()
            ->update(VisitorDefinition::ENTITY_NAME)
            ->set('order_id', ':orderId')
            ->set('order_version_id', ':versionId')
            ->where('id = :id')
            ->andWhere('order_id IS NULL')
            ->setParameter('id', Uuid::fromHexToBytes($visitorId))
            ->setParameter('orderId', Uuid::fromHexToBytes($event->getOrderId()))
            ->setParameter(
                'versionId',
                Uuid::fromHexToBytes(
                    $order->getVersionId() ?: $event->getContext()->getVersionId()
                )
            )
            ->executeStatement();
    }
}

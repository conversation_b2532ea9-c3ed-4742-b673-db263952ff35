#!/usr/bin/env bash
set -euo pipefail

# Check required environment variables
if [ -z "${WORKSPACE_FOLDER:-}" ]; then
    echo "WORKSPACE_FOLDER environment variable is not set"
    exit 1
fi

if [ -z "${PLUGIN_NAME:-}" ]; then
    echo "PLUGIN_NAME environment variable is not set"
    exit 1
fi

# Wait for MySQL to be ready
wait_for_mysql() {
    echo "Waiting for MySQL to be ready..."
    local max_attempts=30
    local attempt=1

    while [ $attempt -le $max_attempts ]; do
        if mysqladmin ping -h"127.0.0.1" -u"root" -p"root" --silent > /dev/null 2>&1; then
            return 0
        fi
        attempt=$((attempt + 1))
        sleep 2
    done

    echo "MySQL did not become ready in time"
    return 1
}

update_sales_channel_domain() {
    echo "Updating sales channel domain..."

    # Check for required environment variables
    if [ -z "${CODESPACE_NAME}" ] || [ -z "${GITHUB_CODESPACES_PORT_FORWARDING_DOMAIN}" ]; then
        echo "GitHub Codespaces environment variables not found"
        return 0
    fi

    # Construct the full domain
    HOSTNAME="${CODESPACE_NAME}-8000.${GITHUB_CODESPACES_PORT_FORWARDING_DOMAIN}"

    echo "Setting sales channel domain to: ${HOSTNAME}"

    # Update the sales channel domain
    cd /var/www/html
    if ! sudo -u www-data php bin/console sales-channel:update:domain "${HOSTNAME}" --previous-domain="localhost"; then
        echo " Failed to update sales channel domain, but continuing..."
    fi

    # Force HTTPS in the database
    echo "Updating protocol to HTTPS in database..."
    sudo mysql -h 127.0.0.1 -u root -proot shopware -e "
        UPDATE sales_channel_domain
        SET url = REPLACE(url, 'http://', 'https://')
        WHERE url LIKE 'http://${HOSTNAME}%';" 2>/dev/null

    return 0
}

# Remove the sample plugin
remove_sample_plugin() {
    echo "Removing DockwareSamplePlugin..."
    cd /var/www/html || return 0

    # Deactivate plugin, ignore errors
    if ! sudo -u www-data php bin/console plugin:deactivate DockwareSamplePlugin 2>/dev/null; then
        echo "Plugin deactivation skipped (plugin may not be active)"
    fi

    # Uninstall plugin, ignore errors
    if ! sudo -u www-data php bin/console plugin:uninstall DockwareSamplePlugin 2>/dev/null; then
        echo "Plugin uninstall skipped (plugin may not be installed)"
    fi

    # Remove plugin directory if it exists
    if [ -d "/var/www/html/custom/plugins/DockwareSamplePlugin" ]; then
        sudo rm -rf /var/www/html/custom/plugins/DockwareSamplePlugin
    fi

    return 0
}

# Setup plugin directory and symlink
setup_plugin_symlink() {
    local plugin_path="/var/www/html/custom/plugins/${PLUGIN_NAME}"

    # Ensure plugins directory exists
    sudo -u www-data mkdir -p /var/www/html/custom/plugins

    # Remove existing symlink or directory if it exists
    if [ -e "${plugin_path}" ]; then
        sudo rm -rf "${plugin_path}"
    fi

    # Verify workspace directory exists
    if [ ! -d "${WORKSPACE_FOLDER}" ]; then
        echo "Workspace directory ${WORKSPACE_FOLDER} does not exist"
        return 1
    fi

    # Create symlink
    echo "Creating symlink to plugin..."
    sudo -u www-data ln -s "${WORKSPACE_FOLDER}" "${plugin_path}"

    # Verify symlink was created successfully
    if [ ! -L "${plugin_path}" ]; then
        echo "Failed to create symlink"
        return 1
    fi

    return 0
}

setup_apache_proxy_config() {
    echo "Setting up Apache proxy configuration..."

    # Add header modification to both HTTP and HTTPS VirtualHost blocks
    sudo sed -i '/<VirtualHost \*:80>/a\    RequestHeader unset X-Forwarded-Host\n    RequestHeader set X-Forwarded-Host "localhost:8000"\n    SetEnv HTTPS "on"\n    SetEnv SERVER_PORT "443"' /etc/apache2/sites-available/000-default.conf
    sudo sed -i '/<VirtualHost \*:443>/a\    RequestHeader unset X-Forwarded-Host\n    RequestHeader set X-Forwarded-Host "localhost:8000"\n    SetEnv HTTPS "on"\n    SetEnv SERVER_PORT "443"' /etc/apache2/sites-available/000-default.conf

    # Ensure required modules are enabled
    sudo a2enmod headers env setenvif || true

    # Reload Apache to apply changes
    sudo service apache2 reload || {
        echo "Apache reload failed, but continuing..."
    }

    return 0
}

# Configure Symfony trusted proxies
setup_trusted_proxies() {
    echo "Setting up trusted proxies configuration..."

    # Ensure the config directory exists
    sudo -u www-data mkdir -p /var/www/html/config/packages

    # Create or update the framework.yaml
    sudo -u www-data tee /var/www/html/config/packages/framework.yaml > /dev/null << 'EOF'
framework:
    trusted_proxies: '**********/16,**********/16'
    trusted_headers:
        - 'x-forwarded-host'
        - 'x-forwarded-proto'
        - 'x-forwarded-for'
EOF

    # Clear cache to apply changes
    cd /var/www/html
    sudo -u www-data php bin/console cache:clear
}

main() {
    echo "Running post-create scripts..."

    wait_for_mysql || {
        return 1
    }
    remove_sample_plugin || {
        return 1
    }
    setup_plugin_symlink || {
        return 1
    }
    update_sales_channel_domain || {
        return 1
    }
    setup_apache_proxy_config || {
        return 1
    }
    setup_trusted_proxies || {
        return 1
    }

    return 0
}

main
exit $?

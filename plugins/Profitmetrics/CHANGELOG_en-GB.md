# 3.2.0
* Add support for Shopware ~6.6
* Multiple fixes and performance improvements

# 3.0.4
* Added compatibility in migration

# 3.0.3
* Added field conversion booster for Google Ads

# 3.0.2
* Implemented functionality to allow the export of orders with negative total values. (Exported as 0 if negative)

# 3.0.1
* Implemented improvements in order synchronization while in headless mode for Shopware 6.5

# 3.0.0
* Shopware 6.5 compatibility

# 2.1.21
* Fixed issue where 'gclid' weren't set when using varnish

# 2.1.20
* Added ga4_session
* Fixed issue where 'gclid' got delete on SalesChannelContextTokenChangeEvent

# 2.1.19
* Changed 'executeQuery' to 'execute' to allow compatibility with Shopware <= 6.4.17

# 2.1.18
* Changed visitor data from being Session based to be context token based.

# 2.1.17
* Added log channel constant accidentally removed

# 2.1.16
* Resolved issue causing tracking failures on certain subdomains with specific path configurations

# 2.1.15
* Fixed issue where visitor data weren't tracked on cached pages

# 2.1.14
* Added support for cookie consent managers: Cookiebot & CookieInformation, which listens to marketing consent

# 2.1.13
* Orders with total amount of 0 are now excluded from order export
* Remove the use of @RouteScope which is deprecated

# 2.1.12
* Added order export max attempts

# 2.1.11
* Fixed issue where orders weren't exported if one order failed to convert data (Also contains logging possibility)
* Fixed issue where order export would break if tax was missing

# 2.1.10
* Added extra validation on visitor data

# 2.1.9
* Fixed issue where cookie weren't set when accepting all cookies (via Cookie bar)
* Fixed issue where decode cookie would just create new cookie instead of reusing the old cookie

# 2.1.8
* Change max length of cua in visitor data varchar field from 255 to 500 characters

# 2.1.7
* Added OrderId to logging if the order export failed

# 2.1.6
* Fixed issue where deleted products would stop the Scheduled task

# 2.1.5
* Fixed issue where order export date weren't updated

# 2.1.4
* Fixed issue, where API test would not give an error, when public id was empty
* Fixed issue where "Send orders" button wouldn't give error message on bad credentials

# 2.1.3
* Fixed issue where the cookie weren't decoded and update values failed

# 2.1.2
* Fixed translation of buttons on config page

# 2.1.1
* Made code more PHP 7.4 compliant by using typed properties

# 2.1.0
* Added scheduled task and command to clean up visitor data older than 90 days
* Added translations to plugin config elements
* Stopped saving tracking data on web crawlers

# 2.0.1
* Fixed error where missing route scope could break the site 

# 2.0.0
* Removed deprecated custom product export

# 1.2.0
* Added pmTPTrack cookie to cookie consent manager
* pmTPTrack cookie is now only set if customer has given consent
* Added API test button

# 1.1.6
* PurchasePrice must always be net
* Set price as gross

# 1.1.5
* Updated order criteria to support selection of state machine(s)

# 1.1.4
* Fixed config bug in administration script

# 1.1.3
* Loop over elements to get the current config

# 1.1.2
* Corrected weight in order export

# 1.1.1
* Update product comparison template to verify a cover image is selected

# 1.1.0
* Deprecated custom product export, where it's now using Product Comparison (Sales Channel)
* Code cleanup
* Added shipping address, zip, country & weight
* Made it possible to export sales channel specific orders by command/order-export 

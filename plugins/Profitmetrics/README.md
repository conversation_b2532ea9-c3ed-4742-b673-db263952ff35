# ProfitMetrics for Shopware 6

Track and analyze your Profit On Ad Spend (POAS) by integrating your Shopware 6 shop directly with ProfitMetrics. This server-side integration ensures 100% accurate data foundation for your marketing analytics.

## Requirements

- Shopware 6.5.0 or higher
- PHP 8.1 or higher
- Valid ProfitMetrics account

## Installation

### Via Shopware Store

1. Purchase and download the plugin from Shopware Store
2. Upload and install the plugin in your Shopware Administration
3. Activate the plugin

## Configuration

Navigate to Settings > System > Plugins > ProfitMetrics in your Shopware Administration.

### General Settings

- **Active**: Enable/disable the plugin
- **Public ID**: Your ProfitMetrics Public ID (required)
- **Order Status**: Choose which order states to send to ProfitMetrics
- **Order Lookback Window**: Maximum age of orders to export (in days, only works with headless mode)
- **Block Tracking Before Consent**: Enable to prevent tracking script initialization until cookie consent is granted
  - Compatible with: CookieBot, CookieInformation, OneTrust, CookieYes, CookieFirst, CookieScript, and Google Consent Mode

### Tracking Settings

- **Google Ads Conversion ID**: Your Google Ads conversion tracking ID (format: AW-XXXXXXXXXX)
- **Conversion Booster for Google Ads**: Add custom Google Ads conversion tracking script
- **Google Analytics 4 Script**: Add your GA4 tracking code

### Advanced Settings

- **Headless Mode**: Enable for custom tracking implementation
  - Contact <EMAIL> to get your tracking script
  - Required for orders older than the configured lookback window

## Features

- Automated order synchronization every 10 minutes
- Multi-language support (English, German, Danish)
- Cookie consent integration
- Custom order status filtering
- Visitor tracking with automatic cleanup
- Marketing attribution tracking
- Server-side order export

## CLI Commands

### Export Orders

```bash
bin/console profitmetrics:order:export [options]

Options:
  -s, --salesChannelId=        Specify sales channel ID for order export
  -d, --dry-run               Skip export and only display orders to be exported
  --visual                    Display table of orders to be exported
  -l, --limit=                Limit number of orders to process
```

### Cleanup Visitor Data

```bash
bin/console profitmetrics:visitor:cleanup [options]

Options:
  -t, --entry-lifetime-seconds=  Maximum retention period for visitor entries in seconds (default: 90 days)
```

## Support

- Website: https://profitmetrics.io
- Contact: https://profitmetrics.io/contact
- Email: <EMAIL>

## License

Proprietary - © ProfitMetrics.io

---

*Note: This plugin is maintained by ProfitMetrics.io. For detailed integration guides and troubleshooting, please contact our support team.*

@import "bootstrap-icons";

// @import url(//fonts.googleapis.com/css2?family=Montserrat:wght@200;300;400;500;600;700&family=Red+Hat+Text:wght@300;400;500;600;700&display=swap);

// @import url(//fonts.googleapis.com/css2?family=Abril+Fatface&display=swap);
/* latin-ext */
@font-face {
	font-family: 'Abril Fatface';
	font-style: normal;
	font-weight: 400;
	font-display: swap;
	src: url('/bundles/festtematheme/fonts/abrilfatface-latin-ext.woff2') format('woff2');
	unicode-range: U+0100-024F, U+0259, U+1E00-1EFF, U+2020, U+20A0-20AB, U+20AD-20CF, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
	font-family: 'Abril Fatface';
	font-style: normal;
	font-weight: 400;
	font-display: swap;
	src: url('/bundles/festtematheme/fonts/abralfatface-latin.woff2') format('woff2');
	unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}
/* latin-ext */
@font-face {
	font-family: 'Montserrat';
	font-style: normal;
	font-weight: 400;
	font-display: swap;
	src: url('/bundles/festtematheme/fonts/JTUSjIg1_i6t8kCHKm459WdhyyTh89ZNpQ.woff2') format('woff2');
	unicode-range: U+0100-02AF, U+0304, U+0308, U+0329, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20CF, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
	font-family: 'Montserrat';
	font-style: normal;
	font-weight: 400;
	font-display: swap;
	src: url('/bundles/festtematheme/fonts/JTUSjIg1_i6t8kCHKm459WlhyyTh89Y.woff2') format('woff2');
	unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}
/* latin-ext */
@font-face {
	font-family: 'Montserrat';
	font-style: normal;
	font-weight: 500;
	font-display: swap;
	src: url('/bundles/festtematheme/fonts/JTUSjIg1_i6t8kCHKm459WdhyyTh89ZNpQ.woff2') format('woff2');
	unicode-range: U+0100-02AF, U+0304, U+0308, U+0329, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20CF, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
	font-family: 'Montserrat';
	font-style: normal;
	font-weight: 500;
	font-display: swap;
	src: url('/bundles/festtematheme/fonts/JTUSjIg1_i6t8kCHKm459WlhyyTh89Y.woff2') format('woff2');
	unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}
/* latin-ext */
@font-face {
	font-family: 'Montserrat';
	font-style: normal;
	font-weight: 600;
	font-display: swap;
	src: url('/bundles/festtematheme/fonts/JTUSjIg1_i6t8kCHKm459WdhyyTh89ZNpQ.woff2') format('woff2');
	unicode-range: U+0100-02AF, U+0304, U+0308, U+0329, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20CF, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
	font-family: 'Montserrat';
	font-style: normal;
	font-weight: 600;
	font-display: swap;
	src: url('/bundles/festtematheme/fonts/JTUSjIg1_i6t8kCHKm459WlhyyTh89Y.woff2') format('woff2');
	unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}
/* latin-ext */
@font-face {
	font-family: 'Montserrat';
	font-style: normal;
	font-weight: 700;
	font-display: swap;
	src: url('/bundles/festtematheme/fonts/JTUSjIg1_i6t8kCHKm459WdhyyTh89ZNpQ.woff2') format('woff2');
	unicode-range: U+0100-02AF, U+0304, U+0308, U+0329, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20CF, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
	font-family: 'Montserrat';
	font-style: normal;
	font-weight: 700;
	font-display: swap;
	src: url('/bundles/festtematheme/fonts/JTUSjIg1_i6t8kCHKm459WlhyyTh89Y.woff2') format('woff2');
	unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

body{
	font-family: 'Montserrat', sans-serif;  
	color:#000;
}
/* header class */
.custom-menu-bar .navbar-toggler {
  color: rgba(0,0,0,0);
  border-color: rgba(0,0,0,0);
  z-index: 100;
}
.custom-menu-bar .navbar-toggler:focus {  
  box-shadow: 0 0 0 0;
}
.topbar{
	background:$sw-color-brand-primary;
	padding:6px 0;	
}
.topbar a{
	font-weight:500;
}
.topbar .mail a{
	color:#fff;
}
.topbar .phone{
	margin:0 0 0 35px;
    font-weight:500;
	color:#fff;
}
.topbar .bi-envelope::before, 
.topbar .bi-telephone-plus::before {
    margin-right: 4px;
}
.topbar li{	
	margin-left:20px;
}
.topbar .list-inline .list-inline-item a .bi{
	color:#fff;
}
.topbar .list-inline{
    margin-bottom: 0;
}
.brand-logo-part .btn-outline-secondary{
	background:$sw-color-brand-primary;
	border-color:$sw-color-brand-primary;	
}
.desktop-view .header-search-col, 
.header-search-container{
    padding-left: 0;
    padding-right: 0;
}
.mobile-view .fes-account{
    display: inline-flex;
}
.mobile-view .header-logo-main{
    display: inline-block;
}
.brand-logo-part .bi{
    color: #000;
    font-size: 20px;
}
.header-actions-col .bi{
	color:#000;
}
.header-wishlist, 
.header-cart{
	max-width: unset !important;
}
.menu-part{
	margin:30px 0 0 0;
}
.menu-part li{
	padding:0 25px;
}
.menu-part li a{
	font-size:18px;
	color:#000;
	font-weight:500;
}
.navbar{
	padding: 0;
}
.navbar-light .navbar-toggler-icon{
	background:url('/bundles/festtematheme/images/menu-icon.png') no-repeat 0 0;
}
.festtema-banner {
    background: url('/bundles/festtematheme/images/dark-shadow-line.png')  #fff repeat-x 0 0;
    display: block;
    width: 100%;
    margin: 15px 0 0 0;
    padding: 15px 0;
}
.header-wishlist,
.header-cart{
	max-width:80px;
	position:relative;
	text-align:center;
}
.header-wishlist .header-wishlist-badge,
.header-cart .header-cart-badge{
	top:0;
	right:-5px;
}
/*header part end */
#algolia-search-input{
	width: 100%;
	box-shadow: 1px 1px 5px 1px rgba($sw-color-brand-primary, .3);
}
.header-search .aa-Form,
.header-search-form {
    width: 100%;
}
.header-search-col{
	padding-left: 0;
	padding-right: 0;
}
.nav-main{
	margin: 0 auto;
}
.festtema-call-to,
.festtema-call-to:hover{
	color: #FFFFFF;
}
.no-padding{
	padding-left: 0;
	padding-right: 0;
}
// Navigation
.navigation-offcanvas-overlay.has-transition {
    -webkit-transition: none !important;
    transition: none !important;
}
.navigation-offcanvas-list-item {
    border-top: none;
	font-size: 1rem;
}
.navigation-offcanvas-list .festtema-main-category{
	font-weight: 600;
}
.navigation-offcanvas-list .navigation-offcanvas-list-item a:hover{
	text-decoration: underline;
	color: $sw-color-brand-primary;
}
.navigation-offcanvas-link.is-back-link{
    color: $sw-color-brand-primary;
}
.festtema-nav-link-block{
	width: 100%;
}
.festtema-nav-line {
    border-top: 1px solid #ddd;
    padding-top: 15px;
    margin-top: 15px;
}
.navigation-offcanvas .close{
	opacity: 1;
}
.navigation-offcanvas-link{
	padding: 5px 1rem 0;
}
.navigation-offcanvas-overlay {
	border-left: 1px solid rgb(221, 221, 221);
}
.navigation-offcanvas-link.is-current-category {
    color: #000000;
    font-weight: 600;
}
.navigation-offcanvas-link.is-back-link{
	border-top: none;
}
.ft-nav-close{
	padding: 3px 1rem !important;
    color: #000000 !important;
	box-shadow: 1px 1px 5px 1px #eaeef199;
	line-height: 20px;
}
.festtema-main-category:first-child{
	padding-top: 20px !important;
}
.navigation-offcanvas-list-item:last-child{
	padding-bottom: 15px;
}
.header-search-form{
	display: inline-flex;
	width: 100%;
}
/* footer */
.copyright {
    background: $sw-color-brand-primary;
    padding: 12px 0 10px 0;
    font-weight: 600;
    font-size: 16px;
    color: #fff;
    text-align: center;
}
.footer {
    background: $sw-color-brand-secondary;
    padding: 40px 0 10px 0;
    margin: 20px 0 0 0;
}
.footer-main {
    border-top: none;
}
.footer-column-headline {
    font-weight: 400;
    font-size: 20px;
    color: $sw-color-brand-primary;
    font-family: 'Abril Fatface', cursive;
}
.footer-column-content, .footer-link, 
.footer li, .footer li a{
    font-size: 16px;
    color: #333;
    font-weight: 500;
}
.site-address{
    list-style-type: none;
}
.footer .site-address li {
    line-height: unset;
    margin-bottom: 15px;
}
.footer li {
    line-height: 36px;
}
.footer .site-address li.bi-geo-alt::before, .footer .site-address li.bi-envelope::before, .footer .site-address li.bi-telephone-plus::before {
    position: absolute;
    margin: 4px 0px 0 -25px;
    font-size: 1rem;
}
.footer .btn-outline-secondary {
    background: $sw-color-brand-primary;
    color: #6c757d;
    border-color: $sw-color-brand-primary;
    color: #fff;
    font-weight: 500;
}
.footer-column {
    border-bottom: 0;
}
.footer-link-item {
    padding: 0;
}
.footer .btn-outline-secondary {
    background: $sw-color-brand-primary;
    color: #6c757d;
    border-color: $sw-color-brand-primary;
    border-top-right-radius: 4px;
    border-bottom-right-radius: 4px;
    border-bottom-left-radius: 0;
    border-top-left-radius: 0;
    color: #fff;
    font-weight: 500;
}
/* end footer*/
.banner {
    background: url('/bundles/festtematheme/images/banner-bg.png') repeat-x 0 0 !important;
    display: block;
    width: 100%;
    margin: 10px 0 0 0;
}
.is-act-home .festtema-banner {
    background: none;
    padding: 0;
    margin: 0;
}
.is-act-home .container-main {
    padding-top: 0 !important;
}
.balloon-img {
    background-size: cover;
}
.ballon-banner-block .col-12{
    padding-left: 0;
    padding-right: 0;
}
.every-need span {
    font-weight: 600;
    color: $sw-color-brand-primary;
}
.every-need h1 {
    font-weight: 400;
    font-family: 'Abril Fatface', cursive;
    color: #000;
    margin: 10px 0 0 0;
}
.every-need p {
    font-weight: 400;
    color: #333333;
    margin: 15px 0 20px 0;
}
.fes-cat-custom .col-12{
    padding-left: 0;
}
.e-market{
	padding:60px 0 10px 0;
}
.e-market h3{
	font-weight:400;
	font-family: 'Abril Fatface', cursive;
	font-size:30px;
	color:$sw-color-brand-primary;
	border-bottom:4px solid $sw-color-brand-primary;
	max-width:265px;
	padding:20px 0 10px 0;
}
.homepage-title{
	font-family: 'Abril Fatface', cursive;
	font-weight:400;
	color: $sw-color-brand-primary;
	padding: 15px 0 0 0;
	text-align:center;
}
.sub-title{	
	font-weight:500;
	color:#333;
	margin:0;
	padding-top: 8px;
	text-align:center;
}
.featured-box{
	position:relative;
}
.fea-images-one,.fea-images-two,.fea-images-three,
.fea-images-four, .fea-images-five,.fea-images-six{
	border-radius:10px;		
}
.about-store{
	margin:10px 0;
}
.title-two{
	font-size:24px;
	font-weight:400;
	color:#000;
	letter-spacing: 0.03em;
}
.about-store .title-two{	
	font-family: 'Abril Fatface', cursive;
	padding:15px 0 0 0;
}
.stroe-box{
	border-top:0px solid #e5e5e5;
	border-right:1px solid #e5e5e5;
	border-bottom:1px solid #e5e5e5;
	border-left:1px solid #e5e5e5;
	box-shadow:0px 6px 4px rgba(0, 0, 0, 0.25);
	border-top-right-radius:0px;
	border-top-left-radius:0px;
	border-bottom-right-radius:10px;
	border-bottom-left-radius:10px;	
}
.stroe-box p{	
	color:#000;
	font-weight:300;
	font-size:16px;
}
.categories-names{
	background: rgba(0, 0, 0, 0.8);
	opacity: 0.9;
	opacity: 90%;
	/* Global values */
	opacity: inherit;
	opacity: initial;
	opacity: revert;
	opacity: revert-layer;
	opacity: unset;
	text-align:center;	
	position: absolute;
	bottom: 0;	
	border-bottom-right-radius:10px;
	border-bottom-left-radius:10px;
	width: 100% !important;
}
.categories-names h3{
	color:#fff;	
	font-weight:600;	
	margin:0;
	padding:0;	
}
.fes-cat-img{
	border-radius: 10px;
}
.range-details{	
	text-align:center;
}
.range-details h3{
	font-weight:400;
	font-family: 'Abril Fatface', cursive;	
	color:#fff;
	letter-spacing: 0.03em;
}
.range-details p{
	font-weight:500;	
	margin:0 auto;
	color:#fff;
	line-height:26px;
	padding:15px 0 40px 0;
}
.trend-box-bg{
	background:#FFFFFF;
	box-shadow:0px 4px 10px rgba(0, 0, 0, 0.25);
	border-radius:10px;
}
.trend-box-bg .left-box,
.trend-box-bg .right-box{
	padding: 0;
}
.costume-box-bg{
	margin:20px 0 0 0;
}
.trend-count .right-box{
	text-align:right;
}
.trend-count .title-two{
	font-weight:400;
	font-size:24px;
	color:$sw-color-brand-primary;
	letter-spacing:0.03em;
	font-family: 'Abril Fatface', cursive;
}
.trend-count .trend-box-details p{	
	padding:10px 0 0px 0px;
}
.trend-count .trend-box-details .btn{
	font-size:18px;
}
.visit-store{
	background:#fff6f9;
	margin:5px 0 0 0;
	padding:0 0 5px 0;
}
.visit-box{
	background:#fff;
	border-radius:10px;
}
.visit-box h3{
	font-weight:400;
	font-family: 'Abril Fatface', cursive;	
	color:#000;
	letter-spacing: 0.03em;	
}
.visit-box p{
	font-weight:400;	
	color:#000;	
	padding:0;	
}
.back-bg{	
	border-radius:10px;
	background:#fff6f9;
	padding:15px 0px;
	margin:20px 0 0 0;
}
.we-back .title{
	margin-bottom:50px;
}
.we-back h3{
	font-weight:400;
	font-family: 'Abril Fatface', cursive;
	font-size:24px;
	color:#000;
	letter-spacing: 0.03em;	
	padding:0px 0 0 0;
}
.we-back p{
	font-size:18px;
	max-width:950px;
	padding:30px 0 0 0;
}
.we-back img{
	padding:20px 0 0 0;
}
.breadcrumb a{
	font-size: 1.05rem;
}
.breadcrumb a.is-active {
    font-weight: 600;
}
.category-image-gallery .grid {
	display: block;
	margin: 0 auto;
	width:100%;
	padding-left: 0;
	font-size: 0;	
}
.category-image-gallery .grid figure {
	display: inline-block;
	position: relative;
	overflow: hidden;
	text-align: center;
	box-shadow: 0px 4px 10px rgb(0 0 0 / 22%);
}
/* Common style */
.category-image-gallery .grid figure img {
	position: relative;
	display: block;
	opacity: 0.8;
	/*height: 240px;*/
}
.category-image-gallery .grid figure figcaption {
	color: #fff;
	font-size: 1.25em;
	-webkit-backface-visibility: hidden;
	backface-visibility: hidden;
}
.category-image-gallery .grid figcaption h2 {
	font-weight: 300;
	margin: 1em;
}
.category-image-gallery .grid figure figcaption::before,.category-image-gallery .grid figure figcaption::after {
	pointer-events: none;
}
.category-image-gallery .grid figure figcaption,.category-image-gallery .grid figure figcaption > a {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
}
.category-image-gallery .grid figure figcaption > a {
	z-index: 1000;
	text-indent: 200%;
	white-space: nowrap;
	font-size: 0;
	opacity: 0;
}
.category-image-gallery .grid figure h2 {
	
	font-weight: 300;
	margin: 1em;
}
.category-image-gallery .grid figure h2 span {
	font-weight: 800;
}
.category-image-gallery figure.effect-steve {
	z-index: auto;
	background: #fff;
	border-radius:10px;
}
.category-image-gallery figure.effect-steve:before {
	box-shadow: 0 3px 30px rgba(0, 0, 0, 0.8);
	opacity: 0;
}
.category-image-gallery figure.effect-steve figcaption {
	z-index: 1;
}
.category-image-gallery figure.effect-steve img {
	opacity: 1;
	-webkit-transition: -webkit-transform 1.0s;
	transition: transform 1.0s;
	-webkit-transform: perspective(1000px) translate3d(0, 0, 0);
	transform: perspective(1000px) translate3d(0, 0, 0);
	border-radius:10px;
}
.category-image-gallery figure.effect-steve h2{
	color: $sw-menulink-color;	
	background: rgba($sw-color-brand-primary, .8);
	opacity: 0.9;
	opacity: 90%;
	/* Global values */
	opacity: inherit;
	opacity: initial;
	opacity: revert;
	opacity: revert-layer;
	opacity: unset;
	position: absolute;
	padding:0 0.25em;
	width: 100%;
	bottom:0;
}
.category-image-gallery figure.effect-steve p {
	margin-top: 1em;
	padding: 0.5em;
	font-weight: 800;
	opacity: 0;
	-webkit-transition: opacity 1.0s, -webkit-transform 1.0s;
	transition: opacity 1.0s, transform 1.0s;
	
}
.category-image-gallery figure.effect-steve:hover:before {
	opacity: 1;
}
.category-image-gallery figure.effect-steve:hover img {
	-webkit-transform: perspective(1000px) translate3d(0, 0, 21px);
	transform: perspective(1000px) translate3d(0, 0, 21px);
}
.category-image-gallery figure.effect-steve:hover h2:before {
	opacity: 0;
}
.category-image-gallery .grid figure,.category-image-gallery figcaption,.category-image-gallery img {
	z-index: 0;
}
.category-image-gallery .grid figure figcaption h2 {
	font-weight:600;
	margin:0;	
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
    height: 35px;
    overflow: hidden;
    text-overflow: ellipsis;
}
.category-image-gallery .grid figure figcaption h2:hover {
    height: inherit;
    -webkit-line-clamp: unset;
}
.category-image-gallery .grid figure:hover h2{
	height: inherit;
}
.category-image-gallery figure h2:hover{
	background: rgba($sw-color-brand-primary, .8);
	opacity: 0.9;
	opacity: 90%;	
	opacity: inherit;
	opacity: initial;
	opacity: revert;
	opacity: revert-layer;
	opacity: unset;	
	transition: transform 1.0s;
	/*transform: translateY(-2.5em);*/
	cursor:pointer;
	
}
.category-image-gallery h1.slogan {
	text-align: center;
	background-color: #2d434e;
	font-weight: 100;
	margin: 0;
	padding: 20px;
} 
.bst-subcategories.with-images .subcategory-navigation-entry .category-image-wrapper{
	height: auto;
    width: 100%;
    margin: 0;
}
.pagination-nav{
    text-align: center;
    padding: 20px 0 0 0;
}
.pagination-nav ul{
    justify-content: center!important;
}
.pagination-nav ul li {
    display: inline;
    margin: 0 3px;
    list-style-type: none;
}
.pagination-nav li .page-link, .pagination-nav .active .page-link {
    border-radius: 50%;
    width: 32px;
    height: 32px;
    display: inline-block;
    font-weight: 500;
    line-height: 17px;
    border: 0;
}
.pagination-nav .active .page-link {
    background: $sw-color-brand-primary;
    color: #fff;
}
.pagination-nav li .page-link {
    background: #fff;
    color: $sw-color-brand-primary;
}
.pagination .page-item.disabled .page-link{
    background: none;
}
.page-link{
    padding: 0.5rem 0.5rem;
}
/**/
.box-fes-bg{
	background:#fff;
	border: 1px solid #CDCDCD;
	box-shadow: 0px 4px 6px rgba(0, 0, 0, 0.25);
	border-radius:10px;
	margin-top:15px;	
	padding:5px 0;
    min-height: 220px;
}
.box-fes-bg img{
	border-top-left-radius:10px;
	border-top-right-radius:10px;
	object-fit: scale-down;
	font-family: "object-fit: scale-down;";
	display:inline-block;
	max-width: 100%;
	text-align:center;	
}
.hovereffect {
    width: 100%;
    height: 100%;
    overflow: hidden;
    position: relative;
    text-align: center;
    cursor: default;
}
.hovereffect .overlay {
    width: 100%;
    position: absolute;
    overflow: hidden;
    left: 0;	
    // top: 4rem;
	bottom:0;	
	height:60px;
	background: #fff;
	color: #3c4a50;
	-webkit-transition: -webkit-transform 0.35s;
	transition: transform 0.35s;
	-webkit-transform: translate3d(0,100%,0);
	transform: translate3d(0,100%,0);
	visibility:hidden;
	z-index: 5;
}
.hovereffect:hover .overlay {
	visibility:unset;
}
.hovereffect .overlay .icon-links{
    display: -webkit-inline-box;
}
.hovereffect .overlay .icon-links button:focus {
    outline: none;
}
.categ-in-title h3{
	font-weight: 500;
	font-size:18px;
	margin:25px 0 0px 0;
}
.categ-in-title p{
	font-weight: 500;
	font-size:18px;
	margin:10px 0 10px 0;
	color:#A7B80E;
}
.categ-in-title h4{
	font-weight:600;
	font-size:24px;
	margin:10px 0 0px 0;
	color: $sw-color-brand-primary;
}
.categ-in-title h4 del{
	font-size:20px !important;
	color: rgba(0, 0, 0, 0.5) !important;
	color:#000;
}
.hovereffect h2 {
    text-transform: uppercase;
    color: #fff;
    text-align: center;
    position: relative;
    font-size: 17px;
    padding: 10px;
    background: rgba(0, 0, 0, 0.6);
	float: left;
	margin: 0px;
	display: inline-block;
}
.hovereffect a.info {
    display: inline-block;
    text-decoration: none;
    padding: 7px 14px;
    text-transform: uppercase;
	color: #fff;
	border: 1px solid #fff;
	margin: 50px 0 0 0;
	background-color: transparent;
}
.hovereffect a.info:hover {
    box-shadow: 0 0 5px #fff;
}
.hovereffect .icon-links a {
	display:inline-block;
	color: #3c4a50;
	font-size: 1.4em;
}
.hovereffect:hover div.icon-links a:hover,
.hovereffect:hover div.icon-links a:focus {
	color: #252d31;
}
.hovereffect h2,
.hovereffect div.icon-links a,
.hovereffect div.icon-links form
{
	-webkit-transition: -webkit-transform 0.35s;
	transition: transform 0.35s;
	-webkit-transform: translate3d(0,200%,0);
	transform: translate3d(0,200%,0);
	visibility: visible;
}
.hovereffect div.icon-links a i:before,
.hovereffect div.icon-links form i:before,
.hovereffect div.icon-links button i:before{
	display: inline-block;
	padding: 8px 10px;
	speak: none;
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
}
.hovereffect:hover .overlay,
.hovereffect:hover h2,
.hovereffect:hover div.icon-links a,
.hovereffect div.icon-links form,
.hovereffect div.icon-links button{
	-webkit-transform: translate3d(0,0,0);
	transform: translate3d(0,0,0);
    visibility: inherit;
}
.hovereffect:hover h2 {
	-webkit-transition-delay: 0.05s;
	transition-delay: 0.05s;
}
.hovereffect:hover div.icon-links i:nth-child(3) {
	-webkit-transition-delay: 0.1s;
	transition-delay: 0.1s;
}
.hovereffect:hover div.icon-links i:nth-child(2) {
	-webkit-transition-delay: 0.15s;
	transition-delay: 0.15s;
}
.hovereffect:hover div.icon-links i:first-child {
	-webkit-transition-delay: 0.2s;
	transition-delay: 0.2s;
}
.overlay .icon-links a, .overlay button{
	float:left;		
}
.overlay a i, .overlay button i,.overlay form i{
	font-size:22px;
}
.overlay .bg-unset-icon{
	background:unset;
	border:0;
    padding-left: 0;
    padding-right: 0;
}
.festtema-buy{
    padding: 0px;
}
.flink-quickview-btn {
    margin-top: 0;
    margin-bottom: 0;
}
.product-detail-name{
    font-weight: 600;
    color: #000;
}
.product-price-info {
    margin-top: 0px;
}
.product-price-unit {
    height: auto;
}
.product-cheapest-price {
    min-height: auto;
    margin-bottom: 0px;
}
.product-price {
    color: $sw-color-brand-primary;
    font-weight: 600;
    font-size: 24px;
}
.overlay .product-action {
    display: initial;
}
.new-arrivals {
	padding: 15px 0 35px 0 !important;
    margin-bottom: 0;
}
.check-out-new {
    background: $sw-color-brand-secondary;
    margin: 10px 0;
    padding: 0 0 50px 0;
}
.products-inner {
    margin: 15px 0;
}
.new-arrivals .nav-tabs .nav-item.show .nav-link,
.new-arrivals .nav-tabs .nav-link.active,
.product-detail-cross-selling .nav-tabs .nav-item.show .nav-link,
.product-detail-cross-selling .nav-tabs .nav-link.active,
.caretgory-new-arrivals .nav-tabs .nav-item.show .nav-link,
.caretgory-new-arrivals .nav-tabs .nav-link.active{
	color:$sw-color-brand-primary;
	background:unset;
	border-bottom:2px solid $sw-color-brand-primary;
	font-weight:600;
	font-size:24px;
}
.new-arrivals .main-width .nav-link,
.product-detail-cross-selling .nav-link,
.caretgory-new-arrivals .main-width .nav-link{
	font-weight:600;
	font-size:24px;
	color:#333;
	border:0;
	padding:.5rem 0rem;	
}
.new-arrivals .main-width .nav-tabs .nav-link,
.product-detail-cross-selling .nav-tabs .nav-link,
.caretgory-new-arrivals .main-width .nav-tabs .nav-link{
	border-top:0;
	border-right:0;
	border-left:0;
}
.new-arrivals .nav-tabs,
.product-detail-cross-selling .nav-tabs,
.caretgory-new-arrivals .nav-tabs{
	border:0;
    justify-content: center;
}
.product-detail-description-text, 
.product-detail-properties-table th, 
.product-detail-properties-table tr,
.product-detail-properties-table td {
    font-size: 14px;
    color: #000 !important;
    margin: 0;
}
.product-detail-description-text{
    font-weight: 400;
}
.product-detail-price-container{
    margin-top: 30px;
    margin-bottom: 10px;
}
.fes-categories .card-tabs .nav-link:hover {
    background: none !important;
}
.product-detail-properties-table tr{
    border-bottom: 1px solid #cccccc87;
}
.fes-categories .table-striped tbody tr:nth-of-type(odd) {
    background-color: #fff;
}
.product-name{
    height: auto;
    line-height: 33px;
	min-height: 62px;
}
.checkout-aside-summary{
    background-color: $sw-color-brand-secondary;
}
.checkout-aside-container{
    background-color: $sw-color-brand-secondary;
    padding: 20px;
}
.begin-checkout-btn{
    border-radius: 10px;
}
.checkout-aside-add-code .btn-secondary, 
.cart-add-product .btn-secondary,
.offcanvas-cart-promotion-form .btn-secondary{
    background-color: $sw-color-brand-primary;
    border-color: $sw-color-brand-primary;
}
.checkout-aside-add-code .btn-secondary .icon-checkmark,
.cart-add-product .btn-secondary .icon-checkmark,
.offcanvas-cart-promotion-form .btn-secondary .icon-checkmark{ 
    color: #fff;
}
.checkout-aside-summary-header, 
.cart-main-header,
.offcanvas-cart-header,
.offcanvas-cart-header-count,
.confirm-main-header,
.finish-header,
.address-main-header,
.checkout-aside-product-header{
    font-weight: 400;
    font-family: 'Abril Fatface', cursive;
    color: #000;
}
.checkout-aside-summary-list-container{
    font-size: 16px;
    font-weight: 500;
    color: #000;
}
.checkout-product-table .card-title,
.cart-item-price{
    font-weight: 500;
    color: #000;
}
.checkout-product-table .cart-item-label,
.offcanvas-cart-items .cart-item-details .cart-item-label {
    color: #000;
    font-weight: 600;
}
.checkout-product-table .cart-item{
    border-bottom: 1px solid #cdcdcd;
}
.shave-on-shop{
	background:$sw-color-brand-secondary;
	padding:60px 0;
}
.shave-on-shop h3{
	font-weight:400;
	font-size:30px;
	color:$sw-color-brand-primary;
	border-bottom:2px solid $sw-color-brand-primary;
	font-family: 'Abril Fatface', cursive;
	max-width:390px;
	line-height:42px;
}
.day-to-day .icon{
	background:#fff;
	box-shadow: 0px 4px 6px rgba(0, 0, 0, 0.25);
	border-radius: 5px;	
	text-align:center;	
}
.day-to-day .text-title{	
	font-weight: 500;	
	display: inline-block;
	vertical-align:top;
}
.base-slider-controls-prev, 
.base-slider-controls-next {
    top: 35%;
}
.account-register .form-group, 
.account-profile-personal .form-group,
.account-address-form .form-group,
.register-personal .form-group,
.register-billing .form-group,
.register-shipping .form-group{
    margin-bottom: 8px;
}
.account-register .form-control, 
.account-register .custom-select,
.account-register .form-select,
.register-personal .form-select,
.register-address .form-select,
.account-profile-personal .form-control,
.account-profile-personal .custom-select,
.account-address-form .form-control,
.account-address-form .custom-select,
.register-personal .form-control,
.register-personal .custom-select,
.register-billing .form-control,
.register-billing .custom-select,
.register-shipping .form-control,
.register-shipping .custom-select,
.login-card .form-control{
    border: 1px solid #000;
    height: 36px;
    border-radius: 10px;
}
.account-register .form-label, 
.account-register .form-check-label,
.account-profile-personal .form-label,
.account-profile-personal .form-check-label,
.account-address-form .form-label,
.account-address-form .form-check-label,
.register-personal .form-label,
.register-personal .form-check-label,
.register-billing .form-label,
.register-billing .form-check-label,
.register-shipping .form-label,
.register-shipping .form-check-label,
.login-card .form-label,
.login-card .form-check-label{
    font-weight: 500;
    font-size: 13px;
}
.account-register .btn,
.register-submit .btn,
.checkout-aside-action .btn,
.login-submit .btn{
    border-radius: 10px;
}
.input-group > .input-group-append > .btn, .input-group > .input-group-append > .input-group-text, .input-group > .input-group-prepend:not(:first-child) > .btn, .input-group > .input-group-prepend:not(:first-child) > .input-group-text, .input-group > .input-group-prepend:first-child > .btn:not(:first-child), .input-group > .input-group-prepend:first-child > .input-group-text:not(:first-child) {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
}

.is-act-confirmpage .checkout{
    padding-top: 0;
}
.wishlist-headline{
    height: auto;
    font-family: 'Abril Fatface', cursive;
    font-weight: 400;
}
.is-act-confirmpage .shave-on-shop,
.is-act-checkoutregisterpage .shave-on-shop,
.is-act-finishpage .shave-on-shop{
    display: none;
}
.account-welcome h1{
    font-family: 'Abril Fatface', cursive;
    font-weight: 400;
}
.categ-add-text h1{
	color:$sw-color-brand-primary;
}
.categ-add-text .btn-primary{
    border-radius: 10px;
}
.main-category-banner .col-12{
    padding-left: 0;
    padding-right: 0;
}
.trendy-category{
    padding: 10px 0 25px 0;
    margin-top: 20px;
}
.trendy-category .card-header{
    display: none;
}
.recent-category-section h2{
	font-weight:400;
	font-size:24px;
	font-family: 'Abril Fatface';
	color:$sw-color-brand-primary;
	padding:20px 0 0 0;
}
.recent-category-section p{
	font-weight:400;
	color:#333;
	line-height:26px;
	padding:10px 0 30px 0;
}
.recent-category-section .btn-primary{
    border-radius: 10px;
}
.caretgory-new-arrivals{
    padding: 40px 0 40px 0;
}
.cart-page h2{
	font-weight:400;	
	font-family: 'Abril Fatface', cursive;	
}
.cart-page .proceed-check h2{
	margin-bottom:20px;
}
.user-product-details .user-pro-img{	
	border-radius:10px;
	border:1px solid #cdcdcd;
	padding:10px 0;
	text-align:center;
}
.user-product-details h3{
	font-weight:500;	
}
.user-product-details .main-price{
	padding:10px 0;
	font-weight: 400;	
}
.user-product-details .re-link{
	text-decoration:underline;
	font-weight: 400;	
	color:#333;
	margin-right:15px;
}
.total-amount h3{
	font-weight:600;
}
.checkout-product-table .card-title{
    border: none;
}
.cart-item-quantity{
    padding: 10px 0 10px 0;
}
.cart-whishlist-remove{
    display: block;
}
.cart-item-remove{
    display: inline-flex;
}
.cart-item-remove-button{
    background: none;
    border: none;
    padding: 0 0 0 12px;
    text-decoration: underline;
    font-weight: 400;
    font-size: 14px;
}
.user-pro-img img{
	border-top-left-radius:0px;
	border-top-right-radius:0px;
	object-fit: scale-down;
	font-family: "object-fit: scale-down;";
	display:inline-block;
	max-width: 100%;
	max-height:177px;
	text-align:center;
}
.checkout-product-table .cart-item{
    padding-bottom: 30px;
}
.cart-item-remove-button{
    padding: 0 10px 0 10px;
}
.product-detail-ordernumber-container{
    padding-top: 10px;
}
.header-logo-picture {
    display: unset;
    max-width: unset;
    margin: unset;
}
.round-box {
	position: relative;	
	border: 1px solid #c8c8c8;
	border-radius: 5px;
}
.round-box img{
	object-fit: scale-down;	
	display:inline-block;
	max-width:100%;
	height:84px;
	text-align:center;
    border-radius: 5px;
	margin: 0 auto;
}
.is-act-checkoutregisterpage .checkout {
    margin-top: 0 !important;
	padding-top: 10px;
}
.subcategory-list{
	background:$sw-color-brand-secondary;
    padding: 10px 0 10px 0;
}
.checkout-aside-summary-label {
    word-break: break-word;
}
.round-box a:hover{
	text-decoration: underline !important;
}
.secondary-background{
	background-color: $sw-color-brand-secondary;
}
.filter-panel-item .filter-panel-item-toggle{
	border: 1px solid #4a545b9c;
}
.filter-panel-item{
	box-shadow: 1px 1px 4px 3px #79849036;
}
.filter-panel{
	padding-top: 10px;
}
.festtema-single-label{
    font-size: 1.1rem;
    font-weight: 600;
	margin: 15px 0 0 0;
}
.filter-panel-item:hover {
    background: rgb(132 25 111 / 11%);
}
.category-image-gallery figure:hover h2{
	width:100%;
}
.bst-subcategories .bst-subcategory-list > div:hover {
    color: #fff;
}
.bst-subcategories .bst-subcategory-list > div a{
	padding: 0;
	border: none;
	background-color: #fff;
}
.product-tip-info-block{
	padding: 20px;
	margin: 30px 0;
}
.read-more, .read-less{
	color: $sw-color-brand-primary;
	text-decoration: underline;
	background: none;
    border: none;
    padding: 0;
    margin: 0;
	display: block;
}
.read-more:focus, .read-less:focus {
	outline: none;
}
.festtema-product-qty,
.product-detail-ordernumber-container{
	display: none;
}
.option-strikethrough {
	position: relative;
}
.option-strikethrough:before {
	position: absolute;
	content: "";
	left: 0;
	top: 50%;
	right: 0;
	border-top: 1px solid;
	border-color: inherit;	
	-webkit-transform:rotate(-13deg);
	-moz-transform:rotate(-13deg);
	-ms-transform:rotate(-13deg);
	-o-transform:rotate(-13deg);
	transform:rotate(-13deg);
}
.product-review-wishlist-block .product-detail-reviews{
	display: inline-block;
}
.product-review-wishlist-block .product-wishlist-action{
	margin-left: 10px;
	vertical-align: middle;
	color: $sw-color-brand-primary;
}
.product-detail-review-item-date {
    padding-bottom: 8px;
}
.product-detail-review-item-points {
    padding-bottom: 5px;
}
.product-detail-review-item-content{
	padding-left: 20px;
	font-size: 14px !important;
}
.product-detail-review-rating{
	padding-top: 15px;
}
.product-detail-review-teaser .h4{
	padding-bottom: 5px;
}
.product-detail-review-item-comment{
	margin-top: 10px;
}
.sizechart .size-chart-btn, 
.sizechart .size-chart-btn:hover,
.sizechart .size-chart-btn:focus,
.sizechart .size-chart-btn:active{
	background: none;
	border: none;
	text-decoration: underline;
	padding: 0;
	color: #606266;
	box-shadow: none;
	outline: none;
}
.sizechart-icon{
	margin-bottom: 4px;
	height: 17px;
}
.holder-list span{
	font-size:15px;
}
.holder-list i{
	font-size: 20px;
    margin-right: 2px;
    vertical-align: middle;
}
.product-detail-price-container{
	margin-top: 15px;
}
.navigation-img-box .round-box img{
	object-fit: unset;
    width: 150px;
    height: 100px;
}
.btn-buy{
	border-radius: 6px;
	background-color: $sw-color-brand-primary !important;
    border-color: $sw-color-brand-primary !important;
}
.product-detail-price {
    font-size: 1.2rem;
}
.indeholder-title{
    margin-top: 30px;
}
.product-detail-tax{
	display: none;
}
.sizechart table tbody tr td:first-child{
	background-color: $sw-color-brand-primary;
	color: #fff;
}
.sizechart .modal-content .modal-title{
	width: 100%;
    text-align: center;
}
.sizechart table thead tr th{
	background-color: $sw-color-brand-primary;
	color: #fff;
}
.sizechart table thead tr th:first-child{
	background-color: transparent;
	color: #000;
	border: none;
}
.is-act-finishpage .checkout {
    padding-top: 10px;
}
.finish-header{
	line-height: 3rem;
}
.checkout-aside-item-image{
	padding: 0;
}
.product-detail-review-item{
	padding-bottom: 15px;
}
.product-detail-review-item .blockquote{
	margin-bottom: 0;
}
.product-detail-review-item p{
	line-height:22px;
}
.product-detail-review-teaser .btn{
	margin-top:15px;
}
.cart-add-product-container,
.cart-shipping-costs-container{
	display: none;
}
.is-act-cartpage .checkout .checkout-aside,
.is-ctl-accountorder .checkout .checkout-aside{
	display: block;
	flex: 100%;
	max-width: 100%;
	padding: 0;
}
.cart-additonal-info-block-container{
	position: sticky;
    top: 80px;
}
.cart-item-img{
	width: auto;
}
.inner-item-details{	
	display: grid;
	grid-template-columns: 1fr -webkit-max-content;
	grid-template-columns: 1fr max-content;
}
.brd-part{
	margin:10px 0;
}
.brd-part li{
	border-bottom:1px solid #ddd;
	padding:10px 0;
}
.brd-part li .last-brd-line-none{
	border:0;
}
.c-s-o-link{
	list-style-type: none;
}
.c-s-o-link li a{
	text-decoration:underline;
	color:#000;
}
.account-overview-newsletter{
	padding: 0 20px;
}
.order-item-actions .btn,
.profile-form-submit,
.account-overview-payment .card-actions .btn,
.account-overview-card .card-actions .btn,
.checkout-card .card-actions .btn{
	background: $sw-color-brand-primary;
    color: #fff;
    border-radius: 10px;
}
.account-payment-card .btn,
.address-action-create .btn,
.address-actions-buttons .btn{
	border-radius: 10px;
}
.cart-item-quantity-label{
	padding-left: 0;
}
.products-inner .base-slider .tns-nav, 
.products-inner .base-slider .base-slider-dots {
    display: block;
}
.products-inner .base-slider .tns-nav .base-slider-dot, 
.products-inner .base-slider .tns-nav button,
.products-inner .base-slider .tns-nav button,
.products-inner .base-slider .base-slider-dots .base-slider-dot, 
.products-inner .base-slider .base-slider-dots button {
    height: 0px;
    width: 0px;
    padding: 0px;
}
.category-description-box h1 {
    font-size: 15px;
	line-height: 22px;
}
.is-tanmar-infinite-scrolling .infinite-scrolling-button-prev, 
.is-tanmar-infinite-scrolling .infinite-scrolling-button-more{
    width: 50%;
}
.product-box{
	border: none;
}
.cart-faq-list li{
	cursor: pointer;
	text-decoration: underline;
	margin-bottom: 5px;
}
.opening-hours.brd-part li {
    cursor: pointer;
}
.is-act-confirmpage .checkout .checkout-main,
.is-act-confirmpage .checkout .checkout-additional{
	margin-left: 0;
}
.checkout-aside-add-code .sr-only{
	display: none;
}
#one-page-checkout .checkout-aside .checkout-aside-container .confirm-tos {
	margin-top: 20px;
}
#one-page-checkout #opc-register-login-tabs .nav-link.active{
	border-bottom: 3px solid $sw-color-brand-primary;
}
#one-page-checkout #billingAddressAddressStreetContainer,
#one-page-checkout #shippingAddressAddressStreetContainer {
	margin-bottom: 0 !important;
}
#one-page-checkout .register-shipping {
	padding-top: 0.5rem;
}
#one-page-checkout .register-different-shipping {
	margin-top: 8px;
}
#one-page-checkout .form-control::placeholder {
    color: #4a545b;
}
#one-page-checkout.grey-step2and3 .checkout-step-2:after,
#one-page-checkout.grey-step2and3 .checkout-aside:after {
	background-color: rgb(255 255 255 / 60%);
}
.payment-method-description>p,
.shipping-method-description>p {
	color: #4a545b;
}
.confirm-shipping .shipping-comment-container {
	display: none;
}
.confirm-payment-shipping {
	margin-bottom: 0;
}
.confirm-address,
.confirm-payment-shipping,
.confirm-product{
    padding-top: 10px;
}
.comment-box-ft{
	border: 1px solid #ddd;
    padding: 15px 20px;
}
.confirm-billing-address,
.confirm-shipping-address,
.finish-address{
	padding-left: 0;
	padding-right: 0;
}
.finish-info{
	border: 1px solid #ddd;
    padding: 15px 0px 0 0;
}
.finish-content{
	margin-bottom: 4rem;
}
.cart-item-quantity-row{
	padding: 0;
}
.is-act-finishpage .checkout-product-table{
	border: 1px solid #ddd;
	padding: 15px 10px;
}
.whats-new .c108-hfi-bgimg {
    border-radius: 10px;
}
.category-description-box .cms-block-image-text-cover .cms-element-text{
	padding-top: 0px;
}
.festtema-modal-body h3{
	font-size: 0.9rem;
    font-weight: normal;
}
.festtema-modal-body h3{
	font-size: 1.7rem;
    font-weight: 600;
}
.festtema-modal-body h4{
	font-size: 1.2rem;
    font-weight: 600;
}
.container-main {
    padding: 5px 0;
}
.festtema-nav-second-level{
	font-size: 1.1rem;
    font-weight: 600;
}
.festtema-nav-third-level{
	font-size: 15px;
}
.festtema-nav-space{
	margin-top: 20px;
}
.subcategory-slider-bgcolor{
	background: rgba($sw-color-brand-secondary, .8);
	margin-bottom: 10px;
}
.subcategory-slider-list{
	padding: 10px 0 5px 0;
}
.is-ctl-accountorder .checkout {
    padding-top: 25px;
}
.shipping-method-price,
.confirm-payment-shipping{
	padding-left: 0;
	padding-right: 0;
}
.shipping-method-price span {
    font-weight: bold;
    text-align: left;
    display: block;
}
.mw-faq-category-image img{
	height: 60px;
}
.mw-faq-questions .tab-label{
	font-weight: 600;
}
.festtema-seo-text h2{
	font-size: 1.3rem !important;
}
.festtema-seo-text h3{
	font-size: 1.1rem !important;
}
#ziplookup h3{
	font-weight: 600;
	font-size: 18px;
	line-height: 1.4;
	margin-bottom: 15px;
}
#ziplookup .inner .ziplookup-bottom{
	margin-top: 35px;
	width: 100%;
}
#ziplookup .inner .zipcode {
    height: 50px;
    padding-right: 40px;
    border: 1px solid #d5d5d5;
    background: #f0f0f0;
    line-height: 40px;
    color: #757575;
    padding: 0 15px;
    font-weight: 600;
    width: 61%;
}
#ziplookup .inner .search-zipcode {
    height: 50px;
    vertical-align: top;
    width: 37%;
    font-weight: bold;
    font-size: 15px;
	background: $sw-color-brand-primary;
    display: inline-block;
    padding: 7px 15px;
    color: #FFFFFF;
    font-size: 13px;
    line-height: 19px;
    text-align: center;
    text-transform: uppercase;
    vertical-align: middle;
}
#ziplookup .inner h4 {
    text-align: left;
    margin-top: 20px;
    font-size: 19px;
    display: inline-block;
}
#ziplookup .inner span.time{
	text-align: center;
    color: $sw-color-brand-primary !important;
    font-weight: 600;
    font-size: 19px !important;
    margin-top: 20px;
    display: inline-block;
}
.deliverytime {
    display: none;
}
.fs-sub-category-title{
	font-family: 'Abril Fatface', cursive;
	font-weight:400;
	color: $sw-color-brand-primary;
	text-align:left;
	font-size: 22px;
	padding-bottom: 5px;
}
.cms-element-product-listing-actions{
	margin-bottom: 0px;
}
.flink-size-chart-button{
	background: none; 
    text-decoration: underline;
    color: #606266;
    font-weight: 600;
}
.cms-element-subcategory-navigation .tns-controls>button{
	background:url('/bundles/festtematheme/images/icons8-less-than-32.png') no-repeat 50% 50%;
	top: 60%;
}
.cms-element-subcategory-navigation .tns-controls > button:last-child{
	background:url('/bundles/festtematheme/images/icons8-more-than-32.png') no-repeat 50% 50%;
}
.cookie-permission-container .btn{
	line-height: 1.3rem;
}
.filter-panel{
	padding-top: 5px;
	margin-bottom: 0px;
}
.filter-panel-items-container,
.filter-panel-item{
	margin-bottom: 3px;
}
.primary-bg-color{
	background-color: $sw-color-brand-primary;
}
.secondary-bg-color{
	background-color: $sw-color-brand-secondary;
}
.register-required-info{
	display: none;
}
.register-different-shipping,
.register-guest-control,
.register-login-collapse-toogle {
    margin-bottom: 0.5rem;
}
.card-title {
	padding-bottom: 6px;
	margin-bottom: 0.4rem;
}
.register-submit {
    text-align: left;
}
.register-address {
    margin-bottom: 1rem;
}
.festtema__category-img{
	padding-left: 0;
}
.festtema-privacy-info{
	position: sticky;
	padding: 10px 5px;
}
.festtema-cart-sub-container{
	background: $sw-color-brand-secondary;
    padding: 30px;
}
.featured-categories{
	text-align: center;
	text-align: -webkit-center;
}
.trend-count .container,
.trend-count{
	padding-left: 0;
	padding-right: 0;
}
.cms-element-product-listing-actions .sorting{
	display: none;
}
.notification-part img{
	float:left;
}
.notification-part p{
	display:block;
	padding:10px 0 0 0;
}
.fest-market p{
	display:block;
	padding:10px 0 0 0;
}
.fal-part img{
	float:left;
}
.fal-part h1, .fal-part h2{
  font-size: 15px;
  line-height: 22px;
  font-weight:bold;
}
.fal-part p{
	font-size:14px;
}
.was-validated .form-control:invalid, 
.form-control.is-invalid{
	border-color: #e52427;
}
.invalid-feedback{
	color: #e52427;
}
.fal-part{
	width:100%;
}
.search-headline{
	text-align: left;
	font-size: 1.2rem;
    font-weight: 600;
	margin-bottom: 0.2rem;
}
.search-page h2{
	font-size: 1rem;
    font-weight: 600;
}
.header-logo-col {
    margin: 0.5rem 0;
}
.brand-logo-part .btn{
	padding: 2px 7px;
}
/* hiren changes 24-03-2023 */
.product-box .product-badges .badge,
.list-price-badge{
	background-color: #e6a23c !important;
}
.festtema-seo-text h1{
	font-size: 1.5rem !important;
}
.festtema-seo-text h4{
	font-size: 1rem !important;
}
.primary-color{
	color: $sw-color-brand-primary !important;
}
.hue-checkbox .hue-price-wrapper{
	margin: 0;
}
.huebert-direct-accessories-block .huebert-accessory_input-wrapper{
	margin-bottom: 0;
}
.hue-checkbox .checkbox-mobile-wrapper{
	margin-top: 0;
    width: auto;
	margin-left: unset;
}
.huebert-direct-accessories-block .huebert-accessory_input-wrapper{
	min-width: unset;
    max-width: unset;
}
.huebert-accessory {
    padding: 15px 0 0 0;
    margin: 0;
}
.gallery-slider-thumbnails-item-inner .weedesign-fallback {
    height: 100%;
    width: 100%;
}
.popular-links li{
	text-align: left;
	padding: 10px;
	margin: 10px 0;
	direction: none;
	box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19);
	list-style-type: none;
	border-radius: 10px;
}
.rwd-table {
	margin: auto;
	min-width: 100%;
	max-width: 100%;
	border-collapse: collapse;
	margin-bottom: 25px;
}
.popular-links a{
	text-decoration: none;
	color:#000;
}
.rwd-table tr:first-child {
	border-top: none;
	color: #000;
	border-radius: 20px;
	list-style-type: none;
	margin-bottom: 30px;
}
.rwd-table th,
.rwd-table td {
	padding: 10px;
	border-radius: 10px;
	box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19); 
} 
.c108-hfi-bgimg{
	height: 100%;
}
.autoheight{
	height: auto;
}
.ft-cms-block-12 .col-12,
.ft-cms-block-text-el .cms-element-text{
	padding-left: 0;
	padding-right: 0;
}
.e-market-logo-offcanvas {
	margin-top: 25px;
}
.is-offcanvas .line-item-total-price {
	padding-right: calc(var(--bs-gutter-x) * .5);
}
.line-item-quantity-select-wrapper {
	padding-left: 0;
	padding-right: 0;
}
.checkout-aside-add-code.input-group > .form-control:not(:first-child){
    border-top-left-radius: 10px !important;
    border-bottom-left-radius: 10px !important;
}
.cart-add-promotion {
	padding-bottom: 25px;
}
.navigation-offcanvas {
	--bs-offcanvas-width: none;
}
.is-act-cartpage .checkout .checkout-aside {
	width: 100%;
}
.cart-offcanvas .offcanvas-content-container {
	padding-left: 15px;
    padding-right: 15px;
	overflow-y: auto;
}
.offcanvas-end .product-detail-levering-text,
.offcanvas-end .product-detail-description,
.offcanvas-end .product-detail-faqs-text,
.offcanvas-end .product-detail-review{
	overflow-y: auto;
	padding: 20px;
}
.padding-left-0 {
	padding-left: 0;
}
.huebert-accessory .sm-img{
	margin-top: 15px;
}
.huebert-direct-accessories-block .accordion {
	--bs-accordion-border-width:0;
}
.huebert-direct-accessories-block .bg-white{
	background-color: unset;
}
.huebert-direct-accessories-block .accordion-header{
	background-color: rgba(0,0,0,.03);
}
.huebert-direct-accessories-block .accordion-button {
	background-color: unset;
}
.cart-offcanvas {
	overflow-y: auto;
}
.offcanvas-cookie {
	padding: 20px;
}
.offcanvas.offcanvas-modal {
	overflow-y: auto;
}
.js-offcanvas-cookie-submit {
	width: 100%;
}
.accountType {
	font-weight: 600;
}
.indehold {
	display: grid;
    grid-template-columns: 1fr 1fr;
	list-style-type: none;
	padding-left: 0;
}
.indehold li {
    display: flex;
    align-items: center;
}
.indehold i {
	font-size: 20px;
    margin-right: 2px;
    vertical-align: middle;
}
.indehold span {
	font-size: 15px;
}
.product-detail-cross-selling .product-detail-tabs-content {
	padding-top: 0;
}
.product-detail-cross-selling .tab-pane {
	margin-top: 0;
}
.ziplookup-bottom em{
	background: none;
}
.e-market-checkout-desktop{
	border: 1px solid #bcc1c7;
    margin-top: 15px;
    background: $sw-color-brand-secondary;
    padding: 20px;
	text-align: center;
}
.e-market-checkout-mobile a{
	position: fixed;
    right: -12px;
    top: 48%;
    z-index: 100;
}
.e-market-checkout-mobile img{
    width: 80px;
}
.topbar-discount-text div{
	color: #fff;
}
.product-detail-tab-preview {
	color: #333333;
}
.fs-wishlist-mobile {
	position: absolute;
    top: 0;
    right: 5%;
}
.product-wishlist-added .icon-wishlist-added,
.product-wishlist-not-added .icon-wishlist-not-added {
	color: $sw-color-brand-primary;
}
.navigation--entry.entry--cart .ngs--free-delivery-remaining-amount-container.ngs--free-delivery-remaining-amount-container-widget {
	top: 0 !important;
}
.header-cart-btn .navigation--entry.entry--cart.is--free-delivery-remaining .ngs--sw-cart-button-container {
    margin-right: 55px;
}
.free-delivery--checkout-box {
    border: 1px solid #bcc1c7;
    padding: 15px 15px 0 15px;
}
.e-market-mobile-icon{
	display: block;
    position: fixed;
    bottom: 36px;
    left: -6px;
}
.collapsible-content {
	max-height: 0;
	overflow: hidden;
	display: block;
}
.collapsible-content.open {
	max-height: 6000px;
	display: inline;
}
.ais-InfiniteHits-loadMore {
	margin-bottom: 60px;
}
.cms-element-product-listing .product-price-unit {
	display: none;
}
.product-slider-item .product-price-unit {
	display: none;
}
.product-detail-cross-selling .card {
	margin-top: 1rem;
}
.ft-pakkeshop {
	width: 100% !important;
}
.ft-select-packageshop {
	background: #f6f6f6;
    padding: 10px 0px 10px 7px;
	cursor: pointer;
}
.ft-select-packageshop span {
	font-weight: 500;
}
.ft-packageshop-icon {
	max-height: 24px;
    max-width: 100%;
    margin-right: 0.25rem;
    margin-left: 0.25rem;
    float: right;
	cursor: pointer;
}
.product-price-wrapper {
	font-weight: 600;
}
.product-detail-tabs-content em {
	background-color: unset;
}
.table.product-bundle-products-table-responsive .bundle-child-product-detail .product-bundle-products-product-cell .line-item-label {
	color: inherit;
}
.products-inner .gallery-slider-item.is-contain .gallery-slider-image, 
.products-inner .gallery-slider-single-image.is-contain .gallery-slider-image {
	height: -webkit-fill-available;
}
.swkweb-product-set-slot-content #netzhirsch-order-countdown,
.swkweb-product-set-slot-content .swkweb-product-set-option-price{
	display: none;
}
.ais-SearchBox-input, .header-search-btn, .header-search-btn:hover{
	border-color: $sw-color-brand-primary;
}
.list-stock-info {
	font-size: 20px;
	position: absolute;
	top: 5%;
	left: 3%;
	z-index: 2;
}
.in-stock-info {
	color: #3cc261;
}
.remote-stock-info {
	color: #ffdd00;
}
.new-product-badge {
	position: absolute;
	bottom: 5%;
	left: -2%;
}
.new-product-badge .badge-new {
	border-radius: 0;
	padding: 2px 6px;
	background: $sw-color-new-badge;
	color: inherit;
}
.badge-discount {
	position: absolute;
	z-index: 1;
	top: 8%;
	right: -2%;
}
.product-price .list-price {
	color: $sw-color-brand-primary;
	font-size: 17px;
	font-weight: 600;
}
.ft-list-price {
	font-size: 14px;
	font-weight: 600;
	padding-left: 5px;
	padding-top: 2px;
	text-decoration: line-through;
}
.ft-imagebox-home img {
	border-radius: 10px;
}
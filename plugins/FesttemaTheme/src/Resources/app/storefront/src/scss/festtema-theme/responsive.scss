@media (min-width:320px) {
	.product-slider-item .product-name {
		font-size: 11px;
	}
	.collapsible-content {
		min-height: 113px;
	}
	.category-image-gallery .grid figure figcaption h2 {
		font-size: 9px;
	}
	.ngs--free-delivery-remaining-amount-container-desktop {
		display: none;
	}
	.topbar-social-icons{
		display: none;
	}
	.topbar-discount-text .tns-item{
		font-size: 12px;
	}
	.alert-netzhirsch-order-countdown .alert-content,
	.product-detail-delivery-information .alert-content{
		font-size: .68rem;
		color: #000;
	}
	.gallery-slider-col{
		padding-left: 0;
		padding-right: 0;
	}
	.breadcrumb {
		margin-bottom: 0;
	}
	.breadcrumb a {
		font-size: 0.7rem;
	}
	.breadcrumb .breadcrumb-placeholder{
		margin: 0 0.3rem;
	}
	.e-market-header{
		display: none;
	}
	.header-logo-main {
		text-align: right;
	}
	.main-logo-ft{
		height: 40px;
		width: 120px;
	}
	.desktop-view{
		display:none;
	}
	.mobile-view{
		display:block;
		padding:0;
	}
	.navigation-offcanvas-placeholder, .navigation-offcanvas-overlay{
		width: 100%;
	}
	.navigation-offcanvas-overlay.has-transition{
		width: 100%;
	}
	.aa-Panel {
		top: 135px !important;
	}
	.mw-faq-categories ul li{
		width: 98%;
		min-height: unset !important;
		margin: 5px 1%;
	}
	.mw-faq-category-description{
		font-size: 12px;
	}
	.mw-faq-category-headline {
		font-size: 0.9rem;
		margin:0;
	}
	.mw-faq-category-image img {
		height: 36px;
	}
	.fal-part img{
		margin-right:20px;
		height:100px;
		width: auto;
	}
	.stroe-box{
		min-height:unset;
		text-align:left;
		padding:10px;
		margin-bottom:15px;
	}
	.fl-side{
		float:left;
	}
	.stroe-box p{
		font-size:12px;
		margin-bottom: 0;
	}
	.side-height{
		min-height:100px; 
		float:left;
	}
	.stroe-box img{		
		margin-right:20px;
	}
	.stroe-box .part-1{
		height: 50px;
		width: 50px;
	}
	.stroe-box .part-2{
		height: 50px;
		width: 59px;
	}
	.about-store .stroe-box .title-two{
		font-size:16px;
		padding:0;
		margin-bottom: 4px;
	}
	.visit-box p{
		font-size:14px;
		margin-bottom: 0px;
	}
	.visit-box{
		text-align:left;
		padding: 5px 20px;
		margin:10px 0 0 0;
		min-height:unset;
		border-top:5px solid $sw-color-brand-primary;	
	}
	.visit-box img{
		float:left;
		height:60px;
		height:61px;
		margin:0 20px 0 0;
	}
	.visit-box h3{
		text-align:left;
		padding-top:0;
		margin-bottom: -5px;
    	font-size: 16px;
	}
	.info-laptop{
		float:left;
		margin-right:20px;
		height:60px;
		width: 80px;
		padding:0;
	}
	.notification-part img{		
		margin-right:20px;
		height:62px;
		width: 60px;
		padding:0;
	}
	.btn.filter-panel-wrapper-toggle{
		display: none;
	}
	.filter-panel-wrapper{
		display: inline-flex;
	}
	.filter-panel-active-container{
		margin-top: 10px;
	}
	.cms-element-product-listing .cms-listing-col {
		margin-bottom: 0px;
	}
	.categ-in-title h3{
		margin: 10px 0 0px 0;
	}
	.product-price {
		font-size: 20px;
	}
	.product-name{
		font-size: 11px;
		line-height: 20px;
		min-height: 45px;
	}
	.categ-in-title p {
		margin: 5px 0 5px 0;
	}
	.box-fes-bg{
		min-height: 160px;
	}
	.product-image{
		max-height: 150px;
		width:auto;
	}
	.homepage-title{
		padding: 8px 0 0 0;
	}
	.sub-title{
		padding-top: 0;
	}
	.cms-block-image-four-column .cms-row > :not(:last-child){
		margin-bottom: 10px;
	}
	.header-search {
		margin: auto;
	}
	.cookie-permission-container .btn{
		line-height: 1.3rem;
	}
	.we-back h3{
		font-size:16px;
		line-height:20px;
	}
	.we-back p{
		font-size:12px;
		line-height:16px;
		max-width:unset;
		padding-top: 10px;
	}
	.we-back img{
		width: auto;
		height: 100px;
	}
	.visit-store img{
		height: 55px;
		width: auto;
		padding-top: 5px;
		padding-bottom: 5px;
	}
	.store-box-icon{
		max-width: unset;
		width: auto;
		height: 50px;
	}
	.about-store .title-two{
		font-size: 14px;
		line-height: 20px;
	}
	.day-to-day .icon{
		display:inline-block;
		width:50px;
		height:50px;
		padding:5px;
	}
	.day-to-day .text-title{
		padding:0 0 0 20px;
		max-width:200px;
		font-size:18px;
	}
	.day-to-day ul li{
		display:inline-block;
		margin:0 0 30px 0;
	}
	.cart-additonal-info-block{
		flex: 100%;
		max-width: 100%;
		padding-left: 20px;
		padding-right: 20px;
	}
	.opening-hours{
		max-width: 200px;
		margin: 0 auto;
		padding-left: 0;
	}
    .media-discription{
		padding-left:15px;
		padding-top:10px;
	}
	.product-detail-name{
		font-size:16px;
		margin-bottom: 10px;
		line-height: 25px;
	}
	.indeholder-title {
		margin-top: 10px;
	}
	.fes-categories .product-tabs .tab-content p{
		font-size:16px;
	}
	.new-arrivals .nav-tabs .nav-item .nav-link,
	.new-arrivals .nav-tabs .nav-link.active{
		font-size:12px;
	}
	.select-pay-cards .col-3{
		width:120px;
		margin:0 4px 10px 4px;
		flex: auto;
	}
	.custom-menu-bar .navbar-toggler{
		position: absolute;
		top: 15px;
		left:3px;
	}
	.user-acc{
		padding:0 0 0 0;
	}
	.user-acc .bi{
		font-size:22px;
		color:#000;
		margin-right:0px;
	}
	.fes-account{
		display:inline-block;
		float:right;
	}
	.m-logo{
		padding-left:43px;
	}
	.main-width{
		width:auto;
	}
	.every-need{
		padding:30px 0px 30px 15px;
	}
	.every-need span{
		font-size:11px;
	}
	.every-need h1{
		font-size:18px;
	}
	.categ-add-text h1{
		font-size:18px;
        line-height: 23px;
	}
	.every-need p{
		font-size:12px;
	}
	.btn-lg{
		font-size:14px;
	}
	.fea-images-one,.fea-images-two,.fea-images-three,
	.fea-images-four,.fea-images-five,.fea-images-six{
		width:250px;
		min-height:115px;
		margin:2px auto;
	}
	.categories-names h3{
		font-size:12px;
	}
	.categories-names{
		padding:1px 0;
	}
	.featured-categories {
		padding: 0 15px 0 0px;
	}
	.multi-img{
		margin:0 0px 0 0px;
	}
	.check-all-images{
		margin:10px 0 10px 0;
	}
	.main-width .nav-link{
		padding:10px 15px;
		margin:0 7px 10px 7px;
	}
	.check-all-images .img-fluid{
		height:101px;
	}
	.range-details h3{
		font-size:28px;
		padding:0 20px;
	}
	.trend-count .left-box img{
		border-radius: 10px;
		border-bottom-right-radius: 0;
		border-bottom-left-radius: 0;	
	}
	.trend-count .trend-box-details{
		padding:5px 5px 10px 10px;
	}
	.trend-box-bg .desk-view,.we-back .desk-view{
		display:none;
	}
	.trend-box-bg .mobile-view,.we-back .mobile-view{
		display:block;
		padding:0;
	}
	.trend-count .right-box img{
		border-radius: 10px;
		border-bottom-right-radius: 0;
		border-bottom-left-radius: 0;	
	}
	.trend-count .trend-box-details p{
		font-size:13px;
		padding: 0;
		margin-bottom: 8px;
	}
	.trend-count .trend-box-details .btn {
		font-size: 13px;
		line-height: 15px;
		padding: 5px 10px;
	}
	.row{
		margin-left:0;
		margin-right:0;
	}
	.check-out-new .carousel-control-next-icon,
	.check-out-new .carousel-control-prev-icon{
		width:30px;
		height:30px;
	}
	.check-out-new .carousel-control-prev{
		left:0%;
	}
	.check-out-new .carousel-control-next{
		right:0%;		
	}
	.range-details{
		padding:50px 0 50px 0;
	}
	.footer .site-address{
		padding-left:23px;
	}
	.footer ul{
		padding:0px 0 0 0;
	}
	.footer h4{
		margin:20px 0 10px 0;
	}
	.bran-logo-part .img-fluid{
		max-width:100%;
		height: auto;
	}
	.categories-add{
		text-align:center;
	}
	.category-image-gallery .grid figure {	
		// width: 278px;
		width: 100%;
		height:95px;
		margin-right:0;		
	}
	.category-image-gallery .grid {
		text-align:left;
	}
	.category-image-gallery figure:hover h2 {
		padding-top:27px;
	}
	.pic-space{
		margin:20px 0 0 0;
	}
	.btn-space{
		margin-bottom:20px;
	}
	.hovereffect .overlay{
		padding:0px 0px;
	}
	.new-arrivals .main-width .nav-link{
		margin:0 15px;
	}
	.show-page-filter{
		font-size:13px;		
		margin:0 0 0 10px;
	}
	.result-filter .fil-btn{
		vertical-align: middle;	
		padding:10px 15px;
	}
	.sorting .btn{
		width:100%;
		padding: .840rem 0 .840rem .75rem;
		margin:15px 0 0 0;
	}
	.sorting .btn .bi {
		margin-left: 127px;
	}
	.overlay .icon-links a, .overlay button{		
		padding-top:10px;
	}
	.breadcrumb{
		padding:0 15px;
	}
	.product-bill-list{
		padding-left:15px;
		text-align: center;
	}
	.cart-page h2{
		font-size:38px;
		text-align:center;
		margin:0 0 40px 0;
	}
	.user-product-details .user-pro-img{
		width:175px;
		height:auto;		
		margin:0 auto;
	}
	.total-amount h3{
		text-align:center;
	}
	.user-product-details h3{
		padding:15px 0 0 0;
		font-size:20px;
	}
	.add-coupon .input-group{
		margin-right:15px;
	}
	.add-coupon .btn-lg{
		font-size:12px;
		padding:10px 13px;
	}
	.continue-shop a{
		font-size:15px;
		margin:0px 0 20px 8px;
	}
	.user-product-details .main-price{
		font-size: 20px;
	}
	.user-product-details .re-link{
		font-size: 18px;
	}
	.proce-che-tax ul li label{
		width:49%;
	}
	.proce-che-tax ul li span{
		text-align:right;
		width:49%;
	}
	.continue-shop{
		text-align:center;
	}
	.proceed-check .btn-lg{
		margin-bottom:20px;
	}
	.day-to-day{
		margin:20px 0 0 0;
	}
	.proceed-check{
		max-width:100%;
		margin-left:0px;
		max-height:unset;
		margin-top: 30px;
	}
	.saved-amount{
		padding-bottom:15px;
		font-size: 16px;
	}
	.fes-categories .product-tabs .tab-content p{		
		margin:20px 0 0 0;
	}
	.product-tabs .color-chart tr td{
		font-size:13px;		
	}
	.product-tabs .size-chart tr th,
	.product-tabs .size-chart tr td{
		font-size:13px;
	}
	.navigation-offcanvas-list .festtema-main-category{	
		font-size:1.3rem;		
	}
	.round-box{
		width:125px;
	}
	/* Hiren changes 24/03/2023 */
	.aa-ItemIcon--picture {
			height:278px;
	}
}	
@media (min-width:360px) {
	.main-width{
		width:auto;
	}
	.main-width .nav-link{
		padding:10px 15px;
		margin:0 7px 10px 7px;
		font-size:12px;
	}
	.row{
		margin-left:0;
		margin-right:0;
	}
	.mw-faq-categories ul li{
		width: 47.4%;
		min-height: 343px !important;
	}
	.product-tabs .size-chart tr th,
	.product-tabs .size-chart tr td{
		font-size:13px;
	}
	.select-pay-cards .col-3{
		width:135px;
		margin:0 6px 10px 6px;
		flex: auto;
	}
	.sorting .btn .bi {
		margin-left: 30px;
	}
	.user-acc{
		padding:0 0 0 0;
	}
	.user-acc .bi{
		font-size:26px;
		color:#000;
	}
	.m-logo{
		padding-left:43px;
	}
	.btn-lg{
		font-size:14px;
	}
	.fea-images-one,.fea-images-two,.fea-images-three,
	.fea-images-four,.fea-images-five,.fea-images-six{
		width:285px;
		min-height:115px;
		margin:5px auto;
	}
	.categories-names{
		padding:2px 0;
	}
	.check-all-images .img-fluid{
		height:120px;
	}
	.trend-count .trend-box-details{
		padding:5px 5px 10px 10px;
	}
	.trend-box-bg .desk-view,.we-back .desk-view{
		display:none;
	}
	.trend-box-bg .mobile-view,.we-back .mobile-view{
		display:block;
		padding:0;
	}
	.check-out-new .carousel-control-next-icon,.check-out-new .carousel-control-prev-icon{
		width:30px;
		height:30px;
	}
	.category-image-gallery .grid figure {	
		// width: 278px;
		width: 100%;
		height:110px;
		margin-right:0;		
	}
	.category-image-gallery figure:hover h2 {
		padding-top:35px;
	}
	.sorting .dropdown{
		float:unset;
	}
	.show-page-filter{
		font-size:14px;		
		margin:0 0 0 10px;
	}
	.sorting .btn .bi {
		margin-left: 165px;
	}
	.add-coupon .input-group{
		margin-right:16px;
	}
	.add-coupon .btn-lg{
		font-size:16px;
		padding:10px 18px;
	}
	.continue-shop a{
		font-size:16px;
		margin:0 0 20px 10px;
	}
	.round-box{
		width:150px;
	}
}
@media (min-width:375px) {
	.product-tabs .size-chart tr th,.product-tabs .size-chart tr td{
		font-size:13px;
	}
	.select-pay-cards .col-3{
		width:140px;
		margin:0 8px 10px 8px;
		flex: auto;
	}
	.fea-images-one,.fea-images-two,.fea-images-three,
	.fea-images-four,.fea-images-five,.fea-images-six{
		width:315px;
		min-height:130px;
		margin:5px auto;
	}
	.check-all-images .img-fluid {
	  height:120px;
	}
	.categories-add{
		text-align:left;
	}
	.category-image-gallery .grid figure {	
		// width: 173px;
		width: 100%;
		height:116px;
		margin-left:0px;
	}
	.category-image-gallery figure:hover h2 {
		padding-top:45px;
	}
	.new-arrivals .main-width .nav-link{
		margin:0 1rem;
	}
	.sorting .dropdown{
		float:unset;
	}
	.sorting .btn .bi {
		margin-left: 180px;
	}
	.add-coupon .input-group{
		margin-right:26px;
	}
	.add-coupon .btn-lg{
		font-size:18px;
		padding:10px 12px;
	}
}
@media (min-width:390px) {
	.product-tabs .size-chart tr th,.product-tabs .size-chart tr td{
		font-size:13px;
	}
	.select-pay-cards .col-3{
		width:146px;
		margin:0 8px 10px 8px;
		flex: auto;
	}
	.fea-images-one, .fea-images-two, .fea-images-three, 
	.fea-images-four, .fea-images-five, .fea-images-six {
		margin:5px auto;
	}
	.categories-names {
	  	width:172px;
	}
	.check-all-images .img-fluid {
	  	height:128px;
	}
	.categories-add{
		text-align:center;
	}
	.category-image-gallery .grid figure {	
		// width: 180px;
		width: 95%;
		height:115px;
		margin-left:5px;
	}
	.category-image-gallery figure:hover h2 {
		padding-top:35px;
	}
	.sorting .dropdown{
		float:unset;
	}
	
	.show-page-filter{
		font-size:16px;		
		margin:0 0 0 10px;
	}
	.sorting .btn .bi {
		margin-left: 195px;
	}
}
@media (min-width:412px) {
	.product-tabs .size-chart tr th,.product-tabs .size-chart tr td{
		font-size:13px;
	}
	.category-image-gallery .grid figure {	
		// width: 185px;
		width: 100%;
		height:127px;
		margin-left:0px;
	}
	.category-image-gallery figure:hover h2 {
		height:127px;
		padding-top:70px;
	}
	.sorting .dropdown{
		float:unset;
	}
	.show-page-filter{
		font-size:14px;		
		margin:0 0 0 10px;
	}
	.sorting .btn .bi {
		margin-left: 215px;
	}
	.add-coupon .input-group{
		margin-right:30px;
	}
	.add-coupon .btn-lg{
		font-size:20px;
		padding: .5rem 1rem;
	}
	.continue-shop a{
		font-size:18px;
		margin:0 0 20px 10px;
	}
}
@media (min-width:414px) {	
	.product-tabs .size-chart tr th,.product-tabs .size-chart tr td{
		font-size:13px;
	}
	.select-pay-cards .col-3{
		width:146px;
		margin:0 8px 10px 8px;
		flex: auto;
	}
	.fea-images-one, .fea-images-two, .fea-images-three, .fea-images-four, .fea-images-five, .fea-images-six {
	  	margin:5px auto;
	}
	.categories-names {
	  	width:183px;
	}
	.check-all-images .img-fluid {
	  	height: 137px;
	}
	.category-image-gallery .grid figure {	
		// width: 185px;
		width: 95%;
		height:125px;
		margin-left:5px;
	}
	.category-image-gallery figure:hover h2 {
		padding-top:42px;
	}
	.new-arrivals .main-width .nav-link{
		margin:0 5px;
	}
	.sorting .dropdown{
		float:unset;
	}
	.sorting .btn .bi {
		margin-left: 215px;
	}
}
@media (min-width:480px) {	
	.category-image-gallery .grid figure figcaption h2 {
		font-size: 13px;
	}
	.main-width{
		width:auto;
	}
	.row{
		margin-left:0;
		margin-right:0;
	}
	.main-width .nav-link{
		padding:10px 15px;
		margin:0 7px 10px 7px;
		font-size:24px;
	}
	.fal-part img{		
		height:140px;
		width: auto;
	}
	.stroe-box p{
		font-size:14px;
	}
	.day-to-day .text-title {
	  padding: 0 15px 20px 15px;
	  max-width: 162px;
	  font-size: 13px;
	}
	.day-to-day .icon {
	  display: inline-block;
	  width: 40px;
	  height: 40px;
	  padding: 5px;
	}
	.day-to-day ul li {
	  display: inline;
	  margin: 0 0 30px 0;
	}
	.product-tabs .size-chart tr th,.product-tabs .size-chart tr td{
		font-size:13px;
	}
	.select-pay-cards .col-3{
		width:47%;
		margin:0 6px 10px 6px;
		flex: auto;
	}
	.user-acc{
		padding:0 0 0 0;
	}
	.user-acc .bi{
		font-size:26px;
		color:#000;
	}
	.m-logo{
		padding-left:43px;
	}
	.btn-lg{
		font-size:14px;
	}
	.fea-images-one,.fea-images-two,.fea-images-three,
	.fea-images-four,.fea-images-five,.fea-images-six{
		width:380px;
		min-height:185px;
		margin:7px auto;
	}
	.featured-categories{
		padding:0 20px 0 0px;
	}
	.categories-names{
		padding:5px 0;
	}
	.check-all-images .img-fluid{
		height:172px;
	}
	.trend-count .left-box img{
		border-radius: 10px;
		border-bottom-right-radius: 0;
		border-bottom-left-radius: 0;	
	}
	.trend-count .trend-box-details{
		padding:8px 0 5px 15px;
	}
	.trend-box-bg .desk-view,.we-back .desk-view{
		display:none;
	}
	.trend-box-bg .mobile-view,.we-back .mobile-view{
		display:block;
		padding:0;
	}
	.trend-count .trend-box-details p{
		font-size:13px;
	}
	.check-out-new .carousel-control-next-icon,.check-out-new .carousel-control-prev-icon{
		width:30px;
		height:30px;
	}
	.category-image-gallery .grid figure {	
		// width: 218px;
		width: 95%;
		height:145px;
		margin-left:5px;
	}
	.category-image-gallery figure:hover h2 {
		padding-top:60px;
	}
	.new-arrivals .main-width .nav-link{
		margin:0 9px;
	}
	.sorting .dropdown{
		float:unset;
	}
	.sorting .btn .bi {
		margin-left: 285px;
	}
	.product-bill-list {
	  padding-left: 15px;
	  text-align: center;
	}
	.add-coupon .input-group{
		margin-right:60px;
	}
	.add-coupon .btn-lg{
		font-size:20px;
		padding: .5rem 1.5rem;
	}
}
@media (min-width: 540px) {	
	.product-name{
		font-size: 15px;
		line-height: 22px;
		min-height: 45px;
	}
	.box-fes-bg{
		min-height: 210px;
	}
	.product-image{
		max-height: 200px;
		width:auto;
	}
	.subcategory-navigation-entry{
		width: 195px;
	}
	.day-to-day .text-title {
	  padding: 0 20px 20px 20px;
	  max-width: 192px;
	  font-size: 15px;
	}
	.product-tabs .size-chart tr th,.product-tabs .size-chart tr td{
		font-size:13px;
	}
	.select-pay-cards .col-3{
		width:47%;
		margin:0 6px 10px 6px;
		flex: auto;
	}
	.user-acc .bi {
	  font-size: 22px;
	  color: #000;
	}
	.fea-images-one, .fea-images-two, .fea-images-three, .fea-images-four, .fea-images-five, .fea-images-six {
	  margin:10px auto;
	}
	.categories-names {
	  width:238px;
	}
	.check-all-images .img-fluid {
	  height: 182px;
	}
	.category-image-gallery .grid figure {	
		// width: 172px;
		width: 96%;
		height:160px;
		margin-left:5px;
	}
	.category-image-gallery figure:hover h2 {
		padding-top:58px;
	}
	.hovereffect .overlay{
		padding:0px 50px;
	}
	.theme-img-gallery .left-space{
		margin-left:10px;
	}
	.sorting .dropdown{
		float:unset;
	}
	.show-page-filter{
		font-size:20px;		
		margin:0 0 0 20px;
	}
	.result-filter .fil-btn{
		vertical-align: middle;	
		padding:12px 45px;
	}
	.sorting .btn .bi {
		margin-left: 345px;
	}
	.cart-page h2{
		font-size:38px;
		text-align:center;
		margin:0 0 50px 0;
	}
	.add-coupon .btn-lg{
		font-size:20px;
		padding: .5rem 1rem;
	}
}
@media (min-width: 576px) {
	
	.header-logo-main{
		position: relative;
    	right: -15%;
	}
	.navigation-offcanvas-overlay.has-transition{
		width: 50%;
	}
	.navigation-offcanvas-placeholder{
		width: 360px;
	}
	.navigation-offcanvas .offcanvas-content-container {
		width: 360px;
	}
	.aa-Panel {
		top: 108px !important;
	}
	.header-actions-col .bi{
		font-size:24px;
	}
	.header-row {
		padding-bottom: 0.1rem;
	}
	.header-logo-col {
		padding-bottom: 5px;
		margin-bottom: 0;
	}
	.trend-count .left-box img {
		border-radius: 10px;
		border-top-right-radius: 0;
		border-bottom-right-radius: 0;
	}
	.trend-count .right-box img {
		border-radius: 10px;
		border-top-right-radius: 0;
		border-bottom-right-radius: 0;
	}
	.subcategory-navigation-entry{
		width: 183px;
	}
	.modal-dialog {
		max-width: 570px;
		margin: 1.75rem auto;
	}
	.category-image-gallery .grid figure {	
		// width: 154px;
		width: 97%;
		height: 118px;
		margin-left: 2px;
	}
	.category-image-gallery figure:hover h2 {
		padding-top:45px;
	}
	.is-act-confirmpage .checkout .checkout-additional, 
	.is-ctl-accountorder .checkout .checkout-additional{
		flex: 100%;
		max-width: 100%;
	}
	.theme-img-gallery li {
		padding: 0 5px;
	}
	.day-to-day ul li {
		display: inline-block;
	}
}
@media (min-width:681px) and (max-width:991px){
	.aa-Source[data-autocomplete-source-id="products"] .aa-List {
	  grid-template-columns: repeat(3,1fr);
	}
}
@media (min-width: 667px) {
	.main-width {
		width: auto;
	}
	.container, .container-sm {
		max-width:unset;
	}
	.subcategory-navigation-entry{
		max-width: 214px;
	}
	.product-tabs .color-chart tr td{
		font-size:18px;		
	}
	.product-tabs .size-chart tr th,.product-tabs .size-chart tr td{
		font-size:18px;
	}
	.select-pay-cards .col-3{
		width:47%;
		margin:0 6px 15px 6px;
		flex: auto;
	}
	.user-acc .bi {
	  font-size: 22px;
	  color: #000;
	}
	.fea-images-one, .fea-images-two, .fea-images-three, .fea-images-four, .fea-images-five, .fea-images-six {
	  margin: 15px 0 0 0;
	}
	.categories-names {
	  width:302px;
	}
	.check-all-images .img-fluid {
	  height: 229px;
	}
	.category-image-gallery .grid figure {	
		// width: 154px;
		width: 97%;
		height: 138px;
		margin-left: 3px;
	}
	.category-image-gallery figure:hover h2 {
		padding-top:60px;
	}
	.btn-space{
		margin-bottom:0px;
	}
	.hovereffect .overlay{
		padding:0px 72px;
	}
	.theme-img-gallery .left-space{
		margin-left:10px;
	}
	.sorting .dropdown{
		float:right;
	}
	.show-page-filter{
		font-size:13px;		
		margin:0 0 0 10px;
	}
	.result-filter .fil-btn{
		vertical-align: middle;	
		padding:10px 15px;
	}
	.sorting .btn{
		width:unset;
		padding: .840rem 0 .840rem .75rem;
		margin:0px 0 0 0;
	}
	.sorting .btn .bi {
		margin-left:75px;
	}
	.product-bill-list{
		padding-left:15px;
		text-align:left;
	}
	.cart-page h2{
		font-size:38px;
		text-align:left;
		margin:0 0 50px 0;
	}
	.user-product-details .user-pro-img{
		width:150px;
		height:auto;
		margin:0;
	}
	.total-amount h3{
		text-align:left;
	}
	.user-product-details h3{
		padding:0 0 0 0;
	}
	.add-coupon .input-group{
		margin-right:20px;
	}
	.add-coupon .btn-lg{
		font-size:15px;
		padding:10px;		
	}
	.continue-shop a{
		font-size:16px;
		margin:0 0 0 10px;
	}
	.continue-shop{
		text-align:right;
	}
	.navigation-offcanvas-list .festtema-main-category{	
		font-size:1.5rem;		
	}
}
@media (min-width:740px) {	
	.header-logo-main{
		position: unset;
	}
	.subcategory-navigation-entry{
		width: 238px;
	}
	.product-tabs .size-chart tr th,.product-tabs .size-chart tr td{
		font-size:18px;
	}
	.select-pay-cards .col-3{
		width:23%;
		margin:0 6px 15px 6px;
		flex: auto;
	}
	.category-image-gallery .grid figure {	
		// width: 172px;
		width: 97%;
		height:155px;
		margin-left:2px;
	}
	.category-image-gallery figure:hover h2 {
		padding-top:58px;
	}
	.btn-space {
		margin:0px 0 20px 0;
	}
	.theme-img-gallery .left-space{
		margin-left:10px;
	}
	.sorting .dropdown{
		float:right;
	}
	.show-page-filter{
		font-size:15px;		
		margin:0 0 0 10px;
	}
	.result-filter .fil-btn{
		vertical-align: middle;	
		padding:10px 25px;
	}
	.sorting .btn{
		width:auto;
		padding: .840rem 0 .840rem .75rem;
		margin:0;
	}
	.sorting .btn .bi {
		margin-left: 30px;
	}
	.breadcrumb{
		padding:0;
	}
	.user-product-details .user-pro-img{
		width:100px;
		height:auto;
		margin:0;
	}
	.add-coupon .input-group{
		margin-right:30px;
	}
	.add-coupon .btn-lg{
		font-size:16px;
		padding: .5rem 1rem;
		margin-bottom:0;
	}
	.continue-shop a{
		font-size:18px;
		margin:0 0 0 10px;
	}
	.proceed-check{
		max-width:100%;
		margin-left:0px;
		max-height:unset;
		margin-top:0px;
	}
	.hovereffect .overlay {
	  padding: 10px 90px 0 90px;
	}
}
@media (min-width: 768px) {
	.product-slider-item .product-name {
		font-size: 16px;
	}
	.topbar-discount-text .tns-item{
		font-size: 14px;
	}
	.alert-netzhirsch-order-countdown .alert-content,
	.product-detail-delivery-information .alert-content{
		font-size: .875rem;
	}
	.gallery-slider-col{
		padding-left: 20px;
		padding-right: 20px;
	}
	.breadcrumb a {
		font-size: 1rem;
	}
	.breadcrumb .breadcrumb-placeholder{
		margin: 0 0.5rem;
	}
	.desktop-view{
		display:block;
	}
	.main-width{
		width:100%;
		margin:0 auto;
		padding:0 15px;
		max-width:unset;
	}
	.mobile-view{
		display:none;
	}
	.aa-Panel {
		top: 115px !important;
	}
	.navbar {
		padding: 2px 0 !important;
	}
	.fl-side{
		float:none;
	}
	.stroe-box{
		min-height:340px;
		text-align:center;
		padding:10px;
		margin-bottom:0px;
	}
	.side-height{
		min-height:unset; 
		float:none;
	}
	.stroe-box p{
		font-size:16px;
		float:none;
	}
	.about-store .stroe-box .title-two{
		font-size:20px;
		padding:20px 0 0 0;
	}
	.stroe-box img{
		float:none;
		margin-right:0;
	}
	.stroe-box .part-1{
		height: 60px;
		width: 60px;
	}
	.stroe-box .part-2{
		height: 60px;
		width: 71px;
	}
	.visit-box p{
		font-size:14px;
	}
	.visit-box{
		text-align:center;
		padding:15px;
		margin:20px 0 0 0;
		min-height:220px;
		border-top:10px solid $sw-color-brand-primary;	
	}
	.visit-box img{
		float:none;
		height:60px;
		width: 61px;
		margin:0;
	}
	.visit-box h3{
		text-align:center;
		padding-top: 10px;
    	padding-bottom: 10px;		
	}
	.info-laptop{
		float:right;
		margin-right:40px;
		height:110px;
		width:148px;
		padding:0;
	}
	.notification-part img{		
		margin-right:40px;
		height:127px;
		width:123px;
	}
	.cms-element-product-listing .cms-listing-col {
		margin-bottom: 10px;
	}
	.categ-in-title h3{
		margin: 18px 0 0px 0;
	}
	.product-price {
		font-size: 24px;
	}
	.product-name{
		font-size: 16px;
	}
	.categ-in-title p {
		margin: 5px 0 5px 0;
	}
	.product-detail-name{
		font-size:24px;
		margin-bottom: 5px;
		line-height: 30px;
	}
	.indeholder-title {
		margin-top: 5px;
	}
	.navbar {
		padding: 5px 0;
	}
	.subcategory-navigation-entry{
		width: 178px;
	}
	.homepage-title{
		font-size:30px;
		padding: 12px 0 0 0;
	}
	.sub-title{
		padding-top: 8px;
	}
	.cookie-permission-container .btn{
		line-height: 34px;
	}
	.we-back h3{
		font-size:23px;
		line-height:30px;
	}
	.we-back p{
		font-size:18px;
		line-height:24px;
		max-width:950;
		padding-top: 30px;
	}
	.we-back img{
		width: auto;
		height: auto;
	}
	.visit-store img{
		padding-top:0;
		padding-bottom:0;
	}
	.store-box-icon{
		max-width: 100%;
		width: auto;
		height: auto;
	}
	.about-store .title-two{
		font-size: 24px;
		line-height: 28px;
	}
	.ft-product-tabs-fix {
		width: 600px;
		margin: 0 auto;
	}
	.day-to-day .text-title {
	  padding: 0 5px 20px 5px;
	  max-width: 118px;
	  font-size: 13px;
	}
	.day-to-day .icon {
	  display: inline-block;
	  width: 40px;
	  height: 40px;
	  padding: 5px;
	}
	.day-to-day ul li {
	  display: inline;
	  margin: 0 0 30px 0;
	}
	.is-act-cartpage .checkout .checkout-aside {
		margin-left: 0;
	}
	.media-discription{
		padding-left:20px;
		padding-top:0;
	}
	.footer-column-headline {
        padding: 0rem 0 0.5rem 0;
    }
	.product-tabs .size-chart tr th,.product-tabs .size-chart tr td{
		font-size:18px;
	}
	.select-pay-cards .col-3{
		width:44%;
		margin:0 6px 15px 6px;
		flex: auto;
	}
	.sorting .btn .bi {
		margin-left: 30px;
	}
	.custom-menu-bar .navbar-toggler{
		position: absolute;
		top:18px;
		left:-20px;
	}
	.every-need h1{
		font-size:53px;
	}
	.categ-add-text h1{
		font-size:53px;
        line-height: 57px;
	}
	.every-need{
		padding:50px 0;
	}
	.every-need span{
		font-size:16px;
	}
	.every-need p{
		font-size:16px;
	}
	.btn-lg{
		font-size:20px;
	}
	.sub-title{
		font-size:20px;
	}
	.fea-images-one,.fea-images-two,.fea-images-three,
	.fea-images-four,.fea-images-five,.fea-images-six{
		width:270px;
		min-height:197px;
		margin:20px 0 0 0;
	}
	.categories-names h3{
		font-size:18px;
	}
	.featured-categories{
		padding: 0 15px 0 0;
	}
	.categories-names{
		padding:8px 0;
	}
	.multi-img{
		margin:0;
	}
	.check-all-images{
		margin:60px 0 0 0;
	}
	.main-width .nav-link{
		padding: .5rem .5rem;
		margin:0 7px;
	}
	.check-all-images .img-fluid{
		height:auto;
	}
	.range-details h3{
		font-size:38px;
		padding:0;
	}
	.trend-count .left-box img{
		border-top-left-radius:10px;
		border-bottom-left-radius:10px;
		border-top-right-radius:0px;
		border-bottom-right-radius:0px;
	}
	.trend-count .trend-box-details{
		padding:10px 0 0px 25px;
	}
	.trend-box-bg .desk-view{
		display:block;
	}
	.trend-box-bg .mobile-view{
		display:none;
	}
	.trend-count .right-box img{
		border-top-right-radius:10px;
		border-bottom-right-radius:10px;
		border-top-left-radius:0px;
		border-bottom-left-radius:0px;
	}
	.trend-count .trend-box-details p{
		font-size:16px;
	}
	.trend-count .trend-box-details .btn {
		font-size: 18px;
		line-height: 38px;
		padding: 2px 12px;
	}
	.e-market img{
		margin:0 0 0 0px;
	}
	.range-details p{
		font-size:20px;
		max-width:630px;
	}
	.trend-count .trend-box-details p{
		max-width:400px;
	}
	.visit-box p{
		max-width:200px;
		margin:0 auto;
	}
	.check-out-new .carousel-control-prev{
		left:0;
	}
	.check-out-new .carousel-control-next{
		right:0;
	}
	.check-out-new .carousel-control-next-icon,.check-out-new .carousel-control-prev-icon{
		width:54px;
		height:54px;
	}
	.range-details{
		padding:120px 0 130px 0;
	}
	.footer ul{
		padding:20px 0 0 0;
	}
	.footer h4{
		margin:0;
	}
	.bran-logo-part .img-fluid{
		max-width:75%;
		height: auto;
	}
	.visit-box h3{
		font-size:23px;
	}
	.categories-add{
		text-align:right;
		padding-right:0;
	}
	.categ-add-text .every-need{
		padding:30px 0 30px 20px;
	}
	.box-fes-bg{
		width:100%;
	}
	.category-image-gallery .grid figure {	
		// width:178px;
		width: 100%;
		height:119px;
		margin-left:0;
	}
	.category-image-gallery figure:hover h2 {
		height:119px;
		padding-top:33px;
	}
	.pic-space{
		margin:0;
	}
	.hovereffect .overlay{
		padding:10px 5px 0 5px;
	}
	.theme-img-gallery .left-space{
		margin-left:10px;
	}
	.sorting .dropdown{
		float:right;
	}
	.show-page-filter{
		font-size:18px;		
		margin:0 0 0 20px;
	}
	.result-filter .fil-btn{
		vertical-align: middle;	
		padding:10px 45px;
	}
	.add-coupon .input-group{
		margin-right:60px;
	}
	.add-coupon .btn-lg{
		font-size:18px;
		padding: .5rem 1rem;
	}
	.continue-shop a{
		font-size:16px;
		margin:0 0 0 0px;
	}
	.user-product-details h3{
		font-size:15px;
	}
	.user-product-details .main-price{
		font-size: 15px;
	}
	.user-product-details .re-link{
		font-size: 13px;
	}
	.proce-che-tax ul li label{
		width:100%;
	}
	.proce-che-tax ul li span{
		text-align:left;
		width:100%;
	}
	.proceed-check .btn-lg{
		padding:12px;
		font-size:16px;
		margin-bottom:0;
	}
	.continue-shop{
		text-align: left;
	}
	.day-to-day{
		margin:0px 0 0 0;
	}
	.proceed-check{
		max-width:33.33333333%;
		margin-left:0px;
		max-height:630px;
		margin-top:0px;
	}
	.fes-categories .product-tabs .tab-content p{		
		margin:0;
	}
	.order-item-detail .order-item-total{
		text-align: center;
	}
	.order-item-product-image .product-image-link {
		justify-content: center;
	}
	.aa-ItemIcon--picture{
        height:378px;
    }
}
@media (min-width:820px) {	
	.subcategory-navigation-entry{
		width: 191px;
	}
	.product-tabs .size-chart tr th,.product-tabs .size-chart tr td{
		font-size:18px;
	}
	.hovereffect .overlay {
	  padding: 10px 10px 0 10px;
	}
	.select-pay-cards .col-3{
		width:22%;
		margin:0 6px 15px 6px;
		flex: auto;
	}
	.category-image-gallery .grid figure {	
		// width: 190px;
		width: 100%;
		height:128px;
		margin-left:0;
	}
	.category-image-gallery figure:hover h2 {
		height:128px;
		padding-top:40px;
	}
	.theme-img-gallery .left-space{
		margin-left:10px;
	}
	.sorting .dropdown{
		float:right;
	}
	.show-page-filter{
		font-size:20px;		
		margin:0 0 0 20px;
	}
	.user-product-details .user-pro-img{
		width:100px;
		height:120px;
		margin:0;
	}
	.continue-shop{
		text-align: right;
	}
	.proceed-check{
		max-width:33.33333333%;
		margin-left:0px;
		max-height:580px;
		margin-top:0px;
	}
}
@media (min-width:844px) {	
	.subcategory-navigation-entry{
		width: 197px;
	}
	.product-tabs .size-chart tr th,.product-tabs .size-chart tr td{
		font-size:18px;
	}
	.hovereffect .overlay {
	  padding: 10px 14px 0 14px;
	}
	.select-pay-cards .col-3{
		width:44%;
		margin:0 6px 15px 6px;
		flex: auto;
	}
	.category-image-gallery .grid figure {	
		// width: 197px;
		width: 100%;
		height:133px;
		margin-left:0;
	}
	.category-image-gallery figure:hover h2 {
		height:133px;
		padding-top:55px;
	}
	.theme-img-gallery .left-space{
		margin-left:10px;
	}
	.sorting .dropdown{
		float:right;
	}
	.user-product-details .user-pro-img{
		width:100px;
		height:auto;
		margin:0;
	}
	.add-coupon .input-group{
		margin-right:60px;
	}
	.proceed-check{
		max-width:33.33333333%;
		margin-left:0px;
		max-height:585px;
		margin-top:0px;
	}
	.saved-amount{
		padding-bottom:0;
	}
	.saved-amount{
		padding-bottom:15px;
		font-size: 16px;
	}
	.custom-menu-bar .navbar-toggler{
		position: absolute;
		top: 20px;
		left:-15px;
	}
}
@media (min-width:896px) {
	.topbar-social-icons{
		display: block;
	}
	.subcategory-navigation-entry{
		width: 210px;
	}
	.product-tabs .size-chart tr th,.product-tabs .size-chart tr td{
		font-size:18px;
	}
	.hovereffect .overlay {
	  padding: 10px 20px 0 20px;
	}
	.select-pay-cards .col-3{
		width:44%;
		margin:0 6px 15px 6px;
		flex: auto;
	}
	.category-image-gallery .grid figure {	
		// width: 210px;
		width: 100%;
		height:140px;
		margin-left:0;
	}
	.category-image-gallery figure:hover h2 {
		height:140px;
		padding-top:60px;
	}
	.theme-img-gallery .left-space{
		margin-left:10px;
	}
	.sorting .dropdown{
		float:right;
	}
	.show-page-filter{
		font-size:20px;		
		margin:0 0 0 20px;
	}
	.overlay .icon-links a, .overlay button{		
		padding-top:0px;
	}
	.user-product-details .user-pro-img{
		width:120px;
		height:auto;
		margin:0;
	}
	.add-coupon .input-group{
		margin-right:10px;
	}
	.add-coupon .btn-lg{
		font-size:15px;
		padding: .5rem 1rem;
	}
	.continue-shop a{
		font-size:11px;
		margin:0 0 0 10px;
	}
	.user-product-details h3{
		font-size:18px;
	}
	.user-product-details .main-price{
		font-size: 20px;
	}
	.user-product-details .re-link{
		font-size: 18px;
	}
	.proce-che-tax ul li span{
		text-align:right;
		width:49%;
	}
	.proceed-check{
		max-width:33.33333333%;
		margin-left:0px;
		max-height:538px;
		margin-top:0px;
	}
	.custom-menu-bar .navbar-toggler {
		position: absolute;
		top: 18px;
		left: -15px;
	}
}
@media (min-width: 980px) { 	
	.aa-Panel {
		top: 110px !important;
	}
	.subcategory-navigation-entry{
		width: 231px;
	}
	.ft-product-tabs-fix{
		width: 820px;
		margin: 0 auto;
	}
	.new-arrivals .nav-tabs .nav-item .nav-link,.new-arrivals .nav-tabs .nav-link.active{
		font-size:20px;
	}
	.day-to-day .text-title {
	  padding: 0 15px 20px 15px;
	  max-width: 162px;
	  font-size: 14px;
	}
	.product-tabs .size-chart tr th,.product-tabs .size-chart tr td{
		font-size:18px;
	}
	.select-pay-cards .col-3{
		width:127px;
		margin:0 6px 15px 6px;
		flex: auto;
	}
	.main-width{
		width:100%;
		margin:0 auto;
		padding:0 15px;
		max-width:unset;
	}
	.mobile-view{
		display:none;
	}
	.every-need{
		padding:117px 0;
	}
	.btn-lg{
		font-size:20px;
	}
	.fea-images-one,.fea-images-two,.fea-images-three,
	.fea-images-four,.fea-images-five,.fea-images-six{
		width:294px;
		height:220px;
		margin:20px 0 0 0;
	}
	.featured-categories{
		padding:0;
	}
	.main-width .nav-link{
		padding: .5rem 1.5rem;
		margin:0 7px;
	}
	.trend-count .left-box img{
		border-top-left-radius:10px;
		border-bottom-left-radius:10px;
		border-top-right-radius:0px;
		border-bottom-right-radius:0px;
	}
	.trend-count .trend-box-details{
		padding:25px 0 0px 60px;
	}
	.trend-box-bg .desk-view{
		display:block;
	}
	.trend-box-bg .mobile-view{
		display:none;
	}
	.trend-count .trend-box-details p{
		font-size:20px;
	}	
	.e-market img{
		margin:0 0 0 60px;
	}
	.range-details p{
		font-size:20px;
		max-width:630px;
	}
	.trend-count .trend-box-details p{
		max-width:400px;
	}
	.visit-box p{
		max-width:200px;
		margin:0 auto;
	}
	.check-out-new .carousel-control-next-icon,.check-out-new .carousel-control-prev-icon{
		width:54px;
		height:54px;
	}
	.bran-logo-part .img-fluid{
		max-width:100%;
		height: auto;
	}
	.visit-box h3{
		font-size:24px;
	}
	.categ-add-text .every-need{
		padding:30px 0 30px 20px;
	}
	.box-fes-bg{
		width:100%;
	}
	.category-image-gallery .grid figure {	
		// width: 231px;
		width: 100%;
		height:155px;
		margin-left:0;
	}
	.category-image-gallery figure:hover h2 {
		height:155px;
		padding-top:70px;
	}
	.hovereffect .overlay{
		padding:10px 30px 0 30px;
	}
	.new-arrivals .main-width .nav-link{
		margin:0 2rem;
	}
	.theme-img-gallery .left-space{
		margin-left:10px;
	}
	.sorting .dropdown{
		float:right;
	}
	.sorting .btn .bi {
		margin-left: 30px;
	}
	.product-bill-list{
		padding-left:30px;
		text-align:left;
	}
	.user-product-details .user-pro-img{
		width:150px;
		height:auto;
		margin:0;
	}
	.add-coupon .btn-lg{
		font-size:18px;
		padding: .5rem 1rem;
	}
	.continue-shop a{
		font-size:13px;
		margin:0 0 0 5px;
	}
	.user-product-details h3{
		font-size:20px;
	}
	.proce-che-tax ul li label{
		width:49%;
	}
	.proceed-check .btn-lg{
		padding: .5rem 3rem;
		font-size:16px;
		margin-bottom:0;
	}
	.saved-amount{
		padding-bottom:0;
		font-size: 18px;
	}
}
@media (min-width: 992px) {
	.ngs--free-delivery-remaining-amount-container-desktop{
		display: block;
	}
	.e-market-header{
		display: block;
	}
	.main-logo-ft{
		height: 74px;
		width: 224px;
	}
	.header-logo-main{
		padding-left: 15px;
	}
	.header-logo-col {
		padding-bottom: 0px;
	}
	.mw-faq-categories ul li {
		width: 22.5%;
		min-height: 260px !important;
	}
	.subcategory-navigation-entry{
		width: 187px;
	}
	.is-act-confirmpage .checkout .checkout-additional, 
	.is-ctl-accountorder .checkout .checkout-additional{
		flex: 0 0 33.**********%;
    	max-width: 33.**********%;
	}
	.cart-additonal-info-block{
		flex: 0 0 33.**********%;
		max-width: 33.**********%;
		padding: 0;
	}
	.opening-hours{
		max-width: 100%;
		margin:0;
	}
	.product-tabs .size-chart tr th,.product-tabs .size-chart tr td{
		font-size:18px;
	}
	.select-pay-cards .col-3{
		width:129px;
		margin:0 6px 15px 6px;
		flex: auto;
	}
	.main-width{
		width:100%;
		margin:0 auto;
		padding:0 15px;
	}
	.mobile-view{
		display:none;
	}
	.categ-add-text h1{
		font-size:36px;
        line-height: 41px;
	}
	.every-need{
		padding:117px 0;
	}
	.btn-lg{
		font-size:20px;
	}
	.fea-images-one,.fea-images-two,.fea-images-three,
	.fea-images-four,.fea-images-five,.fea-images-six{
		width:345px;
		min-height:240px;
		margin:20px 0 0 0;
	}
	.categories-names h3{
		font-size:20px;
	}
	.featured-categories{
		padding:0 15px 0 0;
	}
	.categories-names{
		padding:10px 0;
	}
	.main-width .nav-link{
		padding: .5rem 1.5rem;
		margin:0 7px;
	}
	.trend-count .left-box img{
		border-top-left-radius:10px;
		border-bottom-left-radius:10px;
		border-top-right-radius:0px;
		border-bottom-right-radius:0px;
	}
	.trend-count .trend-box-details{
		padding:25px 0 0px 60px;
	}
	.trend-box-bg .desk-view{
		display:block;
	}
	.trend-box-bg .mobile-view{
		display:none;
	}
	.trend-count .trend-box-details p{
		font-size:20px;
	}	
	.custom-menu-bar .navbar-toggler {
		position: absolute;
		top: 25px;
		left: -15px;
	}
	.e-market img{
		margin:0 0 0 60px;
	}
	.range-details p{
		font-size:20px;
		max-width:630px;
	}
	.trend-count .trend-box-details p{
		max-width:400px;
	}
	.visit-box p{
		max-width:200px;
		margin:0 auto;
	}
	.check-out-new .carousel-control-next-icon,.check-out-new .carousel-control-prev-icon{
		width:54px;
		height:54px;
	}
	.visit-box h3{
		font-size:24px;
	}
	.categ-add-text .every-need{
		padding:0px 0 30px 10px;
	}
	.box-fes-bg{
		width:100%;
	}
	.category-image-gallery .grid figure {	
		// width: 222px;
		width: 100%;
		height:126px;
		margin-left:0;
	}
	.category-image-gallery figure:hover h2 {
		height:126px;
		padding-top:42px;
	}
	.hovereffect .overlay{
		padding:10px 32px 0 32px;
	}
	.sorting .dropdown{
		float:right;
	}
	.sorting .btn .bi {
		margin-left: 30px;
	}
	.product-bill-list{
		padding-left:20px;
		text-align:left;
	}
	.add-coupon .input-group{
		margin-right:15px;
	}
	.add-coupon .btn-lg{
		font-size:20px;
		padding: .5rem 1rem;
	}
	.continue-shop a{
		font-size:13px;
		margin:0 0 0 7px;
	}
}
@media (min-width: 1024px) {
	.product-tabs .size-chart tr th,.product-tabs .size-chart tr td{
		font-size:18px;
	}
	.select-pay-cards .col-3{
		max-width:137px;
		margin:0 6px 15px 6px;
		flex: auto;
	}
	.category-image-gallery .grid{
		text-align:left;
	}
	.sorting .dropdown{
		float:right;
	}
	.sorting .btn .bi {
		margin-left: 30px;
	}
	.product-bill-list{
		padding-left:30px;
		text-align:left;
	}
	.add-coupon .input-group{
		margin-right:20px;
	}
	.continue-shop a{
		font-size:13px;
		margin:0 0 0 10px;
	}
	.hovereffect .overlay {
	  padding: 10px 36px 0 36px;
	}
	.fes-categories .product-tabs .tab-content p{		
		margin:0;
	}
	.navigation-offcanvas-list .festtema-main-category{	
		font-size:1.6rem;		
	}
}
@media (min-width: 1180px) {
	.day-to-day .text-title {
	  padding: 0 15px 20px 15px;
	  max-width:206px;
	  font-size: 18px;
	}
}
@media (min-width: 1200px) {
	.stroe-box{
		min-height:290px;
	}
	.subcategory-navigation-entry{
		width: 196px;
	}
    .day-to-day .icon{
		display:inline-block;
		width:50px;
		height:50px;
		padding:5px;
	}
	.day-to-day .text-title{
		padding:0 0 0 20px;
		max-width:200px;
		font-size:18px;
	}
	.day-to-day ul li{
		display:inline-block;
		margin:0 0 30px 0;
	}
	.product-tabs .size-chart tr th,.product-tabs .size-chart tr td{
		font-size:18px;
	}
	.select-pay-cards .col-3{
		max-width:158px;
		margin:0 10px 15px 10px;
		flex: auto;
	}
	.main-width{
		width:100%;
		margin:0 auto;
		padding:0 15px;
	}
	.mobile-view{
		display:none;
	}
	.categ-add-text h1{
		font-size:48px;
        line-height: 53px;
	}
	.every-need{
		padding:217px 0;
	}
	.btn-lg{
		font-size:20px;
	}
	.fea-images-one,.fea-images-two,.fea-images-three,
	.fea-images-four,.fea-images-five,.fea-images-six{
		width:420px;
		min-height:275px;
		margin:20px 0 0 0;
	}
	.categories-names h3{
		font-size:22px;
	}
	.featured-categories{
		padding: 0 15px 0 0;
	}
	.categories-names{
		padding:12px 0;
	}
	.main-width .nav-link{
		padding: .5rem 2rem;
		margin:0 7px;
	}
	.trend-count .left-box img{
		border-top-left-radius:10px;
		border-bottom-left-radius:10px;
		border-top-right-radius:0px;
		border-bottom-right-radius:0px;
	}
	.trend-count .trend-box-details{
		padding:60px  0 0 60px;
	}
	.trend-box-bg .desk-view{
		display:block;
	}
	.trend-box-bg .mobile-view{
		display:none;
	}
	.trend-count .trend-box-details p{
		font-size:20px;
	}
	.e-market img{
		margin:0 0 0 60px;
	}
	.range-details p{
		font-size:20px;
		max-width:630px;
	}
	.trend-count .trend-box-details p{
		max-width:400px;
	}
	.visit-box p{
		max-width:200px;
		margin:0 auto;
	}
	.check-out-new .carousel-control-next-icon,.check-out-new .carousel-control-prev-icon{
		width:54px;
		height:54px;
	}
	.visit-box h3{
		font-size:24px;
	}
	.categ-add-text .every-need{
		padding:10px 0 30px 10px;
	}
	.box-fes-bg{
		width:100%;
	}
	.category-image-gallery .grid figure {	
		// width: 272px;
		width: 100%;
		height:130px;
		margin-left:0;
	}
	.category-image-gallery figure:hover h2 {
		padding-top:42px;
	}
	.hovereffect .overlay{
		padding:10px 56px;
	}
	.sorting .dropdown{
		float:right;
	}
	.sorting .btn .bi {
		margin-left: 30px;
	}
	.user-product-details .user-pro-img{
		width:175px;
		height:auto;		
		margin:0;
	}
	.add-coupon .input-group{
		margin-right:60px;
	}
	.continue-shop a{
		font-size:16px;
		margin:0 0 0 10px;
	}
	.proceed-check .btn-lg{
		padding: .5rem 4rem;
		font-size:18px;
		margin-bottom:0;
	}
	.fes-categories .product-tabs .tab-content p{		
		margin:0;
	}
}
@media (min-width: 1400px) {
	.collapsible-content {
		min-height: 295px;
	}
	.mw-faq-category-description{
		font-size: 14px;
	}
	.mw-faq-category-headline {
		font-size: 1.4rem;
		margin: 10px 0;
	}
	.mw-faq-category-image img {
		height: 60px;
	}
	.mw-faq-categories ul li{
		width: 22.5%;
		min-height: 350px !important;
		margin: 20px 1%;
	}
	.fal-part img{
		margin-right:40px;
		height:330px;
		width: auto;
	}
	.visit-box p{
		font-size:18px;
	}
	.visit-box{
		text-align:center;
		padding:20px;
		margin:15px 0 0 0;
		min-height:unset;
	}
	.visit-box img{
		float:none;
		height: 100px;
    	width: 100px;
		margin:0;
	}
	.visit-box h3{
		font-size:24px;
		text-align:center;
		padding-top: 15px;
	}
	.homepage-title{
		font-size:34px;
	}
	.product-tabs .size-chart tr th,.product-tabs .size-chart tr td{
		font-size:18px;
	}
	.select-pay-cards .col-3{
		max-width:162px;
		margin:0 11px 15px 11px;
		flex: auto;
	}
	.main-width{
		width:1200px;
		margin:0 auto;
		padding:0;
	}
	.mobile-view{
		display:none;
	}
	.custom-menu-bar .navbar-toggler{
		position: absolute;
		top:28px;
		left:-90px;
	}
	.categ-add-text h1{
		font-size:53px;
        line-height: 57px;
	}
	.every-need{
		padding:217px 0;
	}
	.btn-lg{
		font-size:20px;
	}
	.fea-images-one,.fea-images-two,.fea-images-three,
	.fea-images-four,.fea-images-five,.fea-images-six{
		width:380px;
		margin:20px 0 0 0;
		min-height: 270px;
	}
	.featured-categories{
		padding:0;
	}
	.featured-categories .cms-element-image .cms-image-container{
		max-width: inherit;
	}
	.main-width .nav-link{
		padding: .5rem 2rem;
		margin:0 7px;
	}
	.trend-count .left-box img{
		border-top-left-radius:10px;
		border-bottom-left-radius:10px;
		border-top-right-radius:0px;
		border-bottom-right-radius:0px;
	}
	.trend-count .trend-box-details{
		padding:60px  0 0 60px;
	}
	.trend-box-bg .desk-view{
		display:block;
	}
	.trend-box-bg .mobile-view{
		display:none;
	}
	.trend-count .trend-box-details p{
		font-size:20px;
	}
	.e-market img{
		margin:0 0 0 60px;
	}
	.range-details p{
		font-size:20px;
		max-width:630px;
	}
	.trend-count .trend-box-details p{
		max-width:400px;
	}
	.check-out-new .carousel-control-prev{
		left:-6%;
	}
	.check-out-new .carousel-control-next{
		right:-6%;
	}
	.check-out-new .carousel-control-next-icon,.check-out-new .carousel-control-prev-icon{
		width:54px;
		height:54px;
	}
	.categ-add-text .every-need{
		padding:20px 0 0 10px;
	}
	.box-fes-bg{
		width:100%;
	}
	.hovereffect .overlay{
		padding:10px 60px;
	}
	.sorting .dropdown{
		float:right;
	}
	.sorting .btn .bi {
		margin-left: 30px;
	}
	.continue-shop a{
		font-size:18px;
		margin:0 0 0 10px;
	}
	.continue-shop a {
	  font-size: 16px;
	  margin: 0 0 0 10px;
	}
	.proceed-check{
		max-width:380px;
		margin-left:20px;
		max-height:538px;
		margin-top:0px;
	}
	.fes-categories .product-tabs .tab-content p{		
		margin:0;
	}
}
@media (min-width: 1900px) {
    .header-logo-main{
        float: left;
    }
	.main-width{
		width:1200px;
		margin:0 auto;
		padding:0;
	}
	.mobile-view{
		display:none;
	}
	.custom-menu-bar .navbar-toggler{
		position: absolute;
		top:32px;
		left:-114px;
	}
	.every-need{
		padding:217px 0;
	}
	.btn-lg{
		font-size:20px;
	}
	.homepage-title{
		font-size:35px;
	}
	.fea-images-one,.fea-images-two,.fea-images-three,
	.fea-images-four,.fea-images-five,.fea-images-six{
		width:380px;
		height:280px;
		margin:30px 0 0 0;
	}
	.categories-names h3{
		font-size:24px;
	}
	.featured-categories{
		padding:0;
	}
	.categories-names{
		padding:15px 0;
	}
	.main-width .nav-link{
		padding: .5rem 2rem;
		margin:0 7px;
	}
	.trend-count .left-box img{
		border-top-left-radius:10px;
		border-bottom-left-radius:10px;
		border-top-right-radius:0px;
		border-bottom-right-radius:0px;
	}
	.trend-count .trend-box-details{
		padding:60px  0 0 60px;
	}
	.trend-box-bg .desk-view{
		display:block;
	}
	.trend-box-bg .mobile-view{
		display:none;
	}
	.trend-count .trend-box-details p{
		font-size:20px;
	}
	.e-market img{
		margin:0 0 0 60px;
	}
	.range-details p{
		font-size:20px;
		max-width:630px;
	}
	.trend-count .trend-box-details p{
		max-width:400px;
	}
	.visit-box p{
		max-width:200px;
		margin:0 auto;
	}
	.check-out-new .carousel-control-next-icon,.check-out-new .carousel-control-prev-icon{
		width:54px;
		height:54px;
	}
	.visit-box h3{
		font-size:24px;
	}
	.categ-add-text .every-need{
		padding:110px 0 0 160px;
	}
	.box-fes-bg{
		width:100%;
	}
	.category-image-gallery .grid figure {	
		// width: 278px;
		width: 100%;
		height:175px;
		margin-left:0;
	}
	.category-image-gallery figure:hover h2 {
		height:175px;
		padding-top:77px;
	}
	.hovereffect .overlay{
		padding:10px 58px;
	}
	.sorting .dropdown{
		float:right;
	}
	.sorting .btn .bi {
		margin-left: 30px;
	}
	.continue-shop{
		text-align:right;
		padding-right:0;
	}
	.continue-shop a{
		font-size:18px;
		margin:0 0 0 8px;
	}
	.proceed-check{
		max-width:380px;
		margin-left:20px;
		max-height:558px;
		margin-top:0px;
	}
	.select-pay-cards .col-3{
		width:161px;
		margin:0 10px;
		flex: auto;
	}
	.fes-categories .product-tabs .tab-content p{		
		margin:0;
	}
	.product-tabs .size-chart tr th,.product-tabs .size-chart tr td{
		font-size:18px;
	}
	.navigation-offcanvas-list .festtema-main-category{	
		font-size: 1.8rem;		
	}
}
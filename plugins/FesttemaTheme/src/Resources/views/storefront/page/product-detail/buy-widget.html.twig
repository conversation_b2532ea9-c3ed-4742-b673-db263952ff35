{% sw_extends '@Storefront/storefront/page/product-detail/buy-widget.html.twig' %}

{% block page_product_detail_buy_inner %}
    <div class="js-magnifier-zoom-image-container">
        {% block page_product_detail_rich_snippets %}
            {{ parent() }}
        {% endblock %}

        {% block page_product_detail_buy_container %}
            <div itemprop="offers"
                 itemscope
                 itemtype="{% if page.product.calculatedPrices|length > 1 %}http://schema.org/AggregateOffer{% else %}http://schema.org/Offer{% endif %}">
                {% block page_product_detail_data %}
                    {% block page_product_detail_data_rich_snippet_url %}
                        {{ parent() }}
                    {% endblock %}

                    {% block page_product_detail_data_rich_snippet_price_range %}
                        {{ parent() }}
                    {% endblock %}

                    {% block page_product_detail_data_rich_snippet_price_currency %}
                        {{ parent() }}
                    {% endblock %}

                    {% block page_product_detail_price %}
                        {{ parent() }}
                    {% endblock %}

                    {% block page_product_detail_tax %}
                        {{ parent() }}
                    {% endblock %}

                {% endblock %}

                {% block page_product_detail_configurator_include %}
                    {{ parent() }}
                {% endblock %}

                {% block page_product_detail_buy_form %}
                    {{ parent() }}
                {% endblock %}
            </div>
        {% endblock %}

        {% block page_product_detail_ordernumber_container %}
            {% if page.product.productNumber %}
                <div class="product-detail-ordernumber-container">
                    {% block page_product_detail_ordernumber_label %}
                        {{ parent() }}
                    {% endblock %}

                    {% block page_product_detail_ordernumber %}
                        {{ parent() }}
                    {% endblock %}
                </div>
            {% endif %} 
        {% endblock %}

        {% if config('ZeobvBundleProducts')  %}
            {% if page.product.customFields.zeobvBundleProductsShowOnStorefront %}
                {% sw_include '@ZeobvBundleProducts/zeobv-bundle-products/components/bundle-product-info.html.twig' with {
                    products: page.product.extensions.zeobvBundleProducts.salesChannelProducts,
                    showPrices: page.product.customFields.zeobvBundleProductsShowItemPricesOnStorefront,
                    showTotalPrice: page.product.customFields.zeobvBundleProductsShowItemsTotalPriceOnStorefront
                } %}
            {% endif %}
        {% endif %}

        {% block page_product_detail_delivery_informations %}
            {% if config('AcrisStockNotificationCS') %}
                {% sw_include '@AcrisStockNotificationCS/storefront/component/stock-notification.html.twig' %}
            {% endif %}
        {% endblock %}
    </div>
{% endblock %}

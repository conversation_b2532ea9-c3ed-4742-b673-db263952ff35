{% sw_extends '@HuebertAccessoriesDirectlyBundling/storefront/page/product-detail/hueb-accessories-accordion.html.twig' %}


{% block hueb_accessories_accordion_card_header %}
    <div class="accordion-header" id="heading-{{ crossSelling.crossSelling.id }}">
        <div style="margin-bottom: -10px" class="row">
            <div class="col-lg-8">
                <div class="h4">
                    <button class="accordion-button shadow-none collapsed hue-accordion-btn btn-accordion"
                            type="button"
                            data-bs-toggle="collapse"
                            data-bs-target="#accessories-{{ crossSelling.crossSelling.id }}">
                        {{ crossSelling.crossSelling.name ?: huebert_load_parent_language_name(crossSelling.crossSelling, context.context) }}
                    </button>
                </div>
            </div>
            <div class="col-lg-4">
                {% block hueb_accessories_accordion_bundling %}
                    {% if crossSelling.crossSelling.huebBundle %}
                        {% sw_include '@HuebertAccessoriesDirectlyBundling/storefront/page/product-detail/hueb-bundle-header.html.twig' ignore missing %}
                    {% endif %}
                {% endblock %}
            </div>
        </div>
    </div>
{% endblock %}

{% block hueb_accessories_accordion_card_body %}
    <div id="accessories-{{ crossSelling.crossSelling.id }}" class="accordion-collapse collapse {% if page.acessoryOptions.alwaysShow %}show{% endif %}" aria-labelledby="heading-{{ crossSelling.crossSelling.id }}" data-bs-parent="#accessoryAccordion">
        {%  for product in products %}

            {% set isMarkedAsTagLink = false %}
            {% for tagId in product.tagIds %}
                {% if tagId in page.accessoryTagIds %}
                    {% set isMarkedAsTagLink = true %}
                {% endif %}
            {% endfor %}

            {% set huebQuantity = 0 %}
            {% set accessoryQuantity = 0 %}
            {% for assignedProduct in crossSelling.crossSelling.assignedProducts %}
                {% if assignedProduct.productId == product.id %}
                    {% set huebQuantity = assignedProduct.huebQuantity %}
                    {% set accessoryQuantity = assignedProduct.accessoryQuantity %}
                {% endif %}
            {% endfor %}
            {% if product.stock >= 0 or (product.isCloseout === false and product.stock <= 0) or product.productConfigurator|length > 0 %}
                {% set buyable = product.available and product.childCount <= 0 and product.calculatedMaxPurchase > 0 %}
                {% if buyable %}
                    {% if product.maxPurchase is null %}
                        {% set mp =  100 %}
                    {% else %}
                        {% set mp =  product.maxPurchase %}
                    {% endif %}
                    {% set accessoryConfig = {
                        accessory: {
                            productId: product.id,
                            minPurchase: product.minPurchase ,
                            maxPurchase: mp,
                            purchaseSteps: product.purchaseSteps
                        }
                    } %}

                    {% set pricing = null %}
                    {% set pricingTop = null %}
                    {% set referencePricing = null %}

                    {% set pricing %}
                        {% if product.calculatedPrices|length > 1 %}
                            {% set priceFrom = product.calculatedPrices|last %}
                            {% set priceTo = product.calculatedPrices|first %}
                            {% set loopPrice = product.calculatedPrices|first.unitPrice %}
                            {% for element in product.calculatedPrices %}
                                {% if element.unitPrice <= loopPrice %}
                                    {% set priceFrom = element %}
                                {% elseif element.unitPrice > loopPrice %}
                                    {% set priceTo = element %}
                                {% endif %}
                                {% set loopPrice = element.unitPrice %}
                            {% endfor %}

                            {{ priceFrom.unitPrice|currency }}
                            -
                            {{ priceTo.unitPrice|currency }}

                            {% set pricingTop = priceTo.unitPrice|currency %}
                            {% if priceFrom.referencePrice.price  %}
                                {% set referencePricing %}
                                    ({{ priceFrom.referencePrice.price|currency }}
                                    -
                                    {{ priceTo.referencePrice.price|currency }} / {{ priceFrom.referencePrice.referenceUnit }} {{ priceFrom.referencePrice.unitName }})
                                {% endset %}
                            {% endif %}
                        {% elseif product.calculatedPrices|length == 1 %}
                            {{ product.calculatedPrices.first.unitPrice|currency }}

                            {% set pricingTop = product.calculatedPrices.first.unitPrice %}
                            {% if product.calculatedPrices.first.referencePrice.price %}
                                {% set referencePricing %}
                                    ({{ product.calculatedPrices.first.referencePrice.price|currency }} / {{ product.calculatedPrices.first.referencePrice.referenceUnit }} {{ product.calculatedPrices.first.referencePrice.unitName }})
                                {% endset %}
                            {% endif %}

                        {% else %}
                            {{ product.calculatedPrice.unitPrice|currency }}
                            {% set pricingTop = product.calculatedPrice.unitPrice %}
                            {% if product.calculatedPrice.referencePrice.price %}
                                {% set referencePricing %}
                                    ({{ product.calculatedPrice.referencePrice.price|currency }} / {{ product.calculatedPrice.referencePrice.referenceUnit }} {{ product.calculatedPrice.referencePrice.unitName }})
                                {% endset %}
                            {% endif %}

                        {% endif %}
                    {% endset %}

                    {% block hueb_accessories_accordion_view %}
                        {% set cover = product.cover.media %}
                        {% set displayMode = 'minimal' %}

                        {% set viewClass = "col-xs-12 col-lg-8" %}
                        {% if page.HuebertAccessoriesDirectly.config.accessoryViewStatic %}
                            {% set viewClass = "col-xs-12 col-lg-6" %}
                            {% if config('HuebertAccessoriesDirectly.config.globalSelectableAmount') == '0' %}
                                {% set viewClass = "col-xs-12 col-lg-" %}
                            {% endif %}
                        {% else %}
                            {% if config('HuebertAccessoriesDirectly.config.globalSelectableAmount') == '0' %}
                                {% set viewClass = "col-xs-12 col-lg-9" %}
                            {% endif %}
                        {% endif %}
                        {% if crossSelling.crossSelling.huebBundle %}
                            {% set viewClass = "col-8" %}
                        {% endif %}

                        {% set selectableAmount = crossSelling.crossSelling.selectableAmount %}
                        {% if selectableAmount == 0 %}
                            {% set selectableAmount = false %}
                            {% set viewClass = "col-xs-12 col-lg-8" %}
                        {% endif %}
                        {% if selectableAmount == 1 %}
                            {% set selectableAmount = true %}
                            {% set viewClass = "col-xs-12 col-lg-7" %}
                        {% endif %}

                        {% if config('HuebertAccessoriesDirectly.config.globalSelectableAmount') == '0' %}
                            {% set selectableAmount = false %}
                            {% set viewClass = "col-xs-12 col-lg-10" %}
                        {% endif %}
                        {% if config('HuebertAccessoriesDirectly.config.globalSelectableAmount') == '1' %}
                            {% set selectableAmount = true %}
                            {% set viewClass = "col-xs-12 col-lg-9" %}
                        {% endif %}
                        <div class="huebert-accessory {% if selectableAmount %}hue-selectableAmount{% else %}hue-checkbox{% endif %}" data-product-id="{{ product.id }}" data-cross-selling-id="{{ crossSelling.crossSelling.id }}">
                            {% block hueb_accessories_accordion_view_hover %}
                                {% if page.HuebertAccessoriesDirectly.config.accessoryViewHover and cover.url %}
                                    <div class="huebert-accessory-hover">
                                        {% set attributes = {
                                            'class': 'product-image is-'~displayMode,
                                            'alt': (cover.translated.alt ?: name),
                                            'title': (cover.translated.title ?: name)
                                        } %}

                                        {% if displayMode == 'cover' or displayMode == 'contain' %}
                                            {% set attributes = attributes|merge({ 'data-object-fit': displayMode }) %}
                                        {% endif %}

                                        {% sw_thumbnails 'product-image-thumbnails' with {
                                            media: cover,
                                            sizes: {
                                                'xs': '501px',
                                                'sm': '315px',
                                                'md': '427px',
                                                'lg': '333px',
                                                'xl': '284px'
                                            }
                                        } %}
                                    </div>
                                {% endif %}
                            {% endblock %}

                            <div class="row">
                                <div class="{% if selectableAmount %}col-lg-3{% else %}col-lg-2{% endif %} p-lg-0 no-padding">

                                    <div class="row relative">

                                        <div class="col-12 no-padding">
                                            {% if selectableAmount == 1 %}
                                            <div class="viewport-small">
                                                {{ block('hueb_accessories_accordion_view_product_info') }}
                                            </div>
                                            {% endif %}
                                            <div class="row">
                                                {% set preselectedCheckboxes = config('HuebertAccessoriesDirectly.config.preselectedSelectableAmount') %}
                                                {% block hueb_accessories_accordion_view_amount %}
                                                    {% if not crossSelling.crossSelling.huebBundle %}
                                                        <div class="{% if selectableAmount %}col-8 col-sm-6 col-lg-12 pr-lg-0 {% else %}checkbox-mobile-wrapper col-lg-12{% endif %} padding-left-0">
                                                            <div class="input-group huebert-accessory_input-wrapper {{ not selectableAmount ? 'huebert-checkbox' }}" data-huebert-add-accessory-config="{{ accessoryConfig|json_encode }}" data-huebert-pricing="{{ pricingTop }}">
                                                                {% if selectableAmount %}
                                                                    {% if sameQuantityAsMainProduct is same as(null) or sameQuantityAsMainProduct is same as(false) %}
                                                                        {% set numberOfAccessories = config('HuebertAccessoriesDirectly.config.numberOfAccessories') %}
                                                                        <div class="input-group ">
                                                                            <button type="button"
                                                                                    class="input-group-text quantity-dn"
                                                                                    name="quantity-down[{{product.id}}]"
                                                                            >{% sw_icon 'minus-circle' %}
                                                                            </button>

                                                                        {% if product.isCloseout and accessoryQuantity and accessoryQuantity > product.stock %}
                                                                                                                                                                                    {% set accessoryQuantity = product.stock %}
                                                                        {% endif %}
                                                                        <input class="form-control accessory-quantity" type="number" placeholder="0" name="lineItems[{{ product.id }}][quantity]"
                                                                                aria-label="{{ product.translated.name }} quantity" {% if accessoryQuantity%}value="{{ accessoryQuantity }}"{% endif%} {% if sameQuantityAsMainProduct %}disabled{% endif %}
                                                                                {% if product.isCloseout %}max="{{ product.stock }}"{% endif %}
                                                                        >

                                                                            <button type="button"
                                                                                    class="input-group-text quantity-up"
                                                                                    name="quantity-up[{{product.id}}]"
                                                                            >{% sw_icon 'plus-circle' %}
                                                                            </button>
                                                                        </div>
                                                                        <input type="hidden" name="same-and-selectable" class="same-and-selectable">
                                                                    {% else %}
                                                                        <div class="form-control same-quantity border-0"><span class="same-quantity-as-main">1</span> x</div>
                                                                        <input type="hidden" name="lineItems[{{ product.id }}][quantity]" class="accessory-quantity">
                                                                        <input type="hidden" name="same-quantity" class="same-quantity-input" value="1">
                                                                        <input type="hidden" name="same-and-selectable" class="same-and-selectable" value="1">
                                                                    {% endif %}
                                                                {% else %}
                                                                    <input type="hidden" name="lineItems[{{ product.id }}][quantity]" class="accessory-quantity" {% if accessoryQuantity %}data-quantity="{{ accessoryQuantity }}{% endif %}">
                                                                    <input type="checkbox" class="accessory-checkbox" data-preselected-checkboxes="{{ preselectedCheckboxes }}">
                                                                    <input type="hidden" name="accessory-quantity-as-main" class="accessory-quantity-as-main" value="1">
                                                                    <input type="hidden" name="lineItems[{{ product.id }}][sameQuantity]" value="1">
                                                                    <input type="hidden" name="same-and-selectable" class="same-and-selectable">
                                                                {% endif %}
                                                            </div>
                                                        </div>
                                                        <div style="padding: 0;" class="col-3 sm-img viewport-small {% if selectableAmount == false %}p-abs{% endif %}">
                                                            {{ block('hueb_accessories_accordion_view_thumbnail') }}
                                                        </div>
                                                    {% else %}
                                                        <div class="col-2">
                                                            <div style="width: 50px" class="input-group hueb-accessories-bundle-quantity" data-huebert-add-accessory-config="{{ accessoryConfig|json_encode }}" data-huebert-pricing="{{ pricingTop }}">
                                                                <input readonly="readonly" class="form-control accessory-quantity" value="{{ huebQuantity }}" autocomplete="off" name="lineItems[{{ product.id }}][quantity]" aria-label="{{ product.translated.name }} quantity">
                                                            </div>
                                                        </div>
                                                    {% endif %}
                                                {% endblock %}
                                                {% if selectableAmount == 0 %}
                                                    <div class="viewport-small hue-product-title">
                                                        {{ block('hueb_accessories_accordion_view_product_info') }}
                                                    </div>
                                                {% endif %}
                                                <div class="col-12 viewport-small no-pad hue-price-wrapper">
                                                    {{ block('hueb_accessories_accordion_view_product_price') }}
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                {% block hueb_accessories_accordion_view_product %}
                                    <div class="{{ viewClass }} viewport-default hue-title-price">

                                        {% block hueb_accessories_accordion_view_product_info %}
                                            <a href="{{ seoUrl('frontend.detail.page', {'productId': product.id}) }}" data-bs-modal-class="quickview-modal"
                                                class="" data-bs-toggle="modal"
                                                data-url="{{ seoUrl('widgets.quickview.minimal', {'productId': product.id}) }}"
                                                title="{{ product.translated.name }}"
                                            >
                                                {% if product.customFields.huebert_accessory_directly_customtext %}
                                                    &nbsp;{{ product.customFields.huebert_accessory_directly_customtext }}
                                                {% else %}
                                                    {% if selectableAmount == 0 and accessoryQuantity > 1 %}<strong>{{ accessoryQuantity }}x</strong> {% endif %}{{ product.translated.name }}
                                                {% endif %}
                                            </a>
                                            {% if product.productConfigurator|length <=0 or page.acessoryOptions.activateVariants == false or  crossSelling.crossSelling.huebBundle %}

                                                <div class="product-variant-characteristics huebert-accessory_variants">
                                                    <div class="product-variant-characteristics-text">

                                                        {% for variation in product.variation %}
                                                            {{ variation.group }}:
                                                            <span class="product-variant-characteristics-option">
                                                                            {{ variation.option }}
                                                                        </span>
                                                            {% if product.variation|last != variation %}
                                                                {{ " | " }}
                                                            {% endif %}
                                                        {% endfor %}
                                                    </div>
                                                </div>
                                            {% endif %}
                                        {% endblock %}

                                        {% block hueb_accessories_accordion_view_product_price %}
                                            {% if product.productConfigurator|length <=0 or page.acessoryOptions.activateVariants == false or  crossSelling.crossSelling.huebBundle %}

                                                <div class="product-price huebert-accessory_pricing">
                                                    {{ "huebert-acessories-directly.product-detail.accessory-price"|trans(
                                                        {'%price%': pricing}
                                                    )|sw_sanitize }}{{ pricing }}
                                                </div>

                                                {% if page.acessoryOptions.showUnits%}
                                                    {% if product.purchaseUnit %}
                                                        {% block buy_widget_price_unit %}
                                                            <div class="product-detail-price-unit">
                                                                {% block buy_widget_price_unit_label %}
                                                                    <span class="price-unit-label">
                                                {{ "detail.priceUnitName"|trans|sw_sanitize }}
                                            </span>
                                                                {% endblock %}

                                                                {% block buy_widget_price_unit_content %}
                                                                    <span class="price-unit-content">
                                            {{ product.purchaseUnit }} {{ product.unit.name }} {{ referencePricing }}
                                            </span>
                                                                {% endblock %}
                                                            </div>
                                                        {% endblock %}
                                                    {% endif %}
                                                {% endif %}


                                                {% if referencePricing %}

                                                {% endif %}
                                            {% endif %}
                                        {% endblock %}
                                    </div>
                                    {% if (page.HuebertAccessoriesDirectly.config.showDelivery and product.productConfigurator|length <= 0) or (page.acessoryOptions.activateVariants == false and page.HuebertAccessoriesDirectly.config.showDelivery)  %}

                                        <div class="{% if selectableAmount %}col-lg-3{% else %}col-lg-2{% endif %}"></div>
                                        <div class="{% if selectableAmount %}col-lg-9{% else %}col-lg-10{% endif %} huebert-acessory_deliveryTime">
                                            {% if product.availableStock >= product.minPurchase and product.deliveryTime %}
                                                <p class="delivery-information">
                                                    <span class="delivery-status-indicator bg-success"></span>
                                                    {{ "detail.deliveryTimeAvailable"|trans({
                                                        '%name%': product.deliveryTime.translation('name')
                                                    })|sw_sanitize }}
                                                </p>
                                                <input type="hidden" class="stock-huebert" value="">
                                            {% elseif product.isCloseout and product.availableStock < product.minPurchase %}
                                                <p class="delivery-information">
                                                    <span class="delivery-status-indicator bg-danger"></span>
                                                    {{ "detail.soldOut"|trans|sw_sanitize }}
                                                </p>
                                                <input type="hidden" class="stock-huebert" value="true">
                                            {% elseif product.availableStock < product.minPurchase and product.deliveryTime and product.restockTime %}
                                                <p class="delivery-information">
                                                    <span class="delivery-status-indicator bg-warning"></span>
                                                    {{ "detail.deliveryTimeRestock"|trans({
                                                        '%count%': product.restockTime,
                                                        '%restockTime%': product.restockTime,
                                                        '%name%': product.deliveryTime.translation('name')
                                                    })|sw_sanitize }}
                                                </p>
                                                <input type="hidden" class="stock-huebert" value="">
                                            {% endif %}
                                        </div>
                                    {% endif %}
                                {% endblock %}


                                {% if page.HuebertAccessoriesDirectly.config.accessoryViewStatic %}
                                    <div class="col-lg-2 viewport-default thumbnails-view">
                                        {% block hueb_accessories_accordion_view_thumbnail %}
                                            {% if cover.url %}
                                                {% set attributes = {
                                                    'class': 'product-image is-'~displayMode,
                                                    'alt': (cover.translated.alt ?: name),
                                                    'title': (cover.translated.title ?: name)
                                                } %}

                                                {% if displayMode == 'cover' or displayMode == 'contain' %}
                                                    {% set attributes = attributes|merge({ 'data-object-fit': displayMode }) %}
                                                {% endif %}

                                                {% sw_thumbnails 'product-image-thumbnails' with {
                                                    media: cover,
                                                    sizes: {
                                                        'xs': '501px',
                                                        'sm': '315px',
                                                        'md': '427px',
                                                        'lg': '333px',
                                                        'xl': '284px'
                                                    }
                                                } %}
                                            {% endif %}
                                        {% endblock %}
                                    </div>
                                {% endif %}

                            </div>
                            {% if not crossSelling.crossSelling.huebBundle %}
                            <div class="row">
                                <div class="{% if selectableAmount %}col-lg-3{% else %}col-lg-2{% endif %}"></div>
                                <div class="{% if selectableAmount %}col-lg-9{% else %}col-lg-10{% endif %} product-configurator-variants">
                                    {% block page_product_detail_configurator_include %}

                                        {% if page.acessoryOptions.activateVariants %}
                                            {% if product.parentId and product.productConfigurator|length > 0 %}
                                                <div class="product-detail-configurator-container">
                                                    {% sw_include '@Storefront/storefront/page/product-detail/configurator-cross-selling.html.twig'
                                                    %}
                                                </div>
                                            {% endif %}
                                        {% endif %}
                                    {% endblock %}
                                </div>

                            </div>
                            {% endif %}

                            <input type="hidden" class="generated-variant"value="">
                            <input type="hidden" class="stock-huebert" value="">
                        </div>
                    {% endblock %}

                {% endif %}
            {% endif %}
        {% endfor %}
    </div>
{% endblock %}

{% sw_extends '@Storefront/storefront/page/search/index.html.twig' %}


{% block base_content %}
    {% block page_search %}
        <div class="cms-page search-page main-width">
            {% block page_search_headline %}
                {{ parent() }}
            {% endblock %}

            {% sw_include '@Storefront/storefront/page/search/search-pagelet.html.twig' with { page: page } %}
        </div>
    {% endblock %}
{% endblock %}

{# {% block base_main_inner %}
    {% if page.extensions.acrisSearchSearchResultCmsPages %}
        {% block page_search_cms_pages %}
            <div class="search-cms-pages">
                {% for searchResultCmsPage in page.extensions.acrisSearchSearchResultCmsPages %}
                    {% if searchResultCmsPage.cmsPageResult  %}
                        <div class="search-cms-pages page-item">
                            {% sw_include "@Storefront/storefront/page/content/detail.html.twig" with { 'cmsPage': searchResultCmsPage.cmsPageResult } %}
                        </div>
                    {% endif %}
                {% endfor %}
            </div>
        {% endblock %}
    {% endif %}
    {{ parent() }}
{% endblock %}

{% block page_search %}
<div class="cms-page search-page main-width">
    {% block page_search_headline %}
        {% set emptySearchResult = page.listing.total < 1 and page.extensions.acrisCategorySearchResult.total < 1 and page.extensions.acrisManufacturerSearchResult.total < 1 %}

        <h1 class="search-headline">
            {% block page_search_headline_text_outer %}
                {% if emptySearchResult %}
                    {% block page_search_headline_text_empty %}
                        {{ "acrisSearch.headlineEmpty"|trans({
                            '%searchTerm%': page.searchTerm
                        })|sw_sanitize }}
                    {% endblock %}
                {% else %}
                    {% block page_search_headline_text %}
                        {{ "acrisSearch.headline"|trans({
                            '%searchTerm%': page.searchTerm
                        })|sw_sanitize }}
                    {% endblock %}
                    {% sw_include '@Storefront/storefront/component/search/total.html.twig' with { productSearchResult: page.listing } %}
                {% endif %}
            {% endblock %}
        </h1>

        {% block page_acris_search_product_headline %}
            <h2>{{ "acrisSearch.resultTitle.product"|trans|sw_sanitize }}</h2>

            {% sw_include '@Storefront/storefront/page/search/search-pagelet.html.twig' with { page: page } %}
        {% endblock %}

        {% block page_acris_search_manufacturer %}
            {% if page.extensions.acrisManufacturerSearchResult.total %}
                <div class="search-manufacturer mt-3 mb-3">
                    {% block page_acris_search_manufacturer_headline %}
                        <h2>{{ "acrisSearch.resultTitle.manufacturer"|trans|sw_sanitize }}</h2>
                    {% endblock %}
                    {% block page_acris_search_manufacturer_listing %}
                        {% sw_include '@Storefront/storefront/component/listing/listing.html.twig' with { searchResult: page.extensions.acrisManufacturerSearchResult, useMedia: true } %}
                    {% endblock %}
                </div>
            {% endif %}
        {% endblock %}

        {% block page_acris_search_category %}
            {% if page.extensions.acrisCategorySearchResult.total %}
                <div class="search-category mt-3 mb-3">
                    {% if page.extensions.acrisCategorySearchResult.navigation and page.extensions.acrisCategorySearchResult.navigation.total > 0 %}
                        {% block page_acris_search_category_headline_navigation %}
                            <h2>{{ "acrisSearch.resultTitle.mainNavigation"|trans|sw_sanitize }}</h2>
                        {% endblock %}
                        {% block page_acris_search_category_listing_navigation %}
                            {% sw_include '@Storefront/storefront/component/listing/listing.html.twig' with { searchResult: page.extensions.acrisCategorySearchResult.navigation, useMedia: false } %}
                        {% endblock %}
                    {% endif %}

                    {% if page.extensions.acrisCategorySearchResult.service and page.extensions.acrisCategorySearchResult.service.total > 0 %}
                        {% block page_acris_search_category_headline_service %}
                            <h2>{{ "acrisSearch.resultTitle.serviceNavigation"|trans|sw_sanitize }}</h2>
                        {% endblock %}
                        {% block page_acris_search_category_listing_service %}
                            {% sw_include '@Storefront/storefront/component/listing/listing.html.twig' with { searchResult: page.extensions.acrisCategorySearchResult.service, useMedia: false } %}
                        {% endblock %}
                    {% endif %}

                    {% if page.extensions.acrisCategorySearchResult.footer and page.extensions.acrisCategorySearchResult.footer.total > 0  %}
                        {% block page_acris_search_category_headline_footer %}
                            <h2>{{ "acrisSearch.resultTitle.footerNavigation"|trans|sw_sanitize }}</h2>
                        {% endblock %}
                        {% block page_acris_search_category_listing_footer %}
                            {% sw_include '@Storefront/storefront/component/listing/listing.html.twig' with { searchResult: page.extensions.acrisCategorySearchResult.footer, useMedia: false } %}
                        {% endblock %}
                    {% endif %}
                </div>
            {% endif %}
        {% endblock %}

        {% block page_acris_search_landingpage %}
            {% if page.extensions.acrisLandingpageSearchResult.total %}
                <div class="search-landingpage mt-3 mb-3">
                    {% block page_acris_search_landingpage_headline %}
                        <h2>{{ "acrisSearch.resultTitle.landingPage"|trans|sw_sanitize }}</h2>
                    {% endblock %}
                    {% block page_acris_search_landingpage_listing %}
                        {% sw_include '@Storefront/storefront/component/listing/listing.html.twig' with { searchResult: page.extensions.acrisLandingpageSearchResult, useMedia: false } %}
                    {% endblock %}
                </div>
            {% endif %}
        {% endblock %}
    </div>
    {% endblock %}
{% endblock %} #}
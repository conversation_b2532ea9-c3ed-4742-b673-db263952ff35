{% sw_extends '@Storefront/storefront/base.html.twig' %}

{% block base_header %}
    <header class="header-main">
        {% block base_header_inner %}
            {% sw_include '@Storefront/storefront/layout/header/header.html.twig' %}
        {% endblock %}
    </header>
{% endblock %}

{% block base_navigation %}
{% endblock %}

{% block base_offcanvas_navigation %}
    {% if page.header.navigation %}
        <div class="d-none js-navigation-offcanvas-initial-content{% if context.salesChannel.navigationCategoryId == page.header.navigation.active.id %} is-root{% endif %}">
            {% block base_offcanvas_navigation_inner %} 
                {% sw_include '@Storefront/storefront/layout/navigation/offcanvas/navigation.html.twig' with { navigation: page.header.navigation } %}
            {% endblock %}
        </div>
    {% endif %}
{% endblock %}

{% block base_main %}
    <main class="content-main">
        <div class="festtema-banner ">
            {% block base_flashbags %}
                <div class="flashbags container">
                    {% for type, messages in app.flashes %}
                        {% sw_include '@Storefront/storefront/utilities/alert.html.twig' with { type: type, list: messages } %}
                    {% endfor %}
                </div>
            {% endblock %}

            {% block base_main_inner %}
                
                    {% block base_main_container %}
                        <div class="container-main">
                            {% block base_breadcrumb %}
                                <div class="main-width">
                                    {% sw_include '@Storefront/storefront/layout/breadcrumb.html.twig' with {
                                        context: context,
                                        category: page.product.seoCategory
                                    } only %}
                                </div>
                            {% endblock %}

                            {% block base_content %}{% endblock %}
                        </div>
                    {% endblock %}
                
            {% endblock %}
        </div>
    </main>
    {% if theme_config('sw-topbar-emarket') %}
        {% block layout_emarket_icon_mobile %}
            <div class="e-market-mobile-icon mobile-view">
                <a href="{{ "festtemaTheme.header.eMarketLink"|trans|sw_sanitize }}" class="btn header-actions-btn" target="_new">
                    {# <img src="{{ asset('bundles/festtematheme/images/e-maerket-icon.png', 'asset') }}" alt="e-market" class="img-fluid"> #}
                    <img src="https://static.festtema.dk/media/6b/a9/a0/1687772922/e-market-1.webp" alt="e-market" class="img-fluid" height="24" width="24">
                </a>
            </div>
        {% endblock %}
    {% endif %}
{% endblock %}
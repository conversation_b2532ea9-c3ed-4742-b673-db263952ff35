{% sw_extends '@Storefront/storefront/component/product/card/price-unit.html.twig' %}


{% block component_product_box_price_reference_unit %}
    {% if referencePrice is not null %}
        <span class="price-unit-reference">
            ({{ referencePrice.price|currency }} / {{ referencePrice.referenceUnit }} {{ referencePrice.unitName }})
        </span>
    {% endif %}
{% endblock %}


{% block component_product_box_price %}
    <div class="product-price-wrapper">
        {% set price = real %}
        {% set isListPrice = price.listPrice.percentage > 0 %}
        {% set isRegulationPrice = price.regulationPrice != null %}

        <div class="product-cheapest-price{% if isListPrice and isRegulationPrice and not displayFrom and not displayFromVariants %} with-list-price{% endif %}{% if isRegulationPrice and not displayFrom and displayFromVariants %} with-regulation-price{% endif %}{% if displayFrom and isRegulationPrice %} with-from-price{% endif %}">
            {% if cheapest.unitPrice != real.unitPrice and cheapest.variantId != product.id %}
                <div>{{ "listing.cheapestPriceLabel"|trans|sw_sanitize }}<span class="product-cheapest-price-price"> {{ cheapest.unitPrice|currency }}</span></div>
            {% endif %}
        </div>

        {% if displayFrom or (displayParent and displayFromVariants) %}
            {{ "listing.listingTextFrom"|trans|sw_sanitize }}
        {% endif %}

        <span class="product-price{% if isListPrice and not displayFrom and not displayFromVariants %} with-list-price{% endif %}">
            {{ price.unitPrice|currency }}

            {% if isListPrice and not displayFrom and not displayFromVariants %}
                {% set afterListPriceSnippetExists = "listing.afterListPrice"|trans|length > 0 %}
                {% set beforeListPriceSnippetExists = "listing.beforeListPrice"|trans|length > 0 %}
                {% set hideStrikeTrough = beforeListPriceSnippetExists or afterListPriceSnippetExists %}

                <span class="list-price list-price-line-through">
{#                    {% if beforeListPriceSnippetExists %}{{ "listing.beforeListPrice"|trans|trim|sw_sanitize }}{% endif %}#}

                    <span class="list-price-price">{{ price.listPrice.price|currency }}</span>

                    {% if afterListPriceSnippetExists %}{{ "listing.afterListPrice"|trans|trim|sw_sanitize }}{% endif %}

                    <span class="list-price-percentage">{{ "detail.listPricePercentage"|trans({'%price%': price.listPrice.percentage })|sw_sanitize }}</span>
                </span>
            {% endif %}
        </span>
        {% if isRegulationPrice %}
            <span class="product-price with-regulation-price">
                {% if isListPrice %}<br>{% endif %}<span class="regulation-price">{{ "general.listPricePreviously"|trans({'%price%': price.regulationPrice.price|currency }) }}</span>
            </span>
        {% endif %}
    </div>
{% endblock %}
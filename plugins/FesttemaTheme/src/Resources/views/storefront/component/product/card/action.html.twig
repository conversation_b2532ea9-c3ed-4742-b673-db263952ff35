{% sw_extends '@Storefront/storefront/component/product/card/action.html.twig' %}

{% block page_product_detail_product_buy_button %}
    {% if product.parentId is null %}
        <button class="bg-unset-icon festtema-buy"
                title="{{ "listing.boxAddProduct"|trans|striptags }}">
            <i class="bi bi-bag"></i>
        </button>
    {% endif %}
{% endblock %}

{% block component_product_box_action_detail %}
    <div class="d-grid">
        <a href="{{ seoUrl('frontend.detail.page', {'productId': id}) }}"
            class="btn bg-unset-icon"
            title="{{ "listing.boxProductDetails"|trans|striptags }}">
            <i class="bi bi-bag"></i>
        </a>
    </div>
{% endblock %}

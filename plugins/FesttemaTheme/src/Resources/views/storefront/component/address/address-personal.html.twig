{% sw_extends '@Storefront/storefront/component/address/address-personal.html.twig' %}

{% block component_address_personal_fields %}
    {% block component_address_personal_account_type %}
        {% if onlyCompanyRegistration or (config('core.loginRegistration.showAccountTypeSelection') and not hideCustomerTypeSelect) %}
            <div class="row g-2">
                <div class="form-group col-md-6 col-sm-6 contact-type">
                    {% block component_address_personal_account_type_label %}
                        <label class="form-label"
                               for="{{ idPrefix ~ prefix }}accountType">
                            {{ "account.personalTypeLabel"|trans|sw_sanitize }}{{ "general.required"|trans|sw_sanitize }}
                        </label>
                    {% endblock %}

                    {% block component_address_personal_account_type_select %}
                            {% if onlyCompanyRegistration or config('core.loginRegistration.showAccountTypeSelection') %}
                                <select name="{% if prefix %}{{ prefix }}[accountType]{% else %}accountType{% endif %}"
                                    id="{{ idPrefix ~ prefix }}accountType"
                                    {% if onlyCompanyRegistration %}disabled{% endif %}
                                    required="required"
                                    class="form-select contact-select accountType"
                                    data-form-field-toggle="true"
                                    data-form-field-toggle-target=".js-field-toggle-contact-type-company{% if customToggleTarget %}-{{ prefix }}{% endif %}"
                                    data-form-field-toggle-value="{{ constant('Shopware\\Core\\Checkout\\Customer\\CustomerEntity::ACCOUNT_TYPE_BUSINESS') }}"
                                    data-form-field-toggle-scope="{% if scope == 'parent' %}parent{% else %}all{% endif %}"
                                    {% if scope == 'parent' %}data-form-field-toggle-parent-selector={{ parentSelector }}{% endif %}
                                >
                            {% endif %}

                            {% set isCompany = false %}

                            {% if page.address.company or data.company is not empty %}
                                {% set isCompany = true %}
                            {% endif %}

                            {% if onlyCompanyRegistration or (accountType and accountType == constant('Shopware\\Core\\Checkout\\Customer\\CustomerEntity::ACCOUNT_TYPE_BUSINESS')) %}
                                {% set isCompany = true %}
                            {% endif %}

                            {% set isLoginPage = false %}
                            {% if activeRoute == 'frontend.account.login.page' %}
                                {% set isLoginPage = true %}
                            {% endif %}

                            {% if isLoginPage %}
                                <option disabled="disabled"
                                        selected="selected"
                                        value="">
                                    {{ "account.personalTypePlaceholder"|trans|sw_sanitize }}
                                </option>
                            {% endif %}

                            {% if not onlyCompanyRegistration %}
                                <option value="{{ constant('Shopware\\Core\\Checkout\\Customer\\CustomerEntity::ACCOUNT_TYPE_PRIVATE') }}"
                                    {% if isCompany == false and isLoginPage == false %} selected="selected"{% endif %}>
                                    {{ "account.personalTypePrivate"|trans|sw_sanitize }}
                                </option>
                            {% endif %}

                            <option value="{{ constant('Shopware\\Core\\Checkout\\Customer\\CustomerEntity::ACCOUNT_TYPE_BUSINESS') }}"
                                {% if isCompany == true and isLoginPage == false %} selected="selected"{% endif %}>
                                {{ "account.personalTypeBusiness"|trans|sw_sanitize }}
                            </option>
                        </select>
                        {% if onlyCompanyRegistration %}<input type="hidden" name="accountType" value="{{ constant('Shopware\\Core\\Checkout\\Customer\\CustomerEntity::ACCOUNT_TYPE_BUSINESS') }}">{% endif %}
                    {% endblock %}

                    {% block component_address_personal_account_type_error %}
                    {% endblock %}
                </div>
            </div>
        {% elseif not hideCustomerTypeSelect %}
            <input type="hidden" name="accountType">
        {% endif %}
    {% endblock %}

    {% block component_address_personal_fields_salutation_title %}
        <div class="{{ formRowClass }} d-none">
            {% block component_address_personal_fields_salutation %}
                <div class="form-group col-md-3 col-sm-6">
                    {% block component_address_personal_fields_salutation_label %}
                        <label class="form-label"
                               for="{{ idPrefix ~ prefix }}personalSalutation">
                            {{ "account.personalSalutationLabel"|trans|sw_sanitize }}{{ "general.required"|trans|sw_sanitize }}
                        </label>
                    {% endblock %}

                    {% block component_address_form_salutation_select %}
                        <select id="{{ idPrefix ~ prefix }}personalSalutation"
                                class="{{ formSelectClass }}{% if formViolations.getViolations('/salutationId') is not empty %} is-invalid{% endif %}"
                                name="{% if prefix %}{{ prefix }}[salutationId]{% else %}salutationId{% endif %}"
                                required="required">
                            {% if not data.get('salutationId') %}
                                <option disabled="disabled"                                        
                                        value="">
                                    {{ "account.personalSalutationPlaceholder"|trans|sw_sanitize }}
                                </option>
                            {% endif %}
                            {% for salutation in page.salutations %}
                                <option {% if salutation.salutationKey == 'not_specified' %} selected="selected" {% endif %}
                                        value="{{ salutation.id }}">
                                    {{ salutation.translated.displayName }}
                                </option>
                            {% endfor %}
                        </select>
                    {% endblock %}

                    {% block component_address_form_salutation_select_error %}
                        {% if formViolations.getViolations('/salutationId') is not empty %}
                            {% sw_include '@Storefront/storefront/utilities/form-violation.html.twig' with {
                                violationPath: '/salutationId'
                            } %}
                        {% endif %}
                    {% endblock %}
                </div>
            {% endblock %}

            {% block component_address_personal_fields_title %}
                {% if config('core.loginRegistration.showTitleField') %}
                    <div class="form-group col-md-3 col-sm-6">
                        {% block component_address_personal_fields_title_label %}
                            <label class="form-label"
                                   for="{{ idPrefix ~ prefix }}personalTitle">
                                {{ "account.personalTitleLabel"|trans|sw_sanitize }}
                            </label>
                        {% endblock %}

                        {% block component_address_personal_fields_title_input %}
                            <input type="text"
                                   class="form-control"
                                   autocomplete="section-personal title"
                                   id="{{ idPrefix ~ prefix }}personalTitle"
                                   placeholder="{{ "account.personalTitlePlaceholder"|trans|striptags }}"
                                   name="{% if prefix %}{{ prefix }}[title]{% else %}title{% endif %}"
                                   value="{{ data.get('title') }}">
                        {% endblock %}
                    </div>
                {% endif %}
            {% endblock %}
        </div>
    {% endblock %}

    {% block component_address_personal_fields_name %}{{ parent() }}{% endblock %}

    {% block component_address_personal_company %}{{ parent() }}{% endblock %}

    {% block component_address_personal_vat_id %}{{ parent() }}{% endblock %}

    {% block component_address_personal_fields_birthday %}{{ parent() }}{% endblock %}

{% endblock %}

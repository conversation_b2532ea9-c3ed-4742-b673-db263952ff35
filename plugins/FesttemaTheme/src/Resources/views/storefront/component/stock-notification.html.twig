{% block component_stock_notification %}

    {% if page.product.availableStock < page.product.minPurchase and config('AcrisStockNotificationCS.config').emailNotification == 'alwaysInactive' and page.product.customFields.acris_stock_notification_email_notification == true or
        page.product.availableStock < page.product.minPurchase and config('AcrisStockNotificationCS.config').emailNotification == 'alwaysActive' and page.product.customFields.acris_stock_notification_email_notification_inactive != true or
        element.data.product.availableStock < element.data.product.minPurchase and config('AcrisStockNotificationCS.config').emailNotification == 'alwaysInactive' and element.data.product.customFields.acris_stock_notification_email_notification == true or
        element.data.product.availableStock < element.data.product.minPurchase and config('AcrisStockNotificationCS.config').emailNotification == 'alwaysActive' and element.data.product.customFields.acris_stock_notification_email_notification_inactive != true %}

        {% if btnStyle is not defined %}
            {% set btnStyle = 'primary' %}
        {% endif %}

        <div class="acris-stock-notification">
            {% block component_stock_notification_form %}
                <form action="{{ path('frontend.form.acris-stock-notification.register.handle') }}"
                      method="post"
                      class="acris-stock-notification-form"
                      data-form-validation="true"
                      data-stock-notification-form="true">

                    {% set type = 'warning' %}
                    {% set messages = [ "acrisStockNotification.detail.stockNotificationMessageWarning"|trans|sw_sanitize ] %}
                    {% set typeExists = 'warning' %}
                    {% set messageEmailExist = [ "acrisStockNotification.detail.stockNotificationMessageExistWarning"|trans|sw_sanitize ] %}

                    {% if page.extensions.notificationConfirmed and page.extensions.notificationConfirmed['notificationConfirmed'] is same as(true) %}
                        {% set type = 'success' %}
                        {% set messages = [ "acrisStockNotification.detail.stockNotificationMessageSuccess"|trans|sw_sanitize ] %}
                    {% endif %}

                    <div class="acris-stock-notification-notify">
                        {% sw_include '@Storefront/storefront/utilities/alert.html.twig' with {
                            type: type,
                            list: messages
                        } %}
                    </div>

                    <div class="acris-stock-notification-exists d-none">
                        {% sw_include '@Storefront/storefront/utilities/alert.html.twig' with {
                            type: typeExists,
                            list: messageEmailExist
                        } %}
                    </div>

                    <div class="acris-stock-notification-form-input-group">
                        {% if page.extensions.notificationConfirmed is null or page.extensions.notificationConfirmed['notificationConfirmed'] is not same as(true) %}
                            <div class="input-group">
                                {% block component_stock_notification_input_group %}
                                    <input name="email"
                                           type="email"
                                           placeholder="{{ "acrisStockNotification.detail.stockNotificationInputPlaceholder"|trans|sw_sanitize }}"
                                           required="required"
                                           class="form-control"/>
                                    <div class="input-group-append">
                                        {% block component_stock_notification_input_group_button %}
                                            <button class="btn btn-{{ btnStyle }}"
                                                    type="submit">
                                                {% if btnIcon %}
                                                    {% sw_icon 'envelope' %}
                                                {% else %}
                                                    {{ "acrisStockNotification.detail.stockNotificationButtonText"|trans|sw_sanitize }}
                                                {% endif %}
                                            </button>
                                        {% endblock %}
                                    </div>
                                {% endblock %}
                            </div>
                        {% endif %}
                    </div>

                    {% block component_stock_notification_hidden_fields %}
                        <div class="form-hidden-fields">
                            <input type="hidden" name="option"
                                   value="{{ constant('Acris\\StockNotification\\Storefront\\Controller\\StockNotificationController::NOTIFY') }}"/>
                            <input type="hidden" name="productId" value="{{ page.product.id }}"/>
                            <input type="hidden" name="productVersionId" value="{{ page.product.versionId }}"/>
                            <input type="submit" class="submit--hidden d-none"/>
                        </div>
                    {% endblock %}

                    {% block component_stock_notification_alert_overlay %}
                        <div class="acris-stock-notification-alert-overlay mt-1 d-block"></div>
                    {% endblock %}
                </form>
            {% endblock %}
        </div>
    {% endif %}
{% endblock %}

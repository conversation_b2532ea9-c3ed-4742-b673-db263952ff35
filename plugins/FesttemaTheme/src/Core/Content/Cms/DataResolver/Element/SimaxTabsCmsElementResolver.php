<?php


namespace FesttemaTheme\Core\Content\Cms\DataResolver\Element;


use OutOfBoundsException;
use Shopware\Core\Content\Cms\Aggregate\CmsSlot\CmsSlotEntity;
use Shopware\Core\Content\Cms\DataResolver\CriteriaCollection;
use Shopware\Core\Content\Cms\DataResolver\Element\AbstractCmsElementResolver;
use Shopware\Core\Content\Cms\DataResolver\Element\ElementDataCollection;
use Shopware\Core\Content\Cms\DataResolver\FieldConfig;
use Shopware\Core\Content\Cms\DataResolver\FieldConfigCollection;
use Shopware\Core\Content\Cms\DataResolver\ResolverContext\ResolverContext;
use Shopware\Core\Content\Product\ProductDefinition;
use Shopware\Core\Content\ProductStream\Service\ProductStreamBuilderInterface;
use Shopware\Core\Framework\Context;
use Shopware\Core\Framework\DataAbstractionLayer\EntityRepository;
use Shopware\Core\Framework\DataAbstractionLayer\Search\Criteria;
use Shopware\Core\Framework\DataAbstractionLayer\Search\Filter\EqualsFilter;
use Shopware\Core\Framework\DataAbstractionLayer\Search\Filter\NotFilter;
use Shopware\Core\Framework\DataAbstractionLayer\Search\Grouping\FieldGrouping;
use Shopware\Core\Framework\DataAbstractionLayer\Search\Sorting\FieldSorting;
use Shopware\Core\Framework\Feature;
use Simax\SimaxCmsTabs\Core\Content\Cms\SalesChannel\Struct\SimaxTabsStruct;

class SimaxTabsCmsElementResolver extends AbstractCmsElementResolver
{
    private const STATIC_SEARCH_KEY_PRODUCTS = 'simax-tabs-products';
    const FALLBACK_LIMIT = 10;
    /**
     * @var ProductStreamBuilderInterface
     */
    private $productStreamBuilder;

    /**
     * @var EntityRepository
     */
    private EntityRepository $productRepository;

    public function __construct(ProductStreamBuilderInterface $productStreamBuilder, EntityRepository $productRepository)
    {
        $this->productStreamBuilder = $productStreamBuilder;
        $this->productRepository = $productRepository;
    }

    public function getType(): string
    {
        return 'simax-tabs';
    }

    public function collect(CmsSlotEntity $slot, ResolverContext $resolverContext): ?CriteriaCollection
    {
        $config = $slot->getFieldConfig();
        $collection = new CriteriaCollection();
        $tabs = $config->get('tabs');
        if (!$tabs || $tabs->isMapped() || $tabs->getValue() === null) {
            return null;
        }

        foreach ($tabs->getValue() as $key => $tab) {
            if ((!array_key_exists('productSelectionType', $tab) || $tab['productSelectionType'] == 'static') &&
                $tab['products']) {
                $criteria = new Criteria($tab['products']);
                $criteria->addAssociation('cover');
                $collection->add(
                    self::STATIC_SEARCH_KEY_PRODUCTS . '_' . $slot->getUniqueIdentifier() . '_' .$key ,
                    ProductDefinition::class,
                    $criteria
                );
            }

            if (array_key_exists('productSelectionType', $tab) &&
                $tab['productSelectionType'] == 'product_stream' &&
                $tab['productStreamId']
            ) {
                $collection->add(
                    self::STATIC_SEARCH_KEY_PRODUCTS . '_' . $slot->getUniqueIdentifier() . '_' .$key ,
                    ProductDefinition::class,
                    $this->collectByProductStream($resolverContext, $tab)
                );

            }
        }
        return $collection->all() ? $collection : null;
    }

    public function enrich(CmsSlotEntity $slot, ResolverContext $resolverContext, ElementDataCollection $result): void
    {
        $config = $slot->getFieldConfig();
        $tabs = $config->get('tabs');
        $struct = new SimaxTabsStruct();
        $slot->setData($struct);
        if (!$tabs || $tabs->isMapped() || $tabs->getValue() === null) {
            return;
        }

        foreach ($tabs->getValue() as $key => $tab) {
            $searchKey = self::STATIC_SEARCH_KEY_PRODUCTS . '_' . $slot->getUniqueIdentifier() . '_' . $key;
            $searchResult = $result->get($searchKey);
            if (!$searchResult) {
                continue;
            }
            $products = $searchResult->getEntities();
            foreach($products->getElements() AS $product){
                $parentId = $product->getParentId();
                if(!empty($parentId)){
                    $parentProduct = $this->productRepository->search(new Criteria([$parentId]), Context::createDefaultContext())->first();
                    $product->setName($parentProduct->getName());
                    $product->setDescription($parentProduct->getDescription());
                    $product->setMetaDescription($parentProduct->getMetaDescription());
                    $product->setMetaTitle($parentProduct->getMetaTitle());
                    $translatedArr = ['metaDescription' => $parentProduct->getTranslated()['metaDescription'],
                                    'name' => $parentProduct->getTranslated()['name'],
                                    'keywords' => $parentProduct->getTranslated()['keywords'],
                                    'description' => $parentProduct->getTranslated()['description'],
                                    'metaTitle' => $parentProduct->getTranslated()['metaTitle'],
                                    'packUnit' => $product->getTranslated()['packUnit'],
                                    'packUnitPlural' => $product->getTranslated()['packUnitPlural'],
                                    'customFields' => $product->getTranslated()['customFields'],
                                    'slotConfig' => $product->getTranslated()['slotConfig'],
                                    'customSearchKeywords' => $product->getTranslated()['customSearchKeywords'],
                                ];
                    $product->setTranslated($translatedArr);
                }
            }
            if (!$products) {
                continue;
            }
            $struct->setProducts($key, $products);

        }
    }

    private function collectByProductStream(ResolverContext $resolverContext, $tab): Criteria
    {
        $filters = $this->productStreamBuilder->buildFilters(
            $tab['productStreamId'],
            $resolverContext->getSalesChannelContext()->getContext()
        );

        $sorting = 'name:' . FieldSorting::ASCENDING;
        if ($tab['productStreamSorting']) {
            $sorting = $tab['productStreamSorting'];
        }
        $limit = self::FALLBACK_LIMIT;
        if ($tab['productStreamLimit']) {
            $limit = (int) $tab['productStreamLimit'];
        }

        $criteria = new Criteria();
        $criteria->addFilter(...$filters);
        $criteria->setLimit($limit);
        $criteria->addAssociation('options.group');

        // Ensure storefront presentation settings of product variants
        $criteria->addGroupField(new FieldGrouping('displayGroup'));
        $criteria->addFilter(
            new NotFilter(
                NotFilter::CONNECTION_AND,
                [new EqualsFilter('displayGroup', null)]
            )
        );

        if ($sorting) {
            $sorting = explode(':', $sorting);
            $field = $sorting[0];
            try {
                if (class_exists('\Composer\InstalledVersions')) {
                    $shopwareVersion = \Composer\InstalledVersions::getVersion('shopware/core');
                } else {
                    $shopwareVersion = \PackageVersions\Versions::getVersion('shopware/core');
                }
                if ($shopwareVersion && version_compare($shopwareVersion, '6.4', '<')) {
                    if ($field === 'cheapestPrice') {
                        $field = 'listingPrices';
                    }
                }
            } catch (OutOfBoundsException $e) {
                // development version
            }


            $direction = $sorting[1];

            $criteria->addSorting(new FieldSorting($field, $direction));
        }

        return $criteria;
    }

}

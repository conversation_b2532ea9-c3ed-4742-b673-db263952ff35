<?php declare(strict_types=1);

namespace FesttemaTheme\Storefront\Controller;

use Shopware\Core\Checkout\Order\SalesChannel\AbstractCancelOrderRoute;
use Shopware\Core\Checkout\Order\SalesChannel\AbstractOrderRoute;
use Shopware\Core\Checkout\Order\SalesChannel\AbstractSetPaymentOrderRoute;
use Shopware\Core\Checkout\Order\SalesChannel\OrderService;
use Shopware\Core\Checkout\Payment\SalesChannel\AbstractHandlePaymentMethodRoute;
use Shopware\Core\Framework\DataAbstractionLayer\Search\Criteria;
use Shopware\Core\Framework\Log\Package;
use Shopware\Core\System\SalesChannel\Context\SalesChannelContextServiceInterface;
use Shopware\Core\System\SalesChannel\Entity\SalesChannelRepository;
use Shopware\Core\System\SalesChannel\SalesChannel\AbstractContextSwitchRoute;
use Shopware\Core\System\SalesChannel\SalesChannelContext;
use Shopware\Core\System\SystemConfig\SystemConfigService;
use Shopware\Storefront\Controller\StorefrontController;
use Shopware\Storefront\Page\Account\Order\AccountEditOrderPageLoader;
use Shopware\Storefront\Page\Account\Order\AccountOrderDetailPageLoader;
use Shopware\Storefront\Page\Account\Order\AccountOrderPageLoadedHook;
use Shopware\Storefront\Page\Account\Order\AccountOrderPageLoader;
use Symfony\Component\EventDispatcher\EventDispatcherInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

#[Route(defaults: ['_routeScope' => ['storefront']])]
#[Package('storefront')]
class AccountOrderController extends StorefrontController
{
    private AccountOrderPageLoader $orderPageLoader;

    private AbstractContextSwitchRoute $contextSwitchRoute;

    private AccountEditOrderPageLoader $accountEditOrderPageLoader;

    private AbstractCancelOrderRoute $cancelOrderRoute;

    private AbstractSetPaymentOrderRoute $setPaymentOrderRoute;

    private AbstractHandlePaymentMethodRoute $handlePaymentMethodRoute;

    private EventDispatcherInterface $eventDispatcher;

    private AccountOrderDetailPageLoader $orderDetailPageLoader;

    private AbstractOrderRoute $orderRoute;

    private SalesChannelContextServiceInterface $contextService;

    private SystemConfigService $systemConfigService;

    private OrderService $orderService;

    private SalesChannelRepository $productRepository;

    public function __construct(
        AccountOrderPageLoader $orderPageLoader,
        AccountEditOrderPageLoader $accountEditOrderPageLoader,
        AbstractContextSwitchRoute $contextSwitchRoute,
        AbstractCancelOrderRoute $cancelOrderRoute,
        AbstractSetPaymentOrderRoute $setPaymentOrderRoute,
        AbstractHandlePaymentMethodRoute $handlePaymentMethodRoute,
        EventDispatcherInterface $eventDispatcher,
        AccountOrderDetailPageLoader $orderDetailPageLoader,
        AbstractOrderRoute $orderRoute,
        SalesChannelContextServiceInterface $contextService,
        SystemConfigService $systemConfigService,
        OrderService $orderService,
        SalesChannelRepository $productRepository
    ) {
        $this->orderPageLoader = $orderPageLoader;
        $this->contextSwitchRoute = $contextSwitchRoute;
        $this->accountEditOrderPageLoader = $accountEditOrderPageLoader;
        $this->cancelOrderRoute = $cancelOrderRoute;
        $this->setPaymentOrderRoute = $setPaymentOrderRoute;
        $this->handlePaymentMethodRoute = $handlePaymentMethodRoute;
        $this->eventDispatcher = $eventDispatcher;
        $this->orderDetailPageLoader = $orderDetailPageLoader;
        $this->orderRoute = $orderRoute;
        $this->contextService = $contextService;
        $this->systemConfigService = $systemConfigService;
        $this->orderService = $orderService;
        $this->productRepository = $productRepository;
    }

    #[Route(path: '/account/order', name: 'frontend.account.order.page', options: ['seo' => false], defaults: ['XmlHttpRequest' => true, '_loginRequired' => true, '_loginRequiredAllowGuest' => true, '_noStore' => true], methods: ['GET', 'POST'])]
    #[Route(path: '/account/order', name: 'frontend.account.order.page', options: ['seo' => false], defaults: ['XmlHttpRequest' => true, '_noStore' => true], methods: ['GET', 'POST'])]
    public function orderOverview(Request $request, SalesChannelContext $context): Response
    {
        $page = $this->orderPageLoader->load($request, $context);

        $this->hook(new AccountOrderPageLoadedHook($page, $context));

        foreach($page->getOrders()->getEntities()->getElements() AS $order){
            foreach($order->getLineItems() AS $product){
                $parentId = (isset($product->getPayload()['parentId']))? $product->getPayload()['parentId']: null;
                if(!empty($parentId)){
                    $customFields = $product->getPayload()['customFields'];
                    if(isset($customFields['ft_parent_prod_name'])){
                        $product->setLabel($customFields['ft_parent_prod_name']);
                    }
                }
            } 
        }

        return $this->renderStorefront('@Storefront/storefront/page/account/order-history/index.html.twig', ['page' => $page]);
    }

}

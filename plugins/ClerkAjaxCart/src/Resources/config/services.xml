<?xml version="1.0" ?>

<container xmlns="http://symfony.com/schema/dic/services"
           xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
           xsi:schemaLocation="http://symfony.com/schema/dic/services http://symfony.com/schema/dic/services/services-1.0.xsd">

    <services>
        <!-- Controllers -->
        <service id="ClerkAjaxCart\Storefront\Controller\ClerkCartController" public="true">
            <argument type="service" id="Shopware\Core\Checkout\Cart\SalesChannel\CartService"/>
            <argument type="service" id="Shopware\Core\Checkout\Cart\CartCalculator"/>
            <argument type="service" id="sales_channel.product.repository"/>
            <argument type="service" id="translator"/>
            <call method="setContainer">
                <argument type="service" id="service_container"/>
            </call>
            <tag name="controller.service_arguments"/>
        </service>
    </services>
</container>
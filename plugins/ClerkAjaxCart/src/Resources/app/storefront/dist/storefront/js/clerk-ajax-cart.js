"use strict";(self.webpackChunk=self.webpackChunk||[]).push([["clerk-ajax-cart"],{5771:(e,t,s)=>{var a,r,o,n=s(6285),c=s(8254);class i extends n.Z{init(){var e;this._client=new c.Z,void 0!==(null===(e=window.clerkAjaxCartConfig)||void 0===e?void 0:e.showFlashMessages)&&(this.options.showFlashMessages=window.clerkAjaxCartConfig.showFlashMessages),this._createGlobalFunction()}_getSnippet(e){try{var t,s;return(null===(t=window.clerkAjaxCartConfig)||void 0===t||null===(s=t.snippets)||void 0===s?void 0:s[e])||e}catch(t){return console.warn("ClerkAjaxCart: Could not access snippets, using fallback key:",e),e}}_createGlobalFunction(){const e=this;window.clerkAddToCart=(t,s=null)=>{e._addToCart(s,t,1)},window.clerkAddToCartFromButton=t=>{const s=t.getAttribute("data-clerk-product-id")||t.dataset.productId;s?e._addToCart(t,s,1):console.error("Product not found.")}}_addToCart(e,t,s){e&&(e.classList.add(this.options.loadingClass),e.disabled=!0);const a=new FormData;a.append("lineItems["+t+"][id]",t),a.append("lineItems["+t+"][type]","product"),a.append("lineItems["+t+"][quantity]",s),a.append("lineItems["+t+"][referencedId]",t),a.append("lineItems["+t+"][stackable]",1),a.append("lineItems["+t+"][removable]",1),this._client.post(window.clerkAjaxCartConfig.addToCartUrl,a,(t=>this._onAddToCartSuccess(e,t)),"application/json",!1,(t=>this._onAddToCartError(e,t)))}_onAddToCartSuccess(e,t){let s;try{s="string"==typeof t?JSON.parse(t):t}catch(e){s={success:!0}}e&&(e.classList.remove(this.options.loadingClass),e.disabled=!1,e.classList.add(this.options.successClass),this._showSuccessMessage(e,s),setTimeout((()=>{e.classList.remove(this.options.successClass)}),2e3)),this._updateCartCounter(),this._triggerCartUpdate();const a=new CustomEvent("clerkAjaxCartSuccess",{detail:{button:e,response:s}});document.dispatchEvent(a)}_onAddToCartError(e,t){e&&(e.classList.remove(this.options.loadingClass),e.disabled=!1,e.classList.add(this.options.errorClass),this._showErrorMessage(e,t),setTimeout((()=>{e.classList.remove(this.options.errorClass)}),2e3));const s=new CustomEvent("clerkAjaxCartError",{detail:{button:e,error:t}});document.dispatchEvent(s)}_showSuccessMessage(e,t){if(this.options.showFlashMessages){let e;e=t.message?t.message:this._getSnippet("productAddedToCart.fallback"),this._showFlashMessage(e,"success")}}_showErrorMessage(e,t){this.options.showFlashMessages&&this._showFlashMessage(this._getSnippet("failedToAddProduct"),"error")}_showFlashMessage(e,t){document.querySelectorAll(".clerk-flash-message").forEach((e=>this._removeFlashMessage(e)));const s=document.createElement("div");s.className=`clerk-flash-message clerk-flash-${t}`;const a=document.createElement("div");a.className="clerk-flash-content";const r=document.createElement("span");r.className="clerk-flash-text",r.textContent=e;const o=document.createElement("button");o.className="clerk-flash-close",o.innerHTML="×",o.setAttribute("aria-label","Close"),o.addEventListener("click",(()=>this._removeFlashMessage(s))),a.appendChild(r),a.appendChild(o),s.appendChild(a),document.body.appendChild(s),requestAnimationFrame((()=>{s.classList.add("show")})),setTimeout((()=>{this._removeFlashMessage(s)}),"error"===t?2e3:3e3)}_removeFlashMessage(e){e&&e.parentNode&&(e.classList.remove("show"),setTimeout((()=>{e.parentNode&&e.remove()}),300))}_updateCartCounter(){const e=document.querySelectorAll(".header-cart-count, .cart-counter, .cart-badge, .header-cart-badge, [data-cart-counter], .js-cart-counter, .cart-quantity, .header-cart-quantity");this._client.get("/widgets/checkout/info",(t=>{try{const s=new DOMParser,a=s.parseFromString(t,"text/html").querySelector(".header-cart-count, .cart-counter, .cart-badge, .header-cart-badge, [data-cart-counter], .js-cart-counter, .cart-quantity, .header-cart-quantity");if(a&&e.length>0){const t=a.textContent.trim();e.forEach((e=>{e.textContent=t,e.classList.add("updated"),setTimeout((()=>e.classList.remove("updated")),1e3)}))}else console.warn("ClerkAjaxCart: Could not update cart counter - no counter found in response or on page")}catch(e){console.error("Error updating cart counter:",e)}}),"text/html")}_triggerCartUpdate(){const e=new CustomEvent("cart-widget-update");document.dispatchEvent(e);const t=new CustomEvent("offcanvas-cart-update");document.dispatchEvent(t);const s=document.querySelector("[data-cart-widget]");if(s&&window.PluginManager){const e=window.PluginManager.getPluginInstanceFromElement(s,"CartWidget");e&&"function"==typeof e.fetch&&e.fetch()}}}a=i,o={loadingClass:"is-loading",successClass:"is-success",errorClass:"is-error",showFlashMessages:!0},(r=function(e){var t=function(e,t){if("object"!=typeof e||null===e)return e;var s=e[Symbol.toPrimitive];if(void 0!==s){var a=s.call(e,t||"default");if("object"!=typeof a)return a;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:String(t)}(r="options"))in a?Object.defineProperty(a,r,{value:o,enumerable:!0,configurable:!0,writable:!0}):a[r]=o;window.PluginManager.register("ClerkAjaxCart",i,"body")}},e=>{e.O(0,["vendor-node","vendor-shared"],(()=>{return t=5771,e(e.s=t);var t}));e.O()}]);
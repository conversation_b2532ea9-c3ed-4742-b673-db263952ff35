<?php declare(strict_types=1);

namespace ClerkAjaxCart\Storefront\Controller;

use Shopware\Core\Checkout\Cart\Cart;
use Shopware\Core\Checkout\Cart\CartCalculator;
use Shopware\Core\Checkout\Cart\LineItem\LineItem;
use Shopware\Core\Checkout\Cart\SalesChannel\CartService;
use Shopware\Core\Content\Product\ProductEntity;
use Shopware\Core\Framework\DataAbstractionLayer\Search\Criteria;
use Shopware\Core\Framework\Routing\Annotation\RouteScope;
use Shopware\Core\System\SalesChannel\Entity\SalesChannelRepository;
use Shopware\Core\System\SalesChannel\SalesChannelContext;
use Shopware\Storefront\Controller\StorefrontController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Contracts\Translation\TranslatorInterface;

#[Route(defaults: ['_routeScope' => ['storefront']])]
class ClerkCartController extends StorefrontController
{
    public function __construct(
        private readonly CartService $cartService,
        private readonly CartCalculator $cartCalculator,
        private readonly SalesChannelRepository $productRepository,
        private readonly TranslatorInterface $translator
    ) {
    }

    #[Route(
        path: '/clerk/checkout/line-item/add',
        name: 'clerk.frontend.checkout.line-item.add',
        defaults: ['XmlHttpRequest' => true],
        methods: ['POST']
    )]
    public function addLineItem(Request $request, Cart $cart, SalesChannelContext $context): JsonResponse
    {
        try {
            $lineItems = $request->request->all('lineItems');

            if (empty($lineItems)) {
                return new JsonResponse([
                    'success' => false,
                    'message' => 'No line items provided'
                ], 400);
            }

            $productNames = [];
            foreach ($lineItems as $lineItemData) {
                $lineItem = new LineItem(
                    $lineItemData['id'],
                    $lineItemData['type'] ?? LineItem::PRODUCT_LINE_ITEM_TYPE,
                    $lineItemData['referencedId'] ?? $lineItemData['id'],
                    (int) ($lineItemData['quantity'] ?? 1)
                );

                if (isset($lineItemData['stackable'])) {
                    $lineItem->setStackable((bool) $lineItemData['stackable']);
                }

                if (isset($lineItemData['removable'])) {
                    $lineItem->setRemovable((bool) $lineItemData['removable']);
                }

                // Get product name for the message
                $productName = $this->getProductName($lineItemData['referencedId'] ?? $lineItemData['id'], $context);
                if ($productName) {
                    $productNames[] = $productName;
                }

                $cart = $this->cartService->add($cart, $lineItem, $context);
            }

            // Calculate cart totals
            $cart = $this->cartCalculator->calculate($cart, $context);

            // Create dynamic message with product name(s)
            $message = $this->createSuccessMessage($productNames);

            return new JsonResponse([
                'success' => true,
                'message' => $message,
                'cartCount' => $cart->getLineItems()->count(),
                'cartTotal' => $cart->getPrice()->getTotalPrice()
            ]);

        } catch (\Exception $e) {
            return new JsonResponse([
                'success' => false,
                'message' => 'Failed to add product to cart: ' . $e->getMessage()
            ], 500);
        }
    }

    private function getProductName(string $productId, SalesChannelContext $context): ?string
    {
        try {
            $criteria = new Criteria([$productId]);
            $product = $this->productRepository->search($criteria, $context)->first();

            if ($product instanceof ProductEntity) {
                return $product->getTranslated()['name'] ?? $product->getName();
            }
        } catch (\Exception $e) {
            // Log error but don't fail the cart operation
        }

        return null;
    }

    private function createSuccessMessage(array $productNames): string
    {
        if (empty($productNames)) {
            return $this->translator->trans('clerkAjaxCart.productAddedToCart.fallback');
        }

        if (count($productNames) === 1) {
            return $this->translator->trans('clerkAjaxCart.productAddedToCart', ['%product%' => $productNames[0]]);
        }

        // Multiple products - use a generic message or list them
        $productList = implode(', ', $productNames);
        return $this->translator->trans('clerkAjaxCart.productsAddedToCart', ['%products%' => $productList]);
    }
}

# BsCodeScanner - Barcode & QR Code Scanner Plugin for Shopware 6

A comprehensive barcode and QR code scanner plugin for Shopware 6.5 that enables retail customers to scan product EAN barcodes using their mobile phones and instantly view product information including name, price, and stock availability.

## Features

- **Camera-based Scanning**: Use device camera to scan barcodes and QR codes
- **Manual Input**: Allow manual entry of EAN codes
- **Real-time Product Lookup**: Instant product information retrieval
- **Mobile Responsive**: Optimized for mobile devices
- **Multiple Barcode Formats**: Supports EAN, UPC, Code 128, Code 39, and more
- **Stock Information**: Display real-time stock availability
- **Price Display**: Show product prices with currency
- **Product Images**: Display product images in scan results
- **Admin Configuration**: Comprehensive admin panel settings

## Installation

1. **Upload Plugin**: Copy the plugin to your Shopware installation:
   ```bash
   cp -r BsCodeScanner custom/plugins/
   ```

2. **Install Dependencies**: Install npm dependencies for the plugin:
   ```bash
   cd custom/plugins/BsCodeScanner/src/Resources/app/storefront
   npm install
   ```

3. **Build Assets**: Build the frontend assets:
   ```bash
   npm run build
   # or for development with watch mode:
   npm run watch
   ```

4. **Install Plugin**: Use Shopware CLI or Admin panel:
   ```bash
   cd ../../../../../../../../  # Back to Shopware root
   bin/console plugin:install --activate BsCodeScanner
   ```

5. **Build Shopware Assets**: Build Shopware storefront with plugin assets:
   ```bash
   bin/console bundle:dump
   ./bin/build-storefront.sh
   ```

6. **Clear Cache**: Clear Shopware cache:
   ```bash
   bin/console cache:clear
   ```

## Usage

### For Customers

1. **Access Scanner**: Navigate to `/barcode-scanner` on your storefront
2. **Camera Scanning**: 
   - Click "Start Scanner" to activate camera
   - Point camera at barcode/QR code
   - Scanner will automatically detect and search for product
3. **Manual Input**:
   - Enter EAN code in the manual input field
   - Click "Search" or press Enter

### For Administrators

1. **Configuration**: Go to Settings > System > Plugins > BsCodeScanner
2. **General Settings**:
   - Enable/disable scanner functionality
   - Customize page title and description
3. **Scanner Settings**:
   - Enable/disable camera scanner
   - Camera selection is automatic (prefers back camera, falls back to front camera)
4. **Display Settings**:
   - Show/hide product images
   - Show/hide stock information
   - Show/hide price information

## API Endpoints

### Product Lookup (Admin API)
```
POST /api/bs-code-scanner/product-lookup
Content-Type: application/json

{
    "ean": "1234567890123"
}
```

### Product Lookup (Storefront API)
```
POST /api/bs-code-scanner/product-lookup-storefront
Content-Type: application/json

{
    "ean": "1234567890123"
}
```

## Technical Details

### Requirements
- Shopware 6.5.x
- PHP 8.1+
- Modern browser with camera support
- HTTPS (required for camera access)

### Supported Barcode Formats
- EAN-13
- EAN-8
- UPC-A
- UPC-E
- Code 128
- Code 39
- Codabar
- Interleaved 2 of 5

### Browser Compatibility
- Chrome 53+
- Firefox 36+
- Safari 11+
- Edge 12+

## File Structure

```
BsCodeScanner/
├── composer.json
├── README.md
├── build.sh
└── src/
    ├── BsCodeScanner.php
    ├── Controller/
    │   ├── Api/
    │   │   └── ProductLookupController.php
    │   └── Storefront/
    │       └── ScannerController.php
    └── Resources/
        ├── app/
        │   └── storefront/
        │       ├── build/
        │       │   └── webpack.config.js
        │       └── src/
        │           ├── main.js
        │           └── scss/
        │               └── base.scss
        ├── config/
        │   ├── config.xml
        │   ├── routes.xml
        │   └── services.xml
        ├── public/
        │   ├── css/
        │   │   └── base.css
        │   └── js/
        │       └── main.js
        └── views/
            └── storefront/
                └── page/
                    └── scanner/
                        └── index.html.twig
```

## Troubleshooting

### Camera Not Working
- Ensure HTTPS is enabled (required for camera access)
- Check browser permissions for camera access
- Verify device has a working camera

### Products Not Found
- Ensure products have EAN codes in Shopware
- Check product availability and visibility settings
- Verify API endpoints are accessible

### Styling Issues
- Run `./build.sh` to rebuild assets
- Clear browser cache
- Check CSS file is loaded correctly

## Support

For support and bug reports, please contact the plugin developer or create an issue in the project repository.

## License

This plugin is licensed under the MIT License.

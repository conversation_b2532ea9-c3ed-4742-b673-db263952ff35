import Plugin from 'src/plugin-system/plugin.class';
import { Html5Qrcode } from 'html5-qrcode';

export default class BsCodeScannerPlugin extends Plugin {
    static options = {
        apiUrl: '/bs-code-scanner/api/product-lookup'
    };

    init() {
        this.html5QrCode = null;
        this.isScanning = false;
        this.hasScanned = false;
        this.globalLoadingSpinner = document.getElementById('globalLoadingSpinner');
        this.resultLoadingSpinner = document.getElementById('resultLoadingSpinner');
        this.productInfoCard = document.getElementById('productInfoCard');

        this.startScanBtn = this.el.querySelector('#startScanBtn');
        this.stopScanBtn = this.el.querySelector('#stopScanBtn');
        this.scannedResult = this.el.querySelector('#scannedResult');
        this.productInfo = this.el.querySelector('#productInfo');
        this.messageBox = this.el.querySelector('#messageBox');
        this.productOverlay = document.getElementById('productOverlay');
        this.scanAgainBtn = document.getElementById('scanAgainBtn');
        this.scannerScreen = document.getElementById('scannerScreen');
        this.resultScreen = document.getElementById('resultScreen');

        this.bindEvents();
        document.addEventListener('visibilitychange', () => {
            if (document.hidden) {
                this.stopScanning();
            }
        });
    }

    bindEvents() {
        if (this.startScanBtn) {
            this.startScanBtn.addEventListener('click', () => {
                this.startScanBtn.disabled = true;
                if (this.stopScanBtn) this.stopScanBtn.disabled = false;
                this.initializeScanner();
            });
        }
        if (this.stopScanBtn) {
            this.stopScanBtn.addEventListener('click', () => {
                this.stopScanBtn.disabled = true;
                if (this.startScanBtn) this.startScanBtn.disabled = false;
                this.stopScanning();
            });
        }
        if (this.scanAgainBtn) {
            this.scanAgainBtn.addEventListener('click', () => {
                console.log('[Scanner] Scan Again button clicked. Resetting hasScanned and restarting scan.');
                if (this.productOverlay) {
                    this.productOverlay.classList.add('d-none');
                }
                this.hasScanned = false;
                if (this.resultScreen) this.resultScreen.classList.add('d-none');
                if (this.scannerScreen) this.scannerScreen.classList.remove('d-none');
                this.startScanning();
            });
        }
    }

    async initializeScanner() {
        // Clean up any previous scanner instance
        if (this.html5QrCode) {
            try {
                await this.html5QrCode.stop();
            } catch (e) {
                // Ignore errors if already stopped
            }
            this.html5QrCode = null;
        }
        try {
            this.html5QrCode = new Html5Qrcode("reader");
            this.showMessage('Initializing scanner...', 'warning');
            if (this.globalLoadingSpinner) this.globalLoadingSpinner.classList.remove('d-none');
            // Auto-start scanning after initialization
            setTimeout(() => {
                this.startScanning();
            }, 500);
        } catch (error) {
            console.error('Failed to initialize scanner:', error);
            this.showMessage('Failed to initialize scanner. Please refresh the page.', 'error');
            if (this.globalLoadingSpinner) this.globalLoadingSpinner.classList.add('d-none');
        }
    }

    async startScanning() {
        console.log('[Scanner] startScanning called. isScanning:', this.isScanning, 'hasScanned:', this.hasScanned);
        if (this.isScanning || !this.html5QrCode) return;
        if (this.globalLoadingSpinner) this.globalLoadingSpinner.classList.remove('d-none');
        this.isScanning = true;
        this.hideMessage();
        this.hideProductOverlay();

        // Get the best available camera
        const cameraConfig = await this.getBestCamera();

        try {
            await this.html5QrCode.start(
                cameraConfig,
                {
                    fps: 10,
                    qrbox: { width: 250, height: 250 }
                },
                this.onScanSuccess.bind(this),
                this.onScanError.bind(this)
            );
            this.showMessage('Point your camera at a barcode or QR code', 'warning');
            if (this.globalLoadingSpinner) this.globalLoadingSpinner.classList.add('d-none');
        } catch (err) {
            this.isScanning = false;
            if (this.globalLoadingSpinner) this.globalLoadingSpinner.classList.add('d-none');

            // Hide scanner overlay and show placeholder on error
            // No scannerOverlay in new template, so no need to set display
            // No scannerPlaceholder in new template, so no need to set display

            console.error("Failed to start scanning:", err);
            this.showMessage(`Camera access required. Please allow camera permissions and refresh.`, 'error');
        }
    }

    async getBestCamera() {
        try {
            // First, try to get all available cameras
            const cameras = await Html5Qrcode.getCameras();
            console.log('Available cameras:', cameras);

            if (cameras && cameras.length > 0) {
                // Look for back camera first (environment-facing)
                const backCamera = cameras.find(camera =>
                    camera.label && (
                        camera.label.toLowerCase().includes('back') ||
                        camera.label.toLowerCase().includes('rear') ||
                        camera.label.toLowerCase().includes('environment') ||
                        camera.label.toLowerCase().includes('world') ||
                        camera.label.toLowerCase().includes('facing back')
                    )
                );

                if (backCamera) {
                    console.log('Using back camera:', backCamera.label);
                    return { deviceId: backCamera.id };
                }

                // Look for front camera as fallback
                const frontCamera = cameras.find(camera =>
                    camera.label && (
                        camera.label.toLowerCase().includes('front') ||
                        camera.label.toLowerCase().includes('user') ||
                        camera.label.toLowerCase().includes('facing front') ||
                        camera.label.toLowerCase().includes('selfie')
                    )
                );

                if (frontCamera) {
                    console.log('Using front camera:', frontCamera.label);
                    return { deviceId: frontCamera.id };
                }

                // If no specific camera found, use the first available camera
                console.log('Using first available camera:', cameras[0].label);
                return { deviceId: cameras[0].id };
            }
        } catch (error) {
            console.warn('Could not enumerate cameras, falling back to facingMode:', error);
        }

        return { facingMode: "environment" };
    }

    stopScanning() {
        if (!this.isScanning || !this.html5QrCode) return;

        console.log('Stop scanning...');
        this.isScanning = false;
        this.html5QrCode.stop().then(() => {
            // Hide scanner overlay and show placeholder
            // No scannerOverlay in new template, so no need to set display
            // No scannerPlaceholder in new template, so no need to set display

            // No need to show a message when stopping as we'll show product info
            this.hideMessage();
        }).catch((err) => {
            console.error("Failed to stop scanning:", err);
            this.showMessage('Error stopping scan.', 'error');

            // Hide scanner overlay and show placeholder on error too
            // No scannerOverlay in new template, so no need to set display
            // No scannerPlaceholder in new template, so no need to set display
        });
    }

    onScanSuccess(decodedText) {
        console.log('[Scanner] onScanSuccess called. isScanning:', this.isScanning, 'hasScanned:', this.hasScanned, 'decodedText:', decodedText);
        if (this.isScanning && !this.hasScanned) {
            this.hasScanned = true;
            if (this.scannedResult) {
                this.scannedResult.textContent = decodedText;
            }
            this.fetchProductInfo(decodedText);
        }
    }

    onScanError() {
        // Suppress verbose error messages
    }

    async fetchProductInfo(identifier) {
        console.log('[Scanner] fetchProductInfo called with identifier:', identifier);
        if (this.resultLoadingSpinner) this.resultLoadingSpinner.classList.remove('d-none');
        if (this.productInfoCard) this.productInfoCard.classList.add('d-none');

        // Hide product overlay while fetching
        this.hideProductOverlay();
        this.showMessage('Fetching product information...', 'warning');

        // Show loading spinner, hide product info
        const loadingSpinner = this.el.querySelector('#loadingSpinner');
        const productInfo = this.el.querySelector('#productInfo');
        if (loadingSpinner) loadingSpinner.classList.remove('d-none');
        if (productInfo) productInfo.classList.add('d-none');

        try {
            const response = await fetch(this.options.apiUrl, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Accept': 'application/json'
                },
                body: JSON.stringify({ ean: identifier })
            });

            const data = await response.json();

            this.hideMessage();

            // Hide loading spinner, show product info
            if (loadingSpinner) loadingSpinner.classList.add('d-none');
            if (productInfo) productInfo.classList.remove('d-none');

            if (data.success && data.product) {
                this.displayProductInfo({
                    name: data.product.name || '',
                    ean: data.product.ean || identifier,
                    price: data.product.price ? `${data.product.price.gross} ${data.product.price.currency || 'Kr.'}` : '',
                    stock: data.product.stock || 0,
                    url: data.product.coverUrl || (data.product.media && data.product.media.url) || '',
                    sku: data.product.productNumber || ''
                });
            } else {
                this.displayProductInfo({ name: '', sku: '', price: '', stock: 0 });
            }
        } catch (error) {
            this.hideMessage();
            console.error('Error fetching product:', error);
            // Hide loading spinner, show product info
            if (loadingSpinner) loadingSpinner.classList.add('d-none');
            if (productInfo) productInfo.classList.remove('d-none');
            this.displayProductInfo({ name: '', sku: '', price: '', stock: 0 });
        } finally {
            if (this.resultLoadingSpinner) this.resultLoadingSpinner.classList.add('d-none');
            if (this.productInfoCard) this.productInfoCard.classList.remove('d-none');
        }
    }

    displayProductInfo(product) {
        if (this.globalLoadingSpinner) this.globalLoadingSpinner.classList.add('d-none');
        if (product) {
            this.stopScanning();
            // Update product information
            const productName = this.el.querySelector('#productName');
            const productSKU = this.el.querySelector('#productSKU');
            const productPrice = this.el.querySelector('#productPrice');
            const productStock = this.el.querySelector('#productStock');
            const productImage = this.el.querySelector('#productImage');

            if (productName) productName.textContent = product.name || '';
            if (productSKU) productSKU.textContent = product.sku || '';
            if (productPrice) productPrice.textContent = product.price || '';
            if (productStock) productStock.textContent = product.stock || '0';

            // Show/hide product image
            if (productImage) {
                if (product.url) {
                    productImage.src = product.url;
                    productImage.alt = product.name || 'Product image';
                    productImage.classList.remove('d-none');
                } else {
                    productImage.src = '';
                    productImage.alt = '';
                    productImage.classList.add('d-none');
                }
            }

            this.hideMessage();
            if (this.scannerScreen) this.scannerScreen.classList.add('d-none');
            if (this.resultScreen) this.resultScreen.classList.remove('d-none');
        }
    }

    hideProductOverlay() {
        if (this.productOverlay) {
            this.productOverlay.classList.add('d-none');
        }
    }

    showMessage(message, type = 'warning') {
        if (this.globalLoadingSpinner) this.globalLoadingSpinner.classList.add('d-none');
        this.messageBox.textContent = message;
        this.messageBox.className = `message-box ${type === 'error' ? 'error' : ''}`;
        this.messageBox.classList.remove('d-none');
    }

    hideMessage() {
        this.messageBox.classList.add('d-none');
    }
}

{% block block_barcode_scanner %}
    <div class="container-fluid p-0 d-flex justify-content-center align-items-center" data-bs-code-scanner="true">
        <div class="scanner-container bg-white mb-3 w-100">
            
            <div id="scannerScreen">
                <div class="message-box d-none" id="messageBox"></div>
                <div id="reader" class="w-100 bg-light mb-4" style="min-height: 220px;"></div>
                <div class="d-flex flex-column flex-sm-row gap-3 justify-content-center mb-3">
                    <button id="startScanBtn" class="btn btn-primary">{{ "bs-code-scanner.startScan"|trans }}</button>
                    <button id="stopScanBtn" class="btn btn-secondary" disabled>{{ "bs-code-scanner.stopScan"|trans }}</button>
                </div>

            </div>
            <div id="resultScreen" class="d-none">
                <div id="resultLoadingSpinner" class="loading-spinner d-none">
                    <div class="spinner"></div>
                </div>
                <div class="product-info-card p-4 d-none" id="productInfoCard">
                    <div class="row g-3 align-items-center flex-nowrap">

                        {% if config('BsCodeScanner.config.showProductImage') %}
                        <div class="col-6 text-center mb-3 mb-md-0">
                            <img id="productImage" src="" alt="Product image" class="img-fluid d-none" style="max-height: 180px; max-width: 100%; object-fit: contain;" />
                        </div>
                        {% endif %}

                        <div class="col-6">
                            <div class="fw-bold pc-label small mb-1">{{ "bs-code-scanner.name"|trans }}</div>
                            <div id="productName" class="mb-2"></div>

                            <div class="fw-bold pc-label small mb-1">{{ "bs-code-scanner.sku"|trans }}</div>
                            <div id="productSKU" class="mb-2"></div>

                            {% if config('BsCodeScanner.config.showPrice') %}
                                <div class="fw-bold pc-label small mb-1">{{ "bs-code-scanner.price"|trans }}</div>
                                <div id="productPrice" class="mb-2 text-primary"></div>
                            {% endif %}

                            {% if config('BsCodeScanner.config.showStock') %}
                                <div class="fw-bold pc-label small mb-1">{{ "bs-code-scanner.stock"|trans }}</div>
                                <div id="productStock" class="mb-2"></div>
                            {% endif %}
                        </div>
                    </div>
                    <button id="scanAgainBtn" class="btn btn-primary mt-4 w-100">{{ "bs-code-scanner.scanAgain"|trans }}</button>
                </div>
            </div>
        </div>
    </div>
{% endblock %}

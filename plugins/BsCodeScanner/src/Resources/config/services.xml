<?xml version="1.0" ?>

<container xmlns="http://symfony.com/schema/dic/services"
           xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
           xsi:schemaLocation="http://symfony.com/schema/dic/services http://symfony.com/schema/dic/services/services-1.0.xsd">

    <services>
        <!-- API Controllers -->
        <service id="BsCodeScanner\Controller\Api\ProductLookupController" public="true">
            <argument type="service" id="sales_channel.product.repository"/>
            <call method="setContainer">
                <argument type="service" id="service_container" />
            </call>
            <tag name="controller.service_arguments"/>
        </service>

        <!-- Storefront Controllers -->
<!--        <service id="BsCodeScanner\Controller\Storefront\ScannerController" public="true">-->
<!--            <argument type="service" id="Shopware\Core\System\SystemConfig\SystemConfigService"/>-->
<!--            <call method="setContainer">-->
<!--                <argument type="service" id="service_container"/>-->
<!--            </call>-->
<!--            <call method="setTwig">-->
<!--                <argument type="service" id="twig"/>-->
<!--            </call>-->
<!--            <tag name="controller.service_arguments"/>-->
<!--        </service>-->

    </services>
</container>
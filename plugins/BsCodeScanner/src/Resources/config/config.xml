<?xml version="1.0" encoding="UTF-8"?>

<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="https://raw.githubusercontent.com/shopware/shopware/trunk/src/Core/System/SystemConfig/Schema/config.xsd">

    <card>
        <title>General Settings</title>

        <input-field type="bool">
            <name>active</name>
            <label>Enable Barcode Scanner</label>
            <helpText>Enable or disable the barcode scanner functionality</helpText>
            <defaultValue>true</defaultValue>
        </input-field>
    </card>

    <card>
        <title>Scanner Settings</title>

        <input-field type="bool">
            <name>enableCameraScanner</name>
            <label>Enable Camera Scanner</label>
            <helpText>Allow users to scan barcodes using device camera</helpText>
            <defaultValue>true</defaultValue>
        </input-field>


    </card>

    <card>
        <title>Display Settings</title>

        <input-field type="bool">
            <name>showProductImage</name>
            <label>Show Product Images</label>
            <helpText>Display product images in scan results</helpText>
            <defaultValue>true</defaultValue>
        </input-field>

        <input-field type="bool">
            <name>showStock</name>
            <label>Show Stock Information</label>
            <helpText>Display stock availability in scan results</helpText>
            <defaultValue>true</defaultValue>
        </input-field>

        <input-field type="bool">
            <name>showPrice</name>
            <label>Show Price Information</label>
            <helpText>Display product prices in scan results</helpText>
            <defaultValue>true</defaultValue>
        </input-field>
    </card>

</config>
